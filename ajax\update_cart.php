<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Ph<PERSON>ơng thức không được hỗ trợ'
    ]);
    exit;
}

// Lấy dữ liệu
$product_id = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;
$quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;

// Kiểm tra dữ liệu
if ($product_id <= 0 || $quantity <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Dữ liệu không hợp lệ'
    ]);
    exit;
}

// Cập nhật giỏ hàng
$result = update_cart_item($product_id, $quantity);

// Thêm thông tin bổ sung cho phản hồi
if ($result['success']) {
    // Cập nhật thời gian cập nhật giỏ hàng
    $_SESSION['cart_updated'] = time() * 1000; // Chuyển đổi sang milliseconds để phù hợp với JavaScript

    // Lấy dữ liệu giỏ hàng mới nhất
    $cart_count = get_cart_count(); // Tổng số lượng sản phẩm
    $cart_items_count = get_cart_items_count(); // Số lượng items khác nhau
    $cart_total = get_cart_total();

    $result['count'] = $cart_count; // Tổng số lượng sản phẩm (cho badge)
    $result['items_count'] = $cart_items_count; // Số lượng items khác nhau (cho hiển thị "X sản phẩm")
    $result['total'] = format_currency($cart_total);
    $result['raw_total'] = $cart_total;
    $result['timestamp'] = $_SESSION['cart_updated'];

    // Lưu thông báo cập nhật giỏ hàng vào session để hiển thị sau khi tải lại trang
    $product = get_product_by_id($product_id);
    $product_name = $product ? $product['name'] : 'Sản phẩm';

    // Lưu kết quả cập nhật vào session
    $_SESSION['cart_update_result'] = [
        'success' => true,
        'message' => 'Cập nhật thành công',
        'detail' => "Đã cập nhật số lượng sản phẩm \"$product_name\" thành $quantity",
        'timestamp' => $_SESSION['cart_updated']
    ];

    // Lưu thông tin sản phẩm đã cập nhật
    $_SESSION['cart_update_info'] = [
        'productId' => $product_id,
        'quantity' => $quantity
    ];

    // Log để debug
    error_log("Cart updated - Product ID: $product_id, Quantity: $quantity, Total Count: $cart_count");
}

// Thêm header để ngăn cache
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: application/json');

// Trả về kết quả
echo json_encode($result);
?>
