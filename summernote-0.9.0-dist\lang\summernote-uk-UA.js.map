{"version": 3, "file": "lang/summernote-uk-UA.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,aAAa;QACnBC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE,uBAAuB;QAC9BC,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,OAAO;QACbC,aAAa,EAAE,aAAa;QAC5BC,SAAS,EAAE,eAAe;QAC1BC,WAAW,EAAE,gBAAgB;QAC7BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,UAAU;QACjBC,MAAM,EAAE,mBAAmB;QAC3BC,UAAU,EAAE,kBAAkB;QAC9BC,UAAU,EAAE,iBAAiB;QAC7BC,aAAa,EAAE,iBAAiB;QAChCC,SAAS,EAAE,qBAAqB;QAChCC,UAAU,EAAE,sBAAsB;QAClCC,SAAS,EAAE,wBAAwB;QACnCC,YAAY,EAAE,oBAAoB;QAClCC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,cAAc;QACzBC,aAAa,EAAE,2BAA2B;QAC1CC,SAAS,EAAE,sBAAsB;QACjCC,eAAe,EAAE,kBAAkB;QACnCC,eAAe,EAAE,mBAAmB;QACpCC,oBAAoB,EAAE,6BAA6B;QACnDC,GAAG,EAAE,cAAc;QACnBC,MAAM,EAAE,mBAAmB;QAC3BC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,oBAAoB;QAC/BpB,MAAM,EAAE,gBAAgB;QACxBgB,GAAG,EAAE,WAAW;QAChBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,WAAW;QACjBtB,MAAM,EAAE,oBAAoB;QAC5BuB,MAAM,EAAE,oBAAoB;QAC5BC,IAAI,EAAE,YAAY;QAClBC,aAAa,EAAE,0BAA0B;QACzCT,GAAG,EAAE,kBAAkB;QACvBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,WAAW,EAAE,mBAAmB;QAChCC,WAAW,EAAE,oBAAoB;QACjCC,UAAU,EAAE,yBAAyB;QACrCC,WAAW,EAAE,0BAA0B;QACvCC,MAAM,EAAE,gBAAgB;QACxBC,MAAM,EAAE,mBAAmB;QAC3BC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,YAAY;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,KAAK;QACVC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,mBAAmB;QAC9BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,UAAU;QAChBC,UAAU,EAAE,eAAe;QAC3BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,kBAAkB;QAC3BC,MAAM,EAAE,mBAAmB;QAC3BC,IAAI,EAAE,0BAA0B;QAChCC,MAAM,EAAE,qBAAqB;QAC7BC,KAAK,EAAE,2BAA2B;QAClCC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE,YAAY;QACxBC,UAAU,EAAE,cAAc;QAC1BC,WAAW,EAAE,UAAU;QACvBC,cAAc,EAAE,kBAAkB;QAClCC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,mBAAmB;QAC9BC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE,qBAAqB;QACrCC,MAAM,EAAE,KAAK;QACbC,mBAAmB,EAAE,wBAAwB;QAC7CC,aAAa,EAAE,iBAAiB;QAChCC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,kBAAkB;QACrC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,yBAAyB;QACjC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,kBAAkB;QAC1B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-uk-UA.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'uk-UA': {\n      font: {\n        bold: 'Напівжирний',\n        italic: 'Кур<PERSON>ив',\n        underline: 'Підкреслений',\n        clear: 'Прибрати стилі шрифту',\n        height: 'Висота лінії',\n        name: 'Шр<PERSON><PERSON><PERSON>',\n        strikethrough: 'Закреслений',\n        subscript: 'Нижній індекс',\n        superscript: 'Верхній індекс',\n        size: 'Розмір шрифту',\n      },\n      image: {\n        image: 'Картинка',\n        insert: 'Вставити картинку',\n        resizeFull: 'Відновити розмір',\n        resizeHalf: 'Зменшити до 50%',\n        resizeQuarter: 'Зменшити до 25%',\n        floatLeft: 'Розташувати ліворуч',\n        floatRight: 'Розташувати праворуч',\n        floatNone: 'Початкове розташування',\n        shapeRounded: 'Форма: Заокруглена',\n        shapeCircle: 'Форма: Коло',\n        shapeThumbnail: 'Форма: Мініатюра',\n        shapeNone: 'Форма: Немає',\n        dragImageHere: 'Перетягніть сюди картинку',\n        dropImage: 'Перетягніть картинку',\n        selectFromFiles: 'Вибрати з файлів',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'URL картинки',\n        remove: 'Видалити картинку',\n        original: 'Original',\n      },\n      video: {\n        video: 'Відео',\n        videoLink: 'Посилання на відео',\n        insert: 'Вставити відео',\n        url: 'URL відео',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion чи Youku)',\n      },\n      link: {\n        link: 'Посилання',\n        insert: 'Вставити посилання',\n        unlink: 'Прибрати посилання',\n        edit: 'Редагувати',\n        textToDisplay: 'Текст, що відображається',\n        url: 'URL для переходу',\n        openInNewWindow: 'Відкрити у новому вікні',\n      },\n      table: {\n        table: 'Таблиця',\n        addRowAbove: 'Додати рядок вище',\n        addRowBelow: 'Додати рядок нижче',\n        addColLeft: 'Додати стовпчик ліворуч',\n        addColRight: 'Додати стовпчик праворуч',\n        delRow: 'Видалити рядок',\n        delCol: 'Видалити стовпчик',\n        delTable: 'Видалити таблицю',\n      },\n      hr: {\n        insert: 'Вставити горизонтальну лінію',\n      },\n      style: {\n        style: 'Стиль',\n        p: 'Нормальний',\n        blockquote: 'Цитата',\n        pre: 'Код',\n        h1: 'Заголовок 1',\n        h2: 'Заголовок 2',\n        h3: 'Заголовок 3',\n        h4: 'Заголовок 4',\n        h5: 'Заголовок 5',\n        h6: 'Заголовок 6',\n      },\n      lists: {\n        unordered: 'Маркований список',\n        ordered: 'Нумерований список',\n      },\n      options: {\n        help: 'Допомога',\n        fullscreen: 'На весь екран',\n        codeview: 'Початковий код',\n      },\n      paragraph: {\n        paragraph: 'Параграф',\n        outdent: 'Зменшити відступ',\n        indent: 'Збільшити відступ',\n        left: 'Вирівняти по лівому краю',\n        center: 'Вирівняти по центру',\n        right: 'Вирівняти по правому краю',\n        justify: 'Розтягнути по ширині',\n      },\n      color: {\n        recent: 'Останній колір',\n        more: 'Ще кольори',\n        background: 'Колір фону',\n        foreground: 'Колір шрифту',\n        transparent: 'Прозорий',\n        setTransparent: 'Зробити прозорим',\n        reset: 'Відновити',\n        resetToDefault: 'Відновити початкові',\n      },\n      shortcut: {\n        shortcuts: 'Комбінації клавіш',\n        close: 'Закрити',\n        textFormatting: 'Форматування тексту',\n        action: 'Дія',\n        paragraphFormatting: 'Форматування параграфу',\n        documentStyle: 'Стиль документу',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Відмінити',\n        redo: 'Повторити',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}