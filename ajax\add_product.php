<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    echo json_encode([
        'success' => false,
        'message' => 'Bạn không có quyền thực hiện thao tác này'
    ]);
    exit;
}

// Kiểm tra request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Phương thức không được hỗ trợ'
    ]);
    exit;
}

// Kiểm tra CSRF token
if (!check_csrf_token($_POST['csrf_token'])) {
    echo json_encode([
        'success' => false,
        'message' => 'CSRF token không hợp lệ'
    ]);
    exit;
}

// L<PERSON>y dữ liệu từ form
$category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0;
$name = isset($_POST['name']) ? sanitize($_POST['name']) : '';
$description = isset($_POST['description']) ? sanitize($_POST['description']) : '';
$content = isset($_POST['content']) ? $_POST['content'] : '';
$price = isset($_POST['price']) ? (float)$_POST['price'] : 0;
$sale_price = isset($_POST['sale_price']) ? (float)$_POST['sale_price'] : 0;
$quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 0;
$status = isset($_POST['status']) ? (int)$_POST['status'] : 0;
$featured = isset($_POST['featured']) ? (int)$_POST['featured'] : 0;
$rating = isset($_POST['rating']) ? (int)$_POST['rating'] : 5;
$sold = isset($_POST['sold']) ? (int)$_POST['sold'] : 0;
$views = isset($_POST['views']) ? (int)$_POST['views'] : 0;
$flash_sale = isset($_POST['flash_sale']) ? (int)$_POST['flash_sale'] : 0;
$material = isset($_POST['material']) ? sanitize($_POST['material']) : '';
$dimensions = isset($_POST['dimensions']) ? sanitize($_POST['dimensions']) : '';
$color = isset($_POST['color']) ? sanitize($_POST['color']) : '';
$price_type = isset($_POST['price_type']) ? sanitize($_POST['price_type']) : 'fixed';
$size_options = isset($_POST['size_options']) ? $_POST['size_options'] : '';
$meta_title = isset($_POST['meta_title']) ? sanitize($_POST['meta_title']) : '';
$meta_description = isset($_POST['meta_description']) ? sanitize($_POST['meta_description']) : '';

// Kiểm tra dữ liệu
if (empty($name) || $category_id <= 0 || $price < 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Vui lòng nhập đầy đủ thông tin bắt buộc'
    ]);
    exit;
}

// Tạo slug từ tên sản phẩm
$slug = create_slug($name);

// Kiểm tra slug đã tồn tại chưa
$check_slug = check_slug_exists($slug, 'products');
if ($check_slug) {
    $slug = $slug . '-' . time();
}

// Xử lý upload hình ảnh
$image = '';
if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
    $upload_result = upload_image($_FILES['image'], 'products');
    if ($upload_result['success']) {
        $image = $upload_result['filename'];
    } else {
        echo json_encode([
            'success' => false,
            'message' => $upload_result['message']
        ]);
        exit;
    }
}

// Xử lý upload gallery
$gallery = [];
if (isset($_FILES['gallery']) && is_array($_FILES['gallery']['name'])) {
    for ($i = 0; $i < count($_FILES['gallery']['name']); $i++) {
        if ($_FILES['gallery']['error'][$i] === UPLOAD_ERR_OK) {
            $file = [
                'name' => $_FILES['gallery']['name'][$i],
                'type' => $_FILES['gallery']['type'][$i],
                'tmp_name' => $_FILES['gallery']['tmp_name'][$i],
                'error' => $_FILES['gallery']['error'][$i],
                'size' => $_FILES['gallery']['size'][$i]
            ];
            
            $upload_result = upload_image($file, 'products');
            if ($upload_result['success']) {
                $gallery[] = $upload_result['filename'];
            }
        }
    }
}

// Chuyển gallery thành chuỗi JSON
$gallery_string = !empty($gallery) ? json_encode($gallery) : '';

// Chuyển size_options thành chuỗi JSON nếu có
$size_options_json = !empty($size_options) ? $size_options : null;

// Tạo dữ liệu sản phẩm
$product_data = [
    'category_id' => $category_id,
    'name' => $name,
    'slug' => $slug,
    'description' => $description,
    'content' => $content,
    'price' => $price,
    'sale_price' => $sale_price,
    'image' => $image,
    'gallery' => $gallery_string,
    'quantity' => $quantity,
    'status' => $status,
    'featured' => $featured,
    'rating' => $rating,
    'sold' => $sold,
    'views' => $views,
    'flash_sale' => $flash_sale,
    'material' => $material,
    'dimensions' => $dimensions,
    'color' => $color,
    'price_type' => $price_type,
    'size_options' => $size_options_json,
    'meta_title' => $meta_title,
    'meta_description' => $meta_description
];

// Thêm sản phẩm mới
$result = add_product($product_data);

// Nếu thêm thành công, lấy thông tin sản phẩm vừa thêm
if ($result['success']) {
    $product = get_product_by_id($result['product_id']);
    $category = get_category_by_id($product['category_id']);
    
    // Định dạng lại dữ liệu sản phẩm để hiển thị
    $formatted_product = [
        'id' => $product['id'],
        'name' => $product['name'],
        'category_name' => $category['name'],
        'price' => format_currency($product['price']),
        'sale_price' => !empty($product['sale_price']) ? format_currency($product['sale_price']) : 'Không có',
        'quantity' => $product['quantity'],
        'status' => $product['status'],
        'image' => $product['image'],
        'image_url' => !empty($product['image']) ? BASE_URL . '/uploads/products/' . $product['image'] : '',
        'created_at' => date('d/m/Y H:i', strtotime($product['created_at']))
    ];
    
    $result['product'] = $formatted_product;
}

// Trả về kết quả
header('Content-Type: application/json');
echo json_encode($result);
?>
