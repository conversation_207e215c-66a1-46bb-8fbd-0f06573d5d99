{"version": 3, "file": "lang/summernote-ko-KR.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,IAAI;QACVC,WAAW,EAAE,MAAM;QACnBC,SAAS,EAAE,OAAO;QAClBC,aAAa,EAAE,KAAK;QACpBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,OAAO;QACfC,UAAU,EAAE,aAAa;QACzBC,UAAU,EAAE,YAAY;QACxBC,aAAa,EAAE,YAAY;QAC3BC,UAAU,EAAE,OAAO;QACnBC,SAAS,EAAE,OAAO;QAClBC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,SAAS;QACpBC,YAAY,EAAE,aAAa;QAC3BC,WAAW,EAAE,SAAS;QACtBC,cAAc,EAAE,SAAS;QACzBC,SAAS,EAAE,SAAS;QACpBC,aAAa,EAAE,uBAAuB;QACtCC,SAAS,EAAE,mBAAmB;QAC9BC,eAAe,EAAE,OAAO;QACxBC,eAAe,EAAE,UAAU;QAC3BC,oBAAoB,EAAE,mBAAmB;QACzCC,GAAG,EAAE,QAAQ;QACbC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,KAAK;QACZC,SAAS,EAAE,QAAQ;QACnBrB,MAAM,EAAE,QAAQ;QAChBiB,GAAG,EAAE,SAAS;QACdK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,IAAI;QACVvB,MAAM,EAAE,OAAO;QACfwB,MAAM,EAAE,OAAO;QACfC,IAAI,EAAE,IAAI;QACVC,aAAa,EAAE,YAAY;QAC3BT,GAAG,EAAE,SAAS;QACdU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,GAAG;QACVC,WAAW,EAAE,SAAS;QACtBC,WAAW,EAAE,UAAU;QACvBC,UAAU,EAAE,UAAU;QACtBC,WAAW,EAAE,WAAW;QACxBC,MAAM,EAAE,OAAO;QACfC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFpC,MAAM,EAAE;MACV,CAAC;MACDqC,KAAK,EAAE;QACLA,KAAK,EAAE,KAAK;QACZC,CAAC,EAAE,IAAI;QACPC,UAAU,EAAE,KAAK;QACjBC,GAAG,EAAE,IAAI;QACTC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,KAAK;QACXC,UAAU,EAAE,OAAO;QACnBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,OAAO;QAClBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,KAAK;QACjBC,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE,SAAS;QACzBC,KAAK,EAAE,IAAI;QACXC,cAAc,EAAE,UAAU;QAC1BC,QAAQ,EAAE;MACZ,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,SAAS;QACpBC,KAAK,EAAE,IAAI;QACXC,cAAc,EAAE,WAAW;QAC3BC,MAAM,EAAE,IAAI;QACZC,mBAAmB,EAAE,WAAW;QAChCC,aAAa,EAAE,WAAW;QAC1BC,SAAS,EAAE;MACb,CAAC;MACD3B,IAAI,EAAE;QACJ,iBAAiB,EAAE,OAAO;QAC1B,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,YAAY;QACpB,KAAK,EAAE,GAAG;QACV,OAAO,EAAE,MAAM;QACf,MAAM,EAAE,WAAW;QACnB,QAAQ,EAAE,aAAa;QACvB,WAAW,EAAE,WAAW;QACxB,eAAe,EAAE,YAAY;QAC7B,cAAc,EAAE,OAAO;QACvB,aAAa,EAAE,SAAS;QACxB,eAAe,EAAE,UAAU;QAC3B,cAAc,EAAE,UAAU;QAC1B,aAAa,EAAE,WAAW;QAC1B,qBAAqB,EAAE,cAAc;QACrC,mBAAmB,EAAE,cAAc;QACnC,SAAS,EAAE,YAAY;QACvB,QAAQ,EAAE,YAAY;QACtB,YAAY,EAAE,uBAAuB;QACrC,UAAU,EAAE,wBAAwB;QACpC,UAAU,EAAE,wBAAwB;QACpC,UAAU,EAAE,wBAAwB;QACpC,UAAU,EAAE,wBAAwB;QACpC,UAAU,EAAE,wBAAwB;QACpC,UAAU,EAAE,wBAAwB;QACpC,sBAAsB,EAAE,QAAQ;QAChC,iBAAiB,EAAE;MACrB,CAAC;MACD4B,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,MAAM;QACnBC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ko-KR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'ko-KR': {\n      font: {\n        bold: '굵게',\n        italic: '기울임꼴',\n        underline: '밑줄',\n        clear: '서식 지우기',\n        height: '줄 간격',\n        name: '글꼴',\n        superscript: '위 첨자',\n        subscript: '아래 첨자',\n        strikethrough: '취소선',\n        size: '글자 크기',\n      },\n      image: {\n        image: '그림',\n        insert: '그림 삽입',\n        resizeFull: '100% 크기로 변경',\n        resizeHalf: '50% 크기로 변경',\n        resizeQuarter: '25% 크기로 변경',\n        resizeNone: '원본 크기',\n        floatLeft: '왼쪽 정렬',\n        floatRight: '오른쪽 정렬',\n        floatNone: '정렬하지 않음',\n        shapeRounded: '스타일: 둥근 모서리',\n        shapeCircle: '스타일: 원형',\n        shapeThumbnail: '스타일: 액자',\n        shapeNone: '스타일: 없음',\n        dragImageHere: '텍스트 혹은 사진을 이곳으로 끌어오세요',\n        dropImage: '텍스트 혹은 사진을 내려놓으세요',\n        selectFromFiles: '파일 선택',\n        maximumFileSize: '최대 파일 크기',\n        maximumFileSizeError: '최대 파일 크기를 초과했습니다.',\n        url: '사진 URL',\n        remove: '사진 삭제',\n        original: '원본',\n      },\n      video: {\n        video: '동영상',\n        videoLink: '동영상 링크',\n        insert: '동영상 삽입',\n        url: '동영상 URL',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion, Youku 사용 가능)',\n      },\n      link: {\n        link: '링크',\n        insert: '링크 삽입',\n        unlink: '링크 삭제',\n        edit: '수정',\n        textToDisplay: '링크에 표시할 내용',\n        url: '이동할 URL',\n        openInNewWindow: '새창으로 열기',\n      },\n      table: {\n        table: '표',\n        addRowAbove: '위에 행 삽입',\n        addRowBelow: '아래에 행 삽입',\n        addColLeft: '왼쪽에 열 삽입',\n        addColRight: '오른쪽에 열 삽입',\n        delRow: '행 지우기',\n        delCol: '열 지우기',\n        delTable: '표 삭제',\n      },\n      hr: {\n        insert: '구분선 삽입',\n      },\n      style: {\n        style: '스타일',\n        p: '본문',\n        blockquote: '인용구',\n        pre: '코드',\n        h1: '제목 1',\n        h2: '제목 2',\n        h3: '제목 3',\n        h4: '제목 4',\n        h5: '제목 5',\n        h6: '제목 6',\n      },\n      lists: {\n        unordered: '글머리 기호',\n        ordered: '번호 매기기',\n      },\n      options: {\n        help: '도움말',\n        fullscreen: '전체 화면',\n        codeview: '코드 보기',\n      },\n      paragraph: {\n        paragraph: '문단 정렬',\n        outdent: '내어쓰기',\n        indent: '들여쓰기',\n        left: '왼쪽 정렬',\n        center: '가운데 정렬',\n        right: '오른쪽 정렬',\n        justify: '양쪽 정렬',\n      },\n      color: {\n        recent: '마지막으로 사용한 색',\n        more: '다른 색 선택',\n        background: '배경색',\n        foreground: '글자색',\n        transparent: '투명',\n        setTransparent: '투명으로 설정',\n        reset: '취소',\n        resetToDefault: '기본값으로 설정',\n        cpSelect: '선택',\n      },\n      shortcut: {\n        shortcuts: '키보드 단축키',\n        close: '닫기',\n        textFormatting: '글자 스타일 적용',\n        action: '기능',\n        paragraphFormatting: '문단 스타일 적용',\n        documentStyle: '문서 스타일 적용',\n        extraKeys: '추가 키',\n      },\n      help: {\n        'insertParagraph': '문단 삽입',\n        'undo': '마지막 명령 취소',\n        'redo': '마지막 명령 재실행',\n        'tab': '탭',\n        'untab': '탭 제거',\n        'bold': '굵은 글자로 설정',\n        'italic': '기울임꼴 글자로 설정',\n        'underline': '밑줄 글자로 설정',\n        'strikethrough': '취소선 글자로 설정',\n        'removeFormat': '서식 삭제',\n        'justifyLeft': '왼쪽 정렬하기',\n        'justifyCenter': '가운데 정렬하기',\n        'justifyRight': '오른쪽 정렬하기',\n        'justifyFull': '좌우채움 정렬하기',\n        'insertUnorderedList': '글머리 기호 켜고 끄기',\n        'insertOrderedList': '번호 매기기 켜고 끄기',\n        'outdent': '현재 문단 내어쓰기',\n        'indent': '현재 문단 들여쓰기',\n        'formatPara': '현재 블록의 포맷을 문단(P)으로 변경',\n        'formatH1': '현재 블록의 포맷을 제목1(H1)로 변경',\n        'formatH2': '현재 블록의 포맷을 제목2(H2)로 변경',\n        'formatH3': '현재 블록의 포맷을 제목3(H3)로 변경',\n        'formatH4': '현재 블록의 포맷을 제목4(H4)로 변경',\n        'formatH5': '현재 블록의 포맷을 제목5(H5)로 변경',\n        'formatH6': '현재 블록의 포맷을 제목6(H6)로 변경',\n        'insertHorizontalRule': '구분선 삽입',\n        'linkDialog.show': '링크 대화상자 열기',\n      },\n      history: {\n        undo: '실행 취소',\n        redo: '재실행',\n      },\n      specialChar: {\n        specialChar: '특수문자',\n        select: '특수문자를 선택하세요',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "superscript", "subscript", "strikethrough", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "resizeNone", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}