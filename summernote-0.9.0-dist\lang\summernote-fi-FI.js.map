{"version": 3, "file": "lang/summernote-fi-FI.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,YAAY;QAClBC,MAAM,EAAE,aAAa;QACrBC,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,mBAAmB;QAC1BC,MAAM,EAAE,UAAU;QAClBC,IAAI,EAAE,gBAAgB;QACtBC,aAAa,EAAE,YAAY;QAC3BC,SAAS,EAAE,YAAY;QACvBC,WAAW,EAAE,YAAY;QACzBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,YAAY;QACpBC,UAAU,EAAE,aAAa;QACzBC,UAAU,EAAE,iBAAiB;QAC7BC,aAAa,EAAE,kBAAkB;QACjCC,SAAS,EAAE,oBAAoB;QAC/BC,UAAU,EAAE,kBAAkB;QAC9BC,SAAS,EAAE,eAAe;QAC1BC,YAAY,EAAE,oBAAoB;QAClCC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,uBAAuB;QACvCC,SAAS,EAAE,qBAAqB;QAChCC,aAAa,EAAE,iBAAiB;QAChCC,eAAe,EAAE,sBAAsB;QACvCC,eAAe,EAAE,uBAAuB;QACxCC,oBAAoB,EAAE,iCAAiC;QACvDC,GAAG,EAAE,sBAAsB;QAC3BC,MAAM,EAAE,aAAa;QACrBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,gBAAgB;QAC3BnB,MAAM,EAAE,aAAa;QACrBe,GAAG,EAAE,mBAAmB;QACxBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,QAAQ;QACdrB,MAAM,EAAE,cAAc;QACtBsB,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,SAAS;QACfC,aAAa,EAAE,mBAAmB;QAClCT,GAAG,EAAE,mBAAmB;QACxBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,UAAU;QACjBC,WAAW,EAAE,wBAAwB;QACrCC,WAAW,EAAE,wBAAwB;QACrCC,UAAU,EAAE,kCAAkC;QAC9CC,WAAW,EAAE,gCAAgC;QAC7CC,MAAM,EAAE,aAAa;QACrBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFlC,MAAM,EAAE;MACV,CAAC;MACDmC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,UAAU;QACbC,UAAU,EAAE,SAAS;QACrBC,GAAG,EAAE,OAAO;QACZC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,2BAA2B;QACtCC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,MAAM;QACZC,UAAU,EAAE,aAAa;QACzBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,SAAS;QACpBC,OAAO,EAAE,qBAAqB;QAC9BC,MAAM,EAAE,qBAAqB;QAC7BC,IAAI,EAAE,kBAAkB;QACxBC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAE,gBAAgB;QACvBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE,cAAc;QAC1BC,UAAU,EAAE,cAAc;QAC1BC,WAAW,EAAE,YAAY;QACzBC,cAAc,EAAE,qBAAqB;QACrCC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,eAAe;QAC1BC,KAAK,EAAE,OAAO;QACdC,cAAc,EAAE,kBAAkB;QAClCC,MAAM,EAAE,UAAU;QAClBC,mBAAmB,EAAE,oBAAoB;QACzCC,aAAa,EAAE;MACjB,CAAC;MACDzB,IAAI,EAAE;QACJ,iBAAiB,EAAE,eAAe;QAClC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,+BAA+B;QACvC,KAAK,EAAE,SAAS;QAChB,OAAO,EAAE,sBAAsB;QAC/B,MAAM,EAAE,YAAY;QACpB,QAAQ,EAAE,UAAU;QACpB,WAAW,EAAE,aAAa;QAC1B,eAAe,EAAE,YAAY;QAC7B,cAAc,EAAE,wBAAwB;QACxC,aAAa,EAAE,kBAAkB;QACjC,eAAe,EAAE,SAAS;QAC1B,cAAc,EAAE,gBAAgB;QAChC,aAAa,EAAE,OAAO;QACtB,qBAAqB,EAAE,mCAAmC;QAC1D,mBAAmB,EAAE,iBAAiB;QACtC,SAAS,EAAE,qBAAqB;QAChC,QAAQ,EAAE,qBAAqB;QAC/B,YAAY,EAAE,6BAA6B;QAC3C,UAAU,EAAE,8BAA8B;QAC1C,UAAU,EAAE,8BAA8B;QAC1C,UAAU,EAAE,8BAA8B;QAC1C,UAAU,EAAE,8BAA8B;QAC1C,UAAU,EAAE,8BAA8B;QAC1C,UAAU,EAAE,8BAA8B;QAC1C,sBAAsB,EAAE,kBAAkB;QAC1C,iBAAiB,EAAE;MACrB,CAAC;MACD0B,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,eAAe;QAC5BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-fi-FI.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'fi-FI': {\n      font: {\n        bold: 'Lihavointi',\n        italic: 'Kursivointi',\n        underline: 'Alleviivaus',\n        clear: 'Ty<PERSON>je<PERSON><PERSON> muotoilu',\n        height: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        strikethrough: 'Y<PERSON><PERSON><PERSON><PERSON>',\n        subscript: '<PERSON><PERSON><PERSON>',\n        superscript: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        size: 'Kir<PERSON><PERSON><PERSON>',\n      },\n      image: {\n        image: '<PERSON><PERSON>',\n        insert: 'Lisää kuva',\n        resizeFull: '<PERSON><PERSON> leveys',\n        resizeHalf: 'Puo<PERSON><PERSON> leveys',\n        resizeQuarter: 'Neljä<PERSON><PERSON> leveys',\n        floatLeft: 'Si<PERSON>ita vasemmalle',\n        floatRight: 'Si<PERSON>ita oikealle',\n        floatNone: 'Ei sijoitusta',\n        shapeRounded: 'Muoto: Pyöristetty',\n        shapeCircle: 'Muoto: Ympyrä',\n        shapeThumbnail: 'Muoto: Esikatselukuva',\n        shapeNone: 'Muoto: Ei muotoilua',\n        dragImageHere: 'Vedä kuva tähän',\n        selectFromFiles: 'Val<PERSON>e tiedostoista',\n        maximumFileSize: '<PERSON><PERSON><PERSON><PERSON> tiedosto koko',\n        maximumFileSizeError: 'Maksimi tiedosto koko ylitetty.',\n        url: 'URL-osoitteen mukaan',\n        remove: 'Poista kuva',\n        original: 'Alkuperäinen',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Linkki videoon',\n        insert: 'Lisää video',\n        url: 'Videon URL-osoite',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion tai Youku)',\n      },\n      link: {\n        link: 'Linkki',\n        insert: 'Lisää linkki',\n        unlink: 'Poista linkki',\n        edit: 'Muokkaa',\n        textToDisplay: 'Näytettävä teksti',\n        url: 'Linkin URL-osoite',\n        openInNewWindow: 'Avaa uudessa ikkunassa',\n      },\n      table: {\n        table: 'Taulukko',\n        addRowAbove: 'Lisää rivi yläpuolelle',\n        addRowBelow: 'Lisää rivi alapuolelle',\n        addColLeft: 'Lisää sarake vasemmalle puolelle',\n        addColRight: 'Lisää sarake oikealle puolelle',\n        delRow: 'Poista rivi',\n        delCol: 'Poista sarake',\n        delTable: 'Poista taulukko',\n      },\n      hr: {\n        insert: 'Lisää vaakaviiva',\n      },\n      style: {\n        style: 'Tyyli',\n        p: 'Normaali',\n        blockquote: 'Lainaus',\n        pre: 'Koodi',\n        h1: 'Otsikko 1',\n        h2: 'Otsikko 2',\n        h3: 'Otsikko 3',\n        h4: 'Otsikko 4',\n        h5: 'Otsikko 5',\n        h6: 'Otsikko 6',\n      },\n      lists: {\n        unordered: 'Luettelomerkitty luettelo',\n        ordered: 'Numeroitu luettelo',\n      },\n      options: {\n        help: 'Ohje',\n        fullscreen: 'Koko näyttö',\n        codeview: 'HTML-näkymä',\n      },\n      paragraph: {\n        paragraph: 'Kappale',\n        outdent: 'Pienennä sisennystä',\n        indent: 'Suurenna sisennystä',\n        left: 'Tasaa vasemmalle',\n        center: 'Keskitä',\n        right: 'Tasaa oikealle',\n        justify: 'Tasaa',\n      },\n      color: {\n        recent: 'Viimeisin väri',\n        more: 'Lisää värejä',\n        background: 'Korostusväri',\n        foreground: 'Tekstin väri',\n        transparent: 'Läpinäkyvä',\n        setTransparent: 'Aseta läpinäkyväksi',\n        reset: 'Palauta',\n        resetToDefault: 'Palauta oletusarvoksi',\n      },\n      shortcut: {\n        shortcuts: 'Pikanäppäimet',\n        close: 'Sulje',\n        textFormatting: 'Tekstin muotoilu',\n        action: 'Toiminto',\n        paragraphFormatting: 'Kappaleen muotoilu',\n        documentStyle: 'Asiakirjan tyyli',\n      },\n      help: {\n        'insertParagraph': 'Lisää kappale',\n        'undo': 'Kumoa viimeisin komento',\n        'redo': 'Tee uudelleen kumottu komento',\n        'tab': 'Sarkain',\n        'untab': 'Sarkainmerkin poisto',\n        'bold': 'Lihavointi',\n        'italic': 'Kursiivi',\n        'underline': 'Alleviivaus',\n        'strikethrough': 'Yliviivaus',\n        'removeFormat': 'Poista asetetut tyylit',\n        'justifyLeft': 'Tasaa vasemmalle',\n        'justifyCenter': 'Keskitä',\n        'justifyRight': 'Tasaa oikealle',\n        'justifyFull': 'Tasaa',\n        'insertUnorderedList': 'Luettelomerkillä varustettu lista',\n        'insertOrderedList': 'Numeroitu lista',\n        'outdent': 'Pienennä sisennystä',\n        'indent': 'Suurenna sisennystä',\n        'formatPara': 'Muuta kappaleen formaatti p',\n        'formatH1': 'Muuta kappaleen formaatti H1',\n        'formatH2': 'Muuta kappaleen formaatti H2',\n        'formatH3': 'Muuta kappaleen formaatti H3',\n        'formatH4': 'Muuta kappaleen formaatti H4',\n        'formatH5': 'Muuta kappaleen formaatti H5',\n        'formatH6': 'Muuta kappaleen formaatti H6',\n        'insertHorizontalRule': 'Lisää vaakaviiva',\n        'linkDialog.show': 'Lisää linkki',\n      },\n      history: {\n        undo: 'Kumoa',\n        redo: 'Toista',\n      },\n      specialChar: {\n        specialChar: 'ERIKOISMERKIT',\n        select: 'Valitse erikoismerkit',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}