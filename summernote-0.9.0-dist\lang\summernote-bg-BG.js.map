{"version": 3, "file": "lang/summernote-bg-BG.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAE,UAAU;QAClBC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,mBAAmB;QAC1BC,MAAM,EAAE,UAAU;QAClBC,IAAI,EAAE,OAAO;QACbC,aAAa,EAAE,YAAY;QAC3BC,SAAS,EAAE,cAAc;QACzBC,WAAW,EAAE,cAAc;QAC3BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,aAAa;QACpBC,MAAM,EAAE,kBAAkB;QAC1BC,UAAU,EAAE,YAAY;QACxBC,UAAU,EAAE,eAAe;QAC3BC,aAAa,EAAE,eAAe;QAC9BC,SAAS,EAAE,iBAAiB;QAC5BC,UAAU,EAAE,kBAAkB;QAC9BC,SAAS,EAAE,kBAAkB;QAC7BC,YAAY,EAAE,iBAAiB;QAC/BC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,YAAY;QACvBC,aAAa,EAAE,2BAA2B;QAC1CC,SAAS,EAAE,+BAA+B;QAC1CC,eAAe,EAAE,eAAe;QAChCC,eAAe,EAAE,4BAA4B;QAC7CC,oBAAoB,EAAE,uCAAuC;QAC7DC,GAAG,EAAE,0BAA0B;QAC/BC,MAAM,EAAE,sBAAsB;QAC9BC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,YAAY;QACvBpB,MAAM,EAAE,cAAc;QACtBgB,GAAG,EAAE,YAAY;QACjBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,QAAQ;QACdtB,MAAM,EAAE,eAAe;QACvBuB,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,SAAS;QACfC,aAAa,EAAE,oBAAoB;QACnCT,GAAG,EAAE,WAAW;QAChBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,WAAW,EAAE,mBAAmB;QAChCC,WAAW,EAAE,mBAAmB;QAChCC,UAAU,EAAE,sBAAsB;QAClCC,WAAW,EAAE,uBAAuB;QACpCC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,UAAU;QACbC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,KAAK;QACVC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,iBAAiB;QAC5BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,cAAc;QAC1BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,uBAAuB;QAChCC,MAAM,EAAE,OAAO;QACfC,IAAI,EAAE,qBAAqB;QAC3BC,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE,sBAAsB;QAC7BC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,uBAAuB;QAC/BC,IAAI,EAAE,aAAa;QACnBC,UAAU,EAAE,cAAc;QAC1BC,UAAU,EAAE,gBAAgB;QAC5BC,WAAW,EAAE,WAAW;QACxBC,cAAc,EAAE,qBAAqB;QACrCC,KAAK,EAAE,YAAY;QACnBC,cAAc,EAAE,yBAAyB;QACzCC,QAAQ,EAAE;MACZ,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,qBAAqB;QAChCC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE,uBAAuB;QACvCC,MAAM,EAAE,UAAU;QAClBC,mBAAmB,EAAE,yBAAyB;QAC9CC,aAAa,EAAE,mBAAmB;QAClCC,SAAS,EAAE;MACb,CAAC;MACD3B,IAAI,EAAE;QACJ,iBAAiB,EAAE,iBAAiB;QACpC,MAAM,EAAE,2BAA2B;QACnC,MAAM,EAAE,0BAA0B;QAClC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,SAAS;QACjB,QAAQ,EAAE,uBAAuB;QACjC,WAAW,EAAE,qBAAqB;QAClC,eAAe,EAAE,wBAAwB;QACzC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,qBAAqB;QACpC,eAAe,EAAE,wBAAwB;QACzC,cAAc,EAAE,sBAAsB;QACtC,aAAa,EAAE,yBAAyB;QACxC,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,sBAAsB,EAAE,6BAA6B;QACrD,iBAAiB,EAAE;MACrB,CAAC;MACD4B,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-bg-BG.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'bg-BG': {\n      font: {\n        bold: 'Удебелен',\n        italic: 'Наклонен',\n        underline: 'Подчертан',\n        clear: 'Изчисти стиловете',\n        height: 'Вис<PERSON>чи<PERSON>',\n        name: 'Шриф<PERSON>',\n        strikethrough: 'Задраскано',\n        subscript: 'Долен индекс',\n        superscript: 'Горен индекс',\n        size: 'Размер на шрифта',\n      },\n      image: {\n        image: 'Изображение',\n        insert: 'Постави картинка',\n        resizeFull: 'Цял размер',\n        resizeHalf: 'Размер на 50%',\n        resizeQuarter: 'Размер на 25%',\n        floatLeft: 'Подравни в ляво',\n        floatRight: 'Подравни в дясно',\n        floatNone: 'Без подравняване',\n        shapeRounded: 'Форма: Заоблено',\n        shapeCircle: 'Форма: Кръг',\n        shapeThumbnail: 'Форма: Миниатюра',\n        shapeNone: 'Форма: Без',\n        dragImageHere: 'Пуснете изображението тук',\n        dropImage: 'Пуснете Изображение или Текст',\n        selectFromFiles: 'Изберете файл',\n        maximumFileSize: 'Максимален размер на файла',\n        maximumFileSizeError: 'Достигнат Максимален размер на файла.',\n        url: 'URL адрес на изображение',\n        remove: 'Премахни изображение',\n        original: 'Оригинал',\n      },\n      video: {\n        video: 'Видео',\n        videoLink: 'Видео линк',\n        insert: 'Добави Видео',\n        url: 'Видео URL?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'Връзка',\n        insert: 'Добави връзка',\n        unlink: 'Премахни връзка',\n        edit: 'Промени',\n        textToDisplay: 'Текст за показване',\n        url: 'URL адрес',\n        openInNewWindow: 'Отвори в нов прозорец',\n      },\n      table: {\n        table: 'Таблица',\n        addRowAbove: 'Добави ред отгоре',\n        addRowBelow: 'Добави ред отдолу',\n        addColLeft: 'Добави колона отляво',\n        addColRight: 'Добави колона отдясно',\n        delRow: 'Изтрии ред',\n        delCol: 'Изтрии колона',\n        delTable: 'Изтрии таблица',\n      },\n      hr: {\n        insert: 'Добави хоризонтална линия',\n      },\n      style: {\n        style: 'Стил',\n        p: 'Нормален',\n        blockquote: 'Цитат',\n        pre: 'Код',\n        h1: 'Заглавие 1',\n        h2: 'Заглавие 2',\n        h3: 'Заглавие 3',\n        h4: 'Заглавие 4',\n        h5: 'Заглавие 5',\n        h6: 'Заглавие 6',\n      },\n      lists: {\n        unordered: 'Символен списък',\n        ordered: 'Цифров списък',\n      },\n      options: {\n        help: 'Помощ',\n        fullscreen: 'На цял екран',\n        codeview: 'Преглед на код',\n      },\n      paragraph: {\n        paragraph: 'Параграф',\n        outdent: 'Намаляване на отстъпа',\n        indent: 'Абзац',\n        left: 'Подравняване в ляво',\n        center: 'Център',\n        right: 'Подравняване в дясно',\n        justify: 'Разтягане по ширина',\n      },\n      color: {\n        recent: 'Последния избран цвят',\n        more: 'Още цветове',\n        background: 'Цвят на фона',\n        foreground: 'Цвят на шрифта',\n        transparent: 'Прозрачен',\n        setTransparent: 'Направете прозрачен',\n        reset: 'Възстанови',\n        resetToDefault: 'Възстанови оригиналните',\n        cpSelect: 'Изберете',\n      },\n      shortcut: {\n        shortcuts: 'Клавишни комбинации',\n        close: 'Затвори',\n        textFormatting: 'Форматиране на текста',\n        action: 'Действие',\n        paragraphFormatting: 'Форматиране на параграф',\n        documentStyle: 'Стил на документа',\n        extraKeys: 'Екстра бутони',\n      },\n      help: {\n        'insertParagraph': 'Добави Параграф',\n        'undo': 'Отмени последната промяна',\n        'redo': 'Върни последната промяна',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Удебели',\n        'italic': 'Приложи наклонен стил',\n        'underline': 'Приложи подчераване',\n        'strikethrough': 'Приложи зачеркнат стил',\n        'removeFormat': 'Изчисти стилове',\n        'justifyLeft': 'Подравняване в ляво',\n        'justifyCenter': 'Подравняване в центъра',\n        'justifyRight': 'Подравняване в дясно',\n        'justifyFull': 'Двустранно подравняване',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Вмъкни хоризонтално правило',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Назад',\n        redo: 'Напред',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Избери Специални символи',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}