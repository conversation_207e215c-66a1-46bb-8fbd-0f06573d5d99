/**
 * Search Z-Index Fix CSS
 * Khắc phục vấn đề search-suggestions bị che khuất bởi bottom-header-container
 */

:root {
    /* Z-index hierarchy cho search system */
    --z-search-base: 1000;
    --z-search-container: 1100;
    --z-search-form: 1110;
    --z-search-suggestions: 1200;
    --z-search-overlay: 1150;
}

/* 
 * GIẢI PHÁP 1: Tạo stacking context riêng cho search-container
 * Sử dụng isolation để tạo stacking context mới
 */
.search-container {
    position: relative !important;
    z-index: var(--z-search-container) !important;
    isolation: isolate !important; /* Tạo stacking context mới */
    /* Transform hack để đảm bảo stacking context */
    transform: translateZ(0) !important;
    will-change: transform !important;
}

/* Đảm bảo search-form có z-index cao */
.search-form {
    position: relative !important;
    z-index: var(--z-search-form) !important;
    /* Tạo stacking context cho form */
    transform: translateZ(0) !important;
}

/* 
 * GIẢI PHÁP 2: Đặt search-suggestions ở mức cao nhất
 * Sử dụng z-index cực cao và đảm bảo không bị override
 */
.search-suggestions {
    position: absolute !important;
    top: calc(100% + 4px) !important; /* Dịch xuống một chút như trang tìm kiếm */
    left: 0 !important;
    right: 0 !important;
    z-index: var(--z-search-suggestions) !important;

    /* Tạo stacking context riêng */
    isolation: isolate !important;
    transform: translateZ(0) !important;

    /* Đảm bảo hiển thị trên tất cả */
    pointer-events: auto !important;

    /* Style cơ bản */
    background-color: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 12px !important; /* Bo góc cả 4 viền như trang tìm kiếm */
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important; /* Shadow giống trang tìm kiếm */

    /* Animation */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) translateZ(0);
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Khi search-suggestions được hiển thị */
.search-suggestions:not(.hidden),
.search-suggestions.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) translateZ(0) !important;
}

/* 
 * GIẢI PHÁP 3: Đảm bảo bottom-header-container không che khuất
 * Giảm z-index của bottom-header-container khi cần thiết
 */
.premium-header .bottom-header-container {
    position: relative !important;
    z-index: 50 !important; /* Thấp hơn search-container */
}

/* Đảm bảo mid-header-container có z-index phù hợp */
.premium-header .mid-header-container {
    position: relative !important;
    z-index: 100 !important; /* Cao hơn bottom-header nhưng thấp hơn search */
}

/* 
 * GIẢI PHÁP 4: Xử lý overflow và containment
 * Đảm bảo các container cha không clip search-suggestions
 */
.premium-header {
    overflow: visible !important;
    contain: none !important;
}

.mid-header {
    overflow: visible !important;
    contain: none !important;
}

.bottom-header {
    overflow: visible !important;
    contain: none !important;
}

/* 
 * GIẢI PHÁP 5: Backup z-index cho các trường hợp đặc biệt
 * Sử dụng z-index cực cao nếu các giải pháp trên không hiệu quả
 */
.search-suggestions.force-top {
    z-index: 99999 !important;
    position: fixed !important;
    /* Sẽ được tính toán bằng JavaScript nếu cần */
}

/* 
 * Responsive fixes
 */
@media (max-width: 768px) {
    .search-container {
        z-index: var(--z-search-container) !important;
    }
    
    .search-suggestions {
        z-index: var(--z-search-suggestions) !important;
        /* Trên mobile có thể cần z-index cao hơn */
        max-height: 60vh !important;
    }
}

/* 
 * Debug mode - uncomment để debug z-index issues
 */
/*
.search-container {
    background-color: rgba(255, 0, 0, 0.1) !important;
    border: 2px solid red !important;
}

.search-suggestions {
    background-color: rgba(0, 255, 0, 0.1) !important;
    border: 2px solid green !important;
}

.bottom-header-container {
    background-color: rgba(0, 0, 255, 0.1) !important;
    border: 2px solid blue !important;
}
*/

/* 
 * Fallback cho các browser cũ
 */
@supports not (isolation: isolate) {
    .search-container {
        /* Fallback cho browsers không hỗ trợ isolation */
        position: relative !important;
        z-index: 9999 !important;
    }
    
    .search-suggestions {
        z-index: 99999 !important;
    }
}

/* 
 * Performance optimizations
 */
.search-suggestions {
    /* Tối ưu rendering performance */
    backface-visibility: hidden;
    perspective: 1000px;
    transform-style: preserve-3d;
}

/* 
 * Accessibility improvements
 */
.search-suggestions[aria-hidden="false"] {
    /* Đảm bảo hiển thị khi screen reader đang đọc */
    opacity: 1 !important;
    visibility: visible !important;
    z-index: var(--z-search-suggestions) !important;
}
