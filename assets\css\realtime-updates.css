/**
 * CSS cho các hiệu <PERSON>ng cập nhật thời gian thực
 */

/* <PERSON><PERSON><PERSON>ng thêm vào giỏ hàng */
.cart-animation {
    position: fixed;
    background-color: rgba(249, 115, 22, 0.4);
    border-radius: 50%;
    z-index: 9999;
    pointer-events: none;
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    will-change: transform, opacity, top, left, width, height;
    box-shadow: 0 0 15px rgba(249, 115, 22, 0.6);
    animation: cart-animation-glow 0.6s ease-in-out;
}

/* Hiệu ứng đặc biệt cho mobile */
@media (max-width: 576px) {
    .cart-animation {
        transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
        animation: cart-animation-glow-mobile 0.8s ease-in-out;
        z-index: 99999 !important;
        /* Đ<PERSON><PERSON> b<PERSON>o hiển thị trên tất cả các phần tử khác */
    }

    /* <PERSON><PERSON><PERSON>ng đặc bi<PERSON>t cho nút giỏ hàng trên mobile khi active - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT */
    .mobile-nav-item.active {
        /* animation: mobile-nav-item-active 0.8s ease-in-out; */
    }

    @keyframes mobile-nav-item-active {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.1);
        }

        100% {
            transform: scale(1);
        }
    }
}

@keyframes cart-animation-glow {
    0% {
        box-shadow: 0 0 15px rgba(249, 115, 22, 0.6);
    }

    50% {
        box-shadow: 0 0 25px rgba(249, 115, 22, 0.8);
    }

    100% {
        box-shadow: 0 0 15px rgba(249, 115, 22, 0.6);
    }
}

@keyframes cart-animation-glow-mobile {
    0% {
        box-shadow: 0 0 15px rgba(249, 115, 22, 0.6);
    }

    50% {
        box-shadow: 0 0 30px rgba(249, 115, 22, 0.9);
    }

    100% {
        box-shadow: 0 0 15px rgba(249, 115, 22, 0.6);
    }
}

/* Hiệu ứng nhấp nháy cho badge giỏ hàng - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT */
.cart-badge.badge-pulse,
.mobile-nav-badge.badge-pulse {
    /* animation: badge-pulse-effect 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) 2; */
    transform-origin: center;
    will-change: transform;
}

/* Hiệu ứng đặc biệt cho badge mobile - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT */
.mobile-nav-badge.badge-pulse {
    /* animation: mobile-badge-pulse-effect 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) 2; */
}

@keyframes badge-pulse-effect {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.3);
        box-shadow: 0 0 10px rgba(249, 115, 22, 0.7);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes mobile-badge-pulse-effect {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.5);
        box-shadow: 0 0 12px rgba(249, 115, 22, 0.8);
    }

    100% {
        transform: scale(1);
    }
}

/* Hiệu ứng loading cho nút */
.add-to-cart-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Hiệu ứng cho nút cập nhật giỏ hàng */
.update-cart-btn {
    transition: all 0.3s ease;
}

.update-cart-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Hiệu ứng thông báo - Lưu ý: CSS chính được định nghĩa trong index.php */
/* CSS này chỉ được sử dụng khi không có CSS trong index.php */
.notification-container {
    position: fixed;
    top: 120px;
    right: 1.5rem;
    z-index: 999;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    pointer-events: none;
    max-width: 90%;
    transition: top 0.3s ease;
}

/* Thiết kế đặc biệt cho thông báo giỏ hàng */
.notification.cart-notification {
    background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-left-color: var(--primary-color, #f97316);
    box-shadow: 0 10px 25px rgba(249, 115, 22, 0.15), 0 5px 10px rgba(0, 0, 0, 0.05);
    transform: translateY(-20px);
    opacity: 0;
    will-change: transform, opacity;
    animation: slide-down 0.4s ease-out forwards;
}

@keyframes slide-down {
    0% {
        transform: translateY(-20px);
        opacity: 0;
        will-change: transform, opacity;
    }

    100% {
        transform: translateY(0);
        opacity: 1;
        will-change: transform, opacity;
    }
}

.notification.cart-notification .notification-icon {
    background-color: rgba(249, 115, 22, 0.1);
    color: var(--primary-color, #f97316);
    animation: pulse-icon 1s ease-in-out;
}

@keyframes pulse-icon {
    0% {
        transform: scale(0.8);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}

.notification.cart-notification .notification-message {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    font-size: 14.5px;
}

.notification.cart-notification .text-primary {
    color: var(--primary-color, #f97316);
}

.notification.cart-notification .hover\:underline:hover {
    text-decoration: underline;
}

.notification.cart-notification .notification-progress-bar {
    background-color: var(--primary-color, #f97316);
    animation: progress 3s linear forwards;
}

/* Các CSS cơ bản cho thông báo */
.notification {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.95);
    border-left: 4px solid #f97316;
    border-radius: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    padding: 16px;
    overflow: hidden;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
    max-width: 320px;
    width: 100%;
    opacity: 0;
    pointer-events: auto;
    position: relative;
    margin-bottom: 0.5rem;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    flex-shrink: 0;
    margin-right: 14px;
    font-size: 16px;
}

.notification-content {
    flex: 1;
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: rgba(0, 0, 0, 0.05);
}

.notification-progress-bar {
    height: 100%;
    width: 100%;
    transform-origin: left;
    animation: progress-animation 3s linear forwards;
}

@keyframes progress-animation {
    0% {
        transform: scaleX(1);
    }

    100% {
        transform: scaleX(0);
    }
}

@keyframes slide-out {
    0% {
        transform: translateX(0);
        opacity: 1;
    }

    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Media queries cho thông báo */
@media (max-width: 768px) {
    .notification-container {
        right: 1rem;
        max-width: calc(100% - 2rem);
    }

    .notification {
        max-width: 100%;
    }
}

@media (max-width: 576px) {
    .notification-container {
        right: 0.5rem;
        max-width: calc(100% - 1rem);
    }

    .notification {
        padding: 14px;
    }

    .notification-icon {
        width: 32px;
        height: 32px;
        margin-right: 10px;
    }
}

/* Hiệu ứng loading */
#loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Hiệu ứng highlight cho hàng mới thêm */
.highlight-row {
    animation: highlightFade 3s;
}

@keyframes highlightFade {
    0% {
        background-color: rgba(40, 167, 69, 0.2);
    }

    100% {
        background-color: transparent;
    }
}

/* Hiệu ứng cho tiến trình đơn hàng */
.progress-step {
    transition: all 0.3s ease-in-out;
}

.progress-step.completed .fas {
    animation: checkmark 0.5s ease-in-out;
}

@keyframes checkmark {
    0% {
        transform: scale(0);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}

.progress-step.active {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }

    100% {
        opacity: 1;
    }
}

/* Hiệu ứng cập nhật trạng thái */
.order-status,
.order-detail-status {
    transition: all 0.3s ease-in-out;
    display: inline-flex;
    align-items: center;
}

/* Đảm bảo hiển thị đúng cho các trạng thái */
.order-status.bg-yellow-100,
.order-detail-status.bg-yellow-100 {
    background-color: rgba(254, 249, 195, 1) !important;
    color: rgba(133, 77, 14, 1) !important;
    border: 1px solid rgba(254, 240, 138, 1) !important;
}

.order-status.bg-blue-100,
.order-detail-status.bg-blue-100 {
    background-color: rgba(219, 234, 254, 1) !important;
    color: rgba(30, 64, 175, 1) !important;
    border: 1px solid rgba(191, 219, 254, 1) !important;
}

.order-status.bg-indigo-100,
.order-detail-status.bg-indigo-100 {
    background-color: rgba(224, 231, 255, 1) !important;
    color: rgba(55, 48, 163, 1) !important;
    border: 1px solid rgba(199, 210, 254, 1) !important;
}

.order-status.bg-green-100,
.order-detail-status.bg-green-100 {
    background-color: rgba(220, 252, 231, 1) !important;
    color: rgba(22, 101, 52, 1) !important;
    border: 1px solid rgba(187, 247, 208, 1) !important;
}

.order-status.bg-red-100,
.order-detail-status.bg-red-100 {
    background-color: rgba(254, 226, 226, 1) !important;
    color: rgba(153, 27, 27, 1) !important;
    border: 1px solid rgba(254, 202, 202, 1) !important;
}

.order-status.updated,
.order-detail-status.updated {
    animation: statusUpdate 1s ease-in-out;
}

@keyframes statusUpdate {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

/* Hiệu ứng cho nút đánh giá */
.review-button {
    transition: all 0.3s ease-in-out;
}

.review-button.show {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hiệu ứng cho container đánh giá */
.review-container {
    transition: all 0.3s ease-in-out;
}

.review-container.show {
    animation: slideDown 0.5s ease-in-out;
}

@keyframes slideDown {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}