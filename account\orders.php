<?php
// Bắt đầu session và include các file cần thiết
require_once '../includes/init.php';

// Xử lý hủy đơn hàng trước khi xuất bất kỳ HTML nào
if (isset($_GET['action']) && $_GET['action'] === 'cancel' && isset($_GET['id'])) {
    $order_id = (int)$_GET['id'];

    // Kiểm tra đăng nhập
    if (!is_logged_in()) {
        set_flash_message('error', 'Vui lòng đăng nhập để truy cập trang này.');
        redirect(BASE_URL . '/login.php');
    }

    // Kiểm tra đơn hàng thuộc về người dùng
    $order = get_order_by_id($order_id);

    if ($order && $order['user_id'] == $_SESSION['user_id']) {
        $result = cancel_order($order_id);

        if ($result['success']) {
            set_flash_message('success', $result['message']);
        } else {
            set_flash_message('error', $result['message']);
        }
    } else {
        set_flash_message('error', 'Đơn hàng không tồn tại hoặc không thuộc về bạn.');
    }

    redirect(BASE_URL . '/account/orders.php');
}

// Kiểm tra đăng nhập
if (!is_logged_in()) {
    // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
    $_SESSION['redirect_url'] = current_url();

    set_flash_message('error', 'Vui lòng đăng nhập để truy cập trang này.');
    redirect(BASE_URL . '/login.php');
}

// Lấy thông tin người dùng
$user = get_user_by_id($_SESSION['user_id']);

// Phân trang
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Lọc theo trạng thái
$status = isset($_GET['status']) ? $_GET['status'] : null;

// Lấy danh sách đơn hàng
$orders = get_orders($limit, $offset, $_SESSION['user_id'], $status);
$total_orders = count_orders($_SESSION['user_id'], $status);
$total_pages = ceil($total_orders / $limit);

// Lấy thông tin sản phẩm đầu tiên cho mỗi đơn hàng (để hiển thị hình ảnh)
foreach ($orders as &$order) {
    $order_details = get_order_by_id($order['id']);
    if ($order_details && !empty($order_details['items'])) {
        $order['first_item'] = $order_details['items'][0];
        $order['total_items'] = count($order_details['items']);
    } else {
        $order['first_item'] = null;
        $order['total_items'] = 0;
    }
}

// Thiết lập tiêu đề trang
$page_title = 'Đơn hàng của tôi';
$page_description = 'Quản lý đơn hàng của bạn tại Nội Thất Băng Vũ';

// Include header
include_once '../partials/header.php';

// Thêm CSRF token cho AJAX
$csrf_token = generate_csrf_token();

// Thêm class logged-in vào body để JavaScript có thể kiểm tra
echo '<script>document.body.classList.add("logged-in");</script>';
?>

<!-- CSRF Token cho AJAX -->
<meta name="csrf-token" content="<?php echo $csrf_token; ?>">
<meta name="base-url" content="<?php echo BASE_URL; ?>">

<!-- Breadcrumb - Thiết kế hiện đại -->
<div class="modern-breadcrumb">
    <div class="container mx-auto px-4">
        <div class="breadcrumb-wrapper">
            <div class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>" class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-home"></i>
                    </span>
                    <span>Trang chủ</span>
                </a>
            </div>
            <div class="breadcrumb-divider">
                <i class="fas fa-chevron-right"></i>
            </div>
            <div class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>/account/profile.php" class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-user-circle"></i>
                    </span>
                    <span>Tài khoản của tôi</span>
                </a>
            </div>
            <div class="breadcrumb-divider">
                <i class="fas fa-chevron-right"></i>
            </div>
            <div class="breadcrumb-item active">
                <span class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-shopping-bag"></i>
                    </span>
                    <span>Đơn hàng của tôi</span>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Link CSS cho breadcrumb hiện đại -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-breadcrumb.css">

<!-- Account - Thiết kế hiện đại -->
<div class="py-10 bg-gradient-to-b from-white to-gray-50">
    <div class="container mx-auto px-4">
        <!-- Tiêu đề trang với badge giống trang chủ -->
        <div class="mb-8 text-center md:text-left">
            <div class="section-title-badge inline-flex items-center bg-primary/10 text-primary text-sm font-medium px-4 py-2 rounded-full mb-4">
                <span class="flex w-2 h-2 bg-primary rounded-full mr-2 animate-pulse"></span>
                Quản lý đơn hàng
            </div>
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 relative inline-block">
                <span class="relative z-10">Đơn hàng của tôi</span>
                <span class="absolute bottom-0 left-0 w-full h-1 bg-primary/30 rounded-full"></span>
            </h1>
            <p class="text-gray-600 mt-2 max-w-2xl mx-auto md:mx-0">Theo dõi và quản lý tất cả đơn hàng của bạn tại đây</p>
        </div>

        <div class="flex flex-col md:flex-row md:space-x-6">
            <!-- Sidebar - Thiết kế hiện đại -->
            <div class="md:w-1/4 mb-6 md:mb-0">
                <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-primary/20 transition-all duration-300">
                    <!-- Phần header với avatar và thông tin - Thiết kế mới -->
                    <div class="relative">
                        <!-- Background pattern -->
                        <div class="absolute inset-0 bg-gradient-to-br from-primary to-primary-dark opacity-90"></div>

                        <!-- Pattern overlay -->
                        <div class="absolute inset-0 opacity-10 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xIj48cGF0aCBkPSJNMzYgMzRjMC0yLjIgMS44LTQgNC00czQgMS44IDQgNC0xLjggNC00IDQtNC0xLjgtNC00eiIvPjxwYXRoIGQ9Ik0xNiAxNmMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00em0xNiAwYzIuMiAwIDQgMS44IDQgNHMtMS44IDQtNCA0LTQtMS44LTQtNCAxLjgtNCA0LTR6bTE2IDBjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHpNMTYgMzJjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHptMTYgMGMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00eiIvPjxwYXRoIGQ9Ik0xNiA0OGMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00em0xNiAwYzIuMiAwIDQgMS44IDQgNHMtMS44IDQtNCA0LTQtMS44LTQtNCAxLjgtNCA0LTR6bTE2IDBjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHpNMTYgMTZjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHptMTYgMGMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00em0xNiAwYzIuMiAwIDQgMS44IDQgNHMtMS44IDQtNCA0LTQtMS44LTQtNCAxLjgtNCA0LTR6TTAgMGg2MHY2MEgweiIvPjwvZz48L2c+PC9zdmc+')]"></div>
                    </div>

                    <!-- Avatar lớn căn giữa -->
                    <div class="flex flex-col items-center px-6 pt-8 pb-6 relative">
                        <div class="w-40 h-40 rounded-xl bg-white p-1.5 shadow-lg mb-5 transform transition-transform hover:scale-105 duration-300 overflow-hidden relative z-10">
                            <div class="w-full h-full rounded-xl overflow-hidden bg-gradient-to-r from-primary/20 to-primary/10 flex items-center justify-center text-4xl font-bold text-primary">
                                <?php if (!empty($user['avatar'])): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $user['avatar']; ?>" alt="Avatar" class="w-full h-full object-cover">
                                <?php else: ?>
                                    <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Thông tin người dùng -->
                        <div class="text-center w-full">
                            <h2 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $user['full_name']; ?></h2>
                            <p class="text-gray-500 text-sm flex items-center justify-center mb-3">
                                <i class="fas fa-envelope mr-2 text-primary/70"></i>
                                <?php echo $user['email']; ?>
                            </p>

                            <!-- Badge thành viên -->
                            <div class="inline-flex items-center bg-primary/10 text-primary text-xs font-medium px-3 py-1.5 rounded-full">
                                <i class="fas fa-user-check mr-1.5"></i> Thành viên
                            </div>
                        </div>
                    </div>

                    <!-- Đường phân cách -->
                    <div class="w-full h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>

                    <!-- Menu tài khoản -->
                    <div class="p-4">
                        <ul class="space-y-1">
                            <li>
                                <a href="<?php echo BASE_URL; ?>/account/profile.php"
                                   class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-primary transition-all duration-300">
                                    <i class="fas fa-user-circle mr-3 w-5 text-center"></i>
                                    <span>Thông tin tài khoản</span>
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/account/orders.php"
                                   class="flex items-center px-4 py-3 rounded-lg bg-primary/10 text-primary font-medium transition-all duration-300">
                                    <i class="fas fa-shopping-bag mr-3 w-5 text-center"></i>
                                    <span>Đơn hàng của tôi</span>
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/account/change-password.php"
                                   class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-primary transition-all duration-300">
                                    <i class="fas fa-key mr-3 w-5 text-center"></i>
                                    <span>Đổi mật khẩu</span>
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/logout.php"
                                   class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-red-50 hover:text-red-500 transition-all duration-300">
                                    <i class="fas fa-sign-out-alt mr-3 w-5 text-center"></i>
                                    <span>Đăng xuất</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="md:w-3/4">
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 hover:border-primary/20 transition-all duration-300 overflow-hidden">
                    <!-- Header với gradient -->
                    <div class="bg-gradient-to-r from-primary/5 to-primary/10 px-6 py-5 border-b border-gray-100">
                        <div class="flex items-center">
                            <span class="w-12 h-12 rounded-lg bg-white flex items-center justify-center text-primary shadow-sm mr-4">
                                <i class="fas fa-shopping-bag text-xl"></i>
                            </span>
                            <div>
                                <h2 class="text-xl font-bold text-gray-800">Danh sách đơn hàng</h2>
                                <p class="text-gray-500 text-sm">Quản lý và theo dõi trạng thái đơn hàng của bạn</p>
                            </div>
                        </div>
                    </div>

                    <!-- Bộ lọc trạng thái -->
                    <div class="px-6 py-4 border-b border-gray-100 bg-gray-50">
                        <!-- Desktop: flex-wrap -->
                        <div class="hidden md:flex flex-wrap gap-2">
                            <button data-status=""
                               class="filter-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 <?php echo !$status ? 'bg-primary text-white shadow-md shadow-primary/20' : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'; ?>">
                                <i class="fas fa-list-ul mr-1.5"></i> Tất cả
                            </button>
                            <button data-status="pending"
                               class="filter-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 <?php echo $status === 'pending' ? 'bg-primary text-white shadow-md shadow-primary/20' : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'; ?>">
                                <i class="fas fa-clock mr-1.5"></i> Chờ xử lý
                            </button>
                            <button data-status="processing"
                               class="filter-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 <?php echo $status === 'processing' ? 'bg-primary text-white shadow-md shadow-primary/20' : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'; ?>">
                                <i class="fas fa-spinner mr-1.5"></i> Đang xử lý
                            </button>
                            <button data-status="completed"
                               class="filter-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 <?php echo $status === 'completed' ? 'bg-primary text-white shadow-md shadow-primary/20' : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'; ?>">
                                <i class="fas fa-check-circle mr-1.5"></i> Hoàn thành
                            </button>
                            <button data-status="cancelled"
                               class="filter-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 <?php echo $status === 'cancelled' ? 'bg-primary text-white shadow-md shadow-primary/20' : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'; ?>">
                                <i class="fas fa-times-circle mr-1.5"></i> Đã hủy
                            </button>
                        </div>

                        <!-- Mobile: horizontal scroll -->
                        <div class="block md:hidden">
                            <div class="flex gap-3 overflow-x-auto pb-2 scrollbar-hide" style="scroll-behavior: smooth; -webkit-overflow-scrolling: touch;">
                                <button data-status=""
                                   class="filter-btn flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 <?php echo !$status ? 'bg-primary text-white shadow-md shadow-primary/20' : 'bg-white text-gray-700 border border-gray-200'; ?>">
                                    <i class="fas fa-list-ul mr-1.5"></i> Tất cả
                                </button>
                                <button data-status="pending"
                                   class="filter-btn flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 <?php echo $status === 'pending' ? 'bg-primary text-white shadow-md shadow-primary/20' : 'bg-white text-gray-700 border border-gray-200'; ?>">
                                    <i class="fas fa-clock mr-1.5"></i> Chờ xử lý
                                </button>
                                <button data-status="processing"
                                   class="filter-btn flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 <?php echo $status === 'processing' ? 'bg-primary text-white shadow-md shadow-primary/20' : 'bg-white text-gray-700 border border-gray-200'; ?>">
                                    <i class="fas fa-spinner mr-1.5"></i> Đang xử lý
                                </button>
                                <button data-status="completed"
                                   class="filter-btn flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 <?php echo $status === 'completed' ? 'bg-primary text-white shadow-md shadow-primary/20' : 'bg-white text-gray-700 border border-gray-200'; ?>">
                                    <i class="fas fa-check-circle mr-1.5"></i> Hoàn thành
                                </button>
                                <button data-status="cancelled"
                                   class="filter-btn flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 <?php echo $status === 'cancelled' ? 'bg-primary text-white shadow-md shadow-primary/20' : 'bg-white text-gray-700 border border-gray-200'; ?>">
                                    <i class="fas fa-times-circle mr-1.5"></i> Đã hủy
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">

                    <!-- Loading indicator -->
                    <div id="loading-indicator" class="hidden text-center py-8">
                        <div class="inline-flex items-center">
                            <i class="fas fa-spinner fa-spin text-primary mr-2"></i>
                            <span class="text-gray-600">Đang tải...</span>
                        </div>
                    </div>

                    <!-- Orders container -->
                    <div id="orders-container">
                    <?php if (count($orders) > 0): ?>

                    <!-- Desktop Table View - Ẩn trên mobile -->
                    <div class="hidden md:block overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="bg-gray-50 border-b border-gray-200">
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Mã đơn hàng</th>
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Ngày đặt</th>
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Tổng tiền</th>
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Trạng thái</th>
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-100">
                                <?php foreach ($orders as $order): ?>
                                <tr class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">#<?php echo $order['id']; ?></div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-700 flex items-center">
                                            <i class="far fa-calendar-alt text-primary/70 mr-2"></i>
                                            <?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?>
                                        </div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?php echo format_currency($order['total']); ?></div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <?php
                                        $status_info = get_order_status_info($order['status'], 'user');

                                        // Thay đổi class để phù hợp với thiết kế mới
                                        $status_classes = [
                                            'pending' => 'bg-yellow-100 text-yellow-800 border border-yellow-200',
                                            'processing' => 'bg-blue-100 text-blue-800 border border-blue-200',
                                            'shipping' => 'bg-indigo-100 text-indigo-800 border border-indigo-200',
                                            'completed' => 'bg-green-100 text-green-800 border border-green-200',
                                            'cancelled' => 'bg-red-100 text-red-800 border border-red-200',
                                        ];

                                        $status_class = isset($status_classes[$order['status']]) ? $status_classes[$order['status']] : 'bg-gray-100 text-gray-800 border border-gray-200';
                                        ?>
                                        <span class="px-3 py-1.5 inline-flex items-center text-xs font-medium rounded-full <?php echo $status_class; ?> order-status" data-order-id="<?php echo $order['id']; ?>">
                                            <?php echo $status_info['text']; ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-3">
                                            <a href="<?php echo BASE_URL; ?>/account/order-detail.php?id=<?php echo $order['id']; ?>"
                                               class="inline-flex items-center text-primary hover:text-primary-dark transition-colors duration-200">
                                                <i class="fas fa-eye mr-1.5"></i> Xem
                                            </a>
                                            <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                                            <a href="<?php echo BASE_URL; ?>/account/orders.php?action=cancel&id=<?php echo $order['id']; ?>"
                                               class="inline-flex items-center text-red-500 hover:text-red-700 transition-colors duration-200"
                                               onclick="return confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')">
                                                <i class="fas fa-times mr-1.5"></i> Hủy
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Mobile Card View - Layout theo khung bố cục -->
                    <div class="block md:hidden space-y-3">
                        <?php foreach ($orders as $order): ?>
                        <?php
                        $status_info = get_order_status_info($order['status'], 'user');

                        // Định nghĩa màu sắc cho trạng thái
                        $status_styles = [
                            'pending' => ['bg' => 'bg-orange-100', 'text' => 'text-orange-800', 'border' => 'border-orange-200'],
                            'processing' => ['bg' => 'bg-blue-100', 'text' => 'text-blue-800', 'border' => 'border-blue-200'],
                            'shipping' => ['bg' => 'bg-purple-100', 'text' => 'text-purple-800', 'border' => 'border-purple-200'],
                            'completed' => ['bg' => 'bg-green-100', 'text' => 'text-green-800', 'border' => 'border-green-200'],
                            'cancelled' => ['bg' => 'bg-red-100', 'text' => 'text-red-800', 'border' => 'border-red-200'],
                        ];

                        $status_style = isset($status_styles[$order['status']]) ? $status_styles[$order['status']] :
                            ['bg' => 'bg-gray-100', 'text' => 'text-gray-800', 'border' => 'border-gray-200'];
                        ?>

                        <!-- Card theo khung bố cục -->
                        <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
                            <!-- Header: Mã đơn hàng và ngày giờ -->
                            <div class="px-4 py-3 border-b border-gray-100 flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900">Đơn hàng #<?php echo $order['id']; ?></h3>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500"><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></p>
                                </div>
                            </div>

                            <!-- Content: Layout cải tiến -->
                            <div class="p-4">
                                <!-- Hàng 1: Hình ảnh và thông tin sản phẩm -->
                                <div class="flex gap-3 mb-4">
                                    <!-- Hình ảnh sản phẩm -->
                                    <div class="w-14 h-14 flex-shrink-0 rounded-lg overflow-hidden border border-gray-200 bg-gray-50">
                                        <?php if ($order['first_item'] && !empty($order['first_item']['product_image'])): ?>
                                            <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $order['first_item']['product_image']; ?>"
                                                 alt="<?php echo htmlspecialchars($order['first_item']['product_name']); ?>"
                                                 class="w-full h-full object-cover">
                                        <?php else: ?>
                                            <div class="w-full h-full bg-gray-100 flex items-center justify-center">
                                                <i class="fas fa-image text-gray-400"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Thông tin sản phẩm - 3 dòng -->
                                    <div class="flex-1 min-w-0">
                                        <?php if ($order['first_item']): ?>
                                            <!-- Dòng 1: Tên sản phẩm -->
                                            <h4 class="text-sm font-medium text-gray-900 leading-tight truncate mb-1">
                                                <?php echo htmlspecialchars($order['first_item']['product_name']); ?>
                                            </h4>

                                            <!-- Dòng 2: Danh mục sản phẩm -->
                                            <?php
                                            // Lấy thông tin danh mục từ product_id
                                            $product_info = get_product_by_id($order['first_item']['product_id']);
                                            $category_name = $product_info ? $product_info['category_name'] : 'Nội thất';
                                            ?>
                                            <p class="text-xs text-gray-500 mb-1"><?php echo htmlspecialchars($category_name); ?></p>

                                            <!-- Dòng 3: Số lượng và giá -->
                                            <div class="flex items-center justify-between">
                                                <span class="text-xs text-gray-500">x<?php echo $order['first_item']['quantity']; ?></span>
                                                <span class="text-xs text-gray-500"><?php echo format_currency($order['first_item']['price']); ?></span>
                                            </div>
                                        <?php else: ?>
                                            <!-- Fallback khi không có sản phẩm -->
                                            <h4 class="text-sm font-medium text-gray-900 leading-tight truncate mb-1">Đơn hàng nội thất</h4>
                                            <p class="text-xs text-gray-500 mb-1">Nội thất</p>
                                            <div class="flex items-center justify-between">
                                                <span class="text-xs text-gray-600">-</span>
                                                <span class="text-sm font-semibold text-gray-900">-</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Hàng 2: Tổng tiền -->
                                <div class="flex justify-end mb-4">
                                    <div>
                                        <span class="text-gray-500" style="font-size: 0.9rem;">Tổng: </span>
                                        <span class="font-bold text-gray-900" style="font-size: 0.9rem;"><?php echo format_currency($order['total']); ?></span>
                                    </div>
                                </div>

                                <!-- Hàng 3: Trạng thái -->
                                <div class="mb-4">
                                    <div class="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-md border-l-4 <?php
                                        echo $order['status'] === 'pending' ? 'border-orange-400' :
                                            ($order['status'] === 'processing' ? 'border-blue-400' :
                                            ($order['status'] === 'shipping' ? 'border-purple-400' :
                                            ($order['status'] === 'completed' ? 'border-green-400' : 'border-red-400')));
                                    ?>">
                                        <div class="flex items-center">
                                            <div class="w-2 h-2 rounded-full mr-2 <?php
                                                echo $order['status'] === 'pending' ? 'bg-orange-400' :
                                                    ($order['status'] === 'processing' ? 'bg-blue-400 animate-spin' :
                                                    ($order['status'] === 'shipping' ? 'bg-purple-400 animate-pulse' :
                                                    ($order['status'] === 'completed' ? 'bg-green-400' : 'bg-red-400')));
                                            ?>"></div>
                                            <span class="text-gray-700 font-medium" style="font-size: 0.9rem;">
                                                <?php echo $status_info['text']; ?>
                                            </span>
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            <?php
                                            $status_icons = [
                                                'pending' => 'fas fa-clock',
                                                'processing' => 'fas fa-cog',
                                                'shipping' => 'fas fa-truck',
                                                'completed' => 'fas fa-check-circle',
                                                'cancelled' => 'fas fa-times-circle'
                                            ];
                                            $icon = isset($status_icons[$order['status']]) ? $status_icons[$order['status']] : 'fas fa-question-circle';
                                            ?>
                                            <i class="<?php echo $icon; ?>"></i>
                                        </div>
                                    </div>
                                </div>

                                <!-- Hàng 4: Nút thao tác -->
                                <div class="grid grid-cols-2 gap-2 mt-4">
                                    <a href="<?php echo BASE_URL; ?>/account/order-detail.php?id=<?php echo $order['id']; ?>"
                                       class="bg-white border-2 border-primary text-primary hover:bg-primary hover:text-white text-center py-2 px-3 rounded-md font-medium transition-all duration-300"
                                       style="font-size: 0.9rem;">
                                        Chi tiết
                                    </a>

                                    <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                                    <a href="<?php echo BASE_URL; ?>/account/orders.php?action=cancel&id=<?php echo $order['id']; ?>"
                                       class="bg-white border-2 border-gray-300 text-gray-600 hover:border-red-400 hover:text-red-500 text-center py-2 px-3 rounded-md font-medium transition-all duration-300"
                                       style="font-size: 0.9rem;"
                                       onclick="return confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')">
                                        Hủy
                                    </a>
                                    <?php else: ?>
                                    <div class="bg-gray-50 border-2 border-gray-200 text-gray-400 text-center py-2 px-3 rounded-md font-medium"
                                         style="font-size: 0.9rem;">
                                        Hoàn tất
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Pagination - Thiết kế hiện đại -->
                    <?php if ($total_pages > 1): ?>
                    <div class="mt-8 flex justify-center">
                        <div class="inline-flex rounded-md shadow-sm">
                            <?php if ($page > 1): ?>
                            <a href="<?php echo BASE_URL; ?>/account/orders.php?<?php echo $status ? 'status=' . $status . '&' : ''; ?>page=<?php echo $page - 1; ?>"
                               class="relative inline-flex items-center px-4 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-300">
                                <i class="fas fa-chevron-left mr-1"></i> Trước
                            </a>
                            <?php else: ?>
                            <span class="relative inline-flex items-center px-4 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-500 cursor-not-allowed">
                                <i class="fas fa-chevron-left mr-1"></i> Trước
                            </span>
                            <?php endif; ?>

                            <?php
                            // Hiển thị số trang thông minh
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            // Đảm bảo luôn hiển thị ít nhất 5 trang nếu có thể
                            if ($end_page - $start_page < 4) {
                                if ($start_page == 1) {
                                    $end_page = min($total_pages, $start_page + 4);
                                } else {
                                    $start_page = max(1, $end_page - 4);
                                }
                            }

                            // Hiển thị nút trang đầu nếu cần
                            if ($start_page > 1):
                            ?>
                            <a href="<?php echo BASE_URL; ?>/account/orders.php?<?php echo $status ? 'status=' . $status . '&' : ''; ?>page=1"
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-300">
                                1
                            </a>
                            <?php if ($start_page > 2): ?>
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                ...
                            </span>
                            <?php endif; ?>
                            <?php endif; ?>

                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <?php if ($i == $page): ?>
                            <span class="relative inline-flex items-center px-4 py-2 border border-primary bg-primary text-sm font-medium text-white">
                                <?php echo $i; ?>
                            </span>
                            <?php else: ?>
                            <a href="<?php echo BASE_URL; ?>/account/orders.php?<?php echo $status ? 'status=' . $status . '&' : ''; ?>page=<?php echo $i; ?>"
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-300">
                                <?php echo $i; ?>
                            </a>
                            <?php endif; ?>
                            <?php endfor; ?>

                            <?php
                            // Hiển thị nút trang cuối nếu cần
                            if ($end_page < $total_pages):
                            ?>
                            <?php if ($end_page < $total_pages - 1): ?>
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                ...
                            </span>
                            <?php endif; ?>
                            <a href="<?php echo BASE_URL; ?>/account/orders.php?<?php echo $status ? 'status=' . $status . '&' : ''; ?>page=<?php echo $total_pages; ?>"
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-300">
                                <?php echo $total_pages; ?>
                            </a>
                            <?php endif; ?>

                            <?php if ($page < $total_pages): ?>
                            <a href="<?php echo BASE_URL; ?>/account/orders.php?<?php echo $status ? 'status=' . $status . '&' : ''; ?>page=<?php echo $page + 1; ?>"
                               class="relative inline-flex items-center px-4 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-300">
                                Tiếp <i class="fas fa-chevron-right ml-1"></i>
                            </a>
                            <?php else: ?>
                            <span class="relative inline-flex items-center px-4 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-500 cursor-not-allowed">
                                Tiếp <i class="fas fa-chevron-right ml-1"></i>
                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php else: ?>
                    <!-- Trạng thái trống - Thiết kế hiện đại -->
                    <div class="text-center py-12">
                        <div class="inline-flex items-center justify-center w-24 h-24 bg-primary/10 text-primary rounded-full mb-6">
                            <i class="fas fa-shopping-bag text-5xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-3">Không có đơn hàng nào</h3>
                        <p class="text-gray-600 mb-6 max-w-md mx-auto">Bạn chưa có đơn hàng nào trong danh sách này. Hãy khám phá các sản phẩm của chúng tôi và đặt hàng ngay!</p>
                        <a href="<?php echo BASE_URL; ?>/products.php"
                           class="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary-dark text-white font-medium rounded-full shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1">
                            <i class="fas fa-shopping-cart mr-2"></i> Tiếp tục mua sắm
                        </a>
                    </div>
                    <?php endif; ?>
                    </div> <!-- End orders-container -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSS tùy chỉnh cho trang đơn hàng -->
<style>
    /* Biến CSS cho màu sắc chính */
    :root {
        --primary: #F37321;
        --primary-dark: #E05E00;
        --primary-light: #FF9D5C;
    }

    /* Hiệu ứng hover cho các card */
    .bg-white.rounded-xl {
        transition: all 0.3s ease;
    }

    .bg-white.rounded-xl:hover {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Hiệu ứng cho nút */
    .inline-flex.items-center.px-6.py-3 {
        transition: all 0.3s ease;
    }

    .inline-flex.items-center.px-6.py-3:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px rgba(243, 115, 33, 0.2);
    }

    .inline-flex.items-center.px-6.py-3:active {
        transform: translateY(0);
    }

    /* Ẩn scrollbar cho mobile filter */
    .scrollbar-hide {
        -ms-overflow-style: none;  /* Internet Explorer 10+ */
        scrollbar-width: none;  /* Firefox */
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;  /* Safari and Chrome */
    }

    /* Orders container smooth transitions */
    #orders-container {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }

    #orders-container.loading {
        transform: translateY(5px);
    }

    /* Responsive cho màn hình nhỏ */
    @media (max-width: 768px) {
        .section-title-badge {
            justify-content: center;
            margin-left: auto;
            margin-right: auto;
        }

        .section-title {
            text-align: center;
        }

        .flex.flex-wrap.gap-2 {
            justify-content: center;
        }

        /* Tối ưu hóa cho mobile card - thiết kế sạch sẽ */
        .mobile-order-card {
            margin-bottom: 1rem;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        .mobile-order-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Hiệu ứng đơn giản cho nút */
        .mobile-btn {
            transition: all 0.2s ease;
        }

        .mobile-btn:active {
            transform: scale(0.98);
        }

        /* Tối ưu cho touch */
        .mobile-btn {
            min-height: 44px; /* Apple's recommended touch target size */
        }

        /* Tối ưu padding cho mobile */
        .container.mx-auto.px-4 {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        /* Giảm khoảng cách cho mobile */
        .py-10 {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
    }

    /* Hiệu ứng cho bảng */
    .table tr:hover {
        background-color: rgba(243, 115, 33, 0.03);
    }

    /* Hiệu ứng cho phân trang */
    .inline-flex.rounded-md.shadow-sm a:hover {
        color: var(--primary);
        border-color: var(--primary-light);
    }

    /* CSS tùy chỉnh cho filter buttons */
    /* Override hover effect cho active filter buttons */
    .filter-btn.bg-primary:hover {
        background-color: var(--primary-dark) !important;
        color: white !important;
        border-color: var(--primary-dark) !important;
    }

    /* Đảm bảo inactive filter buttons vẫn có hover effect bình thường */
    .filter-btn.bg-white:hover {
        background-color: #f3f4f6 !important;
        color: #374151 !important;
    }
</style>

<!-- JavaScript cho AJAX filtering -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const ordersContainer = document.getElementById('orders-container');
    const loadingIndicator = document.getElementById('loading-indicator');

    // Function to scroll filter to center
    function scrollFilterToCenter(button) {
        const scrollContainer = button.closest('.overflow-x-auto');
        if (!scrollContainer) return;

        const containerRect = scrollContainer.getBoundingClientRect();
        const buttonRect = button.getBoundingClientRect();

        // Calculate scroll position to center the button
        const scrollLeft = scrollContainer.scrollLeft +
                          (buttonRect.left - containerRect.left) -
                          (containerRect.width / 2) +
                          (buttonRect.width / 2);

        // Smooth scroll to position
        scrollContainer.scrollTo({
            left: scrollLeft,
            behavior: 'smooth'
        });
    }

    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const status = this.getAttribute('data-status');

            // Scroll to center on mobile (only if in overflow container)
            if (window.innerWidth < 768) { // Mobile breakpoint
                scrollFilterToCenter(this);
            }

            // Update active state
            filterBtns.forEach(b => {
                b.classList.remove('bg-primary', 'text-white', 'shadow-md', 'shadow-primary/20');
                b.classList.add('bg-white', 'text-gray-700', 'border', 'border-gray-200');
            });

            this.classList.remove('bg-white', 'text-gray-700', 'border', 'border-gray-200');
            this.classList.add('bg-primary', 'text-white', 'shadow-md', 'shadow-primary/20');

            // Show loading
            loadingIndicator.classList.remove('hidden');
            ordersContainer.classList.add('loading');
            ordersContainer.style.opacity = '0.5';

            // Build URL
            const url = new URL(window.location.href);
            if (status) {
                url.searchParams.set('status', status);
            } else {
                url.searchParams.delete('status');
            }

            // AJAX request
            fetch(url.toString(), {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                // Parse response and extract orders container
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newOrdersContainer = doc.getElementById('orders-container');

                if (newOrdersContainer) {
                    // Smooth content transition (keep this effect)
                    setTimeout(() => {
                        ordersContainer.innerHTML = newOrdersContainer.innerHTML;

                        // Smooth fade in
                        setTimeout(() => {
                            ordersContainer.style.opacity = '1';
                            ordersContainer.classList.remove('loading');
                        }, 100);
                    }, 150);
                }

                // Update URL without reload
                window.history.pushState({}, '', url.toString());

                // Hide loading
                loadingIndicator.classList.add('hidden');
            })
            .catch(error => {
                console.error('Error:', error);
                // Hide loading on error
                loadingIndicator.classList.add('hidden');
                ordersContainer.style.opacity = '1';
                ordersContainer.classList.remove('loading');
            });
        });
    });

    // Auto-scroll to active filter on page load (mobile only)
    if (window.innerWidth < 768) {
        const activeFilter = document.querySelector('.filter-btn.bg-primary');
        if (activeFilter) {
            // Delay to ensure layout is complete
            setTimeout(() => {
                scrollFilterToCenter(activeFilter);
            }, 100);
        }
    }
});
</script>

<?php
// Thêm script theo dõi đơn hàng
echo '<script src="' . BASE_URL . '/assets/js/order-tracking.js"></script>';

// Include footer
include_once '../partials/footer.php';
?>
