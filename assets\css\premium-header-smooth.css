/*
 * Premium Header Smooth Animations CSS
 * Optimized transitions and hardware acceleration
 * Created: 2025-07-27
 * 
 * Features:
 * - Unified timing and easing functions
 * - Hardware acceleration optimizations
 * - Smooth logo transitions
 * - Optimized layer composition
 * - Reduced layout thrashing
 */

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Timing */
  --header-transition-duration: 400ms;
  --header-transition-fast: 200ms;
  --header-transition-slow: 600ms;
  
  /* Easing functions */
  --header-easing-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94); /* easeOutQuad */
  --header-easing-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275); /* easeOutBack */
  --header-easing-elastic: cubic-bezier(0.68, -0.55, 0.265, 1.55); /* easeInOutBack */
  
  /* Transform values */
  --header-scale: 1;
  --header-translate-y: 0px;
  --header-opacity: 1;
}

/* ===== HARDWARE ACCELERATION BASE ===== */
.premium-header,
.premium-header * {
  /* Force hardware acceleration for smooth animations */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== PREMIUM HEADER OPTIMIZATIONS ===== */
.premium-header {
  /* Optimized will-change for better performance */
  will-change: transform, background-color, box-shadow, opacity;
  
  /* Smooth transitions with unified timing */
  transition:
    transform var(--header-transition-duration) var(--header-easing-smooth),
    background-color var(--header-transition-duration) var(--header-easing-smooth),
    box-shadow var(--header-transition-duration) var(--header-easing-smooth),
    opacity var(--header-transition-fast) var(--header-easing-smooth);
  
  /* Prevent layout thrashing */
  contain: layout style paint;
  
  /* Optimized layer composition */
  isolation: isolate;
  z-index: var(--z-header);
}

/* Enhanced smooth transition class */
.premium-header.smooth-transition {
  /* Apply scale transform smoothly */
  transform: scale(var(--header-scale)) translateY(var(--header-translate-y)) translateZ(0);
  
  /* Smooth opacity transitions */
  opacity: var(--header-opacity);
}

/* ===== TOP BAR OPTIMIZATIONS ===== */
.top-bar {
  /* Hardware acceleration */
  will-change: transform, opacity, clip-path;
  transform: translateZ(0);
  
  /* Optimized transitions */
  transition:
    transform var(--header-transition-slow) var(--header-easing-smooth),
    opacity var(--header-transition-duration) var(--header-easing-smooth),
    clip-path var(--header-transition-slow) var(--header-easing-smooth),
    max-height var(--header-transition-duration) var(--header-easing-smooth);
  
  /* Prevent layout shifts */
  contain: layout style;
}

/* Smooth top bar hide animation */
.premium-header.scrolled .top-bar {
  /* Use transform3d for better performance */
  transform: translate3d(0, -100%, 0);
  opacity: 0;
  
  /* Smooth clip-path animation */
  clip-path: inset(0 0 100% 0);
  
  /* Prevent interaction when hidden */
  pointer-events: none;
  
  /* Smooth height transition */
  max-height: 0;
  overflow: hidden;
}

/* ===== MID & BOTTOM HEADER OPTIMIZATIONS ===== */
.mid-header-container,
.bottom-header-container {
  /* Hardware acceleration */
  will-change: background-color, border-color;
  transform: translateZ(0);
  
  /* Smooth background transitions */
  transition:
    background-color var(--header-transition-duration) var(--header-easing-smooth),
    border-color var(--header-transition-duration) var(--header-easing-smooth),
    backdrop-filter var(--header-transition-duration) var(--header-easing-smooth);
  
  /* Optimized layer composition */
  contain: layout style paint;
}

/* Enhanced scrolled state with backdrop blur */
.premium-header.scrolled .mid-header-container,
.premium-header.scrolled .bottom-header-container {
  /* Smooth background transition */
  background-color: rgba(32, 40, 52, 0.95);
  
  /* Add subtle backdrop blur for modern effect */
  backdrop-filter: blur(10px) saturate(180%);
  -webkit-backdrop-filter: blur(10px) saturate(180%);
  
  /* Enhanced border */
  border-color: rgba(255, 255, 255, 0.1);
  
  /* Subtle shadow enhancement */
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.15),
    0 1px 0 rgba(255, 255, 255, 0.05) inset;
}

/* ===== LOGO OPTIMIZATIONS ===== */
.premium-logo-image img {
  /* Hardware acceleration */
  will-change: opacity, filter;
  transform: translateZ(0);
  
  /* Smooth transitions */
  transition:
    opacity var(--header-transition-fast) var(--header-easing-smooth),
    filter var(--header-transition-fast) var(--header-easing-smooth),
    transform var(--header-transition-fast) var(--header-easing-smooth);
  
  /* Prevent layout shifts */
  contain: layout;
}

/* Logo hover effect */
.premium-logo:hover .premium-logo-image img {
  transform: translateZ(0) scale(1.02);
  filter: brightness(1.1);
}

/* ===== NAVIGATION OPTIMIZATIONS ===== */
.nav-link {
  /* Hardware acceleration */
  will-change: color, background-color, transform;
  transform: translateZ(0);
  
  /* Smooth transitions */
  transition:
    color var(--header-transition-fast) var(--header-easing-smooth),
    background-color var(--header-transition-fast) var(--header-easing-smooth),
    transform var(--header-transition-fast) var(--header-easing-smooth);
  
  /* Optimized positioning */
  position: relative;
  contain: layout style;
}

/* Enhanced nav link hover */
.nav-link:hover {
  transform: translateZ(0) translateY(-1px);
}

/* Smooth active indicator */
.nav-link::after {
  /* Hardware acceleration */
  will-change: transform, opacity;
  transform: translateZ(0) scaleX(0);
  
  /* Smooth scale animation */
  transition:
    transform var(--header-transition-duration) var(--header-easing-elastic),
    opacity var(--header-transition-fast) var(--header-easing-smooth);
  
  /* Positioning */
  transform-origin: left center;
}

.nav-item.active .nav-link::after,
.nav-link:hover::after {
  transform: translateZ(0) scaleX(1);
  opacity: 1;
}

/* ===== SEARCH OPTIMIZATIONS ===== */
.search-form {
  /* Hardware acceleration */
  will-change: background-color, border-color, box-shadow, transform;
  transform: translateZ(0);
  
  /* Smooth transitions */
  transition:
    background-color var(--header-transition-duration) var(--header-easing-smooth),
    border-color var(--header-transition-duration) var(--header-easing-smooth),
    box-shadow var(--header-transition-duration) var(--header-easing-smooth),
    transform var(--header-transition-fast) var(--header-easing-smooth);
  
  /* Optimized layer */
  contain: layout style;
}

/* Enhanced search focus effect */
.search-form.focused {
  transform: translateZ(0) scale(1.02);
  box-shadow: 
    0 0 0 2px rgba(249, 115, 22, 0.3),
    0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Smooth search ripple effect */
.search-ripple::before {
  /* Hardware acceleration */
  will-change: width, height, opacity;
  transform: translateZ(0);
  
  /* Smooth ripple animation */
  transition:
    width var(--header-transition-slow) var(--header-easing-smooth),
    height var(--header-transition-slow) var(--header-easing-smooth),
    opacity var(--header-transition-slow) var(--header-easing-smooth);
}

.search-form.focused .search-ripple::before {
  width: 100%;
  height: 100%;
  opacity: 0;
}

/* ===== ACTION BUTTONS OPTIMIZATIONS ===== */
.action-btn {
  /* Hardware acceleration */
  will-change: color, background-color, transform, box-shadow;
  transform: translateZ(0);
  
  /* Smooth transitions */
  transition:
    color var(--header-transition-fast) var(--header-easing-smooth),
    background-color var(--header-transition-fast) var(--header-easing-smooth),
    transform var(--header-transition-fast) var(--header-easing-smooth),
    box-shadow var(--header-transition-fast) var(--header-easing-smooth);
  
  /* Optimized layer */
  contain: layout style;
}

/* Enhanced button hover */
.action-btn:hover {
  transform: translateZ(0) translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ===== SCROLL PAUSE EFFECT ===== */
.premium-header.scroll-pause {
  /* Subtle bounce effect when scroll stops */
  animation: scroll-pause-bounce var(--header-transition-duration) var(--header-easing-bounce);
}

@keyframes scroll-pause-bounce {
  0% { transform: scale(var(--header-scale)) translateY(var(--header-translate-y)) translateZ(0); }
  50% { transform: scale(calc(var(--header-scale) * 1.01)) translateY(var(--header-translate-y)) translateZ(0); }
  100% { transform: scale(var(--header-scale)) translateY(var(--header-translate-y)) translateZ(0); }
}

/* ===== COMPACT MODE OPTIMIZATIONS ===== */
.premium-header.compact {
  /* Smooth compact transition */
  transform: scale(0.98) translateY(-2px) translateZ(0);
}

/* ===== SMOOTH SLIDE DOWN ANIMATION ===== */
.premium-header:not(.scrolled) .top-bar {
  /* Smooth slide down animation */
  animation: smooth-slide-down-enhanced var(--header-transition-duration) var(--header-easing-smooth);
}

@keyframes smooth-slide-down-enhanced {
  0% {
    transform: translate3d(0, -100%, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
/* Reduce paint and layout thrashing */
.premium-header * {
  /* Optimize text rendering */
  text-rendering: optimizeSpeed;
  
  /* Optimize font smoothing */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Optimize for 60fps animations */
@media (prefers-reduced-motion: no-preference) {
  .premium-header {
    /* Enable smooth scrolling optimizations */
    scroll-behavior: smooth;
  }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .premium-header,
  .premium-header * {
    /* Disable animations for accessibility */
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
