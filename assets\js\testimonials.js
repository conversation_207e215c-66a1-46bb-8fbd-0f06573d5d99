document.addEventListener('DOMContentLoaded', function() {
    // Hiệu ứng xuất hiện khi cuộn đến phần cảm nhận khách hàng
    const testimonialSection = document.querySelector('.testimonials-section');
    const testimonialCards = document.querySelectorAll('.testimonial-card');

    if (testimonialSection && testimonialCards.length > 0) {
        // Thiết lập trạng thái ban đầu cho các card
        testimonialCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = `all 0.5s ease ${index * 0.1}s`;
        });

        // Hàm kiểm tra khi cuộn đến phần cảm nhận khách hàng
        const checkTestimonialVisibility = () => {
            const sectionTop = testimonialSection.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;

            if (sectionTop < windowHeight * 0.75) {
                // Hiển thị các card với hiệu ứng staggered
                testimonialCards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 100);
                });

                // Gỡ bỏ event listener sau khi đã hiển thị
                window.removeEventListener('scroll', checkTestimonialVisibility);
            }
        };

        // Kiểm tra ngay khi trang tải xong
        checkTestimonialVisibility();

        // Thêm event listener cho sự kiện cuộn
        window.addEventListener('scroll', checkTestimonialVisibility);
    }

    // Hiệu ứng hover cho các card
    testimonialCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';

            // Hiệu ứng cho ảnh khách hàng
            const photo = this.querySelector('.testimonial-photo');
            if (photo) {
                photo.style.borderColor = '#3B82F6';
                photo.style.transform = 'scale(1.05)';
            }

            // Hiệu ứng cho rating
            const stars = this.querySelectorAll('.testimonial-rating i');
            stars.forEach((star, index) => {
                setTimeout(() => {
                    star.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        star.style.transform = 'scale(1)';
                    }, 200);
                }, index * 50);
            });
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';

            // Khôi phục ảnh khách hàng
            const photo = this.querySelector('.testimonial-photo');
            if (photo) {
                photo.style.borderColor = '#E5E7EB';
                photo.style.transform = 'scale(1)';
            }

            // Khôi phục rating
            const stars = this.querySelectorAll('.testimonial-rating i');
            stars.forEach(star => {
                star.style.transform = 'scale(1)';
            });
        });
    });

    // Xử lý video testimonial
    const videoTriggers = document.querySelectorAll('.video-trigger');

    videoTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();

            const videoUrl = this.getAttribute('data-video-url');
            const videoContainer = this.closest('.testimonial-video');

            if (videoContainer && videoUrl) {
                console.log('Playing video:', videoUrl);

                // Tạo iframe cho video
                const iframe = document.createElement('iframe');
                iframe.src = videoUrl;
                iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
                iframe.allowFullscreen = true;

                // Xóa nội dung cũ và thêm iframe
                videoContainer.innerHTML = '';
                videoContainer.appendChild(iframe);

                // Hiệu ứng xuất hiện
                setTimeout(() => {
                    videoContainer.style.opacity = '1';
                }, 100);
            } else {
                console.error('Video container or URL not found:', videoContainer, videoUrl);
            }
        });
    });

    // Xử lý lỗi ảnh không tồn tại
    const testimonialImages = document.querySelectorAll('.testimonial-photo-item img');
    testimonialImages.forEach(img => {
        img.addEventListener('error', function() {
            console.log('Ảnh không tồn tại:', this.src);
            this.onerror = null;
            const photoItem = this.closest('.testimonial-photo-item');
            if (photoItem) {
                const link = photoItem.querySelector('a');
                if (link) {
                    link.innerHTML = '<div class="no-image-placeholder"><i class="fas fa-image"></i><span>Không có ảnh</span></div>';
                }
            }
        });
    });

    // Xử lý hiển thị nhiều video
    const moreVideosLinks = document.querySelectorAll('.more-videos-link');

    moreVideosLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('More videos link clicked');

            const hiddenVideos = this.nextElementSibling;
            const videoTriggers = hiddenVideos.querySelectorAll('.video-trigger');

            console.log('Found', videoTriggers.length, 'hidden videos');

            // Tạo modal hiển thị danh sách video
            const modal = document.createElement('div');
            modal.className = 'testimonial-videos-modal';
            modal.innerHTML = `
                <div class="testimonial-videos-modal-content">
                    <div class="testimonial-videos-modal-header">
                        <h3>Các video đánh giá</h3>
                        <button class="close-modal">&times;</button>
                    </div>
                    <div class="testimonial-videos-modal-body"></div>
                </div>
            `;

            document.body.appendChild(modal);

            // Thêm các video vào modal
            const modalBody = modal.querySelector('.testimonial-videos-modal-body');
            videoTriggers.forEach((trigger, index) => {
                const videoUrl = trigger.getAttribute('data-video-url');
                const thumbnailUrl = trigger.getAttribute('data-thumbnail');
                console.log('Video URL', index + 1, ':', videoUrl);

                const videoItem = document.createElement('div');
                videoItem.className = 'testimonial-video-item';

                // Tạo cấu trúc HTML tương tự như video chính
                let videoHTML = `
                    <div class="testimonial-video">
                        <a href="#" class="video-trigger-modal" data-video-url="${videoUrl}">`;

                if (thumbnailUrl) {
                    videoHTML += `
                            <div class="video-placeholder with-thumbnail" style="background-image: url('${thumbnailUrl}');">
                                <i class="fas fa-play-circle"></i>
                            </div>`;
                } else {
                    videoHTML += `
                            <div class="video-placeholder">
                                <i class="fas fa-play-circle"></i>
                                <span>Video ${index + 1}</span>
                            </div>`;
                }

                videoHTML += `
                        </a>
                    </div>
                    <div class="testimonial-video-info">
                        <h4 class="testimonial-video-title">Video đánh giá ${index + 1}</h4>
                        <div class="testimonial-video-meta">
                            <i class="fas fa-video"></i> <span>Video đánh giá khách hàng</span>
                        </div>
                    </div>
                `;
                videoItem.innerHTML = videoHTML;

                // Gắn sự kiện click cho video trong modal
                videoItem.querySelector('.video-trigger-modal').addEventListener('click', function(eModal) {
                    eModal.preventDefault();
                    console.log('Playing video in modal:', videoUrl);

                    const videoContainerInModal = this.closest('.testimonial-video');
                    if (videoContainerInModal) {
                        const iframe = document.createElement('iframe');
                        iframe.src = videoUrl;
                        iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
                        iframe.allowFullscreen = true;

                        videoContainerInModal.innerHTML = '';
                        videoContainerInModal.appendChild(iframe);
                    }
                });

                modalBody.appendChild(videoItem);
            });

            // Xử lý đóng modal
            modal.querySelector('.close-modal').addEventListener('click', function() {
                document.body.removeChild(modal);
            });

            // Đóng modal khi click bên ngoài
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });

            // Hiển thị modal
            setTimeout(() => {
                modal.style.opacity = '1';
            }, 10);
        });
    });
});
