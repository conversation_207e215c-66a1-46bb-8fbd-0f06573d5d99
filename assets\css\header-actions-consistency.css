/*
 * Header Actions Consistency CSS for Nội Thất Bàng Vũ
 * Đồng bộ hóa kích cỡ chữ và chiều cao của User Account v<PERSON>
 */

/* Đồng bộ kích cỡ chữ với menu chính */
.action-btn,
.user-account-btn,
.cart-btn {
    font-size: var(--text-md); /* Kích cỡ chữ giống với menu chính */
    font-weight: 500; /* Đ<PERSON> đậm giống với menu chính */
    height: 40px; /* Chiều cao cố định */
    display: flex;
    align-items: center;
}

/* Đảm bảo chiều cao đồng nhất */
.user-dropdown,
.cart-container {
    height: 40px;
    display: flex;
    align-items: center;
}

/* Điều chỉnh khoảng cách giữa icon và text */
.action-btn i,
.user-account-btn i,
.cart-btn i {
    margin-right: var(--spacing-xs);
}

/* <PERSON>iều chỉnh kích thước icon */
.action-btn i,
.user-account-btn i,
.cart-btn i {
    font-size: 1em; /* Kích thước icon tương đối với kích thước chữ */
}

/* Điều chỉnh chiều cao của cart-btn */
.cart-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    line-height: 1;
}

/* Điều chỉnh chiều cao của user-account-btn */
.user-account-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    line-height: 1;
}

/* Đảm bảo user-name có kích thước chữ đồng nhất */
.user-name span {
    font-size: var(--text-md);
    font-weight: 500;
    transition: color 0.3s ease;
}

/* Đồng bộ màu hover với menu chính */
.user-account-btn:hover .user-name span {
    color: #f97316; /* Màu giống với menu chính khi hover */
    text-shadow: 0 0 1px rgba(249, 115, 22, 0.1);
}

/* Điều chỉnh kích thước avatar để to hơn và thêm viền rõ ràng */
.user-avatar {
    width: 36px;
    height: 36px;
    min-width: 36px;
    margin-right: 4px;
    border: 3px solid #f97316; /* Viền đậm và rõ ràng hơn */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: visible;
    position: relative;
}

/* Thêm viền ngoài cho avatar */
.user-avatar::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.8);
    pointer-events: none;
}

/* Điều chỉnh hiệu ứng hover cho avatar */
.user-account-btn:hover .user-avatar {
    transform: scale(1.05);
    border-color: #ff8534; /* Màu sáng hơn khi hover */
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
}

/* Điều chỉnh hiệu ứng hover cho avatar khi header đã cuộn */
.premium-header.scrolled .user-account-btn:hover .user-avatar {
    border-color: white; /* Viền trắng khi hover trong trạng thái scrolled */
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

/* Điều chỉnh hiệu ứng cho default-user-icon */
.user-avatar .default-user-icon {
    font-size: 24px;
    color: var(--primary);
    opacity: 0.8;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, var(--primary-ultra-light), var(--white));
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.user-account-btn:hover .default-user-icon {
    opacity: 1;
    color: var(--primary-dark);
}

/* Điều chỉnh vị trí của cart-badge */
.cart-badge {
    top: -8px;
    right: -8px;
}

/* Đảm bảo các dropdown menu có vị trí đồng nhất */
.user-dropdown-menu,
.mini-cart {
    top: calc(100% + 5px);
}

/* Điều chỉnh kích thước mini-cart */
.mini-cart {
    width: 300px;
    padding: var(--spacing-sm);
}

/* Điều chỉnh kích thước các item trong mini-cart */
.mini-cart-item-image {
    width: 50px;
    height: 50px;
}

/* Điều chỉnh padding của mini-cart-item */
.mini-cart-item {
    padding: var(--spacing-xs) 0;
}

/* Điều chỉnh kích thước chữ trong mini-cart */
.mini-cart-title,
.mini-cart-count,
.mini-cart-item-name,
.mini-cart-item-price,
.mini-cart-item-quantity,
.mini-cart-total-label,
.mini-cart-total-value {
    font-size: var(--text-sm);
}

/* Điều chỉnh padding của mini-cart-header */
.mini-cart-header {
    padding-bottom: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

/* Điều chỉnh padding của mini-cart-footer */
.mini-cart-footer {
    margin-top: var(--spacing-sm);
}

/* Điều chỉnh kích thước nút trong mini-cart */
.mini-cart-button {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--text-sm);
}

/* Đảm bảo các dropdown menu có hiệu ứng đồng nhất */
.user-dropdown:hover .user-dropdown-menu,
.cart-container:hover .mini-cart {
    transform: translateY(0) scale(1);
}

/* Điều chỉnh vị trí của online-dot */
.user-avatar .online-dot {
    bottom: -3px;
    right: -3px;
    width: 10px;
    height: 10px;
    background-color: #22c55e;
    border: 2px solid var(--white);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    animation: pulse 2s infinite;
    z-index: 3; /* Đảm bảo hiển thị trên cùng */
}

/* Responsive */
@media (max-width: 992px) {
    .action-btn,
    .user-account-btn,
    .cart-btn {
        font-size: var(--text-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .user-name span {
        font-size: var(--text-sm);
    }

    /* Điều chỉnh kích thước avatar trên màn hình nhỏ */
    .user-avatar {
        width: 32px;
        height: 32px;
        min-width: 32px;
    }
}
