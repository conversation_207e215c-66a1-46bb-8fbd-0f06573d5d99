<?php
/**
 * File chứa các hàm tiện ích sử dụng trong dự án
 */

// Require database connection
require_once __DIR__ . '/db.php';

/**
 * Chuyển đổi chuỗi thành slug
 */
function slugify($text) {
    // Chuyển đổi sang chữ thường
    $text = strtolower($text);

    // Chuyển đổi các ký tự có dấu thành không dấu
    $text = preg_replace('/(à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ)/', 'a', $text);
    $text = preg_replace('/(è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ)/', 'e', $text);
    $text = preg_replace('/(ì|í|ị|ỉ|ĩ)/', 'i', $text);
    $text = preg_replace('/(ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ)/', 'o', $text);
    $text = preg_replace('/(ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ)/', 'u', $text);
    $text = preg_replace('/(ỳ|ý|ỵ|ỷ|ỹ)/', 'y', $text);
    $text = preg_replace('/(đ)/', 'd', $text);

    // Xóa các ký tự đặc biệt
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);

    // Xóa khoảng trắng thừa
    $text = preg_replace('/[\s]+/', ' ', $text);

    // Thay khoảng trắng bằng dấu gạch ngang
    $text = preg_replace('/[\s]/', '-', $text);

    return $text;
}

/**
 * Định dạng giá tiền
 */
function format_currency($amount) {
    return number_format($amount, 0, '.', '.') . ' đ';
}

/**
 * Tạo URL thân thiện SEO cho sản phẩm
 */
function get_product_url($slug) {
    return BASE_URL . '/san-pham/' . $slug;
}

/**
 * Tạo URL thân thiện SEO cho danh mục
 */
function get_category_url($slug) {
    return BASE_URL . '/danh-muc/' . $slug;
}

/**
 * Tạo URL thân thiện SEO cho bài viết blog
 */
function get_blog_url($slug) {
    return BASE_URL . '/blog/' . $slug;
}

/**
 * Lấy URL hiện tại
 */
function current_url() {
    return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
}

/**
 * Chuyển hướng đến URL khác
 */
function redirect($url) {
    // Kiểm tra xem đã có output nào chưa
    if (headers_sent()) {
        // Nếu đã có output, sử dụng JavaScript để chuyển hướng
        echo '<script>window.location.href="' . $url . '";</script>';
        echo '<noscript><meta http-equiv="refresh" content="0;url=' . $url . '"></noscript>';
        exit;
    } else {
        // Nếu chưa có output, sử dụng header để chuyển hướng
        header("Location: $url");
        exit;
    }
}

/**
 * Hiển thị thông báo
 */
function set_flash_message($type, $message) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

/**
 * Lấy thông báo
 */
function get_flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $flash_message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $flash_message;
    }
    return null;
}

/**
 * Hiển thị thông báo
 */
function display_flash_message() {
    $flash_message = get_flash_message();
    if ($flash_message) {
        $type = $flash_message['type'];
        $message = $flash_message['message'];

        $alert_class = 'info';
        $icon_class = 'fa-info-circle';

        if ($type === 'success') {
            $alert_class = 'success';
            $icon_class = 'fa-check-circle';
        } elseif ($type === 'error') {
            $alert_class = 'danger';
            $icon_class = 'fa-exclamation-circle';
        } elseif ($type === 'warning') {
            $alert_class = 'warning';
            $icon_class = 'fa-exclamation-triangle';
        }

        echo '<div id="flash-message-container" class="alert-container">';
        echo '<div class="custom-alert custom-alert-' . $alert_class . ' fade-in" role="alert" data-auto-close="true">';
        echo '<div class="alert-icon"><i class="fas ' . $icon_class . '"></i></div>';
        echo '<div class="alert-content">' . $message . '</div>';
        echo '<button type="button" class="alert-close" onclick="this.parentElement.classList.add(\'fade-out\'); setTimeout(() => this.parentElement.parentElement.remove(), 300);">';
        echo '<i class="fas fa-times"></i>';
        echo '</button>';
        echo '</div>';
        echo '</div>';
    }
}

// Các hàm is_logged_in() và is_admin() đã được chuyển sang auth.php

/**
 * Lọc dữ liệu đầu vào
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Các hàm generate_csrf_token() và check_csrf_token() đã được chuyển sang auth.php

/**
 * Tạo chuỗi ngẫu nhiên
 */
function random_string($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $random_string = '';
    for ($i = 0; $i < $length; $i++) {
        $random_string .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $random_string;
}

/**
 * Loại bỏ dấu tiếng Việt
 */
function remove_accents($string) {
    if (!$string) return '';

    $string = strtolower($string);

    $replacements = [
        'à' => 'a', 'á' => 'a', 'ạ' => 'a', 'ả' => 'a', 'ã' => 'a',
        'â' => 'a', 'ầ' => 'a', 'ấ' => 'a', 'ậ' => 'a', 'ẩ' => 'a', 'ẫ' => 'a',
        'ă' => 'a', 'ằ' => 'a', 'ắ' => 'a', 'ặ' => 'a', 'ẳ' => 'a', 'ẵ' => 'a',
        'è' => 'e', 'é' => 'e', 'ẹ' => 'e', 'ẻ' => 'e', 'ẽ' => 'e',
        'ê' => 'e', 'ề' => 'e', 'ế' => 'e', 'ệ' => 'e', 'ể' => 'e', 'ễ' => 'e',
        'ì' => 'i', 'í' => 'i', 'ị' => 'i', 'ỉ' => 'i', 'ĩ' => 'i',
        'ò' => 'o', 'ó' => 'o', 'ọ' => 'o', 'ỏ' => 'o', 'õ' => 'o',
        'ô' => 'o', 'ồ' => 'o', 'ố' => 'o', 'ộ' => 'o', 'ổ' => 'o', 'ỗ' => 'o',
        'ơ' => 'o', 'ờ' => 'o', 'ớ' => 'o', 'ợ' => 'o', 'ở' => 'o', 'ỡ' => 'o',
        'ù' => 'u', 'ú' => 'u', 'ụ' => 'u', 'ủ' => 'u', 'ũ' => 'u',
        'ư' => 'u', 'ừ' => 'u', 'ứ' => 'u', 'ự' => 'u', 'ử' => 'u', 'ữ' => 'u',
        'ỳ' => 'y', 'ý' => 'y', 'ỵ' => 'y', 'ỷ' => 'y', 'ỹ' => 'y',
        'đ' => 'd'
    ];

    return strtr($string, $replacements);
}

/**
 * Tải file lên
 */
function upload_file($file, $target_dir, $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp']) {
    // Kiểm tra lỗi
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $error_message = 'Lỗi khi tải file lên';

        // Hiển thị thông báo lỗi chi tiết hơn
        switch ($file['error']) {
            case UPLOAD_ERR_INI_SIZE:
                $error_message = 'Kích thước file vượt quá giới hạn upload_max_filesize trong php.ini';
                break;
            case UPLOAD_ERR_FORM_SIZE:
                $error_message = 'Kích thước file vượt quá giới hạn MAX_FILE_SIZE trong form HTML';
                break;
            case UPLOAD_ERR_PARTIAL:
                $error_message = 'File chỉ được tải lên một phần';
                break;
            case UPLOAD_ERR_NO_FILE:
                $error_message = 'Không có file nào được tải lên';
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $error_message = 'Thiếu thư mục tạm';
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $error_message = 'Không thể ghi file vào ổ đĩa';
                break;
            case UPLOAD_ERR_EXTENSION:
                $error_message = 'Tải file bị dừng bởi extension';
                break;
        }

        return [
            'success' => false,
            'message' => $error_message
        ];
    }

    // Kiểm tra kích thước file (max 10MB)
    if ($file['size'] > 10 * 1024 * 1024) {
        return [
            'success' => false,
            'message' => 'Kích thước file quá lớn (tối đa 10MB)'
        ];
    }

    // Kiểm tra loại file
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    // Kiểm tra MIME type thực tế của file
    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mime_type = $finfo->file($file['tmp_name']);

    // Xác định loại file dựa trên MIME type
    $is_valid_type = false;

    // Kiểm tra nếu là video
    if (in_array($file_extension, ['mp4', 'webm', 'ogg']) && strpos($mime_type, 'video/') === 0) {
        $is_valid_type = true;
    }
    // Kiểm tra nếu là hình ảnh
    else if (in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']) && strpos($mime_type, 'image/') === 0) {
        $is_valid_type = true;
    }

    if (!$is_valid_type && !in_array($file_extension, $allowed_types)) {
        return [
            'success' => false,
            'message' => 'Loại file không được hỗ trợ. MIME type: ' . $mime_type
        ];
    }

    // Tạo tên file mới
    $new_filename = random_string() . '.' . $file_extension;
    $target_file = $target_dir . $new_filename;

    // Tạo thư mục đích nếu chưa tồn tại
    if (!is_dir($target_dir)) {
        mkdir($target_dir, 0755, true);
    }

    if (move_uploaded_file($file['tmp_name'], $target_file)) {
        return [
            'success' => true,
            'filename' => $new_filename,
            'message' => 'Tải file lên thành công'
        ];
    } else {
        return [
            'success' => false,
            'message' => 'Không thể di chuyển file đã tải lên'
        ];
    }
}

/**
 * Sao chép file và tạo tên mới duy nhất
 *
 * @param string $source_path Đường dẫn file gốc
 * @param string $target_dir Thư mục đích để lưu file sao chép
 * @return array ['success' => bool, 'filename' => string|null, 'message' => string]
 */
function duplicate_file($source_path, $target_dir) {
    if (!file_exists($source_path)) {
        return ['success' => false, 'filename' => null, 'message' => 'File nguồn không tồn tại.'];
    }
    // Đảm bảo thư mục đích kết thúc bằng dấu /
    $target_dir = rtrim($target_dir, '/') . '/';

    if (!is_dir($target_dir)) {
        if (!mkdir($target_dir, 0755, true)) {
            return ['success' => false, 'filename' => null, 'message' => 'Không thể tạo thư mục đích.'];
        }
    }

    $file_extension = strtolower(pathinfo($source_path, PATHINFO_EXTENSION));
    $new_filename = random_string(20) . '.' . $file_extension; // Sử dụng hàm random_string đã có
    $target_file_path = $target_dir . $new_filename;

    if (copy($source_path, $target_file_path)) {
        return ['success' => true, 'filename' => $new_filename, 'message' => 'Sao chép file thành công.'];
    } else {
        // Thêm thông tin lỗi chi tiết
        $error = error_get_last();
        $error_message = 'Không thể sao chép file.';
        if ($error !== null) {
            $error_message .= ' Lỗi: ' . $error['message'];
        }
        error_log("Lỗi khi sao chép file từ {$source_path} sang {$target_file_path}: " . $error_message);
        return ['success' => false, 'filename' => null, 'message' => $error_message];
    }
}

/**
 * Đếm số đơn hàng theo ngày
 */
function count_orders_by_date($date) {
    global $db;
    try {
        if (!$db) {
            error_log("Database connection not available in count_orders_by_date");
            return 0;
        }
        $sql = "SELECT COUNT(*) as total FROM orders WHERE DATE(created_at) = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$date]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)$result['total'];
    } catch (PDOException $e) {
        error_log("Error in count_orders_by_date: " . $e->getMessage());
        return 0;
    }
}

/**
 * Lấy sản phẩm bán chạy nhất trong ngày
 */
function get_best_selling_product($date) {
    global $db;
    try {
        if (!$db) {
            error_log("Database connection not available in get_best_selling_product");
            return 'Không có dữ liệu';
        }
        $sql = "SELECT p.name, COUNT(*) as total_sold 
                FROM order_items oi 
                JOIN orders o ON oi.order_id = o.id 
                JOIN products p ON oi.product_id = p.id 
                WHERE DATE(o.created_at) = ? 
                GROUP BY p.id, p.name 
                ORDER BY total_sold DESC 
                LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$date]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['name'] : 'Chưa có sản phẩm bán ra';
    } catch (PDOException $e) {
        error_log("Error in get_best_selling_product: " . $e->getMessage());
        return 'Không có dữ liệu';
    }
}

/**
 * Đếm số khách hàng mới trong ngày
 */
function count_new_customers_by_date($date) {
    global $db;
    try {
        if (!$db) {
            error_log("Database connection not available in count_new_customers_by_date");
            return 0;
        }
        $sql = "SELECT COUNT(*) as total FROM users WHERE DATE(created_at) = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$date]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)$result['total'];
    } catch (PDOException $e) {
        error_log("Error in count_new_customers_by_date: " . $e->getMessage());
        return 0;
    }
}

/**
 * Generate HTML cho Filter Results Header
 * Sử dụng cho cả server-side rendering và AJAX response
 */
function generate_filter_results_header($filters, $products, $total_products, $sort_label = 'Mới nhất') {
    // Kiểm tra xem có filter nào được áp dụng không
    $has_filters = !empty($filters['keyword']) ||
                   !empty($filters['category_ids']) ||
                   !empty($filters['price_min']) ||
                   !empty($filters['price_max']) ||
                   !empty($filters['promotion_filters']);

    if (!$has_filters) {
        return ''; // Không hiển thị header nếu không có filter
    }

    $keyword = $filters['keyword'] ?? '';
    $category_ids = $filters['category_ids'] ?? [];
    $price_min = $filters['price_min'] ?? null;
    $price_max = $filters['price_max'] ?? null;
    $promotion_filters = $filters['promotion_filters'] ?? [];

    // Lấy thông tin categories
    $current_categories = [];
    if (!empty($category_ids)) {
        foreach ($category_ids as $cat_id) {
            $cat_info = get_category_by_id($cat_id);
            if ($cat_info) {
                $current_categories[] = $cat_info;
            }
        }
    }

    $is_empty = empty($products);
    $empty_class = $is_empty ? 'empty-state' : '';
    $icon_class = $is_empty ? 'empty-icon' : '';

    ob_start();
    ?>
    <div class="filter-results-header mb-6 bg-gradient-to-r from-orange-50/80 to-amber-50/80 border border-orange-200/40 rounded-xl p-5 shadow-sm backdrop-blur-sm relative overflow-hidden gradient-bg <?php echo $empty_class; ?>">
        <!-- Animated Background Pattern -->
        <div class="filter-bg-pattern absolute inset-0 opacity-[0.04] pointer-events-none"></div>

        <!-- Subtle decorative background elements -->
        <div class="decorative-element absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-100/20 to-amber-100/20 rounded-full -translate-y-10 translate-x-10"></div>

        <div class="relative z-10">
            <div class="flex items-center justify-between flex-wrap gap-4">
                <div class="flex items-center space-x-3">
                    <div class="icon-container w-10 h-10 bg-gradient-to-br from-orange-500 to-amber-600 rounded-lg flex items-center justify-center shadow-md <?php echo $icon_class; ?>">
                        <?php if ($is_empty): ?>
                            <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                        <?php elseif (!empty($keyword)): ?>
                            <i class="fas fa-search text-white text-sm"></i>
                        <?php else: ?>
                            <i class="fas fa-filter text-white text-sm"></i>
                        <?php endif; ?>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center gap-2 mb-1">
                            <h2 class="text-lg md:text-xl font-semibold text-gray-800">
                                <?php if ($is_empty): ?>
                                    Không tìm thấy kết quả
                                <?php elseif (!empty($keyword)): ?>
                                    Kết quả tìm kiếm
                                <?php else: ?>
                                    Kết quả lọc
                                <?php endif; ?>
                            </h2>
                            <?php if (!$is_empty): ?>
                                <div class="count-badge px-2 py-1 bg-gradient-to-r from-orange-500 to-amber-500 text-white text-xs font-semibold rounded-full" id="results-count">
                                    <?php echo number_format($total_products); ?> sản phẩm
                                </div>
                            <?php else: ?>
                                <div class="count-badge px-2 py-1 bg-gray-400 text-white text-xs font-semibold rounded-full">
                                    0 sản phẩm
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="text-gray-600 text-sm leading-relaxed">
                            <?php if ($is_empty): ?>
                                <div class="text-gray-500 font-medium">
                                    Không tìm thấy sản phẩm phù hợp với tiêu chí tìm kiếm.
                                </div>
                            <?php else: ?>
                                <div class="flex flex-wrap items-center gap-2">
                                    <?php if (!empty($keyword)): ?>
                                        <span>Tìm kiếm cho</span>
                                        <span class="inline-flex items-center px-2 py-0.5 bg-white/60 border border-orange-100 rounded text-gray-700 font-medium text-xs">
                                            "<?php echo htmlspecialchars($keyword); ?>"
                                        </span>
                                    <?php endif; ?>
                                    <?php if (!empty($current_categories)): ?>
                                        <?php if (!empty($keyword)): ?><span>trong</span><?php endif; ?>
                                        <?php foreach ($current_categories as $index => $cat): ?>
                                            <?php if ($index > 0): ?><span class="text-gray-400">,</span><?php endif; ?>
                                            <span class="inline-flex items-center px-2 py-0.5 bg-white/60 border border-orange-100 rounded text-gray-700 font-medium text-xs">
                                                <i class="fas fa-tag text-orange-400 text-xs mr-1"></i>
                                                <?php echo htmlspecialchars($cat['name']); ?>
                                            </span>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                    <span class="text-gray-500">•</span>
                                    <span class="inline-flex items-center text-xs text-gray-500">
                                        <i class="fas fa-sort text-orange-400 text-xs mr-1"></i>
                                        Sắp xếp: <span class="font-medium text-gray-600 ml-1"><?php echo htmlspecialchars($sort_label); ?></span>
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions flex items-center space-x-2">
                    <?php if (!empty($keyword)): ?>
                        <button onclick="scrollToProductsSearch()" class="luxury-button group inline-flex items-center px-3 py-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white rounded-lg transition-all duration-200 text-xs font-semibold shadow-md hover:shadow-lg">
                            <i class="fas fa-search mr-1.5 text-xs"></i>
                            Tìm khác
                        </button>
                    <?php else: ?>
                        <a href="<?php echo BASE_URL; ?>/products.php" class="luxury-button group inline-flex items-center px-3 py-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white rounded-lg transition-all duration-200 text-xs font-semibold shadow-md hover:shadow-lg">
                            <i class="fas fa-th-large mr-1.5 text-xs"></i>
                            Xem tất cả
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Active Filters Display -->
        <div class="mt-4 pt-4 border-t border-orange-100/60 relative">
            <div class="active-filters-container flex flex-wrap items-center gap-2">
                <p class="text-xs text-gray-600 font-medium mr-2">Bộ lọc:</p>

                <?php if (!empty($keyword)): ?>
                    <div class="filter-tag group inline-flex items-center px-3 py-1.5 bg-orange-50/80 border border-orange-200/60 hover:border-orange-300/80 rounded-lg text-xs text-orange-700 hover:text-orange-800 transition-all duration-200">
                        <i class="fas fa-search text-orange-400 text-xs mr-2"></i>
                        <span class="font-medium">Từ khóa: "<?php echo htmlspecialchars($keyword); ?>"</span>
                        <button type="button" class="remove-btn ml-2 w-4 h-4 bg-orange-100 hover:bg-orange-200 rounded-full flex items-center justify-center text-orange-500 hover:text-orange-600 transition-all duration-200" onclick="removeKeywordFilter()">
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    </div>
                <?php endif; ?>

                <?php foreach ($current_categories as $cat): ?>
                    <div class="filter-tag group inline-flex items-center px-3 py-1.5 bg-orange-50/80 border border-orange-200/60 hover:border-orange-300/80 rounded-lg text-xs text-orange-700 hover:text-orange-800 transition-all duration-200">
                        <i class="fas fa-tag text-orange-400 text-xs mr-2"></i>
                        <span class="font-medium">Danh mục: <?php echo htmlspecialchars($cat['name']); ?></span>
                        <button type="button" class="remove-btn ml-2 w-4 h-4 bg-orange-100 hover:bg-orange-200 rounded-full flex items-center justify-center text-orange-500 hover:text-orange-600 transition-all duration-200" onclick="removeCategoryFilter(<?php echo $cat['id']; ?>)">
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    </div>
                <?php endforeach; ?>

                <?php if ($price_min || $price_max): ?>
                    <div class="filter-tag group inline-flex items-center px-3 py-1.5 bg-orange-50/80 border border-orange-200/60 hover:border-orange-300/80 rounded-lg text-xs text-orange-700 hover:text-orange-800 transition-all duration-200">
                        <i class="fas fa-dollar-sign text-orange-400 text-xs mr-2"></i>
                        <span class="font-medium">Giá:
                            <?php if ($price_min && $price_max): ?>
                                <?php echo number_format($price_min); ?>đ - <?php echo number_format($price_max); ?>đ
                            <?php elseif ($price_min): ?>
                                Từ <?php echo number_format($price_min); ?>đ
                            <?php else: ?>
                                Đến <?php echo number_format($price_max); ?>đ
                            <?php endif; ?>
                        </span>
                        <button type="button" class="remove-btn ml-2 w-4 h-4 bg-orange-100 hover:bg-orange-200 rounded-full flex items-center justify-center text-orange-500 hover:text-orange-600 transition-all duration-200" onclick="removePriceFilter()">
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($promotion_filters)): ?>
                    <?php
                    $promotion_labels = [
                        'sale' => 'Đang giảm giá',
                        'flash_sale' => 'Flash Sale',
                        'featured' => 'Sản phẩm nổi bật'
                    ];
                    foreach ($promotion_filters as $promo): ?>
                        <div class="filter-tag group inline-flex items-center px-3 py-1.5 bg-orange-50/80 border border-orange-200/60 hover:border-orange-300/80 rounded-lg text-xs text-orange-700 hover:text-orange-800 transition-all duration-200">
                            <i class="fas fa-tags text-orange-400 text-xs mr-2"></i>
                            <span class="font-medium">Khuyến mãi: <?php echo $promotion_labels[$promo] ?? $promo; ?></span>
                            <button type="button" class="remove-btn ml-2 w-4 h-4 bg-orange-100 hover:bg-orange-200 rounded-full flex items-center justify-center text-orange-500 hover:text-orange-600 transition-all duration-200" onclick="removePromotionFilter('<?php echo htmlspecialchars($promo); ?>')">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
?>
