/**
 * Mini Cart Update JavaScript for Nội Thất Bàng Vũ
 * Cập nhật mini cart khi thêm sản phẩm vào giỏ hàng
 */

document.addEventListener('DOMContentLoaded', function () {
  console.log('Mini Cart Update JS loaded');

  // Đăng ký hàm cập nhật mini cart
  window.updateMiniCart = updateMiniCart;

  // Lắng nghe sự kiện cập nhật giỏ hàng
  document.addEventListener('cart:updated', function(event) {
    console.log('Mini cart received cart:updated event:', event.detail);

    // Đồng bộ hóa mini cart
    syncMiniCart();
  });
});

/**
 * Cập nhật mini cart khi thêm sản phẩm vào giỏ hàng
 * @param {number} cartCount - Số lượng sản phẩm trong giỏ hàng
 * @param {Array} cartItems - <PERSON>h sách sản phẩm trong giỏ hàng
 * @param {number} cartTotal - Tổng tiền giỏ hàng
 */
function updateMiniCart(cartCount, cartItems, cartTotal) {
  console.log('Updating mini cart:', { cartCount, cartItems, cartTotal });

  // Cập nhật số lượng sản phẩm trong mini cart
  const miniCartCount = document.querySelector('.mini-cart-count');
  if (miniCartCount) {
    miniCartCount.textContent = cartCount + ' sản phẩm';
  }

  // Cập nhật danh sách sản phẩm trong mini cart
  const miniCartItems = document.querySelector('.mini-cart-items');
  if (miniCartItems) {
    // Nếu giỏ hàng có sản phẩm
    if (cartItems && cartItems.length > 0) {
      // Lấy 3 sản phẩm mới nhất (thay vì 3 sản phẩm đầu tiên)
      // Đảo ngược mảng để lấy sản phẩm mới nhất, sau đó lấy 3 sản phẩm, rồi đảo ngược lại
      const displayItems = [...cartItems].reverse().slice(0, 3).reverse();

      // Tạo HTML cho danh sách sản phẩm
      let itemsHTML = '';
      displayItems.forEach(item => {
        itemsHTML += `
          <div class="mini-cart-item">
            <div class="mini-cart-item-image">
              <img src="${BASE_URL}/uploads/products/${item.image}" alt="${item.name}">
            </div>
            <div class="mini-cart-item-info">
              <div class="mini-cart-item-name">${item.name}</div>
              <div class="mini-cart-item-price">${formatCurrency(item.price)}</div>
              <div class="mini-cart-item-quantity">Số lượng: ${item.quantity}</div>
            </div>
          </div>
        `;
      });

      // Thêm thông báo nếu có nhiều hơn 3 sản phẩm
      if (cartItems.length > 3) {
        itemsHTML += `
          <div class="mini-cart-more-items">
            <a href="${BASE_URL}/cart.php" class="mini-cart-view-more">
              + ${cartItems.length - 3} sản phẩm khác
            </a>
          </div>
        `;
      }

      // Cập nhật HTML
      miniCartItems.innerHTML = itemsHTML;
    } else {
      // Nếu giỏ hàng trống
      miniCartItems.innerHTML = `
        <div class="mini-cart-empty">
          <p>Giỏ hàng của bạn đang trống</p>
        </div>
      `;
    }
  }

  // Cập nhật tổng tiền giỏ hàng
  const miniCartTotal = document.querySelector('.mini-cart-total-value');
  if (miniCartTotal) {
    miniCartTotal.textContent = formatCurrency(cartTotal);
  }
}

/**
 * Format số tiền thành định dạng tiền tệ
 * @param {number|string} amount - Số tiền cần format
 * @returns {string} - Chuỗi đã được format
 */
function formatCurrency(amount) {
  // Xử lý trường hợp amount là chuỗi đã được format
  if (typeof amount === 'string' && amount.includes('đ')) {
    return amount;
  }

  // Nếu amount không phải là số, chuyển đổi nó
  if (typeof amount !== 'number') {
    // Loại bỏ các ký tự không phải số và dấu chấm/phẩy
    if (typeof amount === 'string') {
      amount = amount.replace(/[^\d.,]/g, '');
      amount = amount.replace(/,/g, '.');
    }
    amount = parseFloat(amount);
  }

  // Nếu amount không phải là số hợp lệ, trả về 0đ
  if (isNaN(amount)) {
    return '0đ';
  }

  // Format số tiền
  try {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + 'đ';
  } catch (error) {
    console.error('Error formatting currency:', error);
    return amount + 'đ';
  }
}

/**
 * Đồng bộ hóa mini cart với giỏ hàng hiện tại
 */
function syncMiniCart() {
  // Gửi yêu cầu AJAX để lấy thông tin giỏ hàng hiện tại
  fetch(`${BASE_URL}/ajax/get_cart_data.php`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Cập nhật mini cart
        updateMiniCart(data.count, data.items, data.total);
      }
    })
    .catch(error => console.error('Error syncing mini cart:', error));
}

/**
 * Cập nhật mini cart từ dữ liệu trả về khi thêm sản phẩm vào giỏ hàng
 * @param {Object} data - Dữ liệu trả về từ API add_to_cart.php
 */
function updateMiniCartFromAddToCartResponse(data) {
  console.log('updateMiniCartFromAddToCartResponse called with data:', data);

  if (!data) {
    console.error('No data provided to updateMiniCartFromAddToCartResponse');
    return false;
  }

  if (!data.success) {
    console.error('Data indicates unsuccessful operation:', data.message || 'Unknown error');
    return false;
  }

  if (!data.items) {
    console.error('No items data in response');
    return false;
  }

  try {
    // Cập nhật mini cart với dữ liệu trả về trực tiếp từ add_to_cart.php
    console.log('Updating mini cart with data:', {
      count: data.count,
      items: data.items,
      total: data.total
    });

    updateMiniCart(data.count, data.items, data.total);
    return true;
  } catch (error) {
    console.error('Error in updateMiniCartFromAddToCartResponse:', error);
    return false;
  }
}

// Đồng bộ hóa mini cart khi tải trang
document.addEventListener('DOMContentLoaded', function() {
  // Đồng bộ hóa mini cart sau khi trang đã tải
  setTimeout(syncMiniCart, 500);
});

// Export các hàm để sử dụng từ các file JavaScript khác
window.updateMiniCartFromAddToCartResponse = updateMiniCartFromAddToCartResponse;
