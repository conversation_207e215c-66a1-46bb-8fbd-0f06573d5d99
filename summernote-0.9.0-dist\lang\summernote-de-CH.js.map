{"version": 3, "file": "lang/summernote-de-CH.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,eAAe;QAC1BC,KAAK,EAAE,cAAc;QACrBC,MAAM,EAAE,YAAY;QACpBC,IAAI,EAAE,YAAY;QAClBC,aAAa,EAAE,iBAAiB;QAChCC,SAAS,EAAE,cAAc;QACzBC,WAAW,EAAE,cAAc;QAC3BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,eAAe;QACvBC,UAAU,EAAE,gBAAgB;QAC5BC,UAAU,EAAE,YAAY;QACxBC,aAAa,EAAE,YAAY;QAC3BC,SAAS,EAAE,aAAa;QACxBC,UAAU,EAAE,cAAc;QAC1BC,SAAS,EAAE,gBAAgB;QAC3BC,YAAY,EAAE,mBAAmB;QACjCC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,gBAAgB;QAChCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,qBAAqB;QACpCC,SAAS,EAAE,uBAAuB;QAClCC,eAAe,EAAE,iBAAiB;QAClCC,eAAe,EAAE,sBAAsB;QACvCC,oBAAoB,EAAE,oCAAoC;QAC1DC,GAAG,EAAE,UAAU;QACfC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,WAAW;QACtBpB,MAAM,EAAE,gBAAgB;QACxBgB,GAAG,EAAE,WAAW;QAChBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,eAAe;QACvBuB,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,YAAY;QAClBC,aAAa,EAAE,aAAa;QAC5BT,GAAG,EAAE,UAAU;QACfU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,WAAW,EAAE,kBAAkB;QAC/BC,WAAW,EAAE,mBAAmB;QAChCC,UAAU,EAAE,gBAAgB;QAC5BC,WAAW,EAAE,iBAAiB;QAC9BC,MAAM,EAAE,eAAe;QACvBC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,QAAQ;QAChBC,CAAC,EAAE,QAAQ;QACXC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,WAAW;QAChBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,UAAU;QACtBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE,oBAAoB;QAC7BC,MAAM,EAAE,oBAAoB;QAC5BC,IAAI,EAAE,kBAAkB;QACxBC,MAAM,EAAE,sBAAsB;QAC9BC,KAAK,EAAE,mBAAmB;QAC1BC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE,kBAAkB;QAC9BC,UAAU,EAAE,cAAc;QAC1BC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,oBAAoB;QACpCC,KAAK,EAAE,cAAc;QACrBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE,YAAY;QACnBC,cAAc,EAAE,kBAAkB;QAClCC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,oBAAoB;QACzCC,aAAa,EAAE,gBAAgB;QAC/BC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ2B,eAAe,EAAE,iBAAiB;QAClCC,IAAI,EAAE,6BAA6B;QACnCC,IAAI,EAAE,8BAA8B;QACpCC,GAAG,EAAE,mBAAmB;QACxBC,KAAK,EAAE,kBAAkB;QACzB7F,IAAI,EAAE,cAAc;QACpBC,MAAM,EAAE,gBAAgB;QACxBC,SAAS,EAAE,gBAAgB;QAC3BI,aAAa,EAAE,gBAAgB;QAC/BwF,YAAY,EAAE,iBAAiB;QAC/BC,WAAW,EAAE,aAAa;QAC1BC,aAAa,EAAE,QAAQ;QACvBC,YAAY,EAAE,cAAc;QAC5BC,WAAW,EAAE,WAAW;QACxBC,mBAAmB,EAAE,qBAAqB;QAC1CC,iBAAiB,EAAE,mBAAmB;QACtClC,OAAO,EAAE,4BAA4B;QACrCC,MAAM,EAAE,4BAA4B;QACpCkC,UAAU,EAAE,+CAA+C;QAC3DC,QAAQ,EAAE,mCAAmC;QAC7CC,QAAQ,EAAE,mCAAmC;QAC7CC,QAAQ,EAAE,mCAAmC;QAC7CC,QAAQ,EAAE,mCAAmC;QAC7CC,QAAQ,EAAE,mCAAmC;QAC7CC,QAAQ,EAAE,mCAAmC;QAC7CC,oBAAoB,EAAE,iCAAiC;QACvD,iBAAiB,EAAE;MACrB,CAAC;MACDC,OAAO,EAAE;QACPnB,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE;MACR,CAAC;MACDmB,WAAW,EAAE;QACXA,WAAW,EAAE,eAAe;QAC5BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-de-CH.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'de-CH': {\n      font: {\n        bold: '<PERSON><PERSON>',\n        italic: 'Kursiv',\n        underline: '<PERSON><PERSON><PERSON><PERSON>',\n        clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        height: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        name: '<PERSON>hrift<PERSON>',\n        strikethrough: 'Durchgestrichen',\n        subscript: 'Tiefgestellt',\n        superscript: 'Hochgestellt',\n        size: 'Schriftgrösse',\n      },\n      image: {\n        image: 'Bild',\n        insert: 'Bild einfügen',\n        resizeFull: 'Originalgrösse',\n        resizeHalf: '1/2 Grösse',\n        resizeQuarter: '1/4 Grösse',\n        floatLeft: 'Linksbündig',\n        floatRight: 'Rechtsbündig',\n        floatNone: 'Kein Textfluss',\n        shapeRounded: 'Abgerundete Ecken',\n        shapeCircle: 'Kreisförmig',\n        shapeThumbnail: '\"Vorschaubild\"',\n        shapeNone: '<PERSON><PERSON>',\n        dragImageHere: 'Bild hierher ziehen',\n        dropImage: 'Bild oder Text nehmen',\n        selectFromFiles: 'Datei auswählen',\n        maximumFileSize: 'Maximale Dateigrösse',\n        maximumFileSizeError: 'Maximale Dateigrösse überschritten',\n        url: 'Bild URL',\n        remove: 'Bild entfernen',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Videolink',\n        insert: 'Video einfügen',\n        url: 'Video URL',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion oder Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Link einfügen',\n        unlink: 'Link entfernen',\n        edit: 'Bearbeiten',\n        textToDisplay: 'Anzeigetext',\n        url: 'Link URL',\n        openInNewWindow: 'In neuem Fenster öffnen',\n      },\n      table: {\n        table: 'Tabelle',\n        addRowAbove: '+ Zeile oberhalb',\n        addRowBelow: '+ Zeile unterhalb',\n        addColLeft: '+ Spalte links',\n        addColRight: '+ Spalte rechts',\n        delRow: 'Zeile löschen',\n        delCol: 'Spalte löschen',\n        delTable: 'Tabelle löschen',\n      },\n      hr: {\n        insert: 'Horizontale Linie einfügen',\n      },\n      style: {\n        style: 'Stil',\n        normal: 'Normal',\n        p: 'Normal',\n        blockquote: 'Zitat',\n        pre: 'Quellcode',\n        h1: 'Überschrift 1',\n        h2: 'Überschrift 2',\n        h3: 'Überschrift 3',\n        h4: 'Überschrift 4',\n        h5: 'Überschrift 5',\n        h6: 'Überschrift 6',\n      },\n      lists: {\n        unordered: 'Aufzählung',\n        ordered: 'Nummerierung',\n      },\n      options: {\n        help: 'Hilfe',\n        fullscreen: 'Vollbild',\n        codeview: 'Quellcode anzeigen',\n      },\n      paragraph: {\n        paragraph: 'Absatz',\n        outdent: 'Einzug verkleinern',\n        indent: 'Einzug vergrössern',\n        left: 'Links ausrichten',\n        center: 'Zentriert ausrichten',\n        right: 'Rechts ausrichten',\n        justify: 'Blocksatz',\n      },\n      color: {\n        recent: 'Letzte Farbe',\n        more: 'Weitere Farben',\n        background: 'Hintergrundfarbe',\n        foreground: 'Schriftfarbe',\n        transparent: 'Transparenz',\n        setTransparent: 'Transparenz setzen',\n        reset: 'Zurücksetzen',\n        resetToDefault: 'Auf Standard zurücksetzen',\n      },\n      shortcut: {\n        shortcuts: 'Tastenkürzel',\n        close: 'Schliessen',\n        textFormatting: 'Textformatierung',\n        action: 'Aktion',\n        paragraphFormatting: 'Absatzformatierung',\n        documentStyle: 'Dokumentenstil',\n        extraKeys: 'Weitere Tasten',\n      },\n      help: {\n        insertParagraph: 'Absatz einfügen',\n        undo: 'Letzte Anweisung rückgängig',\n        redo: 'Letzte Anweisung wiederholen',\n        tab: 'Einzug hinzufügen',\n        untab: 'Einzug entfernen',\n        bold: 'Schrift Fett',\n        italic: 'Schrift Kursiv',\n        underline: 'Unterstreichen',\n        strikethrough: 'Durchstreichen',\n        removeFormat: 'Entfernt Format',\n        justifyLeft: 'Linksbündig',\n        justifyCenter: 'Mittig',\n        justifyRight: 'Rechtsbündig',\n        justifyFull: 'Blocksatz',\n        insertUnorderedList: 'Unnummerierte Liste',\n        insertOrderedList: 'Nummerierte Liste',\n        outdent: 'Aktuellen Absatz ausrücken',\n        indent: 'Aktuellen Absatz einrücken',\n        formatPara: 'Formatiert aktuellen Block als Absatz (P-Tag)',\n        formatH1: 'Formatiert aktuellen Block als H1',\n        formatH2: 'Formatiert aktuellen Block als H2',\n        formatH3: 'Formatiert aktuellen Block als H3',\n        formatH4: 'Formatiert aktuellen Block als H4',\n        formatH5: 'Formatiert aktuellen Block als H5',\n        formatH6: 'Formatiert aktuellen Block als H6',\n        insertHorizontalRule: 'Fügt eine horizontale Linie ein',\n        'linkDialog.show': 'Zeigt den Linkdialog',\n      },\n      history: {\n        undo: 'Rückgängig',\n        redo: 'Wiederholen',\n      },\n      specialChar: {\n        specialChar: 'Sonderzeichen',\n        select: 'Zeichen auswählen',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "normal", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "insertParagraph", "undo", "redo", "tab", "untab", "removeFormat", "justifyLeft", "justifyCenter", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "formatPara", "formatH1", "formatH2", "formatH3", "formatH4", "formatH5", "formatH6", "insertHorizontalRule", "history", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}