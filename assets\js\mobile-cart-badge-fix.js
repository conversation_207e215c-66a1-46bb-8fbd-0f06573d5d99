/**
 * Mobile Cart Badge Fix JavaScript for Nội Thất Bàng Vũ
 * Khắc phục vấn đề badge giỏ hàng không hiển thị khi thêm sản phẩm vào giỏ hàng trên giao diện điện thoại
 */

document.addEventListener('DOMContentLoaded', function () {
  console.log('Mobile Cart Badge Fix loaded');

  // Đăng ký sự kiện cho các nút thêm vào giỏ hàng
  registerMobileCartEvents();

  // Đảm bảo badge được hiển thị đúng khi tải trang
  initializeMobileCartBadge();
});

/**
 * Đăng ký sự kiện cho các nút thêm vào giỏ hàng
 */
function registerMobileCartEvents() {
  // Tìm tất cả các nút thêm vào giỏ hàng
  const addToCartButtons = document.querySelectorAll('.add-to-cart-btn, .quick-add-to-cart-btn');

  console.log('Registering mobile cart events for', addToCartButtons.length, 'buttons');

  // Kiểm tra xem đã có các hàm xử lý giỏ hàng khác chưa
  const hasExistingCartHandlers = typeof window.syncCartBadges === 'function' ||
                                 typeof window.updateAllCartBadges === 'function' ||
                                 typeof window.handleAddToCartSuccess === 'function';

  // Nếu đã có các hàm xử lý giỏ hàng khác, không đăng ký sự kiện mới
  if (hasExistingCartHandlers) {
    console.log('Existing cart handlers detected, skipping direct event registration');

    // Thay vào đó, chỉ đăng ký hàm cập nhật badge
    window.updateMobileCartBadge = updateMobileCartBadge;
    return;
  }

  // LOẠI BỎ event listener trùng lặp - đã được xử lý trong cart-realtime.js
  // addToCartButtons.forEach(button => {
  //   // Kiểm tra xem nút đã được đăng ký sự kiện chưa
  //   if (!button.dataset.mobileCartRegistered) {
  //     button.addEventListener('click', function(e) {
  //       console.log('Add to cart button clicked (mobile fix)');
  //       // Lấy ID sản phẩm
  //       const productId = this.getAttribute('data-product-id') || this.dataset.productId;
  //       if (!productId) {
  //         console.warn('Product ID not found for button:', this);
  //         return;
  //       }
  //       // Thêm sản phẩm vào giỏ hàng và cập nhật badge
  //       addToCartAndUpdateBadge(productId);
  //     });
  //     // Đánh dấu nút đã được đăng ký sự kiện
  //     button.dataset.mobileCartRegistered = 'true';
  //   }
  // });
}

/**
 * Thêm sản phẩm vào giỏ hàng và cập nhật badge
 */
function addToCartAndUpdateBadge(productId, quantity = 1) {
  // Kiểm tra xem đang trong quá trình thêm vào giỏ hàng không
  if (window.isAddingToCartMobile) {
    return;
  }

  // Đánh dấu đang trong quá trình thêm vào giỏ hàng
  window.isAddingToCartMobile = true;

  // Lưu số lượng giỏ hàng hiện tại
  const currentCount = parseInt(localStorage.getItem('cartCount') || '0');

  // Gửi yêu cầu thêm vào giỏ hàng
  fetch(`${BASE_URL}/ajax/add_to_cart.php`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: `product_id=${productId}&quantity=${quantity}`,
  })
    .then(response => response.json())
    .then(data => {
      // Đánh dấu đã hoàn thành quá trình thêm vào giỏ hàng
      window.isAddingToCartMobile = false;

      if (data.success) {
        console.log('Product added to cart successfully (mobile fix)');

        // Cập nhật số lượng giỏ hàng trong localStorage
        localStorage.setItem('cartCount', data.count.toString());

        // Cập nhật cart badge ngay lập tức với dữ liệu từ server
        console.log('Updating cart badges immediately with count:', data.count);

        // Ưu tiên sử dụng hàm updateAllCartBadgesImmediate nếu có
        if (typeof window.updateAllCartBadgesImmediate === 'function') {
          console.log('Using updateAllCartBadgesImmediate');
          window.updateAllCartBadgesImmediate(data.count);
        } else {
          // Fallback: cập nhật mobile badge trực tiếp
          console.log('Using mobile-cart-badge-fix to update badge');
          updateMobileCartBadge(data.count);
        }

        // Sau đó gọi handleAddToCartSuccess để cập nhật mini cart (nhưng không để nó override badge count)
        if (typeof window.handleAddToCartSuccess === 'function') {
          console.log('Calling handleAddToCartSuccess for mini cart update');
          window.handleAddToCartSuccess(data.count, data);
        }

        // Hiển thị thông báo thành công
        if (typeof showNotificationRealtime === 'function') {
          showNotificationRealtime('Đã thêm sản phẩm vào giỏ hàng', 'success');
        } else if (typeof showNotification === 'function') {
          showNotification('Đã thêm sản phẩm vào giỏ hàng', 'success');
        }
      } else {
        console.error('Error adding product to cart:', data.message);

        // Hiển thị thông báo lỗi
        if (typeof showNotificationRealtime === 'function') {
          showNotificationRealtime(data.message || 'Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng', 'error');
        } else if (typeof showNotification === 'function') {
          showNotification(data.message || 'Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng', 'error');
        }
      }
    })
    .catch(error => {
      console.error('Error adding product to cart:', error);

      // Đánh dấu đã hoàn thành quá trình thêm vào giỏ hàng
      window.isAddingToCartMobile = false;

      // Hiển thị thông báo lỗi
      if (typeof showNotificationRealtime === 'function') {
        showNotificationRealtime('Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng', 'error');
      } else if (typeof showNotification === 'function') {
        showNotification('Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng', 'error');
      }
    });
}

/**
 * Cập nhật badge trên mobile bottom navigation
 */
function updateMobileCartBadge(count) {
  // Tìm badge trên mobile bottom navigation
  let mobileNavBadge = document.querySelector('.mobile-nav-badge');
  const mobileNavItem = document.querySelector('.mobile-nav-item[href*="/cart.php"]');

  console.log('Updating mobile cart badge:', {
    count: count,
    hasMobileNavBadge: !!mobileNavBadge,
    hasMobileNavItem: !!mobileNavItem
  });

  if (count > 0) {
    // Nếu badge đã tồn tại, cập nhật nó
    if (mobileNavBadge) {
      mobileNavBadge.textContent = count > 99 ? '99+' : count;
      mobileNavBadge.setAttribute('data-count', count > 99 ? '99+' : count);
      mobileNavBadge.style.display = 'flex';

      // Thêm hiệu ứng nhấp nháy - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT
      // mobileNavBadge.classList.add('badge-pulse');
      setTimeout(() => {
        // mobileNavBadge.classList.remove('badge-pulse');
      }, 1000);
    }
    // Nếu badge chưa tồn tại nhưng có mobile nav item, tạo mới badge
    else if (mobileNavItem && !mobileNavItem.querySelector('.mobile-nav-badge')) {
      mobileNavBadge = document.createElement('span');
      mobileNavBadge.className = 'mobile-nav-badge mobile-cart-badge';
      mobileNavBadge.setAttribute('data-count', count > 99 ? '99+' : count);
      mobileNavBadge.textContent = count > 99 ? '99+' : count;
      mobileNavBadge.style.display = 'flex';
      mobileNavItem.appendChild(mobileNavBadge);

      // Thêm hiệu ứng nhấp nháy - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT
      // mobileNavBadge.classList.add('badge-pulse');
      setTimeout(() => {
        // mobileNavBadge.classList.remove('badge-pulse');
      }, 1000);

      console.log('Created new mobile cart badge');
    }
  } else if (mobileNavBadge) {
    // Ẩn badge nếu giỏ hàng trống
    mobileNavBadge.style.display = 'none';
  }
}

/**
 * Khởi tạo badge giỏ hàng trên mobile khi tải trang
 */
function initializeMobileCartBadge() {
  // Kiểm tra xem có các hàm xử lý giỏ hàng khác không
  const hasExistingCartHandlers = typeof window.syncCartBadges === 'function' ||
                                 typeof window.updateAllCartBadges === 'function';

  // Nếu có các hàm xử lý giỏ hàng khác, không cần khởi tạo lại
  if (hasExistingCartHandlers) {
    console.log('Existing cart handlers detected, skipping initialization');

    // Đăng ký hàm cập nhật badge để các hàm khác có thể sử dụng
    window.updateMobileCartBadge = updateMobileCartBadge;
    return;
  }

  // Lấy số lượng giỏ hàng từ localStorage
  const cartCount = parseInt(localStorage.getItem('cartCount') || '0');

  // Nếu có sản phẩm trong giỏ hàng, cập nhật badge
  if (cartCount > 0) {
    updateMobileCartBadge(cartCount);
  }

  // Đồng bộ hóa với server để đảm bảo số lượng chính xác
  fetch(`${BASE_URL}/ajax/get_cart_count.php`)
    .then(response => response.json())
    .then(data => {
      if (data.success && data.count !== cartCount) {
        // Cập nhật localStorage
        localStorage.setItem('cartCount', data.count.toString());

        // Cập nhật badge
        updateMobileCartBadge(data.count);
      }
    })
    .catch(error => console.error('Error initializing mobile cart badge:', error));
}
