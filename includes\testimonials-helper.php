<?php
/**
 * <PERSON>àm lấy danh sách cảm nhận khách hàng
 *
 * @param int $limit Số lượng cảm nhận tối đa cần lấy
 * @param int $offset Vị trí bắt đầu lấy
 * @param int $status Trạng thái (1: hiển thị, 0: ẩn, null: tất cả)
 * @return array Mảng chứa thông tin cảm nhận khách hàng
 */
function get_testimonials($limit = 6, $offset = 0, $status = 1) {
    global $conn;

    $sql = "SELECT * FROM testimonials";
    $params = [];

    // Thêm điều kiện trạng thái nếu có
    if ($status !== null) {
        $sql .= " WHERE status = :status";
        $params[':status'] = $status;
    }

    // Sắp xếp theo thời gian tạo mới nhất
    $sql .= " ORDER BY created_at DESC";

    // G<PERSON>ớ<PERSON> hạn số lượng kết quả
    if ($limit > 0) {
        $sql .= " LIMIT :offset, :limit";
        $params[':offset'] = $offset;
        $params[':limit'] = $limit;
    }

    try {
        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            if ($key == ':offset' || $key == ':limit') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();
        $testimonials = $stmt->fetchAll();

        // Xử lý các trường dữ liệu
        foreach ($testimonials as &$row) {
            $row['product_tags'] = !empty($row['product_tags']) ? explode(',', $row['product_tags']) : array();
        }

        return $testimonials;
    } catch (PDOException $e) {
        error_log("Lỗi khi lấy danh sách cảm nhận khách hàng: " . $e->getMessage());
        return array();
    }
}

/**
 * Hàm đếm tổng số cảm nhận khách hàng
 *
 * @param int $status Trạng thái (1: hiển thị, 0: ẩn, null: tất cả)
 * @return int Tổng số cảm nhận khách hàng
 */
function count_testimonials($status = 1) {
    global $conn;

    $sql = "SELECT COUNT(*) as total FROM testimonials";
    $params = [];

    // Thêm điều kiện trạng thái nếu có
    if ($status !== null) {
        $sql .= " WHERE status = :status";
        $params[':status'] = $status;
    }

    try {
        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $row = $stmt->fetch();

        return intval($row['total']);
    } catch (PDOException $e) {
        error_log("Lỗi khi đếm cảm nhận khách hàng: " . $e->getMessage());
        return 0;
    }
}

/**
 * Hàm thêm mới cảm nhận khách hàng
 *
 * @param array $data Dữ liệu cảm nhận khách hàng
 * @return int|bool ID của cảm nhận vừa thêm hoặc false nếu thất bại
 */
function add_testimonial($data) {
    global $conn;

    try {
        // Chuẩn bị câu lệnh SQL
        $sql = "INSERT INTO testimonials (customer_name, customer_age, customer_address, customer_photo, review_photos, customer_video, customer_video_thumbnail, review_videos, review_videos_thumbnails, rating, content, product_tags, status)
                VALUES (:customer_name, :customer_age, :customer_address, :customer_photo, :review_photos, :customer_video, :customer_video_thumbnail, :review_videos, :review_videos_thumbnails, :rating, :content, :product_tags, :status)";

        $stmt = $conn->prepare($sql);

        // Bind các tham số
        $stmt->bindValue(':customer_name', $data['customer_name']);
        $stmt->bindValue(':customer_age', isset($data['customer_age']) && $data['customer_age'] !== '' ? $data['customer_age'] : null, PDO::PARAM_INT);
        $stmt->bindValue(':customer_address', isset($data['customer_address']) ? $data['customer_address'] : null);
        $stmt->bindValue(':customer_photo', isset($data['customer_photo']) ? $data['customer_photo'] : null);
        $stmt->bindValue(':review_photos', isset($data['review_photos']) ? $data['review_photos'] : null);
        $stmt->bindValue(':customer_video', isset($data['customer_video']) ? $data['customer_video'] : null);
        $stmt->bindValue(':customer_video_thumbnail', isset($data['customer_video_thumbnail']) ? $data['customer_video_thumbnail'] : null);
        $stmt->bindValue(':review_videos', isset($data['review_videos']) ? $data['review_videos'] : null);
        $stmt->bindValue(':review_videos_thumbnails', isset($data['review_videos_thumbnails']) ? $data['review_videos_thumbnails'] : null);
        $stmt->bindValue(':rating', isset($data['rating']) ? $data['rating'] : 5, PDO::PARAM_INT);
        $stmt->bindValue(':content', $data['content']);
        $stmt->bindValue(':product_tags', isset($data['product_tags']) ? $data['product_tags'] : null);
        $stmt->bindValue(':status', isset($data['status']) ? $data['status'] : 1, PDO::PARAM_INT);

        // Thực thi câu lệnh
        if ($stmt->execute()) {
            return $conn->lastInsertId();
        }
    } catch (PDOException $e) {
        error_log("Lỗi khi thêm cảm nhận khách hàng: " . $e->getMessage());
    }

    return false;
}

/**
 * Hàm cập nhật cảm nhận khách hàng
 *
 * @param int $id ID của cảm nhận cần cập nhật
 * @param array $data Dữ liệu cần cập nhật
 * @return bool Kết quả cập nhật
 */
function update_testimonial($id, $data) {
    global $conn;

    try {
        // Xây dựng câu lệnh SQL động dựa trên dữ liệu cần cập nhật
        $updates = [];
        $params = [':id' => $id];

        if (isset($data['customer_name'])) {
            $updates[] = "customer_name = :customer_name";
            $params[':customer_name'] = $data['customer_name'];
        }

        if (isset($data['customer_age'])) {
            $updates[] = "customer_age = :customer_age";
            $params[':customer_age'] = $data['customer_age'];
        }

        if (isset($data['customer_address'])) {
            $updates[] = "customer_address = :customer_address";
            $params[':customer_address'] = $data['customer_address'];
        }

        if (isset($data['customer_photo'])) {
            $updates[] = "customer_photo = :customer_photo";
            $params[':customer_photo'] = $data['customer_photo'];
        }

        if (isset($data['review_photos'])) {
            $updates[] = "review_photos = :review_photos";
            $params[':review_photos'] = $data['review_photos'];
        }

        if (isset($data['customer_video'])) {
            $updates[] = "customer_video = :customer_video";
            $params[':customer_video'] = $data['customer_video'];
        }

        if (isset($data['customer_video_thumbnail'])) {
            $updates[] = "customer_video_thumbnail = :customer_video_thumbnail";
            $params[':customer_video_thumbnail'] = $data['customer_video_thumbnail'];
        }

        if (isset($data['review_videos'])) {
            $updates[] = "review_videos = :review_videos";
            $params[':review_videos'] = $data['review_videos'];
        }

        if (isset($data['review_videos_thumbnails'])) {
            $updates[] = "review_videos_thumbnails = :review_videos_thumbnails";
            $params[':review_videos_thumbnails'] = $data['review_videos_thumbnails'];
        }

        if (isset($data['rating'])) {
            $updates[] = "rating = :rating";
            $params[':rating'] = $data['rating'];
        }

        if (isset($data['content'])) {
            $updates[] = "content = :content";
            $params[':content'] = $data['content'];
        }

        if (isset($data['product_tags'])) {
            $updates[] = "product_tags = :product_tags";
            $params[':product_tags'] = $data['product_tags'];
        }

        if (isset($data['status'])) {
            $updates[] = "status = :status";
            $params[':status'] = $data['status'];
        }

        // Nếu không có dữ liệu cập nhật
        if (empty($updates)) {
            return false;
        }

        // Tạo câu lệnh SQL
        $sql = "UPDATE testimonials SET " . implode(', ', $updates) . " WHERE id = :id";

        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            if ($key == ':id' || $key == ':rating' || $key == ':status' || $key == ':customer_age') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        // Thực thi câu lệnh
        return $stmt->execute();
    } catch (PDOException $e) {
        error_log("Lỗi khi cập nhật cảm nhận khách hàng: " . $e->getMessage());
        return false;
    }
}

/**
 * Hàm xóa cảm nhận khách hàng
 *
 * @param int $id ID của cảm nhận cần xóa
 * @return bool Kết quả xóa
 */
function delete_testimonial($id) {
    global $conn;

    try {
        $sql = "DELETE FROM testimonials WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':id', $id, PDO::PARAM_INT);

        return $stmt->execute();
    } catch (PDOException $e) {
        error_log("Lỗi khi xóa cảm nhận khách hàng: " . $e->getMessage());
        return false;
    }
}

/**
 * Hàm lấy thông tin chi tiết của một cảm nhận khách hàng
 *
 * @param int $id ID của cảm nhận cần lấy
 * @return array|bool Thông tin cảm nhận hoặc false nếu không tìm thấy
 */
function get_testimonial($id) {
    global $conn;

    try {
        $sql = "SELECT * FROM testimonials WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

        $testimonial = $stmt->fetch();

        if ($testimonial) {
            // Xử lý các trường dữ liệu
            $testimonial['product_tags'] = !empty($testimonial['product_tags']) ? explode(',', $testimonial['product_tags']) : array();

            return $testimonial;
        }
    } catch (PDOException $e) {
        error_log("Lỗi khi lấy thông tin cảm nhận khách hàng: " . $e->getMessage());
    }

    return false;
}
