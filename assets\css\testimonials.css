/* CSS cho ph<PERSON><PERSON> nhận kh<PERSON>ch hàng */
.testimonials-section {
    position: relative;
    padding: 4rem 0;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

/* Tùy chỉnh nút đi<PERSON>u h<PERSON>ớng */
.testimonials-button-next,
.testimonials-button-prev {
    color: var(--primary-color, #F37321);
    --swiper-navigation-size: 30px;
    background-color: rgba(255, 255, 255, 0.8);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.testimonials-button-next:hover,
.testimonials-button-prev:hover {
    background-color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.testimonials-button-next:after,
.testimonials-button-prev:after {
    font-weight: bold;
    font-size: 18px;
}

/* Tùy chỉnh pagination */
.testimonials-pagination {
    bottom: 10px !important;
}

.testimonials-pagination .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background: #ccc;
    opacity: 0.5;
}

.testimonials-pagination .swiper-pagination-bullet-active {
    background: var(--primary-color, #F37321);
    opacity: 1;
    width: 30px;
    border-radius: 5px;
}

.testimonials-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
}

.testimonials-bg::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.03) 0%, rgba(59, 130, 246, 0) 70%);
    z-index: -1;
}

.testimonials-bg::after {
    content: '';
    position: absolute;
    bottom: -30%;
    right: -30%;
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, rgba(243, 115, 33, 0.03) 0%, rgba(243, 115, 33, 0) 70%);
    z-index: -1;
}

.testimonials-title-container {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.testimonials-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: rgba(59, 130, 246, 0.1);
    color: #3B82F6;
    font-weight: 600;
    border-radius: 9999px;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    position: relative;
    overflow: hidden;
}

.testimonials-badge .badge-icon {
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.testimonials-heading {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.testimonials-heading::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 100%;
    height: 0.25rem;
    background: rgba(59, 130, 246, 0.3);
    border-radius: 9999px;
}

.testimonials-description {
    max-width: 36rem;
    margin: 0 auto;
    color: #6B7280;
    font-size: 1rem;
    line-height: 1.5;
}

/* Testimonial Cards */
.testimonials-container {
    position: relative;
    z-index: 1;
    padding-top: 10px; /* Thêm padding để tránh bị cắt khi hover */
}

/* Swiper wrapper cần có đủ không gian cho hiệu ứng hover */
.testimonials-swiper {
    padding-top: 5px;
    padding-bottom: 20px;
    overflow: visible; /* Cho phép hiệu ứng hover hiển thị ra ngoài */
}

.testimonials-swiper .swiper-wrapper {
    padding-top: 5px; /* Thêm padding để card có thể nhảy lên */
}

.testimonial-card {
    background: #FFFFFF;
    border-radius: 1rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.01);
    padding: 1.5rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(229, 231, 235, 0.5);
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: rgba(59, 130, 246, 0.2);
}

.testimonial-product {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #1F2937;
}

.testimonial-rating-stars {
    display: flex;
    color: #F59E0B;
}

.testimonial-rating-stars i {
    margin-right: 0.25rem;
}

.product-name {
    text-align: right;
    font-size: 0.95rem;
}

.testimonial-content {
    position: relative;
    z-index: 1;
    margin-bottom: 1.5rem;
    font-style: italic;
    color: #4B5563;
    line-height: 1.6;
    flex-grow: 1;
    /* Bỏ giới hạn số dòng hiển thị */
    /* display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis; */
}

.testimonial-customer {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
    margin-top: auto;
}

.testimonial-photo {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 0.75rem;
    border: 2px solid #E5E7EB;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.testimonial-card:hover .testimonial-photo {
    border-color: #3B82F6;
}

.testimonial-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.testimonial-info {
    flex: 1;
}

.testimonial-name {
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
}

.testimonial-meta {
    display: flex;
    align-items: center;
    color: #6B7280;
    font-size: 0.875rem;
    flex-wrap: wrap;
}

.testimonial-location {
    display: flex;
    align-items: center;
    margin-right: 1rem;
}

.testimonial-location i {
    margin-right: 0.25rem;
    color: #3B82F6;
}

.testimonial-age {
    display: flex;
    align-items: center;
}

.testimonial-age i {
    margin-right: 0.25rem;
    color: #3B82F6;
}

.testimonial-rating i {
    color: #F59E0B;
    margin-right: 0.25rem;
}

.testimonial-tags {
    margin-top: 0.75rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.testimonial-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    background: rgba(59, 130, 246, 0.1);
    color: #3B82F6;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.testimonial-tag i {
    margin-right: 0.25rem;
    font-size: 0.7rem;
}

/* Testimonial Media */
.testimonial-media {
    margin-top: 1rem;
    width: 100%;
    box-sizing: border-box;
    overflow: visible !important;
    padding: 0;
    max-width: 100%;
}

/* CSS cho grid layout mới */
.testimonial-photos-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-top: 12px;
    position: relative;
}

.testimonial-photo-box {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 100%;
    border-radius: 8px;
    background: #f5f5f5;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-photo-box:hover {
    z-index: 10;
}

.testimonial-photo-link {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: block;
    text-decoration: none;
    overflow: hidden;
    border-radius: inherit;
}

.testimonial-photo-link img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    display: block;
}

.testimonial-photo-link:hover img {
    transform: scale(1.05);
}

/* CSS cũ cho backward compatibility */
.testimonial-photos {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
    margin-top: 1rem;
    width: 100%;
    box-sizing: border-box;
    padding: 0;
    position: relative;
}

/* Đảm bảo hiển thị đúng trên desktop */
@media (min-width: 769px) {
    .testimonial-photo-item {
        width: 100%;
        height: 0;
        padding-bottom: 100%;
    }
}

.testimonial-photo-item {
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    width: 100%;
    height: 0;
    padding-bottom: 100%;
    box-sizing: border-box;
}

.testimonial-photo-item a {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: block;
}

.testimonial-photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    position: absolute;
    top: 0;
    left: 0;
}

.testimonial-photo-item:hover img {
    /* transform: scale(1.1); */
}

/* Placeholder khi không có ảnh */
.no-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f1f1f1;
    color: #999;
    font-size: 0.875rem;
    text-align: center;
    padding: 1rem;
}

.no-image-placeholder i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #ccc;
}

/* CSS cho overlay "+X" mới */
.photo-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.7) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 10 !important;
    border-radius: 8px !important;
    transition: background-color 0.3s ease !important;
}

.photo-overlay:hover {
    background: rgba(0, 0, 0, 0.8) !important;
}

.overlay-text {
    color: white !important;
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.testimonial-video {
    margin-top: 1rem;
    border-radius: 0.5rem;
    overflow: hidden;
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    background: #f1f1f1;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.testimonial-video iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

.video-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
}

.video-placeholder:hover {
    background: rgba(0, 0, 0, 0.6);
}

.video-placeholder.with-thumbnail {
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
}

.video-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.25), rgba(0, 0, 0, 0.25));
    z-index: 1;
}

.video-placeholder i {
    position: relative;
    z-index: 2;
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.video-placeholder:hover i {
    transform: scale(1.1);
}

.video-placeholder span {
    position: relative;
    z-index: 2;
    font-size: 1rem;
    color: white;
    margin-top: 0.5rem;
    opacity: 0.9;
}

.testimonial-more-videos {
    margin-top: 0.5rem;
}

.more-videos-link {
    display: inline-flex;
    align-items: center;
    color: #3B82F6;
    font-size: 0.875rem;
    font-weight: 500;
}

.more-videos-link i {
    margin-right: 0.25rem;
}

.hidden-videos {
    display: none;
}

/* Video Modal */
.testimonial-videos-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 99999 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    /* backdrop-filter: blur(5px); */ /* Bỏ hiệu ứng làm mờ nền để tăng hiệu suất */
}

.testimonial-videos-modal-content {
    width: 90%;
    max-width: 900px; /* Tăng chiều rộng tối đa */
    max-height: 85vh; /* Giảm một chút chiều cao để tạo khoảng trống */
    background: #fff;
    border-radius: 12px; /* Bo góc nhiều hơn */
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3), 0 10px 10px rgba(0, 0, 0, 0.2); /* Hiệu ứng bóng đổ đẹp hơn */
    border: 1px solid rgba(255, 255, 255, 0.1); /* Viền mỏng */
    animation: modalFadeIn 0.3s ease; /* Animation khi hiển thị */
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.testimonial-videos-modal-header {
    padding: 1.25rem 1.5rem; /* Padding to hơn */
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08); /* Viền mờ hơn */
    background: linear-gradient(to right, #f8f9fa, #ffffff); /* Gradient nhẹ nhàng */
}

.testimonial-videos-modal-header h3 {
    margin: 0;
    font-size: 1.5rem; /* Phông chữ to hơn */
    color: #1F2937;
    font-weight: 600; /* Semi-bold */
    display: flex;
    align-items: center;
}

.testimonial-videos-modal-header h3::before {
    content: "\f03d"; /* Biểu tượng video */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 0.75rem;
    color: var(--primary-color, #F37321); /* Sử dụng màu chính */
    font-size: 1.25rem;
}

.close-modal {
    background: rgba(0, 0, 0, 0.05);
    border: none;
    width: 36px;
    height: 36px;
    font-size: 1.25rem;
    color: #6B7280;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    outline: none;
}

.close-modal:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #4B5563;
    transform: rotate(90deg);
}

.testimonial-videos-modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); /* Các cột nhỏ hơn, thích hợp hơn */
    gap: 1.25rem;
    background-color: #f9fafb; /* Màu nền nhẹ nhàng */
}

/* Thanh cuộn tùy chỉnh */
.testimonial-videos-modal-body::-webkit-scrollbar {
    width: 8px;
}

.testimonial-videos-modal-body::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

.testimonial-videos-modal-body::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 4px;
}

.testimonial-videos-modal-body::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.25);
}

.testimonial-video-item {
    border-radius: 10px;
    overflow: hidden;
    background: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.testimonial-video-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.testimonial-video-item .testimonial-video {
    margin-top: 0;
    border-radius: 10px 10px 0 0; /* Bo góc trên */
}

.testimonial-video-item .video-placeholder {
    border-radius: 10px 10px 0 0; /* Bo góc trên */
}

/* Thêm phần mô tả video (tùy chọn) */
.testimonial-video-info {
    padding: 12px 15px;
    background: white;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.testimonial-video-title {
    font-weight: 600;
    font-size: 1rem;
    color: #1F2937;
    margin: 0 0 4px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.testimonial-video-meta {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    color: #6B7280;
}

.testimonial-video-meta i {
    margin-right: 5px;
    color: var(--primary-color, #F37321);
    opacity: 0.8;
}

/* Responsive */
@media (max-width: 768px) {
    .testimonial-videos-modal-content {
        width: 95%;
        max-height: 90vh;
    }
    
    .testimonial-videos-modal-header {
        padding: 1rem;
    }
    
    .testimonial-videos-modal-header h3 {
        font-size: 1.25rem;
    }
    
    .testimonial-videos-modal-body {
        padding: 1rem;
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 1rem;
    }
    
    .close-modal {
        width: 32px;
        height: 32px;
    }
}

@media (max-width: 480px) {
    .testimonial-videos-modal-content {
        width: 100%;
        max-height: 100vh;
        border-radius: 0;
    }
    
    .testimonial-videos-modal-body {
        grid-template-columns: 1fr; /* 1 cột duy nhất trên điện thoại */
    }
    
    .testimonial-video-item {
        margin-bottom: 1rem;
    }
    
    .testimonial-videos-modal-header h3::before {
        display: none; /* Ẩn biểu tượng trên mobile để tiết kiệm không gian */
    }
}
