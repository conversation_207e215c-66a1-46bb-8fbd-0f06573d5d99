/**
 * Mobile Header JavaScript for Nội Thất Bàng Vũ
 * Xử lý hiệu ứng cuộn và thay đổi logo cho header trên điện thoại
 */

document.addEventListener('DOMContentLoaded', function () {
  // <PERSON><PERSON>c phần tử DOM
  const mobileHeader = document.querySelector('.mobile-header');
  const mobileLogoImage = document.getElementById('mobile-logo');

  // Lưu đường dẫn logo ban đầu và logo trắng
  const originalLogoSrc = BASE_URL + '/assets/images/logo/logo.svg';
  const whiteLogoSrc = BASE_URL + '/assets/images/logo/logo-chu-trang.svg';

  // Biến theo dõi trạng thái cuộn
  let ticking = false;
  let lastScrollUpdate = 0;
  const scrollThreshold = 10;
  const throttleDelay = 10;

  // Thêm class để kích hoạt hiệu ứng transition mượt mà
  if (mobileHeader) {
    mobileHeader.classList.add('smooth-transition');
  }

  // Xử lý hiệu ứng cuộn mượt mà
  function handleScroll() {
    const now = Date.now();

    // Throttle: chỉ xử lý nếu đã qua đủ thời gian từ lần cập nhật trước
    if (now - lastScrollUpdate > throttleDelay) {
      lastScrollUpdate = now;

      if (!ticking) {
        window.requestAnimationFrame(function() {
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          const scrollProgress = Math.min(1, scrollTop / (scrollThreshold * 2));

          // Thay vì thêm/xóa class ngay lập tức, áp dụng style trực tiếp để có hiệu ứng mượt mà
          if (scrollTop > 0) {
            // Áp dụng hiệu ứng mờ dần khi cuộn xuống
            const opacity = Math.min(scrollProgress * 2, 1);
            const scale = 0.98 + (0.02 * opacity);
            const translateY = Math.min(scrollProgress * 2, 1) * -2;

            // Thêm class scrolled khi vượt qua ngưỡng
            if (scrollTop > scrollThreshold) {
              if (!mobileHeader.classList.contains('scrolled')) {
                mobileHeader.classList.add('scrolled');
                if (mobileLogoImage) {
                  mobileLogoImage.src = whiteLogoSrc;
                }
              }
            } else {
              if (mobileHeader.classList.contains('scrolled')) {
                mobileHeader.classList.remove('scrolled');
                if (mobileLogoImage) {
                  mobileLogoImage.src = originalLogoSrc;
                }
              }
            }

            // Áp dụng hiệu ứng transform mượt mà
            mobileHeader.style.setProperty('--header-opacity', opacity);
            mobileHeader.style.setProperty('--header-scale', scale);
            mobileHeader.style.setProperty('--header-translate-y', `${translateY}px`);
          } else {
            // Reset về trạng thái ban đầu khi ở đầu trang
            mobileHeader.classList.remove('scrolled');
            if (mobileLogoImage) {
              mobileLogoImage.src = originalLogoSrc;
            }
            mobileHeader.style.setProperty('--header-opacity', 0);
            mobileHeader.style.setProperty('--header-scale', 1);
            mobileHeader.style.setProperty('--header-translate-y', '0px');
          }

          ticking = false;
        });

        ticking = true;
      }
    }
  }

  // Kiểm tra trạng thái ban đầu khi trang tải
  const initialScrollTop = window.pageYOffset || document.documentElement.scrollTop;
  if (initialScrollTop > scrollThreshold) {
    mobileHeader.classList.add('scrolled');
    if (mobileLogoImage) {
      mobileLogoImage.src = whiteLogoSrc;
    }

    // Thiết lập các biến CSS cho hiệu ứng mượt mà
    const scrollProgress = Math.min(1, initialScrollTop / (scrollThreshold * 2));
    const opacity = Math.min(scrollProgress * 2, 1);
    const scale = 0.98 + (0.02 * opacity);
    const translateY = Math.min(scrollProgress * 2, 1) * -2;

    mobileHeader.style.setProperty('--header-opacity', opacity);
    mobileHeader.style.setProperty('--header-scale', scale);
    mobileHeader.style.setProperty('--header-translate-y', `${translateY}px`);
  }

  // Đăng ký sự kiện cuộn
  if (mobileHeader) {
    window.addEventListener('scroll', handleScroll, { passive: true });
  }

  // Xử lý active item trong bottom nav
  function setActiveNavItem() {
    const currentPath = window.location.pathname;
    const navItems = document.querySelectorAll('.mobile-nav-item');

    navItems.forEach(function (item) {
      const itemPath = item.getAttribute('href');
      if (itemPath && currentPath.includes(itemPath) && itemPath !== '/') {
        item.classList.add('active');
      } else if (itemPath === '/' && currentPath === '/') {
        item.classList.add('active');
      } else {
        item.classList.remove('active');
      }
    });
  }

  // Thiết lập active item khi tải trang
  setActiveNavItem();
});
