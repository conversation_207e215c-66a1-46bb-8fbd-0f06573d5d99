<?php
// Include init
require_once '../includes/init.php';

// Bắt đầu output buffering để tránh lỗi header
ob_start();

// Ghi log để debug
error_log('AJAX update_order_status.php được gọi với dữ liệu: ' . print_r($_POST, true));

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    // Trả về lỗi với HTTP status code 403 Forbidden
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'Bạn không có quyền thực hiện thao tác này'
    ]);
    ob_end_flush();
    exit;
}

// Kiểm tra request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    // Trả về lỗi với HTTP status code 405 Method Not Allowed
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '<PERSON><PERSON><PERSON><PERSON> thức không được hỗ trợ'
    ]);
    ob_end_flush();
    exit;
}

// Kiểm tra CSRF token
if (!isset($_POST['csrf_token']) || !check_csrf_token($_POST['csrf_token'])) {
    // Log lỗi để debug
    error_log('CSRF token không hợp lệ. Token nhận được: ' . (isset($_POST['csrf_token']) ? $_POST['csrf_token'] : 'không có'));
    error_log('CSRF token trong session: ' . (isset($_SESSION['csrf_token']) ? $_SESSION['csrf_token'] : 'không có'));

    // Trả về lỗi với HTTP status code 403 Forbidden
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'CSRF token không hợp lệ hoặc đã hết hạn'
    ]);
    ob_end_flush();
    exit;
}

// Lấy dữ liệu
$order_id = isset($_POST['order_id']) ? (int)$_POST['order_id'] : 0;
$status = isset($_POST['status']) ? $_POST['status'] : '';

// Kiểm tra dữ liệu
if ($order_id <= 0 || empty($status)) {
    // Trả về lỗi với HTTP status code 400 Bad Request
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Dữ liệu không hợp lệ'
    ]);
    ob_end_flush();
    exit;
}

// Kiểm tra trạng thái hợp lệ
$valid_statuses = ['pending', 'processing', 'shipping', 'completed', 'cancelled'];
if (!in_array($status, $valid_statuses)) {
    // Trả về lỗi với HTTP status code 400 Bad Request
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Trạng thái không hợp lệ'
    ]);
    ob_end_flush();
    exit;
}

try {
    // Cập nhật trạng thái đơn hàng
    $result = update_order_status($order_id, $status);

    // Nếu cập nhật thành công, lấy thông tin trạng thái mới
    if ($result['success']) {
        // Lấy thông tin trạng thái
        $status_info = get_order_status_info($status, 'admin');

        // Thêm thông tin trạng thái vào kết quả
        $result['status_text'] = $status_info['text'];
        $result['status_class'] = $status_info['class'];
        $result['status'] = $status;

        // Nếu trạng thái là 'completed', trả về thông tin sản phẩm đã bán
        if ($status === 'completed') {
            // Lấy thông tin đơn hàng
            $order = get_order_by_id($order_id);

            if ($order && isset($order['items']) && is_array($order['items'])) {
                // Lấy thông tin sản phẩm đã bán
                $products = [];
                foreach ($order['items'] as $item) {
                    $product = get_product_by_id($item['product_id']);
                    if ($product) {
                        $products[] = [
                            'id' => $product['id'],
                            'name' => $product['name'],
                            'sold' => $product['sold'],
                            'quantity' => $item['quantity']
                        ];
                    }
                }

                $result['products'] = $products;
            }
        }

        // Log thành công
        error_log("Cập nhật trạng thái đơn hàng thành công. ID: {$order_id}, Trạng thái: {$status}");
    } else {
        // Log lỗi
        error_log("Lỗi cập nhật trạng thái đơn hàng. ID: {$order_id}, Trạng thái: {$status}, Lỗi: {$result['message']}");
    }

    // Trả về kết quả dưới dạng JSON
    header('Content-Type: application/json');
    echo json_encode($result);
} catch (Exception $e) {
    // Log lỗi
    error_log("Exception khi cập nhật trạng thái đơn hàng: " . $e->getMessage());

    // Trả về lỗi với HTTP status code 500 Internal Server Error
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Lỗi máy chủ nội bộ: ' . $e->getMessage()
    ]);
} finally {
    // Kết thúc output buffering
    if (ob_get_level() > 0) {
        ob_end_flush();
    }
}
?>
