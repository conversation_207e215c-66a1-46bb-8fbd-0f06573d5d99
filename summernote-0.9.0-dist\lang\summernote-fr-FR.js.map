{"version": 3, "file": "lang/summernote-fr-FR.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,UAAU;QAClBC,SAAS,EAAE,UAAU;QACrBC,KAAK,EAAE,0BAA0B;QACjCC,MAAM,EAAE,YAAY;QACpBC,IAAI,EAAE,mBAAmB;QACzBC,aAAa,EAAE,OAAO;QACtBC,WAAW,EAAE,UAAU;QACvBC,SAAS,EAAE,QAAQ;QACnBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,mBAAmB;QAC3BC,UAAU,EAAE,kBAAkB;QAC9BC,UAAU,EAAE,uBAAuB;QACnCC,aAAa,EAAE,uBAAuB;QACtCC,SAAS,EAAE,iBAAiB;QAC5BC,UAAU,EAAE,iBAAiB;QAC7BC,SAAS,EAAE,mBAAmB;QAC9BC,YAAY,EAAE,0BAA0B;QACxCC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,iBAAiB;QACjCC,SAAS,EAAE,eAAe;QAC1BC,aAAa,EAAE,oDAAoD;QACnEC,SAAS,EAAE,6BAA6B;QACxCC,eAAe,EAAE,oBAAoB;QACrCC,eAAe,EAAE,4BAA4B;QAC7CC,oBAAoB,EAAE,qCAAqC;QAC3DC,GAAG,EAAE,iBAAiB;QACtBC,MAAM,EAAE,oBAAoB;QAC5BC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,YAAY;QACvBpB,MAAM,EAAE,mBAAmB;QAC3BgB,GAAG,EAAE,iBAAiB;QACtBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,iBAAiB;QACzBuB,MAAM,EAAE,mBAAmB;QAC3BC,IAAI,EAAE,UAAU;QAChBC,aAAa,EAAE,kBAAkB;QACjCT,GAAG,EAAE,aAAa;QAClBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,WAAW,EAAE,6BAA6B;QAC1CC,WAAW,EAAE,8BAA8B;QAC3CC,UAAU,EAAE,8BAA8B;QAC1CC,WAAW,EAAE,8BAA8B;QAC3CC,MAAM,EAAE,oBAAoB;QAC5BC,MAAM,EAAE,sBAAsB;QAC9BC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,QAAQ;QACXC,UAAU,EAAE,UAAU;QACtBC,GAAG,EAAE,aAAa;QAClBC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,eAAe;QAC1BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,MAAM;QACZC,UAAU,EAAE,aAAa;QACzBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE,qBAAqB;QAC9BC,MAAM,EAAE,sBAAsB;QAC9BC,IAAI,EAAE,kBAAkB;QACxBC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,+BAA+B;QACvCC,IAAI,EAAE,kBAAkB;QACxBC,UAAU,EAAE,iBAAiB;QAC7BC,UAAU,EAAE,mBAAmB;QAC/BC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,yBAAyB;QACzCC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,YAAY;QACvBC,KAAK,EAAE,QAAQ;QACfC,cAAc,EAAE,wBAAwB;QACxCC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,+BAA+B;QACpDC,aAAa,EAAE,mBAAmB;QAClCC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,oBAAoB;QACvC,MAAM,EAAE,8BAA8B;QACtC,MAAM,EAAE,8BAA8B;QACtC,KAAK,EAAE,YAAY;QACnB,OAAO,EAAE,oBAAoB;QAC7B,MAAM,EAAE,0BAA0B;QAClC,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,oBAAoB;QACjC,eAAe,EAAE,uBAAuB;QACxC,cAAc,EAAE,qBAAqB;QACrC,aAAa,EAAE,kBAAkB;QACjC,eAAe,EAAE,SAAS;QAC1B,cAAc,EAAE,kBAAkB;QAClC,aAAa,EAAE,gCAAgC;QAC/C,qBAAqB,EAAE,wBAAwB;QAC/C,mBAAmB,EAAE,yBAAyB;QAC9C,SAAS,EAAE,mCAAmC;QAC9C,QAAQ,EAAE,oCAAoC;QAC9C,YAAY,EAAE,8CAA8C;QAC5D,UAAU,EAAE,6CAA6C;QACzD,UAAU,EAAE,6CAA6C;QACzD,UAAU,EAAE,6CAA6C;QACzD,UAAU,EAAE,6CAA6C;QACzD,UAAU,EAAE,6CAA6C;QACzD,UAAU,EAAE,6CAA6C;QACzD,sBAAsB,EAAE,gCAAgC;QACxD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,4BAA4B;QAClCC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,qBAAqB;QAClCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-fr-FR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'fr-FR': {\n      font: {\n        bold: 'Gras',\n        italic: 'Italique',\n        underline: '<PERSON><PERSON><PERSON>',\n        clear: 'Effacer la mise en forme',\n        height: '<PERSON><PERSON><PERSON>',\n        name: '<PERSON><PERSON><PERSON> de police',\n        strikethrough: '<PERSON><PERSON>',\n        superscript: 'Exposant',\n        subscript: 'Indice',\n        size: 'Taille de police',\n      },\n      image: {\n        image: 'Image',\n        insert: 'Insérer une image',\n        resizeFull: 'Taille originale',\n        resizeHalf: 'Redimensionner à 50 %',\n        resizeQuarter: 'Redimensionner à 25 %',\n        floatLeft: 'Aligné à gauche',\n        floatRight: 'Aligné à droite',\n        floatNone: 'Pas d\\'alignement',\n        shapeRounded: 'Forme: Rectangle arrondi',\n        shapeCircle: 'Forme: Cercle',\n        shapeThumbnail: 'Forme: Vignette',\n        shapeNone: 'Forme: Aucune',\n        dragImageHere: 'Faites glisser une image ou un texte dans ce cadre',\n        dropImage: '<PERSON><PERSON><PERSON> l\\'image ou le texte',\n        selectFromFiles: 'Choisir un fichier',\n        maximumFileSize: 'Taille de fichier maximale',\n        maximumFileSizeError: 'Taille maximale du fichier dépassée',\n        url: 'URL de l\\'image',\n        remove: 'Supprimer l\\'image',\n        original: 'Original',\n      },\n      video: {\n        video: 'Vidéo',\n        videoLink: 'Lien vidéo',\n        insert: 'Insérer une vidéo',\n        url: 'URL de la vidéo',\n        providers: '(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'Lien',\n        insert: 'Insérer un lien',\n        unlink: 'Supprimer un lien',\n        edit: 'Modifier',\n        textToDisplay: 'Texte à afficher',\n        url: 'URL du lien',\n        openInNewWindow: 'Ouvrir dans une nouvelle fenêtre',\n      },\n      table: {\n        table: 'Tableau',\n        addRowAbove: 'Ajouter une ligne au-dessus',\n        addRowBelow: 'Ajouter une ligne en dessous',\n        addColLeft: 'Ajouter une colonne à gauche',\n        addColRight: 'Ajouter une colonne à droite',\n        delRow: 'Supprimer la ligne',\n        delCol: 'Supprimer la colonne',\n        delTable: 'Supprimer le tableau',\n      },\n      hr: {\n        insert: 'Insérer une ligne horizontale',\n      },\n      style: {\n        style: 'Style',\n        p: 'Normal',\n        blockquote: 'Citation',\n        pre: 'Code source',\n        h1: 'Titre 1',\n        h2: 'Titre 2',\n        h3: 'Titre 3',\n        h4: 'Titre 4',\n        h5: 'Titre 5',\n        h6: 'Titre 6',\n      },\n      lists: {\n        unordered: 'Liste à puces',\n        ordered: 'Liste numérotée',\n      },\n      options: {\n        help: 'Aide',\n        fullscreen: 'Plein écran',\n        codeview: 'Afficher le code HTML',\n      },\n      paragraph: {\n        paragraph: 'Paragraphe',\n        outdent: 'Diminuer le retrait',\n        indent: 'Augmenter le retrait',\n        left: 'Aligner à gauche',\n        center: 'Centrer',\n        right: 'Aligner à droite',\n        justify: 'Justifier',\n      },\n      color: {\n        recent: 'Dernière couleur sélectionnée',\n        more: 'Plus de couleurs',\n        background: 'Couleur de fond',\n        foreground: 'Couleur de police',\n        transparent: 'Transparent',\n        setTransparent: 'Définir la transparence',\n        reset: 'Restaurer',\n        resetToDefault: 'Restaurer la couleur par défaut',\n      },\n      shortcut: {\n        shortcuts: 'Raccourcis',\n        close: 'Fermer',\n        textFormatting: 'Mise en forme du texte',\n        action: 'Action',\n        paragraphFormatting: 'Mise en forme des paragraphes',\n        documentStyle: 'Style du document',\n        extraKeys: 'Touches supplémentaires',\n      },\n      help: {\n        'insertParagraph': 'Insérer paragraphe',\n        'undo': 'Défaire la dernière commande',\n        'redo': 'Refaire la dernière commande',\n        'tab': 'Tabulation',\n        'untab': 'Tabulation arrière',\n        'bold': 'Mettre en caractère gras',\n        'italic': 'Mettre en italique',\n        'underline': 'Mettre en souligné',\n        'strikethrough': 'Mettre en texte barré',\n        'removeFormat': 'Nettoyer les styles',\n        'justifyLeft': 'Aligner à gauche',\n        'justifyCenter': 'Centrer',\n        'justifyRight': 'Aligner à droite',\n        'justifyFull': 'Justifier à gauche et à droite',\n        'insertUnorderedList': 'Basculer liste à puces',\n        'insertOrderedList': 'Basculer liste ordonnée',\n        'outdent': 'Diminuer le retrait du paragraphe',\n        'indent': 'Augmenter le retrait du paragraphe',\n        'formatPara': 'Changer le paragraphe en cours en normal (P)',\n        'formatH1': 'Changer le paragraphe en cours en entête H1',\n        'formatH2': 'Changer le paragraphe en cours en entête H2',\n        'formatH3': 'Changer le paragraphe en cours en entête H3',\n        'formatH4': 'Changer le paragraphe en cours en entête H4',\n        'formatH5': 'Changer le paragraphe en cours en entête H5',\n        'formatH6': 'Changer le paragraphe en cours en entête H6',\n        'insertHorizontalRule': 'Insérer séparation horizontale',\n        'linkDialog.show': 'Afficher fenêtre d\\'hyperlien',\n      },\n      history: {\n        undo: 'Annuler la dernière action',\n        redo: 'Restaurer la dernière action annulée',\n      },\n      specialChar: {\n        specialChar: 'Caractères spéciaux',\n        select: 'Choisir des caractères spéciaux',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "superscript", "subscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}