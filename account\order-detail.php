<?php
// Bắt đầu session và include các file cần thiết
require_once '../includes/init.php';

// Xử lý hủy đơn hàng trước khi xuất bất kỳ HTML nào
if (isset($_GET['action']) && $_GET['action'] === 'cancel' && isset($_GET['id'])) {
    $order_id = (int)$_GET['id'];

    // Kiểm tra đăng nhập
    if (!is_logged_in()) {
        set_flash_message('error', 'Vui lòng đăng nhập để truy cập trang này.');
        redirect(BASE_URL . '/login.php');
    }

    // Kiểm tra đơn hàng thuộc về người dùng
    $order = get_order_by_id($order_id);

    if ($order && $order['user_id'] == $_SESSION['user_id']) {
        $result = cancel_order($order_id);

        if ($result['success']) {
            set_flash_message('success', $result['message']);
        } else {
            set_flash_message('error', $result['message']);
        }
    } else {
        set_flash_message('error', 'Đơn hàng không tồn tại hoặc không thuộc về bạn.');
    }

    redirect(BASE_URL . '/account/order-detail.php?id=' . $order_id);
}

// Kiểm tra đăng nhập
if (!is_logged_in()) {
    // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
    $_SESSION['redirect_url'] = current_url();

    set_flash_message('error', 'Vui lòng đăng nhập để truy cập trang này.');
    redirect(BASE_URL . '/login.php');
}

// Lấy ID đơn hàng từ URL
$order_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Nếu không có ID đơn hàng, chuyển hướng về trang đơn hàng
if ($order_id <= 0) {
    redirect(BASE_URL . '/account/orders.php');
}

// Lấy thông tin đơn hàng
$order = get_order_by_id($order_id);

// Nếu không tìm thấy đơn hàng hoặc đơn hàng không thuộc về người dùng, chuyển hướng về trang đơn hàng
if (!$order || $order['user_id'] != $_SESSION['user_id']) {
    set_flash_message('error', 'Đơn hàng không tồn tại hoặc không thuộc về bạn.');
    redirect(BASE_URL . '/account/orders.php');
}

// Lấy thông tin người dùng
$user = get_user_by_id($_SESSION['user_id']);

// Thiết lập tiêu đề trang
$page_title = 'Chi tiết đơn hàng #' . $order_id;
$page_description = 'Chi tiết đơn hàng #' . $order_id . ' tại Nội Thất Băng Vũ';

// Include header
include_once '../partials/header.php';

// Thêm CSRF token cho AJAX
$csrf_token = generate_csrf_token();

// Thêm class logged-in vào body để JavaScript có thể kiểm tra
echo '<script>document.body.classList.add("logged-in");</script>';
?>

<!-- CSRF Token cho AJAX -->
<meta name="csrf-token" content="<?php echo $csrf_token; ?>">
<meta name="base-url" content="<?php echo BASE_URL; ?>">

<!-- Breadcrumb - Thiết kế hiện đại -->
<div class="bg-gradient-to-r from-gray-50 to-gray-100 py-4 shadow-sm">
    <div class="container mx-auto px-4">
        <div class="flex items-center text-sm text-gray-600">
            <a href="<?php echo BASE_URL; ?>" class="hover:text-primary transition-colors duration-300">
                <i class="fas fa-home mr-1"></i> Trang chủ
            </a>
            <span class="mx-2 text-gray-400">/</span>
            <a href="<?php echo BASE_URL; ?>/account/profile.php" class="hover:text-primary transition-colors duration-300">
                Tài khoản của tôi
            </a>
            <span class="mx-2 text-gray-400">/</span>
            <a href="<?php echo BASE_URL; ?>/account/orders.php" class="hover:text-primary transition-colors duration-300">
                Đơn hàng của tôi
            </a>
            <span class="mx-2 text-gray-400">/</span>
            <span class="text-primary font-medium">Chi tiết đơn hàng #<?php echo $order_id; ?></span>
        </div>
    </div>
</div>

<!-- Account - Thiết kế hiện đại -->
<div class="py-10 bg-gradient-to-b from-white to-gray-50">
    <div class="container mx-auto px-4">
        <!-- Tiêu đề trang với badge giống trang chủ -->
        <div class="mb-8 text-center md:text-left">
            <div class="section-title-badge inline-flex items-center bg-primary/10 text-primary text-sm font-medium px-4 py-2 rounded-full mb-4">
                <span class="flex w-2 h-2 bg-primary rounded-full mr-2 animate-pulse"></span>
                Chi tiết đơn hàng
            </div>
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 relative inline-block">
                <span class="relative z-10">Đơn hàng #<?php echo $order_id; ?></span>
                <span class="absolute bottom-0 left-0 w-full h-1 bg-primary/30 rounded-full"></span>
            </h1>
            <p class="text-gray-600 mt-2 max-w-2xl mx-auto md:mx-0">Xem chi tiết và theo dõi trạng thái đơn hàng của bạn</p>
        </div>

        <div class="flex flex-col md:flex-row md:space-x-6">
            <!-- Sidebar - Thiết kế hiện đại -->
            <div class="md:w-1/4 mb-6 md:mb-0">
                <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-primary/20 transition-all duration-300">
                    <!-- Phần header với avatar và thông tin - Thiết kế mới -->
                    <div class="relative">
                        <!-- Background pattern -->
                        <div class="absolute inset-0 bg-gradient-to-br from-primary to-primary-dark opacity-90"></div>

                        <!-- Pattern overlay -->
                        <div class="absolute inset-0 opacity-10 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xIj48cGF0aCBkPSJNMzYgMzRjMC0yLjIgMS44LTQgNC00czQgMS44IDQgNC0xLjggNC00IDQtNC0xLjgtNC00eiIvPjxwYXRoIGQ9Ik0xNiAxNmMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00em0xNiAwYzIuMiAwIDQgMS44IDQgNHMtMS44IDQtNCA0LTQtMS44LTQtNCAxLjgtNCA0LTR6bTE2IDBjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHpNMTYgMzJjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHptMTYgMGMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00eiIvPjxwYXRoIGQ9Ik0xNiA0OGMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00em0xNiAwYzIuMiAwIDQgMS44IDQgNHMtMS44IDQtNCA0LTQtMS44LTQtNCAxLjgtNCA0LTR6bTE2IDBjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHpNMTYgMTZjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHptMTYgMGMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00em0xNiAwYzIuMiAwIDQgMS44IDQgNHMtMS44IDQtNCA0LTQtMS44LTQtNCAxLjgtNCA0LTR6TTAgMGg2MHY2MEgweiIvPjwvZz48L2c+PC9zdmc+')]"></div>
                    </div>

                    <!-- Avatar lớn căn giữa -->
                    <div class="flex flex-col items-center px-6 pt-8 pb-6 relative">
                        <div class="w-40 h-40 rounded-xl bg-white p-1.5 shadow-lg mb-5 transform transition-transform hover:scale-105 duration-300 overflow-hidden relative z-10">
                            <div class="w-full h-full rounded-xl overflow-hidden bg-gradient-to-r from-primary/20 to-primary/10 flex items-center justify-center text-4xl font-bold text-primary">
                                <?php if (!empty($user['avatar'])): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $user['avatar']; ?>" alt="Avatar" class="w-full h-full object-cover">
                                <?php else: ?>
                                    <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Thông tin người dùng -->
                        <div class="text-center w-full">
                            <h2 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $user['full_name']; ?></h2>
                            <p class="text-gray-500 text-sm flex items-center justify-center mb-3">
                                <i class="fas fa-envelope mr-2 text-primary/70"></i>
                                <?php echo $user['email']; ?>
                            </p>

                            <!-- Badge thành viên -->
                            <div class="inline-flex items-center bg-primary/10 text-primary text-xs font-medium px-3 py-1.5 rounded-full">
                                <i class="fas fa-user-check mr-1.5"></i> Thành viên
                            </div>
                        </div>
                    </div>

                    <!-- Đường phân cách -->
                    <div class="w-full h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>

                    <!-- Menu tài khoản -->
                    <div class="p-4">
                        <ul class="space-y-1">
                            <li>
                                <a href="<?php echo BASE_URL; ?>/account/profile.php"
                                   class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-primary transition-all duration-300">
                                    <i class="fas fa-user-circle mr-3 w-5 text-center"></i>
                                    <span>Thông tin tài khoản</span>
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/account/orders.php"
                                   class="flex items-center px-4 py-3 rounded-lg bg-primary/10 text-primary font-medium transition-all duration-300">
                                    <i class="fas fa-shopping-bag mr-3 w-5 text-center"></i>
                                    <span>Đơn hàng của tôi</span>
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/account/change-password.php"
                                   class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-primary transition-all duration-300">
                                    <i class="fas fa-key mr-3 w-5 text-center"></i>
                                    <span>Đổi mật khẩu</span>
                                </a>
                            </li>
                            <li class="pt-2 mt-2 border-t border-gray-100">
                                <a href="<?php echo BASE_URL; ?>/logout.php"
                                   class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-red-50 hover:text-red-600 transition-all duration-300">
                                    <i class="fas fa-sign-out-alt mr-3 w-5 text-center"></i>
                                    <span>Đăng xuất</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="md:w-3/4">
                <!-- Phần thông tin đơn hàng -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 hover:border-primary/20 transition-all duration-300 overflow-hidden mb-6">
                    <!-- Header với gradient và nút thao tác -->
                    <div class="bg-gradient-to-r from-primary/5 to-primary/10 px-6 py-5 border-b border-gray-100">
                        <div class="flex flex-col md:flex-row md:justify-between md:items-center">
                            <div class="flex items-center mb-4 md:mb-0">
                                <span class="w-12 h-12 rounded-lg bg-white flex items-center justify-center text-primary shadow-sm mr-4">
                                    <i class="fas fa-info-circle text-xl"></i>
                                </span>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-800">Thông tin đơn hàng</h2>
                                    <p class="text-gray-500 text-sm">Chi tiết về đơn hàng và trạng thái hiện tại</p>
                                </div>
                            </div>

                            <div class="flex flex-wrap gap-3">
                                <a href="<?php echo BASE_URL; ?>/account/orders.php"
                                   class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-all duration-300 border border-gray-200">
                                    <i class="fas fa-arrow-left mr-2"></i> Quay lại
                                </a>
                                <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                                <a href="<?php echo BASE_URL; ?>/account/order-detail.php?id=<?php echo $order_id; ?>&action=cancel"
                                   class="inline-flex items-center px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
                                   onclick="return confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')">
                                    <i class="fas fa-times mr-2"></i> Hủy đơn hàng
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Nội dung thông tin đơn hàng -->
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <!-- Thông tin đơn hàng -->
                            <div class="bg-gradient-to-br from-gray-50 to-gray-100 p-5 rounded-xl border border-gray-200 hover:border-primary/20 transition-all duration-300 shadow-sm">
                                <div class="flex items-center mb-4">
                                    <span class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                                        <i class="fas fa-shopping-cart"></i>
                                    </span>
                                    <h3 class="text-lg font-bold text-gray-800">Thông tin đơn hàng</h3>
                                </div>

                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <div class="w-32 text-gray-500 text-sm">Mã đơn hàng:</div>
                                        <div class="font-medium text-gray-800">#<?php echo $order['id']; ?></div>
                                    </div>

                                    <div class="flex items-center">
                                        <div class="w-32 text-gray-500 text-sm">Ngày đặt hàng:</div>
                                        <div class="font-medium text-gray-800 flex items-center">
                                            <i class="far fa-calendar-alt text-primary/70 mr-2"></i>
                                            <?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?>
                                        </div>
                                    </div>

                                    <div class="flex items-center">
                                        <div class="w-32 text-gray-500 text-sm">Tổng tiền:</div>
                                        <div class="font-medium text-primary"><?php echo format_currency($order['total']); ?></div>
                                    </div>

                                    <div class="flex items-center">
                                        <div class="w-32 text-gray-500 text-sm">Trạng thái:</div>
                                        <div>
                                            <?php
                                            $status_info = get_order_status_info($order['status'], 'user');

                                            // Thay đổi class để phù hợp với thiết kế mới
                                            $status_classes = [
                                                'pending' => 'bg-yellow-100 text-yellow-800 border border-yellow-200',
                                                'processing' => 'bg-blue-100 text-blue-800 border border-blue-200',
                                                'shipping' => 'bg-indigo-100 text-indigo-800 border border-indigo-200',
                                                'completed' => 'bg-green-100 text-green-800 border border-green-200',
                                                'cancelled' => 'bg-red-100 text-red-800 border border-red-200',
                                            ];

                                            $status_class = isset($status_classes[$order['status']]) ? $status_classes[$order['status']] : 'bg-gray-100 text-gray-800 border border-gray-200';
                                            ?>
                                            <span class="px-3 py-1.5 inline-flex items-center text-xs font-medium rounded-full <?php echo $status_class; ?> order-detail-status" data-order-id="<?php echo $order['id']; ?>">
                                                <?php echo $status_info['text']; ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Thông tin giao hàng -->
                            <div class="bg-gradient-to-br from-gray-50 to-gray-100 p-5 rounded-xl border border-gray-200 hover:border-primary/20 transition-all duration-300 shadow-sm">
                                <div class="flex items-center mb-4">
                                    <span class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                                        <i class="fas fa-shipping-fast"></i>
                                    </span>
                                    <h3 class="text-lg font-bold text-gray-800">Thông tin giao hàng</h3>
                                </div>

                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <div class="w-32 text-gray-500 text-sm">Họ và tên:</div>
                                        <div class="font-medium text-gray-800"><?php echo $order['full_name']; ?></div>
                                    </div>

                                    <div class="flex items-center">
                                        <div class="w-32 text-gray-500 text-sm">Email:</div>
                                        <div class="font-medium text-gray-800 flex items-center">
                                            <i class="far fa-envelope text-primary/70 mr-2"></i>
                                            <?php echo $order['email']; ?>
                                        </div>
                                    </div>

                                    <div class="flex items-center">
                                        <div class="w-32 text-gray-500 text-sm">Số điện thoại:</div>
                                        <div class="font-medium text-gray-800 flex items-center">
                                            <i class="fas fa-phone-alt text-primary/70 mr-2"></i>
                                            <?php echo $order['phone']; ?>
                                        </div>
                                    </div>

                                    <div class="flex">
                                        <div class="w-32 text-gray-500 text-sm">Địa chỉ:</div>
                                        <div class="font-medium text-gray-800"><?php echo $order['address']; ?></div>
                                    </div>

                                    <?php if ($order['note']): ?>
                                    <div class="flex">
                                        <div class="w-32 text-gray-500 text-sm">Ghi chú:</div>
                                        <div class="font-medium text-gray-800 italic"><?php echo $order['note']; ?></div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                    <!-- Chi tiết đơn hàng - Tiêu đề -->
                    <div class="mt-6 mb-4">
                        <div class="flex items-center">
                            <span class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                                <i class="fas fa-list-alt"></i>
                            </span>
                            <h3 class="text-lg font-bold text-gray-800">Chi tiết đơn hàng</h3>
                        </div>
                    </div>

                    <!-- Bảng chi tiết đơn hàng -->
                    <div class="overflow-x-auto bg-white rounded-xl border border-gray-200 shadow-sm">
                        <table class="w-full">
                            <thead>
                                <tr class="bg-gray-50 border-b border-gray-200">
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Sản phẩm</th>
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Giá</th>
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Số lượng</th>
                                    <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Tổng</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-100">
                                <?php foreach ($order['items'] as $item): ?>
                                <tr class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-4 py-4">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-16 w-16 rounded-lg overflow-hidden border border-gray-200 bg-white">
                                                <?php if ($item['product_image']): ?>
                                                <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $item['product_image']; ?>" alt="<?php echo $item['product_name']; ?>" class="h-full w-full object-cover">
                                                <?php else: ?>
                                                <div class="h-full w-full bg-gray-100 flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400 text-xl"></i>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900 hover:text-primary transition-colors duration-200"><?php echo $item['product_name']; ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-4">
                                        <div class="text-sm text-gray-900"><?php echo format_currency($item['price']); ?></div>
                                    </td>
                                    <td class="px-4 py-4">
                                        <div class="inline-flex items-center justify-center px-3 py-1 bg-gray-100 text-gray-800 rounded-lg">
                                            <span class="text-sm font-medium"><?php echo $item['quantity']; ?></span>
                                        </div>
                                    </td>
                                    <td class="px-4 py-4">
                                        <div class="text-sm font-medium text-primary"><?php echo format_currency($item['price'] * $item['quantity']); ?></div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Tổng cộng -->
                    <div class="mt-6 bg-gray-50 rounded-xl border border-gray-200 p-4 shadow-sm">
                        <div class="flex flex-col space-y-3">
                            <div class="flex justify-between items-center pb-3 border-b border-gray-200">
                                <span class="text-gray-600">Tạm tính:</span>
                                <span class="font-medium text-gray-800"><?php echo format_currency($order['total']); ?></span>
                            </div>
                            <div class="flex justify-between items-center pb-3 border-b border-gray-200">
                                <span class="text-gray-600">Phí vận chuyển:</span>
                                <span class="font-medium text-green-600">Miễn phí</span>
                            </div>
                            <div class="flex justify-between items-center pt-2">
                                <span class="text-lg font-bold text-gray-800">Tổng cộng:</span>
                                <span class="text-lg font-bold text-primary"><?php echo format_currency($order['total']); ?></span>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>

                <!-- Phần trạng thái đơn hàng -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 hover:border-primary/20 transition-all duration-300 overflow-hidden">
                    <!-- Header với gradient -->
                    <div class="bg-gradient-to-r from-primary/5 to-primary/10 px-6 py-5 border-b border-gray-100">
                        <div class="flex items-center">
                            <span class="w-12 h-12 rounded-lg bg-white flex items-center justify-center text-primary shadow-sm mr-4">
                                <i class="fas fa-truck text-xl"></i>
                            </span>
                            <div>
                                <h2 class="text-xl font-bold text-gray-800">Trạng thái đơn hàng</h2>
                                <p class="text-gray-500 text-sm">Theo dõi tiến trình xử lý đơn hàng của bạn</p>
                            </div>
                        </div>
                    </div>

                    <!-- Nội dung trạng thái đơn hàng -->
                    <div class="p-6">
                        <div class="relative order-progress" data-order-id="<?php echo $order['id']; ?>">
                            <!-- Đường kẻ dọc kết nối các bước -->
                            <div class="absolute left-6 top-0 h-full w-1 bg-gradient-to-b from-gray-200 via-gray-200 to-gray-200 rounded-full"></div>

                            <!-- Bước 1: Đơn hàng đã đặt -->
                            <div class="relative flex items-start mb-8 progress-step completed">
                                <div class="flex items-center h-12 w-12 rounded-full bg-primary text-white justify-center z-10 shadow-md">
                                    <i class="fas fa-check text-lg"></i>
                                </div>
                                <div class="ml-6">
                                    <h4 class="text-lg font-bold text-gray-800 mb-1">Đơn hàng đã đặt</h4>
                                    <p class="text-gray-600 flex items-center">
                                        <i class="far fa-calendar-alt text-primary/70 mr-2"></i>
                                        <?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?>
                                    </p>
                                    <div class="mt-2 bg-green-50 text-green-700 text-sm px-3 py-1 rounded-full inline-flex items-center">
                                        <i class="fas fa-check-circle mr-1.5"></i> Hoàn thành
                                    </div>
                                </div>
                            </div>

                            <!-- Bước 2: Đơn hàng đang xử lý -->
                            <div class="relative flex items-start mb-8 progress-step <?php echo in_array($order['status'], ['pending', 'processing', 'shipping', 'completed']) ? 'completed' : ''; ?>">
                                <div class="flex items-center h-12 w-12 rounded-full <?php echo in_array($order['status'], ['processing', 'shipping', 'completed']) ? 'bg-primary text-white shadow-md' : 'bg-gray-200 text-gray-500 border border-gray-300'; ?> justify-center z-10">
                                    <?php if (in_array($order['status'], ['processing', 'shipping', 'completed'])): ?>
                                    <i class="fas fa-check text-lg"></i>
                                    <?php else: ?>
                                    <i class="fas fa-cog text-lg"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-6">
                                    <h4 class="text-lg font-bold text-gray-800 mb-1">Đơn hàng đang xử lý</h4>
                                    <p class="text-gray-600">
                                        <?php if (in_array($order['status'], ['processing', 'shipping', 'completed'])): ?>
                                        Đơn hàng của bạn đã được xác nhận và đang được chuẩn bị
                                        <?php else: ?>
                                        Đơn hàng của bạn đang chờ xác nhận
                                        <?php endif; ?>
                                    </p>
                                    <?php if (in_array($order['status'], ['processing', 'shipping', 'completed'])): ?>
                                    <div class="mt-2 bg-green-50 text-green-700 text-sm px-3 py-1 rounded-full inline-flex items-center">
                                        <i class="fas fa-check-circle mr-1.5"></i> Hoàn thành
                                    </div>
                                    <?php elseif ($order['status'] === 'pending'): ?>
                                    <div class="mt-2 bg-yellow-50 text-yellow-700 text-sm px-3 py-1 rounded-full inline-flex items-center">
                                        <i class="fas fa-clock mr-1.5"></i> Đang xử lý
                                    </div>
                                    <?php elseif ($order['status'] === 'cancelled'): ?>
                                    <div class="mt-2 bg-red-50 text-red-700 text-sm px-3 py-1 rounded-full inline-flex items-center">
                                        <i class="fas fa-times-circle mr-1.5"></i> Đã hủy
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Bước 3: Đơn hàng đang giao -->
                            <div class="relative flex items-start mb-8 progress-step <?php echo in_array($order['status'], ['shipping', 'completed']) ? 'completed' : (in_array($order['status'], ['processing']) ? 'active' : ''); ?>">
                                <div class="flex items-center h-12 w-12 rounded-full <?php echo in_array($order['status'], ['shipping', 'completed']) ? 'bg-primary text-white shadow-md' : 'bg-gray-200 text-gray-500 border border-gray-300'; ?> justify-center z-10">
                                    <?php if (in_array($order['status'], ['shipping', 'completed'])): ?>
                                    <i class="fas fa-check text-lg"></i>
                                    <?php else: ?>
                                    <i class="fas fa-shipping-fast text-lg"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-6">
                                    <h4 class="text-lg font-bold text-gray-800 mb-1">Đơn hàng đang giao</h4>
                                    <p class="text-gray-600">
                                        <?php if (in_array($order['status'], ['shipping', 'completed'])): ?>
                                        Đơn hàng đang được giao đến địa chỉ của bạn
                                        <?php else: ?>
                                        Đơn hàng sẽ được giao sau khi xử lý
                                        <?php endif; ?>
                                    </p>
                                    <?php if (in_array($order['status'], ['shipping', 'completed'])): ?>
                                    <div class="mt-2 bg-green-50 text-green-700 text-sm px-3 py-1 rounded-full inline-flex items-center">
                                        <i class="fas fa-check-circle mr-1.5"></i> Hoàn thành
                                    </div>
                                    <?php elseif ($order['status'] === 'processing'): ?>
                                    <div class="mt-2 bg-blue-50 text-blue-700 text-sm px-3 py-1 rounded-full inline-flex items-center">
                                        <i class="fas fa-clock mr-1.5"></i> Sắp tới
                                    </div>
                                    <?php elseif ($order['status'] === 'cancelled'): ?>
                                    <div class="mt-2 bg-red-50 text-red-700 text-sm px-3 py-1 rounded-full inline-flex items-center">
                                        <i class="fas fa-times-circle mr-1.5"></i> Đã hủy
                                    </div>
                                    <?php else: ?>
                                    <div class="mt-2 bg-gray-100 text-gray-600 text-sm px-3 py-1 rounded-full inline-flex items-center">
                                        <i class="fas fa-clock mr-1.5"></i> Chờ xử lý
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Bước 4: Đơn hàng hoàn thành hoặc đã hủy -->
                            <div class="relative flex items-start progress-step <?php echo $order['status'] === 'completed' ? 'completed' : ($order['status'] === 'cancelled' ? 'completed' : (in_array($order['status'], ['shipping']) ? 'active' : '')); ?>">
                                <div class="flex items-center h-12 w-12 rounded-full <?php echo $order['status'] === 'completed' ? 'bg-primary text-white shadow-md' : ($order['status'] === 'cancelled' ? 'bg-red-500 text-white shadow-md' : 'bg-gray-200 text-gray-500 border border-gray-300'); ?> justify-center z-10">
                                    <?php if ($order['status'] === 'completed'): ?>
                                    <i class="fas fa-check text-lg"></i>
                                    <?php elseif ($order['status'] === 'cancelled'): ?>
                                    <i class="fas fa-times text-lg"></i>
                                    <?php else: ?>
                                    <i class="fas fa-flag-checkered text-lg"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-6">
                                    <h4 class="text-lg font-bold text-gray-800 mb-1">
                                        <?php if ($order['status'] === 'cancelled'): ?>
                                        Đơn hàng đã hủy
                                        <?php else: ?>
                                        Đơn hàng hoàn thành
                                        <?php endif; ?>
                                    </h4>
                                    <p class="text-gray-600">
                                        <?php if ($order['status'] === 'completed'): ?>
                                        Đơn hàng đã được giao thành công
                                        <?php elseif ($order['status'] === 'cancelled'): ?>
                                        Đơn hàng đã bị hủy
                                        <?php elseif ($order['status'] === 'shipping'): ?>
                                        Đơn hàng sẽ hoàn thành sau khi giao hàng
                                        <?php else: ?>
                                        Đơn hàng sẽ hoàn thành sau khi giao hàng
                                        <?php endif; ?>
                                    </p>
                                    <?php if ($order['status'] === 'completed'): ?>
                                    <div class="mt-2 bg-green-50 text-green-700 text-sm px-3 py-1 rounded-full inline-flex items-center">
                                        <i class="fas fa-check-circle mr-1.5"></i> Hoàn thành
                                    </div>
                                    <?php elseif ($order['status'] === 'cancelled'): ?>
                                    <div class="mt-2 bg-red-50 text-red-700 text-sm px-3 py-1 rounded-full inline-flex items-center">
                                        <i class="fas fa-times-circle mr-1.5"></i> Đã hủy
                                    </div>
                                    <?php elseif ($order['status'] === 'shipping'): ?>
                                    <div class="mt-2 bg-blue-50 text-blue-700 text-sm px-3 py-1 rounded-full inline-flex items-center">
                                        <i class="fas fa-clock mr-1.5"></i> Sắp tới
                                    </div>
                                    <?php else: ?>
                                    <div class="mt-2 bg-gray-100 text-gray-600 text-sm px-3 py-1 rounded-full inline-flex items-center">
                                        <i class="fas fa-clock mr-1.5"></i> Chờ xử lý
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSS tùy chỉnh cho trang chi tiết đơn hàng -->
<style>
    /* Biến CSS cho màu sắc chính */
    :root {
        --primary: #F37321;
        --primary-dark: #E05E00;
        --primary-light: #FF9D5C;
    }

    /* Hiệu ứng hover cho các card */
    .bg-white.rounded-xl {
        transition: all 0.3s ease;
    }

    .bg-white.rounded-xl:hover {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Hiệu ứng cho các bước trạng thái đơn hàng */
    .order-progress .progress-step {
        transition: all 0.5s ease;
    }

    .order-progress .progress-step:hover {
        transform: translateX(5px);
    }

    /* Hiệu ứng cho đường kẻ dọc */
    .order-progress .absolute.left-6 {
        background: linear-gradient(to bottom, var(--primary) 0%, var(--primary) 25%, var(--primary-light) 50%, #e5e7eb 75%, #e5e7eb 100%);
    }

    /* Hiệu ứng cho các nút */
    .inline-flex.items-center.px-4.py-2 {
        transition: all 0.3s ease;
    }

    .inline-flex.items-center.px-4.py-2:hover {
        transform: translateY(-2px);
    }

    .inline-flex.items-center.px-4.py-2:active {
        transform: translateY(0);
    }

    /* Hiệu ứng cho bảng */
    .overflow-x-auto table tr {
        transition: background-color 0.2s ease;
    }

    .overflow-x-auto table tr:hover {
        background-color: rgba(243, 115, 33, 0.03);
    }

    /* Hiệu ứng cho hình ảnh sản phẩm */
    .h-16.w-16.rounded-lg {
        transition: all 0.3s ease;
    }

    .h-16.w-16.rounded-lg:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    /* Responsive cho màn hình nhỏ */
    @media (max-width: 768px) {
        .section-title-badge {
            justify-content: center;
            margin-left: auto;
            margin-right: auto;
        }

        .section-title {
            text-align: center;
        }

        .flex.flex-col.md\:flex-row.md\:justify-between.md\:items-center {
            text-align: center;
        }

        .flex.flex-wrap.gap-3 {
            justify-content: center;
            margin-top: 1rem;
        }

        .order-progress .progress-step {
            padding-bottom: 2rem;
        }

        .order-progress .progress-step:last-child {
            padding-bottom: 0;
        }
    }

    /* Hiệu ứng cho các badge trạng thái */
    .inline-flex.items-center.text-xs.font-medium.rounded-full {
        transition: all 0.3s ease;
    }

    .inline-flex.items-center.text-xs.font-medium.rounded-full:hover {
        transform: scale(1.05);
    }

    /* Hiệu ứng cho các thẻ thông tin */
    .bg-gradient-to-br.from-gray-50.to-gray-100 {
        transition: all 0.3s ease;
    }

    .bg-gradient-to-br.from-gray-50.to-gray-100:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        border-color: var(--primary-light);
    }
</style>

<?php
// Thêm script theo dõi đơn hàng
echo '<script src="' . BASE_URL . '/assets/js/order-tracking.js"></script>';

// Include footer
include_once '../partials/footer.php';
?>
