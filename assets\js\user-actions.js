/**
 * User Actions JavaScript for Nội Thất Bàng Vũ
 * Xử lý tương tác giữa User Account và Cart
 */

document.addEventListener('DOMContentLoaded', function () {
  console.log('User Actions JS loaded');

  // Xử lý dropdown menu
  initDropdowns();
});

/**
 * Khởi tạo các dropdown
 */
function initDropdowns() {
  // User dropdown
  const userDropdown = document.querySelector('.user-dropdown');
  const userDropdownBtn = document.querySelector('.user-account-btn');
  const userDropdownMenu = document.querySelector('.user-dropdown-menu');

  // Cart dropdown
  const cartContainer = document.querySelector('.cart-container');
  const cartBtn = document.querySelector('.cart-btn');
  const miniCart = document.querySelector('.mini-cart');

  // Thêm sự kiện click cho document để đóng dropdown khi click ra ngoài
  document.addEventListener('click', function (e) {
    // Đóng user dropdown khi click ra ngoài
    if (userDropdown && !userDropdown.contains(e.target)) {
      userDropdown.classList.remove('active');
    }

    // Đóng mini cart khi click ra ngoài
    if (cartContainer && !cartContainer.contains(e.target)) {
      cartContainer.classList.remove('active');
    }
  });

  // Thêm sự kiện click cho user dropdown button
  if (userDropdownBtn && userDropdown) {
    userDropdownBtn.addEventListener('click', function (e) {
      e.preventDefault();
      
      // Toggle active class
      userDropdown.classList.toggle('active');
      
      // Đóng cart dropdown nếu đang mở
      if (cartContainer) {
        cartContainer.classList.remove('active');
      }
    });
  }

  // Thêm sự kiện click cho cart button
  if (cartBtn && cartContainer) {
    cartBtn.addEventListener('click', function (e) {
      // Không ngăn chặn hành vi mặc định để vẫn có thể chuyển đến trang giỏ hàng
      
      // Thêm active class
      cartContainer.classList.add('active');
      
      // Đóng user dropdown nếu đang mở
      if (userDropdown) {
        userDropdown.classList.remove('active');
      }
    });
  }

  // Thêm sự kiện hover cho cart container
  if (cartContainer && miniCart) {
    cartContainer.addEventListener('mouseenter', function () {
      cartContainer.classList.add('active');
    });

    cartContainer.addEventListener('mouseleave', function () {
      // Chỉ đóng nếu không có sự kiện click
      if (!cartContainer.classList.contains('clicked')) {
        cartContainer.classList.remove('active');
      }
    });
  }

  // Thêm sự kiện hover cho user dropdown
  if (userDropdown && userDropdownMenu) {
    userDropdown.addEventListener('mouseenter', function () {
      userDropdown.classList.add('active');
    });

    userDropdown.addEventListener('mouseleave', function () {
      // Chỉ đóng nếu không có sự kiện click
      if (!userDropdown.classList.contains('clicked')) {
        userDropdown.classList.remove('active');
      }
    });
  }
}
