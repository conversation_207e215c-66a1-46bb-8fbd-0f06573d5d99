/**
 * Mobile Filter Modal JavaScript
 * Di chuyển sidebar gốc vào modal để giữ nguyên tất cả tính năng
 */

class MobileFilterModal {
    constructor() {
        this.modal = null;
        this.overlay = null;
        this.filterBtn = null;
        this.isOpen = false;
        this.originalSidebar = null;
        this.sidebarParent = null;
        this.sidebarNextSibling = null;

        this.init();
    }

    init() {
        // Lưu reference đến sidebar gốc
        this.originalSidebar = document.querySelector('.sidebar-filters');
        if (!this.originalSidebar) return;

        // Lưu vị trí gốc của sidebar
        this.sidebarParent = this.originalSidebar.parentNode;
        this.sidebarNextSibling = this.originalSidebar.nextSibling;

        // Tạo modal elements
        this.createModal();

        // Bind events
        this.bindEvents();

        // Đếm filters hiện tại
        this.updateFilterCount();
    }

    createModal() {
        // Tạo nút filter button
        this.createFilterButton();

        // Tạo modal overlay với inline styles để đảm bảo hoạt động
        this.overlay = document.createElement('div');
        this.overlay.className = 'filter-modal-overlay';
        this.overlay.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 0.7) !important;
            z-index: 2147483647 !important;
            opacity: 0 !important;
            visibility: hidden !important;
            transition: opacity 0.3s ease, visibility 0.3s ease !important;
            pointer-events: none !important;
            display: block !important;
        `;
        this.overlay.addEventListener('click', () => this.closeModal());

        // Tạo modal container với inline styles
        this.modal = document.createElement('div');
        this.modal.className = 'filter-modal';
        this.modal.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            width: 100% !important;
            max-width: 400px !important;
            height: 100vh !important;
            background: white !important;
            z-index: 2147483647 !important;
            transform: translate3d(100%, 0, 0) !important;
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
            overflow-y: auto !important;
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15) !important;
        `;

        // Tạo modal header
        const header = this.createModalHeader();

        // Tạo modal content container (sẽ chứa sidebar gốc)
        const content = document.createElement('div');
        content.className = 'filter-modal-content';

        // Tạo modal footer
        const footer = this.createModalFooter();

        // Ghép các phần lại
        this.modal.appendChild(header);
        this.modal.appendChild(content);
        this.modal.appendChild(footer);

        // Thêm vào DOM
        document.body.appendChild(this.overlay);
        document.body.appendChild(this.modal);

        // Debug log để kiểm tra
        console.log('✅ Modal created successfully:', {
            overlay: this.overlay,
            modal: this.modal,
            overlayInDOM: document.body.contains(this.overlay),
            modalInDOM: document.body.contains(this.modal)
        });
    }

    createFilterButton() {
        // Tạo container cho filter button
        const filterContainer = document.createElement('div');
        filterContainer.className = 'mobile-filter-container';

        // Tạo filter button
        this.filterBtn = document.createElement('button');
        this.filterBtn.className = 'mobile-filter-btn';
        this.filterBtn.innerHTML = `
            <i class="fas fa-filter filter-icon"></i>
            <span class="filter-text">Bộ lọc sản phẩm</span>
        `;
        this.filterBtn.addEventListener('click', () => {
            // Hiển thị overlay ngay lập tức
            this.showOverlayImmediately();

            // Thêm hiệu ứng click
            this.filterBtn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.filterBtn.style.transform = '';
                this.openModal();
            }, 150);
        });

        // Tạo description
        const description = document.createElement('div');
        description.className = 'mobile-filter-description';
        description.innerHTML = `
            <i class="fas fa-info-circle"></i>
            <span>Nhấn để lọc theo danh mục, giá và khuyến mãi</span>
        `;

        // Ghép các phần lại
        filterContainer.appendChild(this.filterBtn);
        filterContainer.appendChild(description);

        // Tìm phần header của products content để chèn filter
        const productsHeader = document.querySelector('main.products-content .px-8.py-6.bg-white.border-b.border-gray-200');
        if (productsHeader) {
            // Chèn filter container vào cuối header
            productsHeader.appendChild(filterContainer);
        } else {
            // Fallback: tìm products content
            const productsContent = document.querySelector('.p-6.products-content');
            if (productsContent) {
                productsContent.insertBefore(filterContainer, productsContent.firstChild);
            } else {
                // Fallback cuối: append vào body
                document.body.appendChild(filterContainer);
            }
        }
    }

    createModalHeader() {
        const header = document.createElement('div');
        header.className = 'filter-modal-header';
        header.innerHTML = `
            <div class="filter-modal-title">
                <h2>
                    <div>
                        <div style="font-size: 20px; font-weight: 700; color: #1f2937; margin-bottom: 2px;">
                            Bộ lọc sản phẩm
                        </div>
                        <div style="font-size: 13px; font-weight: 500; color: #6b7280; opacity: 0.8;">
                            Tùy chỉnh tìm kiếm của bạn
                        </div>
                    </div>
                </h2>
                <button class="filter-modal-close" type="button" title="Đóng bộ lọc">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Bind close button
        header.querySelector('.filter-modal-close').addEventListener('click', () => this.closeModal());

        return header;
    }

    createModalFooter() {
        const footer = document.createElement('div');
        footer.className = 'filter-modal-footer';
        footer.innerHTML = `
            <button type="button" class="btn btn-secondary" id="modal-reset-filters" title="Xóa tất cả bộ lọc">
                <i class="fas fa-refresh"></i>
                <span>Đặt lại</span>
            </button>
            <button type="button" class="btn btn-primary" id="modal-apply-filters" title="Áp dụng bộ lọc và xem kết quả">
                <i class="fas fa-search"></i>
                <span>Áp dụng bộ lọc</span>
            </button>
        `;

        // Bind footer buttons
        footer.querySelector('#modal-reset-filters').addEventListener('click', () => this.resetFilters());
        footer.querySelector('#modal-apply-filters').addEventListener('click', () => this.applyFilters());

        return footer;
    }

    showOverlayImmediately() {
        // Hiển thị overlay ngay lập tức mà không cần chờ modal
        if (this.overlay) {
            this.overlay.classList.add('active');
            this.overlay.style.opacity = '1';
            this.overlay.style.visibility = 'visible';
            this.overlay.style.pointerEvents = 'auto';
            document.body.classList.add('filter-modal-open');

            console.log('🚀 Overlay shown immediately');
        }
    }

    updateFilterCount() {
        let count = 0;

        // Đếm keyword search
        const urlParams = new URLSearchParams(window.location.search);
        const keyword = urlParams.get('keyword');
        if (keyword && keyword.trim() !== '') count++;

        // Đếm từ sidebar gốc (luôn có dữ liệu chính xác)
        if (this.originalSidebar) {
            // Đếm checkboxes được chọn
            const checkedBoxes = this.originalSidebar.querySelectorAll('input[type="checkbox"]:checked');
            count += checkedBoxes.length;

            // Đếm price filters
            const minPrice = this.originalSidebar.querySelector('input[data-price-field="min"]')?.value;
            const maxPrice = this.originalSidebar.querySelector('input[data-price-field="max"]')?.value;
            if (minPrice || maxPrice) count++;
        }

        this.activeFiltersCount = count;

        // Update button với text thông minh
        if (this.filterBtn) {
            const buttonText = this.filterBtn.querySelector('.filter-text');

            if (buttonText) {
                if (count > 0) {
                    // Có filters: hiển thị số lượng trong text
                    buttonText.textContent = count === 1 ? 'Đã lọc 1 tiêu chí' : `Đã lọc ${count} tiêu chí`;
                    this.filterBtn.classList.add('has-filters');
                } else {
                    // Không có filters: text mặc định
                    buttonText.textContent = 'Bộ lọc sản phẩm';
                    this.filterBtn.classList.remove('has-filters');
                }

                // Animation khi update text
                buttonText.classList.add('updated');
                setTimeout(() => buttonText.classList.remove('updated'), 300);
            }
        }
    }

    openModal() {
        if (!this.originalSidebar) return;

        this.isOpen = true;

        // Di chuyển sidebar gốc vào modal
        const modalContent = this.modal.querySelector('.filter-modal-content');
        modalContent.appendChild(this.originalSidebar);

        // Ẩn các nút apply/reset trong modal content
        this.hideModalButtons();

        // Rebind price input events trong modal
        this.rebindPriceInputEvents();

        // Hiển thị modal (overlay đã được hiển thị trước đó)
        // Chỉ cần đảm bảo overlay vẫn active (có thể đã được set bởi showOverlayImmediately)
        this.overlay.classList.add('active');
        this.overlay.style.opacity = '1';
        this.overlay.style.visibility = 'visible';
        this.overlay.style.pointerEvents = 'auto';

        this.modal.classList.add('active');
        this.modal.style.transform = 'translate3d(0, 0, 0)';

        document.body.classList.add('filter-modal-open');

        // Debug log để kiểm tra
        console.log('🚀 Modal opened successfully:', {
            overlayOpacity: this.overlay.style.opacity,
            overlayVisibility: this.overlay.style.visibility,
            overlayZIndex: window.getComputedStyle(this.overlay).zIndex,
            overlayDisplay: window.getComputedStyle(this.overlay).display,
            modalTransform: this.modal.style.transform,
            modalZIndex: window.getComputedStyle(this.modal).zIndex,
            bodyClass: document.body.className.includes('filter-modal-open'),
            viewportWidth: window.innerWidth
        });

        // Update filter count
        this.updateFilterCount();
    }

    hideModalButtons() {
        // Ẩn các nút apply/reset filters trong modal content
        const applyBtn = this.originalSidebar.querySelector('#applyFilters');
        const resetBtn = this.originalSidebar.querySelector('#resetFiltersBtn');

        if (applyBtn && applyBtn.parentElement) {
            applyBtn.parentElement.style.display = 'none';
        }

        if (resetBtn && resetBtn.parentElement && resetBtn.parentElement !== applyBtn.parentElement) {
            resetBtn.parentElement.style.display = 'none';
        }
    }

    closeModal() {
        if (!this.originalSidebar) return;

        this.isOpen = false;

        // Bắt đầu animation đóng modal trước
        this.overlay.classList.remove('active');
        this.overlay.style.opacity = '0';
        this.overlay.style.visibility = 'hidden';
        this.overlay.style.pointerEvents = 'none';

        this.modal.classList.remove('active');
        this.modal.style.transform = 'translate3d(100%, 0, 0)';

        document.body.classList.remove('filter-modal-open');

        // Chờ animation hoàn thành trước khi di chuyển sidebar
        setTimeout(() => {
            // Khôi phục hiển thị các nút trước khi di chuyển sidebar
            this.showModalButtons();

            // Di chuyển sidebar về vị trí gốc sau khi modal đã ẩn hoàn toàn
            if (this.sidebarNextSibling) {
                this.sidebarParent.insertBefore(this.originalSidebar, this.sidebarNextSibling);
            } else {
                this.sidebarParent.appendChild(this.originalSidebar);
            }

            console.log('🔒 Modal closed successfully');
        }, 300); // Chờ 300ms để animation hoàn thành
    }

    showModalButtons() {
        // Hiển thị lại các nút apply/reset filters
        const applyBtn = this.originalSidebar.querySelector('#applyFilters');
        const resetBtn = this.originalSidebar.querySelector('#resetFiltersBtn');

        if (applyBtn && applyBtn.parentElement) {
            applyBtn.parentElement.style.display = '';
        }

        if (resetBtn && resetBtn.parentElement && resetBtn.parentElement !== applyBtn.parentElement) {
            resetBtn.parentElement.style.display = '';
        }
    }

    rebindPriceInputEvents() {
        // Copy các hàm từ products.php để xử lý price input
        const formatNumberWithDots = (num) => {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        };

        const removeDotsFromNumber = (str) => {
            return str.replace(/\./g, '');
        };

        const handlePriceInput = (input) => {
            let value = input.value;

            // Remove all non-digit characters except dots
            value = value.replace(/[^\d.]/g, '');

            // Remove existing dots
            value = removeDotsFromNumber(value);

            // Only keep digits
            value = value.replace(/\D/g, '');

            // Format with dots if there's a value
            if (value) {
                value = formatNumberWithDots(value);
            }

            // Update input value
            input.value = value;
        };

        // Rebind price input events trong modal
        const priceInputs = this.originalSidebar.querySelectorAll('.price-input');
        priceInputs.forEach(input => {
            // Remove existing listeners (nếu có)
            const newInput = input.cloneNode(true);
            input.parentNode.replaceChild(newInput, input);

            // Add new listeners
            newInput.addEventListener('input', function() {
                handlePriceInput(this);
            });

            newInput.addEventListener('paste', function() {
                setTimeout(() => {
                    handlePriceInput(this);
                }, 10);
            });

            // Format existing value
            if (newInput.value) {
                handlePriceInput(newInput);
            }
        });

        // Rebind price preset buttons
        const pricePresets = this.originalSidebar.querySelectorAll('.price-preset');
        pricePresets.forEach(button => {
            // Remove existing listeners
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // Add new listener
            newButton.addEventListener('click', function() {
                const minPrice = this.getAttribute('data-min');
                const maxPrice = this.getAttribute('data-max');
                const priceMinInput = document.getElementById('price-min');
                const priceMaxInput = document.getElementById('price-max');

                // Check if this button is currently active
                const isCurrentlyActive = this.classList.contains('from-orange-500');

                if (isCurrentlyActive) {
                    // If active, deactivate it (clear values)
                    priceMinInput.value = '';
                    priceMaxInput.value = '';

                    // Remove active class from this preset
                    this.className = this.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
                    this.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
                } else {
                    // If not active, activate it (set values)
                    priceMinInput.value = minPrice || '';
                    priceMaxInput.value = maxPrice || '';

                    if (minPrice) handlePriceInput(priceMinInput);
                    if (maxPrice) handlePriceInput(priceMaxInput);

                    // Remove active class from all presets first
                    document.querySelectorAll('.price-preset').forEach(btn => {
                        btn.className = btn.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
                        btn.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
                    });

                    // Add active class to clicked preset - chỉ thêm from-orange-500 để trigger outline style
                    this.className = this.className.replace(/bg-white|text-gray-700|border-gray-200/g, '');
                    this.classList.add('from-orange-500', 'bg-white', 'text-gray-700', 'border-gray-200');
                }
            });
        });
    }

    applyFilters() {
        // Không cần sync vì đang dùng sidebar gốc
        // Trigger form submission
        this.triggerFilterUpdate();

        // Close modal
        this.closeModal();
    }

    resetFilters() {
        if (!this.originalSidebar) return;

        // Reset tất cả filters trong sidebar gốc
        const checkboxes = this.originalSidebar.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => cb.checked = false);

        const priceInputs = this.originalSidebar.querySelectorAll('input[data-price-field]');
        priceInputs.forEach(input => input.value = '');

        // Reset price preset buttons
        const pricePresets = this.originalSidebar.querySelectorAll('.price-preset');
        pricePresets.forEach(btn => {
            btn.className = btn.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
            btn.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
        });

        this.updateFilterCount();

        // Đóng modal với hiệu ứng slide trước khi redirect
        this.closeModal();

        // Chờ animation đóng modal hoàn thành rồi mới redirect
        setTimeout(() => {
            window.location.href = 'products.php?scroll=1';
        }, 350); // 350ms để đảm bảo animation hoàn thành (300ms + buffer)
    }

    triggerFilterUpdate() {
        // Tạo form ẩn để submit filters
        const form = document.createElement('form');
        form.method = 'GET';
        form.action = window.location.pathname;
        form.style.display = 'none';

        // Giữ lại các parameters hiện tại (keyword, sort, etc.)
        const currentUrl = new URL(window.location);
        const preserveParams = ['keyword', 'sort', 'items_per_page'];

        preserveParams.forEach(param => {
            const value = currentUrl.searchParams.get(param);
            if (value) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = param;
                input.value = value;
                form.appendChild(input);
            }
        });

        // Thêm filter values từ sidebar gốc
        this.createHiddenInputs(form);

        // Submit form
        document.body.appendChild(form);
        form.submit();
    }

    createHiddenInputs(form) {
        if (!this.originalSidebar) return;

        // Add new hidden inputs based on sidebar state
        const checkedCategories = this.originalSidebar.querySelectorAll('input[name="category[]"]:checked');
        checkedCategories.forEach(cb => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'category[]';
            input.value = cb.value;
            form.appendChild(input);
        });

        const checkedPromotions = this.originalSidebar.querySelectorAll('input[name="promotion[]"]:checked');
        checkedPromotions.forEach(cb => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'promotion[]';
            input.value = cb.value;
            form.appendChild(input);
        });

        const minPrice = this.originalSidebar.querySelector('input[data-price-field="min"]')?.value;
        const maxPrice = this.originalSidebar.querySelector('input[data-price-field="max"]')?.value;

        // Helper function để remove dots
        const removeDotsFromNumber = (str) => {
            return str.replace(/\./g, '');
        };

        if (minPrice) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'price_min';
            input.value = removeDotsFromNumber(minPrice); // Remove dots trước khi submit
            form.appendChild(input);
        }

        if (maxPrice) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'price_max';
            input.value = removeDotsFromNumber(maxPrice); // Remove dots trước khi submit
            form.appendChild(input);
        }
    }

    bindEvents() {
        // Keyboard events
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeModal();
            }
        });

        // Prevent modal close when clicking inside modal
        if (this.modal) {
            this.modal.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        // Listen for changes in sidebar to update count
        if (this.originalSidebar) {
            // Sử dụng event delegation để lắng nghe tất cả changes
            this.originalSidebar.addEventListener('change', () => {
                setTimeout(() => this.updateFilterCount(), 100);
            });

            this.originalSidebar.addEventListener('input', () => {
                setTimeout(() => this.updateFilterCount(), 100);
            });
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MobileFilterModal();
});
