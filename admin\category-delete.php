<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

// Kiểm tra ID danh mục
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    set_flash_message('error', 'ID danh mục không hợp lệ.');
    redirect('categories.php');
}

$category_id = intval($_GET['id']);
$category = get_category_by_id($category_id);

if (!$category) {
    set_flash_message('error', 'Danh mục không tồn tại.');
    redirect('categories.php');
}

// Kiểm tra xem có yêu cầu xóa cưỡng chế không
$force_delete = isset($_GET['force']) && $_GET['force'] == 1;

// Nếu không phải xóa cưỡng chế, kiểm tra trước
if (!$force_delete) {
    // Thử xóa danh mục (không cưỡng chế)
    $result = delete_category($category_id, false);

    // Nếu có danh mục con hoặc sản phẩm, hiển thị trang xác nhận
    if (!$result['success'] && (isset($result['has_subcategories']) || isset($result['has_products']))) {
        // Include header
        $page_title = 'Xác nhận xóa danh mục';
        include_once 'partials/header.php';
        ?>

        <!-- Content -->
        <div class="container-fluid">
            <h1 class="h3 mb-4 text-gray-800">Xác nhận xóa danh mục</h1>

            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> Cảnh báo!</h5>
                <p><?php echo $result['message']; ?></p>

                <?php if (isset($result['has_subcategories']) && $result['has_subcategories']): ?>
                <p>Danh mục con sẽ bị xóa:</p>
                <ul>
                    <?php
                    $subcategories = get_all_subcategories($category_id);
                    foreach ($subcategories as $subcat):
                    ?>
                    <li><?php echo htmlspecialchars($subcat['name']); ?></li>
                    <?php endforeach; ?>
                </ul>
                <?php endif; ?>

                <?php if (isset($result['has_products']) && $result['has_products']): ?>
                <p>Tất cả sản phẩm thuộc danh mục này và các danh mục con sẽ bị xóa.</p>
                <?php endif; ?>

                <p class="mb-0">Bạn có chắc chắn muốn xóa danh mục này và tất cả dữ liệu liên quan?</p>
            </div>

            <div class="mb-4">
                <a href="category-delete.php?id=<?php echo $category_id; ?>&force=1" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Xóa tất cả
                </a>
                <a href="categories.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
            </div>
        </div>

        <?php
        // Include footer
        include_once 'partials/footer.php';
        exit;
    }
} else {
    // Xóa cưỡng chế
    $result = delete_category($category_id, true);

    // Xóa hình ảnh của danh mục chính nếu có
    if ($result['success'] && !empty($category['image'])) {
        $image_path = '../uploads/categories/' . $category['image'];
        if (file_exists($image_path)) {
            unlink($image_path);
        }
    }

    // Xóa hình ảnh của các danh mục con nếu có
    if ($result['success'] && isset($result['deleted_subcategories']) && $result['deleted_subcategories'] > 0) {
        $subcategories = get_all_subcategories($category_id);
        foreach ($subcategories as $subcat) {
            if (!empty($subcat['image'])) {
                $image_path = '../uploads/categories/' . $subcat['image'];
                if (file_exists($image_path)) {
                    unlink($image_path);
                }
            }
        }
    }
}

// Thông báo kết quả và chuyển hướng
set_flash_message($result['success'] ? 'success' : 'error', $result['message']);
redirect('categories.php');
?>
