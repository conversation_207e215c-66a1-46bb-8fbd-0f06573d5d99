<?php
// Include init
require_once '../includes/init.php';

// Ki<PERSON>m tra request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Ph<PERSON>ơng thức không được hỗ trợ'
    ]);
    exit;
}

// Lấy dữ liệu
$product_id = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;

// Kiểm tra dữ liệu
if ($product_id <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Dữ liệu không hợp lệ'
    ]);
    exit;
}

// Xóa khỏi giỏ hàng
$result = remove_from_cart($product_id);

// Thêm thông tin bổ sung cho phản hồi
if ($result['success']) {
    // Cập nhật thời gian cập nhật giỏ hàng
    $_SESSION['cart_updated'] = time() * 1000; // Chuyển đổi sang milliseconds để phù hợp với JavaScript

    $result['count'] = get_cart_count(); // Tổng số lượng sản phẩm (cho badge)
    $result['items_count'] = get_cart_items_count(); // Số lượng items khác nhau (cho hiển thị "X sản phẩm")
    $result['total'] = format_currency(get_cart_total());
    $result['timestamp'] = $_SESSION['cart_updated'];
}

// Trả về kết quả
header('Content-Type: application/json');
echo json_encode($result);
?>
