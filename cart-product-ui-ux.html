<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>N<PERSON><PERSON> Thất Việt - <PERSON><PERSON><PERSON> hàng nội thất</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" xintegrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <style>
        /* Custom styles */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }
        /* Custom range input styling */
        input[type=range] {
            -webkit-appearance: none;
            appearance: none;
            width: 100%;
            height: 4px;
            background: #d3d3d3;
            outline: none;
            opacity: 0.7;
            -webkit-transition: .2s;
            transition: opacity .2s;
            border-radius: 5px;
        }

        input[type=range]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: #ca8a04; /* yellow-600 */
            cursor: pointer;
            border-radius: 50%;
        }

        input[type=range]::-moz-range-thumb {
            width: 16px;
            height: 16px;
            background: #ca8a04;
            cursor: pointer;
            border-radius: 50%;
        }

        /* Custom checkbox styling */
        .custom-checkbox:checked {
            background-color: #ca8a04;
            border-color: #ca8a04;
        }
    </style>
</head>
<body class="bg-white">

    <div class="min-h-screen">
        <!-- Header Navigation -->
        <header class="bg-white border-b sticky top-0 z-20">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between h-16">
                    <!-- Logo -->
                    <div class="flex items-center space-x-8">
                        <a href="#" class="text-xl font-bold text-yellow-700">Nội Thất Việt</a>
                        <nav class="hidden md:flex items-center space-x-6">
                            <a href="#" class="text-gray-700 hover:text-yellow-600 font-medium border-b-2 border-yellow-600 pb-1">Sản phẩm</a>
                            <a href="#" class="text-gray-500 hover:text-yellow-600">Phòng khách</a>
                            <a href="#" class="text-gray-500 hover:text-yellow-600">Phòng ngủ</a>
                            <a href="#" class="text-gray-500 hover:text-yellow-600">Phòng bếp</a>
                            <a href="#" class="text-gray-500 hover:text-yellow-600">Giới thiệu</a>
                        </nav>
                    </div>
                    <!-- User/Auth Section -->
                    <div class="flex items-center space-x-4">
                        <button class="text-gray-600 hover:text-yellow-700">
                             <i class="fa-solid fa-magnifying-glass"></i>
                        </button>
                         <button class="text-gray-600 hover:text-yellow-700">
                             <i class="fa-solid fa-cart-shopping"></i>
                        </button>
                        <button class="px-4 py-2 text-sm font-medium text-gray-700 border rounded-lg hover:bg-gray-50">Đăng nhập</button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="container mx-auto px-4 mt-6">
            <div class="flex flex-col lg:flex-row gap-8">

                <!-- Left Sidebar: Filters -->
                <aside class="w-full lg:w-1/4 xl:w-1/5">
                    <div class="p-6 bg-white rounded-lg shadow-sm border">
                        <h2 class="text-2xl font-bold mb-6">Bộ lọc</h2>

                        <!-- Product Type Filter -->
                        <div class="mb-6">
                            <h3 class="font-semibold mb-3 text-gray-800">Loại sản phẩm</h3>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 custom-checkbox" checked>
                                    <span class="ml-3 text-gray-700">Tủ quần áo</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 custom-checkbox">
                                    <span class="ml-3 text-gray-700">Bàn làm việc</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 custom-checkbox">
                                    <span class="ml-3 text-gray-700">Bàn trang điểm</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 custom-checkbox">
                                    <span class="ml-3 text-gray-700">Giường ngủ</span>
                                </label>
                                 <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 custom-checkbox">
                                    <span class="ml-3 text-gray-700">Sofa</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 custom-checkbox">
                                    <span class="ml-3 text-gray-700">Kệ Tivi</span>
                                </label>
                            </div>
                        </div>

                        <!-- Price Range Filter -->
                        <div class="mb-6">
                            <h3 class="font-semibold mb-3 text-gray-800">Khoảng giá</h3>
                             <div class="space-y-2 mb-4">
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 custom-checkbox">
                                    <span class="ml-3 text-gray-700">Dưới 2 triệu</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 custom-checkbox" checked>
                                    <span class="ml-3 text-yellow-600 font-semibold">2 - 5 triệu</span>
                                </label>
                                 <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 custom-checkbox">
                                    <span class="ml-3 text-gray-700">5 - 10 triệu</span>
                                </label>
                            </div>
                            <div class="relative">
                               <input type="range" min="0" max="10000000" value="5000000" class="w-full">
                               <div class="flex justify-between text-xs text-gray-500 mt-2">
                                   <span>0đ</span>
                                   <span class="font-semibold text-yellow-600">5tr</span>
                                   <span>10tr+</span>
                               </div>
                            </div>
                        </div>

                        <!-- Material Filter -->
                        <div>
                            <h3 class="font-semibold mb-3 text-gray-800">Chất liệu</h3>
                            <div class="flex flex-wrap gap-2">
                                <button class="px-3 py-1.5 border rounded-full text-sm hover:border-yellow-600 hover:text-yellow-600">Tất cả</button>
                                <button class="px-3 py-1.5 border rounded-full text-sm border-yellow-600 text-yellow-600 bg-yellow-50">Gỗ sồi</button>
                                <button class="px-3 py-1.5 border rounded-full text-sm hover:border-yellow-600 hover:text-yellow-600">Gỗ công nghiệp</button>
                                <button class="px-3 py-1.5 border rounded-full text-sm hover:border-yellow-600 hover:text-yellow-600">Kim loại</button>
                            </div>
                        </div>
                    </div>
                </aside>

                <!-- Right Content: Listings -->
                <main class="w-full lg:w-3/4 xl:w-4/5">
                    <!-- Category Tabs -->
                    <div class="border-b mb-4">
                        <nav class="flex space-x-6 -mb-px">
                            <a href="#" class="px-1 pb-3 text-gray-500 hover:text-gray-800">Phòng khách</a>
                            <a href="#" class="px-1 pb-3 text-gray-900 border-b-2 border-gray-900 font-semibold">Phòng ngủ</a>
                            <a href="#" class="px-1 pb-3 text-gray-500 hover:text-gray-800">Phòng bếp</a>
                            <a href="#" class="px-1 pb-3 text-gray-500 hover:text-gray-800">Phòng làm việc</a>
                            <a href="#" class="px-1 pb-3 text-gray-500 hover:text-gray-800">Trang trí</a>
                        </nav>
                    </div>

                    <!-- Results Info & Active Filters -->
                    <div class="mb-6">
                        <h1 class="text-2xl font-bold">Tìm thấy 258 sản phẩm</h1>
                        <div class="flex flex-wrap gap-2 mt-3">
                            <span class="flex items-center bg-yellow-100 text-yellow-800 text-sm font-medium px-3 py-1 rounded-full">
                                Tủ quần áo
                                <button class="ml-2 text-yellow-600 hover:text-yellow-800">&times;</button>
                            </span>
                            <span class="flex items-center bg-yellow-100 text-yellow-800 text-sm font-medium px-3 py-1 rounded-full">
                                2 - 5 triệu
                                <button class="ml-2 text-yellow-600 hover:text-yellow-800">&times;</button>
                            </span>
                             <span class="flex items-center bg-yellow-100 text-yellow-800 text-sm font-medium px-3 py-1 rounded-full">
                                Gỗ sồi
                                <button class="ml-2 text-yellow-600 hover:text-yellow-800">&times;</button>
                            </span>
                        </div>
                    </div>

                    <!-- Product Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                        
                        <!-- Card 1 -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                            <div class="relative">
                                <img src="https://placehold.co/400x300/fde68a/422006?text=Tủ+Quần+Áo" alt="Tủ quần áo gỗ sồi" class="w-full h-56 object-cover">
                                <div class="absolute top-0 right-0 m-2 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded">-15%</div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-800 truncate group-hover:text-yellow-700">Tủ quần áo 3 cánh gỗ sồi</h3>
                                <p class="text-gray-500 text-sm mb-2">Phòng ngủ</p>
                                <div class="flex items-baseline mb-3">
                                    <p class="text-xl font-bold text-yellow-700">4.250.000₫</p>
                                    <p class="text-sm text-gray-400 line-through ml-2">5.000.000₫</p>
                                </div>
                                <div class="flex items-center text-gray-600 space-x-4 text-xs border-b pb-3 mb-3">
                                    <span>C: 200cm</span>
                                    <span>R: 120cm</span>
                                    <span>S: 60cm</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <button class="bg-yellow-600 text-white px-5 py-2 rounded-lg text-sm font-semibold hover:bg-yellow-700 transition-colors">Thêm vào giỏ</button>
                                    <button class="text-gray-400 hover:text-red-500 text-xl">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Card 2 -->
                         <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                            <div class="relative">
                                <img src="https://placehold.co/400x300/fef3c7/422006?text=Bàn+Làm+Việc" alt="Bàn làm việc thông minh" class="w-full h-56 object-cover">
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-800 truncate group-hover:text-yellow-700">Bàn làm việc thông minh Z-Desk</h3>
                                <p class="text-gray-500 text-sm mb-2">Phòng làm việc</p>
                                <div class="flex items-baseline mb-3">
                                    <p class="text-xl font-bold text-yellow-700">3.800.000₫</p>
                                </div>
                                <div class="flex items-center text-gray-600 space-x-4 text-xs border-b pb-3 mb-3">
                                     <span>C: 75cm</span>
                                     <span>R: 140cm</span>
                                     <span>S: 70cm</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <button class="bg-yellow-600 text-white px-5 py-2 rounded-lg text-sm font-semibold hover:bg-yellow-700 transition-colors">Thêm vào giỏ</button>
                                    <button class="text-red-500 text-xl">
                                        <i class="fa-solid fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Card 3 -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                            <div class="relative">
                                <img src="https://placehold.co/400x300/fef9c3/422006?text=Bàn+Trang+Điểm" alt="Bàn trang điểm có đèn" class="w-full h-56 object-cover">
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-800 truncate group-hover:text-yellow-700">Bàn trang điểm gỗ có đèn LED</h3>
                                <p class="text-gray-500 text-sm mb-2">Phòng ngủ</p>
                                <div class="flex items-baseline mb-3">
                                    <p class="text-xl font-bold text-yellow-700">2.990.000₫</p>
                                </div>
                                 <div class="flex items-center text-gray-600 space-x-4 text-xs border-b pb-3 mb-3">
                                     <span>C: 135cm</span>
                                     <span>R: 80cm</span>
                                     <span>S: 40cm</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <button class="bg-yellow-600 text-white px-5 py-2 rounded-lg text-sm font-semibold hover:bg-yellow-700 transition-colors">Thêm vào giỏ</button>
                                    <button class="text-gray-400 hover:text-red-500 text-xl">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Thêm các card khác để lấp đầy -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                            <div class="relative">
                                <img src="https://placehold.co/400x300/fbbf24/422006?text=Giường+Ngủ" alt="Giường ngủ" class="w-full h-56 object-cover">
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-800 truncate group-hover:text-yellow-700">Giường ngủ kiểu Nhật 1.6x2m</h3>
                                <p class="text-gray-500 text-sm mb-2">Phòng ngủ</p>
                                <p class="text-xl font-bold text-yellow-700 mb-3">6.100.000₫</p>
                                <div class="flex items-center text-gray-600 space-x-4 text-xs border-b pb-3 mb-3">
                                     <span>C: 30cm</span>
                                     <span>R: 160cm</span>
                                     <span>D: 200cm</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <button class="bg-yellow-600 text-white px-5 py-2 rounded-lg text-sm font-semibold hover:bg-yellow-700 transition-colors">Thêm vào giỏ</button>
                                    <button class="text-gray-400 hover:text-red-500 text-xl">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                            <div class="relative">
                                <img src="https://placehold.co/400x300/facc15/422006?text=Sofa" alt="Sofa" class="w-full h-56 object-cover">
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-800 truncate group-hover:text-yellow-700">Sofa băng vải nỉ cao cấp</h3>
                                <p class="text-gray-500 text-sm mb-2">Phòng khách</p>
                                <p class="text-xl font-bold text-yellow-700 mb-3">8.500.000₫</p>
                                <div class="flex items-center text-gray-600 space-x-4 text-xs border-b pb-3 mb-3">
                                     <span>C: 85cm</span>
                                     <span>R: 220cm</span>
                                     <span>S: 90cm</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <button class="bg-yellow-600 text-white px-5 py-2 rounded-lg text-sm font-semibold hover:bg-yellow-700 transition-colors">Thêm vào giỏ</button>
                                    <button class="text-red-500 text-xl">
                                        <i class="fa-solid fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                            <div class="relative">
                                <img src="https://placehold.co/400x300/fde047/422006?text=Kệ+Tivi" alt="Kệ Tivi" class="w-full h-56 object-cover">
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-800 truncate group-hover:text-yellow-700">Kệ tivi gỗ công nghiệp</h3>
                                <p class="text-gray-500 text-sm mb-2">Phòng khách</p>
                                <p class="text-xl font-bold text-yellow-700 mb-3">2.150.000₫</p>
                                <div class="flex items-center text-gray-600 space-x-4 text-xs border-b pb-3 mb-3">
                                     <span>C: 50cm</span>
                                     <span>R: 180cm</span>
                                     <span>S: 40cm</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <button class="bg-yellow-600 text-white px-5 py-2 rounded-lg text-sm font-semibold hover:bg-yellow-700 transition-colors">Thêm vào giỏ</button>
                                    <button class="text-gray-400 hover:text-red-500 text-xl">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>
                </main>
            </div>
        </div>
    </div>
</body>
</html>
