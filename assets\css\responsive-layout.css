/*
 * Responsive Layout Optimization
 * Tối <PERSON>u bố cục responsive cho trang web nội thất
 */

/* Container ch<PERSON>h có max-width để tránh nội dung bị dàn trải quá rộng */
.container {
    max-width: 1400px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    padding-left: 1rem;
    padding-right: 1rem;
    width: 100%;
}

/* Giới hạn chiều rộng tối đa cho phần văn bản để dễ đọc */
/* .readable-content {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
} */

/* Áp dụng readable-content cho các phần nội dung chính */
.product-content p,
.product-content ul,
.product-content ol,
.product-content h1,
.product-content h2,
.product-content h3,
.product-content h4,
.product-content h5,
.product-content h6 {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* <PERSON><PERSON> c<PERSON><PERSON> hai cột với tỷ lệ 40:60 */
.two-column-layout {
    display: flex;
    flex-wrap: wrap;
    margin-left: -1rem;
    margin-right: -1rem;
}

.two-column-layout .column-left {
    flex: 0 0 100%;
    max-width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
    margin-bottom: 1.5rem;
}

.two-column-layout .column-right {
    flex: 0 0 100%;
    max-width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
}

/* Bố cục hai cột trên màn hình trung bình và lớn */
@media (min-width: 768px) {
    .two-column-layout .column-left {
        flex: 0 0 40%;
        max-width: 40%;
        margin-bottom: 0;
    }

    .two-column-layout .column-right {
        flex: 0 0 60%;
        max-width: 60%;
    }
}

/* Tối ưu cho màn hình lớn */
@media (min-width: 1440px) {
    .container {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    /* Tăng khoảng cách giữa các cột trên màn hình lớn */
    .two-column-layout {
        margin-left: -1.5rem;
        margin-right: -1.5rem;
    }

    .two-column-layout .column-left,
    .two-column-layout .column-right {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

/* Tối ưu cho màn hình nhỏ */
@media (max-width: 767px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Tối ưu cho trang chi tiết sản phẩm */
.product-detail-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
}

@media (min-width: 768px) {
    .product-detail-container {
        flex-direction: row;
        width: 100%;
    }

    .product-images-container {
        flex: 0 0 40%;
        max-width: 40%;
        position: relative;
    }

    .product-info-container {
        flex: 0 0 60%;
        max-width: 60%;
        width: 60%;
    }
}

/* Sticky product images */
.sticky-product-images {
    position: sticky;
    top: 20px;
    z-index: 10;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

/* Full-width content section */
.full-width-content {
    width: 100%;
    max-width: 100%;
    margin-top: 2rem;
    box-sizing: border-box;
}

/* Tối ưu cho phần nội dung sản phẩm */
.product-content {
    width: 100%;
    max-width: 100%;
}

/* Đảm bảo phần thông tin sản phẩm có chiều rộng bằng với phần trên */
.product-detail-container+.full-width-content {
    max-width: 100%;
    width: 100%;
}

.product-detail-container+.full-width-content .bg-white,
.product-detail-container+.full-width-content .product-details-section {
    max-width: 100%;
    width: 100%;
}

/* Tối ưu cho phần chi tiết sản phẩm */
.product-details-section {
    width: 100%;
    box-sizing: border-box;
}

@media (min-width: 768px) {
    .product-details-section {
        width: 100%;
        margin-top: 1.5rem;
    }

    /* Đảm bảo phần thông tin sản phẩm có chiều rộng bằng với tổng chiều rộng của phần ảnh và thông tin */
    .product-detail-container,
    .product-details-section,
    .product-info-section {
        max-width: 100%;
        box-sizing: border-box;
    }

    /* Đảm bảo các phần có cùng style */
    .product-info-section,
    .product-details-section {
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        padding: 1.5rem;
    }
}

/* Tối ưu cho header, main và footer */
.site-header,
.site-main,
.site-footer {
    width: 100%;
}

/* Tối ưu cho navigation */
.site-navigation {
    width: 100%;
}

/* Đảm bảo tất cả các container đều có max-width 1400px */
.container,
.site-main .container,
header .container,
footer .container,
nav .container,
.site-header .container,
.site-footer .container,
.site-navigation .container {
    max-width: 1400px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    width: 100%;
}

/* Các quy tắc bổ sung cho main content */

/* Tối ưu cho các card sản phẩm */
.product-card-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1rem;
}

@media (min-width: 576px) {
    .product-card-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .product-card-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 992px) {
    .product-card-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }
}

/* Tối ưu cho sidebar và nội dung chính */
.main-with-sidebar {
    display: flex;
    flex-wrap: wrap;
    margin-left: -1rem;
    margin-right: -1rem;
}

.sidebar {
    flex: 0 0 100%;
    max-width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
    margin-bottom: 1.5rem;
}

.main-content {
    flex: 0 0 100%;
    max-width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 992px) {
    .sidebar {
        flex: 0 0 25%;
        max-width: 25%;
        margin-bottom: 0;
    }

    .main-content {
        flex: 0 0 75%;
        max-width: 75%;
    }
}