{"version": 3, "file": "lang/summernote-sr-RS-Latin.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,YAAY;QAClBC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,sBAAsB;QAC7BC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,aAAa;QACnBC,aAAa,EAAE,WAAW;QAC1BC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,aAAa;QAC1BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,cAAc;QACtBC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,eAAe;QAC3BC,aAAa,EAAE,eAAe;QAC9BC,SAAS,EAAE,eAAe;QAC1BC,UAAU,EAAE,gBAAgB;QAC5BC,SAAS,EAAE,cAAc;QACzBC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,oBAAoB;QACnCC,SAAS,EAAE,oBAAoB;QAC/BC,eAAe,EAAE,qBAAqB;QACtCC,eAAe,EAAE,mBAAmB;QACpCC,oBAAoB,EAAE,6BAA6B;QACnDC,GAAG,EAAE,cAAc;QACnBC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,eAAe;QAC1BpB,MAAM,EAAE,cAAc;QACtBgB,GAAG,EAAE,WAAW;QAChBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,aAAa;QACrBuB,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,OAAO;QACbC,aAAa,EAAE,iBAAiB;QAChCT,GAAG,EAAE,iBAAiB;QACtBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,eAAe;QAC5BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,KAAK;QACRC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,KAAK;QACVC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,cAAc;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,oBAAoB;QAChCC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,kBAAkB;QAC3BC,MAAM,EAAE,mBAAmB;QAC3BC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAE,YAAY;QACpBC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,WAAW;QACjBC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,aAAa;QACzBC,WAAW,EAAE,UAAU;QACvBC,cAAc,EAAE,UAAU;QAC1BC,KAAK,EAAE,QAAQ;QACfC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,sBAAsB;QACjCC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE,qBAAqB;QACrCC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,wBAAwB;QAC7CC,aAAa,EAAE,gBAAgB;QAC/BC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,kBAAkB;QACrC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,yBAAyB;QACjC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,kBAAkB;QAC1B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-sr-RS-Latin.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'sr-RS': {\n      font: {\n        bold: 'Podebljano',\n        italic: 'Kurz<PERSON>',\n        underline: 'Podvučeno',\n        clear: 'Ukloni stilove fonta',\n        height: 'Visina linije',\n        name: '<PERSON>ont Family',\n        strikethrough: 'Precrtano',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Veličina fonta',\n      },\n      image: {\n        image: 'Slika',\n        insert: 'Umetni sliku',\n        resizeFull: 'Puna veličina',\n        resizeHalf: 'Umanji na 50%',\n        resizeQuarter: '<PERSON><PERSON><PERSON> na 25%',\n        floatLeft: 'Uz levu ivicu',\n        floatRight: 'Uz desnu ivicu',\n        floatNone: 'Bez ravnanja',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Prevuci sliku ovde',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Iza<PERSON>i iz datoteke',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'Adresa slike',\n        remove: 'Ukloni sliku',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Veza ka videu',\n        insert: 'Umetni video',\n        url: 'URL video',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion ili Youku)',\n      },\n      link: {\n        link: 'Veza',\n        insert: 'Umetni vezu',\n        unlink: 'Ukloni vezu',\n        edit: 'Uredi',\n        textToDisplay: 'Tekst za prikaz',\n        url: 'Internet adresa',\n        openInNewWindow: 'Otvori u novom prozoru',\n      },\n      table: {\n        table: 'Tabela',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Umetni horizontalnu liniju',\n      },\n      style: {\n        style: 'Stil',\n        p: 'pni',\n        blockquote: 'Citat',\n        pre: 'Kod',\n        h1: 'Zaglavlje 1',\n        h2: 'Zaglavlje 2',\n        h3: 'Zaglavlje 3',\n        h4: 'Zaglavlje 4',\n        h5: 'Zaglavlje 5',\n        h6: 'Zaglavlje 6',\n      },\n      lists: {\n        unordered: 'Obična lista',\n        ordered: 'Numerisana lista',\n      },\n      options: {\n        help: 'Pomoć',\n        fullscreen: 'Preko celog ekrana',\n        codeview: 'Izvorni kod',\n      },\n      paragraph: {\n        paragraph: 'Paragraf',\n        outdent: 'Smanji uvlačenje',\n        indent: 'Povečaj uvlačenje',\n        left: 'Poravnaj u levo',\n        center: 'Centrirano',\n        right: 'Poravnaj u desno',\n        justify: 'Poravnaj obostrano',\n      },\n      color: {\n        recent: 'Poslednja boja',\n        more: 'Više boja',\n        background: 'Boja pozadine',\n        foreground: 'Boja teksta',\n        transparent: 'Providna',\n        setTransparent: 'Providna',\n        reset: 'Opoziv',\n        resetToDefault: 'Podrazumevana',\n      },\n      shortcut: {\n        shortcuts: 'Prečice sa tastature',\n        close: 'Zatvori',\n        textFormatting: 'Formatiranje teksta',\n        action: 'Akcija',\n        paragraphFormatting: 'Formatiranje paragrafa',\n        documentStyle: 'Stil dokumenta',\n        extraKeys: 'Dodatne kombinacije',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Poništi',\n        redo: 'Ponovi',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}