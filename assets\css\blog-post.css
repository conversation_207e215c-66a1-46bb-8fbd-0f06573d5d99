/* Blog Post CSS - <PERSON><PERSON><PERSON>t <PERSON> */

/* Blog Post Container */
.blog-post-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Blog Post Layout */
.blog-post-with-sidebar {
    display: grid;
    grid-template-columns: 3fr 1fr;
    gap: 2rem;
}

/* Blog Post Content */
.blog-post-content {
    background-color: #fff;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Blog Post Header */
.blog-post-header {
    padding: 2rem;
    border-bottom: 1px solid #f0f0f0;
}

.blog-post-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.blog-post-category {
    display: inline-block;
    background-color: rgba(243, 115, 33, 0.1);
    color: #F37321;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    text-decoration: none;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.blog-post-category:hover {
    background-color: #F37321;
    color: white;
}

.blog-post-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.blog-post-meta {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #888;
    margin-bottom: 1rem;
}

.blog-post-author {
    display: flex;
    align-items: center;
    margin-right: 1.5rem;
}

.blog-post-author-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 0.75rem;
}

.blog-post-author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.blog-post-author-name {
    font-weight: 600;
    color: #555;
}

.blog-post-date,
.blog-post-views {
    display: flex;
    align-items: center;
    margin-right: 1.5rem;
}

.blog-post-date i,
.blog-post-views i {
    margin-right: 0.5rem;
    color: #F37321;
}

/* Featured Image */
.blog-post-featured-image {
    width: 100%;
    height: 500px;
    overflow: hidden;
}

.blog-post-featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Blog Post Body */
.blog-post-body {
    padding: 2rem;
}

.blog-post-body p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
    color: #444;
    font-size: 1.05rem;
}

.blog-post-body h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #333;
    margin: 2rem 0 1rem;
}

.blog-post-body h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin: 1.75rem 0 1rem;
}

.blog-post-body ul,
.blog-post-body ol {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

.blog-post-body li {
    margin-bottom: 0.5rem;
    line-height: 1.7;
}

.blog-post-body blockquote {
    border-left: 4px solid #F37321;
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    background-color: #f9f9f9;
    font-style: italic;
    color: #555;
}

.blog-post-body img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
}

.blog-post-body a {
    color: #F37321;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.2s ease;
}

.blog-post-body a:hover {
    border-color: #F37321;
}

/* Blog Post Tags */
.blog-post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #f0f0f0;
}

.blog-post-tags-title {
    font-weight: 600;
    color: #555;
    margin-right: 0.75rem;
}

.blog-post-tag {
    display: inline-block;
    background-color: #f0f0f0;
    color: #666;
    font-size: 0.8rem;
    padding: 0.35rem 0.75rem;
    border-radius: 1rem;
    text-decoration: none;
    transition: all 0.2s ease;
}

.blog-post-tag:hover {
    background-color: #F37321;
    color: white;
}

/* Share Buttons */
.blog-post-share {
    display: flex;
    align-items: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #f0f0f0;
}

.blog-post-share-title {
    font-weight: 600;
    color: #555;
    margin-right: 1rem;
}

.blog-post-share-buttons {
    display: flex;
    gap: 0.75rem;
}

.blog-post-share-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: transform 0.2s ease, opacity 0.2s ease;
}

.blog-post-share-button:hover {
    transform: translateY(-3px);
    opacity: 0.9;
}

.share-facebook {
    background-color: #3b5998;
}

.share-twitter {
    background-color: #1da1f2;
}

.share-linkedin {
    background-color: #0077b5;
}

.share-pinterest {
    background-color: #bd081c;
}

/* Author Box */
.blog-post-author-box {
    display: flex;
    background-color: #f9f9f9;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: 3rem;
}

.blog-post-author-box-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.blog-post-author-box-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.blog-post-author-box-content h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.blog-post-author-box-content p {
    font-size: 0.95rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.blog-post-author-box-social {
    display: flex;
    gap: 0.75rem;
}

.blog-post-author-box-social a {
    color: #666;
    text-decoration: none;
    transition: color 0.2s ease;
}

.blog-post-author-box-social a:hover {
    color: #F37321;
}

/* Related Posts */
.blog-post-related {
    margin-top: 3rem;
}

.blog-post-related-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1.5rem;
    text-align: center;
}

.blog-post-related-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

/* Comments */
.blog-post-comments {
    margin-top: 3rem;
}

.blog-post-comments-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1.5rem;
}

.blog-post-comment {
    display: flex;
    margin-bottom: 2rem;
}

.blog-post-comment-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 1rem;
    flex-shrink: 0;
}

.blog-post-comment-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.blog-post-comment-content {
    flex-grow: 1;
}

.blog-post-comment-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.blog-post-comment-author {
    font-weight: 600;
    color: #333;
}

.blog-post-comment-date {
    font-size: 0.85rem;
    color: #888;
}

.blog-post-comment-body {
    font-size: 0.95rem;
    color: #555;
    line-height: 1.6;
    margin-bottom: 0.75rem;
}

.blog-post-comment-reply {
    display: inline-block;
    font-size: 0.85rem;
    color: #F37321;
    text-decoration: none;
    font-weight: 600;
}

.blog-post-comment-reply:hover {
    text-decoration: underline;
}

.blog-post-comment-replies {
    margin-left: 4rem;
    margin-top: 1.5rem;
}

/* Comment Form */
.blog-post-comment-form {
    margin-top: 2rem;
    background-color: #f9f9f9;
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.blog-post-comment-form-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.blog-post-comment-form form {
    display: grid;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #555;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 0.5rem;
    font-size: 0.95rem;
}

.form-group textarea {
    min-height: 150px;
    resize: vertical;
}

.form-submit {
    display: inline-block;
    background-color: #F37321;
    color: white;
    font-size: 0.95rem;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
    justify-self: start;
}

.form-submit:hover {
    background-color: #e06518;
}

/* Responsive */
@media (max-width: 768px) {
    .blog-post-with-sidebar {
        grid-template-columns: 1fr;
    }
    
    .blog-post-title {
        font-size: 1.75rem;
    }
    
    .blog-post-featured-image {
        height: 300px;
    }
    
    .blog-post-related-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .blog-post-author-box {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .blog-post-author-box-avatar {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .blog-post-author-box-social {
        justify-content: center;
    }
}

@media (max-width: 640px) {
    .blog-post-related-grid {
        grid-template-columns: 1fr;
    }
    
    .blog-post-meta {
        flex-wrap: wrap;
    }
    
    .blog-post-author {
        margin-bottom: 0.75rem;
    }
}
