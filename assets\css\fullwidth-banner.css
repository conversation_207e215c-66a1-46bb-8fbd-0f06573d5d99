/**
 * Fullwidth Banner CSS
 * CSS riêng cho banner toàn màn hình mà không ảnh hưởng đến header
 */

/* Ẩn nút mặc định của Swiper */
.swiper-button-next,
.swiper-button-prev {
    display: none;
}

/* Banner Section - Thiết lập full width được tối ưu trong fix-overflow.css */
.banner-section {
    /* <PERSON><PERSON><PERSON> thu<PERSON> t<PERSON> positioning được xử lý trong fix-overflow.css */
    margin-top: -1px; /* Di chuyển banner lên sát với header */
    box-sizing: border-box;
}

/* Sử dụng kỹ thuật vw/vh để tạo banner full width */
.banner-section .swiper-container,
.banner-section .swiper-wrapper,
.banner-section .swiper-slide {
    width: 100%;
    overflow: hidden;
}

/* Tối ưu hóa hiệu ứng fade cho Swiper */
.banner-swiper .swiper-slide {
    opacity: 0;
    /* Swiper sẽ kiểm soát opacity transition */
}

.banner-swiper .swiper-slide-active {
    opacity: 1;
}

/* Tối ưu hóa hiệu suất cho fade effect và ngăn flicker */
.banner-swiper .swiper-slide img {
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
    transition: transform 0.3s ease-out;
}

/* Đảm bảo slide wrapper mượt mà */
.banner-swiper .swiper-wrapper {
    transform-style: preserve-3d;
}

/* Ngăn chặn hiện tượng flash/flicker khi chuyển slide */
.banner-swiper .swiper-slide-duplicate {
    opacity: 0 !important;
}

/* Đảm bảo nội dung banner không bị tràn ra ngoài */
.banner-slide-inner {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 85vh !important;
    /* Chiều cao tối ưu - tăng từ 75vh lên 85vh */
}

/* Container trong banner được xử lý trong fix-overflow.css với responsive breakpoints */

/* Định vị các nút điều hướng */
.navigation-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 20;
    pointer-events: none;
    /* Cho phép click xuyên qua container nhưng không qua các nút */
}

.navigation-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background-color: rgba(255, 255, 255, 0.15);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: auto;
    /* Cho phép click vào nút */
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    opacity: 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 1.2rem;
}

.navigation-button:hover {
    background-color: #F37321;
    transform: translateY(-50%) scale(1.1);
    opacity: 1;
    box-shadow: 0 0 25px rgba(249, 115, 22, 0.4);
    border-color: rgba(255, 255, 255, 0.4);
}

.banner-section:hover .navigation-button {
    opacity: 0.85;
}

.navigation-button-prev {
    left: 25px;
}

.navigation-button-next {
    right: 25px;
}

/* Định vị dots (pagination) */
.swiper-pagination-custom {
    position: absolute;
    bottom: 30px;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 20;
    gap: 10px;
}

/* Hiệu ứng animation */
@keyframes fadeInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeInLeft {
    from {
        transform: translateX(-30px);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInRight {
    from {
        transform: translateX(30px);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(249, 115, 22, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(249, 115, 22, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(249, 115, 22, 0);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-8px);
    }

    100% {
        transform: translateY(0px);
    }
}

.custom-bullet {
    width: 12px;
    height: 12px;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    display: inline-block;
    margin: 0 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.custom-bullet::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    border-radius: 50%;
    transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.custom-bullet:hover::after {
    transform: scale(1.5);
    opacity: 0;
}

.custom-bullet.active {
    background-color: #F37321;
    width: 35px;
    border-radius: 12px;
    box-shadow: 0 0 10px rgba(249, 115, 22, 0.5);
}

.banner-content {
    opacity: 0;
    transform: translateY(30px);
    /* JavaScript sẽ kiểm soát hoàn toàn transition */
    will-change: opacity, transform;
    backface-visibility: hidden;
    transform-style: preserve-3d;
}

.banner-title {
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.02em;
    line-height: 1.2;
    position: relative;
    display: inline-block;
}

.banner-title:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #F37321, rgba(249, 115, 22, 0.3));
    border-radius: 4px;
}

/* Hiệu ứng shadow glow cho các phần tử */
.shadow-glow {
    box-shadow: 0 0 15px rgba(249, 115, 22, 0.5);
}

.shadow-glow-light {
    box-shadow: 0 0 18px rgba(255, 255, 255, 0.2);
}

/* Hiệu ứng gradient text */
.text-gradient {
    background: linear-gradient(90deg, #F37321, #FFB347);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
    position: relative;
    white-space: nowrap;
}

.text-gradient::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, rgba(249, 115, 22, 0.5), rgba(255, 179, 71, 0.2));
}

/* Hiệu ứng cho feature items */
.feature-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 0.75rem;
    background-color: rgba(255, 255, 255, 0.08);
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.15);
    position: relative;
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(249, 115, 22, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.5s ease;
}

.feature-item:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-4px);
    border-color: rgba(249, 115, 22, 0.4);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.feature-item:hover::before {
    transform: translateX(0);
}

.feature-item .fas {
    transition: transform 0.4s ease;
}

.feature-item:hover .fas {
    color: #F37321;
    transform: scale(1.2);
}

/* Hiệu ứng backdrop blur */
.backdrop-blur-sm {
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
}

.backdrop-blur-md {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

/* Hiệu ứng cho các nút CTA */
.cta-button {
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: all 0.4s ease;
    box-shadow: 0 4px 15px rgba(249, 115, 22, 0.3);
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.15);
    z-index: -1;
    transition: width 0.4s ease;
}

.cta-button::after {
    content: '';
    position: absolute;
    width: 15px;
    height: 100%;
    top: 0;
    right: -30px;
    background-color: rgba(255, 255, 255, 0.3);
    transform: skewX(-25deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.cta-button:hover::before {
    width: 100%;
}

.cta-button:hover::after {
    right: 110%;
    opacity: 0.3;
}

.cta-button:hover {
    transform: translateY(-5px) scale(1.03);
    box-shadow: 0 12px 25px rgba(249, 115, 22, 0.5);
    letter-spacing: 0.03em;
}

.cta-button-alt {
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: all 0.4s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cta-button-alt::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: transform 0.8s ease;
}

.cta-button-alt::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #F37321, rgba(255, 255, 255, 0.5));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
}

.cta-button-alt:hover::before {
    transform: translateX(100%);
}

.cta-button-alt:hover::after {
    transform: scaleX(1);
}

.cta-button-alt:hover {
    transform: translateY(-5px) scale(1.03);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
    letter-spacing: 0.03em;
}

.banner-content .fas {
    transition: transform 0.3s ease, color 0.3s ease;
}

.banner-content .feature-item:hover .fas {
    color: #F37321;
    transform: scale(1.2);
}

/* Thiết kế phần thông tin liên hệ */
.contact-info {
    transition: all 0.3s ease;
}

.contact-info>div {
    transition: all 0.3s ease;
}

.contact-info>div:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Thiết kế nút scroll down */
.scroll-down-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transform: translateY(0);
    animation: float 1.5s infinite ease-in-out;
}

.mouse {
    width: 30px;
    height: 50px;
    border: 2px solid white;
    border-radius: 20px;
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 5px;
}

.wheel {
    position: absolute;
    background-color: white;
    width: 6px;
    height: 10px;
    border-radius: 3px;
    top: 8px;
    animation: scroll 1.5s infinite;
}

@keyframes scroll {
    0% {
        opacity: 1;
        transform: translateY(0);
    }

    50% {
        opacity: 0.8;
    }

    100% {
        opacity: 0;
        transform: translateY(16px);
    }
}

.arrow-container {
    display: flex;
    justify-content: center;
}

.arrow-down {
    width: 10px;
    height: 10px;
    border-right: 2px solid white;
    border-bottom: 2px solid white;
    transform: rotate(45deg);
    animation: arrow-down 1.5s infinite;
    opacity: 0.8;
}

@keyframes arrow-down {
    0% {
        opacity: 0;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

/* Thiết kế nội dung trong banner có badge */
.banner-badge {
    position: relative;
    transition: all 0.4s ease;
}

.banner-badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.banner-badge .fas {
    animation: pulse 2s infinite;
}

/* Media queries - Responsive design */
@media (min-width: 1400px) {
    .banner-content {
        padding-left: 3rem;
    }

    .banner-title {
        font-size: 4.5rem;
    }

    .banner-title .text-gradient {
        font-size: 4.8rem;
    }

    .banner-features {
        margin-bottom: 2.5rem !important;
    }
}

@media (min-width: 1200px) and (max-width: 1399px) {
    .banner-content {
        padding-left: 2rem;
    }

    .banner-title {
        font-size: 4rem;
    }

    .banner-title .text-gradient {
        font-size: 4.2rem;
    }

    .banner-features {
        margin-bottom: 2rem !important;
    }
}

@media (max-width: 1199px) {
    .banner-title {
        font-size: 3.5rem;
    }

    .banner-features {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: flex-start;
        gap: 10px;
    }

    .feature-item {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }
}

@media (max-width: 991px) {
    .banner-slide-inner {
        height: 80vh !important;
    }

    .banner-content {
        padding-left: 1rem;
    }

    .banner-title {
        font-size: 3rem;
    }

    .banner-features {
        margin-bottom: 1.5rem !important;
    }

    .feature-item {
        font-size: 0.85rem;
        padding: 0.35rem 0.7rem;
    }

    .flex.flex-wrap.gap-3 {
        gap: 0.5rem !important;
    }

    .navigation-button {
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }
}

@media (max-width: 767px) {
    .banner-slide-inner {
        height: 75vh !important;
    }

    .banner-content {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .banner-title {
        font-size: 2.5rem;
    }

    .banner-title:after {
        width: 60px;
        height: 3px;
        bottom: -8px;
    }

    .banner-features {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 1.2rem !important;
    }

    .feature-item {
        width: 100%;
        max-width: 280px;
        margin-bottom: 0.5rem;
        font-size: 0.8rem;
    }

    .flex.flex-wrap.gap-3 {
        gap: 0.75rem !important;
        flex-direction: column;
        align-items: flex-start;
    }

    .flex.flex-wrap.gap-3 a {
        width: 100%;
        max-width: 280px;
        text-align: center;
    }

    .navigation-button {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }

    .contact-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 575px) {
    .banner-slide-inner {
        height: 70vh !important;
    }

    .banner-title {
        font-size: 2rem;
        line-height: 1.2;
    }

    .banner-title .text-gradient {
        font-size: 2.1rem;
    }

    .banner-content p {
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .banner-content .mb-4 {
        margin-bottom: 0.75rem !important;
    }

    .banner-content .mb-6 {
        margin-bottom: 1rem !important;
    }

    .cta-button,
    .cta-button-alt {
        padding: 0.5rem 1rem !important;
        font-size: 0.85rem !important;
    }

    .banner-content .fas {
        font-size: 0.9rem;
    }

    .banner-content .inline-flex {
        padding: 0.3rem 0.8rem !important;
        margin-bottom: 0.75rem !important;
    }

    .banner-content .mb-6.bg-black\/20 {
        padding: 0.5rem 0.75rem !important;
    }

    .navigation-button {
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
    }
}

@media (max-width: 374px) {
    .banner-slide-inner {
        height: 65vh !important;
    }

    .banner-title {
        font-size: 1.75rem;
        line-height: 1.2;
    }

    .banner-title .text-gradient {
        font-size: 1.85rem;
    }

    .banner-content p {
        font-size: 0.8rem;
    }

    .feature-item {
        padding: 0.3rem 0.6rem;
        font-size: 0.75rem;
    }

    .cta-button,
    .cta-button-alt {
        padding: 0.4rem 0.8rem !important;
        font-size: 0.8rem !important;
    }

    .banner-content .gap-2 {
        gap: 0.25rem !important;
    }

    .banner-content .inline-flex {
        padding: 0.25rem 0.6rem !important;
        font-size: 0.75rem !important;
    }
}