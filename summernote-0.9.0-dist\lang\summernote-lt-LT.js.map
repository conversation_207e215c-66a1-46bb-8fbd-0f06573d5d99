{"version": 3, "file": "lang/summernote-lt-LT.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,aAAa;QACnBC,MAAM,EAAE,UAAU;QAClBC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,gBAAgB;QACvBC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,oBAAoB;QAC1BC,aAAa,EAAE,aAAa;QAC5BC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE,UAAU;QACrBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,cAAc;QACrBC,MAAM,EAAE,qBAAqB;QAC7BC,UAAU,EAAE,cAAc;QAC1BC,UAAU,EAAE,oBAAoB;QAChCC,aAAa,EAAE,oBAAoB;QACnCC,SAAS,EAAE,qBAAqB;QAChCC,UAAU,EAAE,sBAAsB;QAClCC,SAAS,EAAE,iBAAiB;QAC5BC,YAAY,EAAE,wBAAwB;QACtCC,WAAW,EAAE,oBAAoB;QACjCC,cAAc,EAAE,mBAAmB;QACnCC,SAAS,EAAE,cAAc;QACzBC,aAAa,EAAE,yBAAyB;QACxCC,SAAS,EAAE,oBAAoB;QAC/BC,eAAe,EAAE,mBAAmB;QACpCC,eAAe,EAAE,wBAAwB;QACzCC,oBAAoB,EAAE,kCAAkC;QACxDC,GAAG,EAAE,0BAA0B;QAC/BC,MAAM,EAAE,sBAAsB;QAC9BC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,YAAY;QACvBpB,MAAM,EAAE,cAAc;QACtBgB,GAAG,EAAE,YAAY;QACjBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,SAAS;QACftB,MAAM,EAAE,iBAAiB;QACzBuB,MAAM,EAAE,mBAAmB;QAC3BC,IAAI,EAAE,WAAW;QACjBC,aAAa,EAAE,iBAAiB;QAChCT,GAAG,EAAE,gCAAgC;QACrCU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,eAAe;QAC5BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,CAAC,EAAE,KAAK;QACRC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,OAAO;QACZC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,wBAAwB;QACnCC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,qBAAqB;QACjCC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,WAAW;QACtBC,OAAO,EAAE,mBAAmB;QAC5BC,MAAM,EAAE,mBAAmB;QAC3BC,IAAI,EAAE,kBAAkB;QACxBC,MAAM,EAAE,mBAAmB;QAC3BC,KAAK,EAAE,mBAAmB;QAC1BC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,0BAA0B;QAClCC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE,aAAa;QACzBC,UAAU,EAAE,eAAe;QAC3BC,WAAW,EAAE,WAAW;QACxBC,cAAc,EAAE,iCAAiC;QACjDC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,oBAAoB;QAC/BC,KAAK,EAAE,UAAU;QACjBC,cAAc,EAAE,qBAAqB;QACrCC,MAAM,EAAE,UAAU;QAClBC,mBAAmB,EAAE,yBAAyB;QAC9CC,aAAa,EAAE,mBAAmB;QAClCC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,kBAAkB;QACrC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,yBAAyB;QACjC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,kBAAkB;QAC1B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,mBAAmB;QACzBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-lt-LT.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'lt-LT': {\n      font: {\n        bold: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        italic: '<PERSON><PERSON><PERSON><PERSON>',\n        underline: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        clear: '<PERSON> formatavimo',\n        height: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        name: '<PERSON><PERSON><PERSON> pavadinimas',\n        strikethrough: '<PERSON><PERSON><PERSON><PERSON>',\n        superscript: 'Vir<PERSON><PERSON><PERSON>',\n        subscript: 'Indeksas',\n        size: 'Šrifto dydis',\n      },\n      image: {\n        image: 'Pa<PERSON><PERSON><PERSON>l<PERSON><PERSON>',\n        insert: 'Įterpti paveikslėlį',\n        resizeFull: '<PERSON>ln<PERSON> dydis',\n        resizeHalf: 'Sumažinti dydį 50%',\n        resizeQuarter: 'Su<PERSON><PERSON>inti dydį 25%',\n        floatLeft: 'Kairinis lygiavimas',\n        floatRight: 'Dešininis lygiavimas',\n        floatNone: 'Jokio lygiavimo',\n        shapeRounded: 'Forma: apvalūs kraštai',\n        shapeCircle: 'Forma: apskritimas',\n        shapeThumbnail: 'Forma: miniatiūra',\n        shapeNone: 'Forma: jokia',\n        dragImageHere: 'Vilkite paveikslėlį čia',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Pasirinkite failą',\n        maximumFileSize: 'Maskimalus failo dydis',\n        maximumFileSizeError: 'Maskimalus failo dydis viršytas!',\n        url: 'Paveikslėlio URL adresas',\n        remove: 'Ištrinti paveikslėlį',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video Link',\n        insert: 'Insert Video',\n        url: 'Video URL?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'Nuoroda',\n        insert: 'Įterpti nuorodą',\n        unlink: 'Pašalinti nuorodą',\n        edit: 'Redaguoti',\n        textToDisplay: 'Rodomas tekstas',\n        url: 'Koks URL adresas yra susietas?',\n        openInNewWindow: 'Atidaryti naujame lange',\n      },\n      table: {\n        table: 'Lentelė',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Įterpti horizontalią liniją',\n      },\n      style: {\n        style: 'Stilius',\n        p: 'pus',\n        blockquote: 'Citata',\n        pre: 'Kodas',\n        h1: 'Antraštė 1',\n        h2: 'Antraštė 2',\n        h3: 'Antraštė 3',\n        h4: 'Antraštė 4',\n        h5: 'Antraštė 5',\n        h6: 'Antraštė 6',\n      },\n      lists: {\n        unordered: 'Suženklintasis sąrašas',\n        ordered: 'Sunumeruotas sąrašas',\n      },\n      options: {\n        help: 'Pagalba',\n        fullscreen: 'Viso ekrano režimas',\n        codeview: 'HTML kodo peržiūra',\n      },\n      paragraph: {\n        paragraph: 'Pastraipa',\n        outdent: 'Sumažinti įtrauką',\n        indent: 'Padidinti įtrauką',\n        left: 'Kairinė lygiuotė',\n        center: 'Centrinė lygiuotė',\n        right: 'Dešininė lygiuotė',\n        justify: 'Abipusis išlyginimas',\n      },\n      color: {\n        recent: 'Paskutinė naudota spalva',\n        more: 'Daugiau spalvų',\n        background: 'Fono spalva',\n        foreground: 'Šrifto spalva',\n        transparent: 'Permatoma',\n        setTransparent: 'Nustatyti skaidrumo intensyvumą',\n        reset: 'Atkurti',\n        resetToDefault: 'Atstatyti numatytąją spalvą',\n      },\n      shortcut: {\n        shortcuts: 'Spartieji klavišai',\n        close: 'Uždaryti',\n        textFormatting: 'Teksto formatavimas',\n        action: 'Veiksmas',\n        paragraphFormatting: 'Pastraipos formatavimas',\n        documentStyle: 'Dokumento stilius',\n        extraKeys: 'Papildomi klavišų deriniai',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Anuliuoti veiksmą',\n        redo: 'Perdaryti veiksmą',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "superscript", "subscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}