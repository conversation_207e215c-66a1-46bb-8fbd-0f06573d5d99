{"version": 3, "file": "lang/summernote-fa-IR.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,OAAO;QACfC,SAAS,EAAE,SAAS;QACpBC,KAAK,EAAE,oBAAoB;QAC3BC,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,UAAU;QAChBC,aAAa,EAAE,UAAU;QACzBC,SAAS,EAAE,SAAS;QACpBC,WAAW,EAAE,WAAW;QACxBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,iBAAiB;QACzBC,UAAU,EAAE,wBAAwB;QACpCC,UAAU,EAAE,qBAAqB;QACjCC,aAAa,EAAE,0BAA0B;QACzCC,SAAS,EAAE,eAAe;QAC1BC,UAAU,EAAE,iBAAiB;QAC7BC,SAAS,EAAE,cAAc;QACzBC,YAAY,EAAE,UAAU;QACxBC,WAAW,EAAE,YAAY;QACzBC,cAAc,EAAE,iBAAiB;QACjCC,SAAS,EAAE,cAAc;QACzBC,aAAa,EAAE,yBAAyB;QACxCC,SAAS,EAAE,0BAA0B;QACrCC,eAAe,EAAE,wBAAwB;QACzCC,eAAe,EAAE,sBAAsB;QACvCC,oBAAoB,EAAE,sCAAsC;QAC5DC,GAAG,EAAE,YAAY;QACjBC,MAAM,EAAE,WAAW;QACnBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,YAAY;QACvBpB,MAAM,EAAE,cAAc;QACtBgB,GAAG,EAAE,cAAc;QACnBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,iBAAiB;QACzBuB,MAAM,EAAE,UAAU;QAClBC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE,eAAe;QAC9BT,GAAG,EAAE,kCAAkC;QACvCU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,WAAW,EAAE,kBAAkB;QAC/BC,WAAW,EAAE,mBAAmB;QAChCC,UAAU,EAAE,gBAAgB;QAC5BC,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE,UAAU;QAClBC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,OAAO;QACVC,UAAU,EAAE,SAAS;QACrBC,GAAG,EAAE,IAAI;QACTC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,iBAAiB;QAC5BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,iBAAiB;QAC7BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,eAAe;QACxBC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,QAAQ;QACdC,MAAM,EAAE,UAAU;QAClBC,KAAK,EAAE,UAAU;QACjBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,uBAAuB;QAC/BC,IAAI,EAAE,WAAW;QACjBC,UAAU,EAAE,cAAc;QAC1BC,UAAU,EAAE,SAAS;QACrBC,WAAW,EAAE,QAAQ;QACrBC,cAAc,EAAE,mBAAmB;QACnCC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,kBAAkB;QAC7BC,KAAK,EAAE,MAAM;QACbC,cAAc,EAAE,UAAU;QAC1BC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,eAAe;QACpCC,aAAa,EAAE,WAAW;QAC1BC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,iBAAiB;QACpC,MAAM,EAAE,2BAA2B;QACnC,MAAM,EAAE,iCAAiC;QACzC,KAAK,EAAE,IAAI;QACX,OAAO,EAAE,QAAQ;QACjB,MAAM,EAAE,mBAAmB;QAC3B,QAAQ,EAAE,mBAAmB;QAC7B,WAAW,EAAE,wBAAwB;QACrC,eAAe,EAAE,uBAAuB;QACxC,cAAc,EAAE,mBAAmB;QACnC,aAAa,EAAE,QAAQ;QACvB,eAAe,EAAE,SAAS;QAC1B,cAAc,EAAE,UAAU;QAC1B,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,yBAAyB;QAChD,mBAAmB,EAAE,sBAAsB;QAC3C,SAAS,EAAE,sBAAsB;QACjC,QAAQ,EAAE,+BAA+B;QACzC,YAAY,EAAE,0BAA0B;QACxC,UAAU,EAAE,2BAA2B;QACvC,UAAU,EAAE,2BAA2B;QACvC,UAAU,EAAE,2BAA2B;QACvC,UAAU,EAAE,2BAA2B;QACvC,UAAU,EAAE,2BAA2B;QACvC,UAAU,EAAE,2BAA2B;QACvC,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,aAAa;QAC1BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-fa-IR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'fa-IR': {\n      font: {\n        bold: 'درشت',\n        italic: 'خمیده',\n        underline: 'میان خط',\n        clear: 'پاک کردن فرمت فونت',\n        height: 'فاصله ی خطی',\n        name: 'اسم فونت',\n        strikethrough: 'خط خورده',\n        subscript: 'زیرنویس',\n        superscript: 'بالا نویس',\n        size: 'اندازه ی فونت',\n      },\n      image: {\n        image: 'تصویر',\n        insert: 'وارد کردن تصویر',\n        resizeFull: 'تغییر به اندازه ی کامل',\n        resizeHalf: 'تغییر به اندازه نصف',\n        resizeQuarter: 'تغییر به اندازه یک چهارم',\n        floatLeft: 'چسباندن به چپ',\n        floatRight: 'چسباندن به راست',\n        floatNone: 'بدون چسبندگی',\n        shapeRounded: 'شکل: گرد',\n        shapeCircle: 'شکل: دایره',\n        shapeThumbnail: 'شکل: تصویر کوچک',\n        shapeNone: 'شکل: هیچکدام',\n        dragImageHere: 'یک تصویر را اینجا بکشید',\n        dropImage: 'تصویر یا متن را رها کنید',\n        selectFromFiles: 'فایل ها را انتخاب کنید',\n        maximumFileSize: 'حداکثر اندازه پرونده',\n        maximumFileSizeError: 'از حداکثر اندازه فایل بیشتر شده است.',\n        url: 'آدرس تصویر',\n        remove: 'حذف تصویر',\n        original: 'اصلی',\n      },\n      video: {\n        video: 'ویدیو',\n        videoLink: 'لینک ویدیو',\n        insert: 'افزودن ویدیو',\n        url: 'آدرس ویدیو ؟',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion یا Youku)',\n      },\n      link: {\n        link: 'لینک',\n        insert: 'اضافه کردن لینک',\n        unlink: 'حذف لینک',\n        edit: 'ویرایش',\n        textToDisplay: 'متن جهت نمایش',\n        url: 'این لینک به چه آدرسی باید برود ؟',\n        openInNewWindow: 'در یک پنجره ی جدید باز شود',\n      },\n      table: {\n        table: 'جدول',\n        addRowAbove: 'افزودن ردیف بالا',\n        addRowBelow: 'افزودن ردیف پایین',\n        addColLeft: 'افزودن ستون چپ',\n        addColRight: 'افزودن ستون راست',\n        delRow: 'حذف ردیف',\n        delCol: 'حذف ستون',\n        delTable: 'حذف جدول',\n      },\n      hr: {\n        insert: 'افزودن خط افقی',\n      },\n      style: {\n        style: 'استیل',\n        p: 'نرمال',\n        blockquote: 'نقل قول',\n        pre: 'کد',\n        h1: 'سرتیتر 1',\n        h2: 'سرتیتر 2',\n        h3: 'سرتیتر 3',\n        h4: 'سرتیتر 4',\n        h5: 'سرتیتر 5',\n        h6: 'سرتیتر 6',\n      },\n      lists: {\n        unordered: 'لیست غیر ترتیبی',\n        ordered: 'لیست ترتیبی',\n      },\n      options: {\n        help: 'راهنما',\n        fullscreen: 'نمایش تمام صفحه',\n        codeview: 'مشاهده ی کد',\n      },\n      paragraph: {\n        paragraph: 'پاراگراف',\n        outdent: 'کاهش تو رفتگی',\n        indent: 'افزایش تو رفتگی',\n        left: 'چپ چین',\n        center: 'میان چین',\n        right: 'راست چین',\n        justify: 'بلوک چین',\n      },\n      color: {\n        recent: 'رنگ اخیرا استفاده شده',\n        more: 'رنگ بیشتر',\n        background: 'رنگ پس زمینه',\n        foreground: 'رنگ متن',\n        transparent: 'بی رنگ',\n        setTransparent: 'تنظیم حالت بی رنگ',\n        reset: 'بازنشاندن',\n        resetToDefault: 'حالت پیش فرض',\n      },\n      shortcut: {\n        shortcuts: 'دکمه های میان بر',\n        close: 'بستن',\n        textFormatting: 'فرمت متن',\n        action: 'عملیات',\n        paragraphFormatting: 'فرمت پاراگراف',\n        documentStyle: 'استیل سند',\n        extraKeys: 'کلیدهای اضافی',\n      },\n      help: {\n        'insertParagraph': 'افزودن پاراگراف',\n        'undo': 'آخرین فرمان را لغو می کند',\n        'redo': 'دستور آخر را دوباره اجرا می کند',\n        'tab': 'تب',\n        'untab': 'لغو تب',\n        'bold': 'استایل ضخیم میدهد',\n        'italic': 'استایل مورب میدهد',\n        'underline': 'استایل زیرخط دار میدهد',\n        'strikethrough': 'استایل خط خورده میدهد',\n        'removeFormat': 'حذف همه استایل ها',\n        'justifyLeft': 'چپ چین',\n        'justifyCenter': 'وسط چین',\n        'justifyRight': 'راست چین',\n        'justifyFull': 'چینش در کل عرض',\n        'insertUnorderedList': 'تغییر بع لیست غیرترتیبی',\n        'insertOrderedList': 'تغییر بع لیست ترتیبی',\n        'outdent': 'گذر از پاراگراف فعلی',\n        'indent': 'قرارگیری بر روی پاراگراف جاری',\n        'formatPara': 'تغییر فرمت متن به تگ <p>',\n        'formatH1': 'تغییر فرمت متن به تگ <h1>',\n        'formatH2': 'تغییر فرمت متن به تگ <h2>',\n        'formatH3': 'تغییر فرمت متن به تگ <h3>',\n        'formatH4': 'تغییر فرمت متن به تگ <h4>',\n        'formatH5': 'تغییر فرمت متن به تگ <h5>',\n        'formatH6': 'تغییر فرمت متن به تگ <h6>',\n        'insertHorizontalRule': 'وارد کردن به صورت افقی',\n        'linkDialog.show': 'نمایش پیام لینک',\n      },\n      history: {\n        undo: 'واچیدن',\n        redo: 'بازچیدن',\n      },\n      specialChar: {\n        specialChar: 'کاراکتر خاص',\n        select: 'انتخاب کاراکتر خاص',\n      },      \n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}