{"version": 3, "file": "lang/summernote-pt-PT.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,YAAY;QACvBC,KAAK,EAAE,yBAAyB;QAChCC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,OAAO;QACbC,aAAa,EAAE,SAAS;QACxBC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,aAAa;QAC1BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE,gBAAgB;QACxBC,UAAU,EAAE,wBAAwB;QACpCC,UAAU,EAAE,sBAAsB;QAClCC,aAAa,EAAE,yBAAyB;QACxCC,SAAS,EAAE,gBAAgB;QAC3BC,UAAU,EAAE,eAAe;QAC3BC,SAAS,EAAE,WAAW;QACtBC,YAAY,EAAE,oBAAoB;QAClCC,WAAW,EAAE,gBAAgB;QAC7BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,eAAe;QAC1BC,aAAa,EAAE,8BAA8B;QAC7CC,SAAS,EAAE,6BAA6B;QACxCC,eAAe,EAAE,iCAAiC;QAClDC,eAAe,EAAE,2BAA2B;QAC5CC,oBAAoB,EAAE,oDAAoD;QAC1EC,GAAG,EAAE,oBAAoB;QACzBC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,iBAAiB;QAC5BpB,MAAM,EAAE,eAAe;QACvBgB,GAAG,EAAE,eAAe;QACpBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,iBAAiB;QACzBuB,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE,mBAAmB;QAClCT,GAAG,EAAE,iCAAiC;QACtCU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,uBAAuB;QACpCC,WAAW,EAAE,wBAAwB;QACrCC,UAAU,EAAE,6BAA6B;QACzCC,WAAW,EAAE,6BAA6B;QAC1CC,MAAM,EAAE,eAAe;QACvBC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,CAAC,EAAE,WAAW;QACdC,UAAU,EAAE,SAAS;QACrBC,GAAG,EAAE,QAAQ;QACbC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,sBAAsB;QACjCC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,iBAAiB;QAC7BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,WAAW;QACtBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,oBAAoB;QAC1BC,MAAM,EAAE,mBAAmB;QAC3BC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE,OAAO;QACnBC,UAAU,EAAE,OAAO;QACnBC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,oBAAoB;QACpCC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE,kBAAkB;QAClCC,QAAQ,EAAE;MACZ,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,oBAAoB;QAC/BC,KAAK,EAAE,QAAQ;QACfC,cAAc,EAAE,qBAAqB;QACrCC,MAAM,EAAE,MAAM;QACdC,mBAAmB,EAAE,yBAAyB;QAC9CC,aAAa,EAAE;MACjB,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,mBAAmB;QACtC,MAAM,EAAE,2BAA2B;QACnC,MAAM,EAAE,0BAA0B;QAClC,KAAK,EAAE,iBAAiB;QACxB,OAAO,EAAE,iBAAiB;QAC1B,MAAM,EAAE,oBAAoB;QAC5B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,oBAAoB;QACrC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,6BAA6B;QAC5C,eAAe,EAAE,4BAA4B;QAC7C,cAAc,EAAE,4BAA4B;QAC5C,aAAa,EAAE,qBAAqB;QACpC,qBAAqB,EAAE,6BAA6B;QACpD,mBAAmB,EAAE,yBAAyB;QAC9C,SAAS,EAAE,wBAAwB;QACnC,QAAQ,EAAE,yBAAyB;QACnC,YAAY,EAAE,yCAAyC;QACvD,UAAU,EAAE,wCAAwC;QACpD,UAAU,EAAE,wCAAwC;QACpD,UAAU,EAAE,wCAAwC;QACpD,UAAU,EAAE,wCAAwC;QACpD,UAAU,EAAE,wCAAwC;QACpD,UAAU,EAAE,wCAAwC;QACpD,sBAAsB,EAAE,0BAA0B;QAClD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-pt-PT.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'pt-PT': {\n      font: {\n        bold: 'Negrito',\n        italic: 'Itálico',\n        underline: 'Sublinhado',\n        clear: 'Remover estilo da fonte',\n        height: '<PERSON>ura da linha',\n        name: '<PERSON><PERSON>',\n        strikethrough: 'Risca<PERSON>',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Tamanho da fonte',\n      },\n      image: {\n        image: 'Imagem',\n        insert: 'Inserir imagem',\n        resizeFull: 'Redimensionar Completo',\n        resizeHalf: 'Redimensionar Metade',\n        resizeQuarter: 'Redimensionar Um Quarto',\n        floatLeft: 'Float Esquerda',\n        floatRight: 'Float Direita',\n        floatNone: 'Sem Float',\n        shapeRounded: 'Forma: Arredondado',\n        shapeCircle: 'Forma: Círculo',\n        shapeThumbnail: 'Forma: Minhatura',\n        shapeNone: 'Forma: Nenhum',\n        dragImageHere: 'Arraste uma imagem para aqui',\n        dropImage: 'Arraste uma imagem ou texto',\n        selectFromFiles: 'Selecione a partir dos arquivos',\n        maximumFileSize: 'Tamanho máximo do fixeiro',\n        maximumFileSizeError: 'Tamanho máximo do fixeiro é maior que o permitido.',\n        url: 'Endereço da imagem',\n        remove: 'Remover Imagem',\n        original: 'Original',\n      },\n      video: {\n        video: 'Vídeo',\n        videoLink: 'Link para vídeo',\n        insert: 'Inserir vídeo',\n        url: 'URL do vídeo?',\n        providers: '(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Inserir ligação',\n        unlink: 'Remover ligação',\n        edit: 'Editar',\n        textToDisplay: 'Texto para exibir',\n        url: 'Que endereço esta licação leva?',\n        openInNewWindow: 'Abrir numa nova janela',\n      },\n      table: {\n        table: 'Tabela',\n        addRowAbove: 'Adicionar linha acima',\n        addRowBelow: 'Adicionar linha abaixo',\n        addColLeft: 'Adicionar coluna à Esquerda',\n        addColRight: 'Adicionar coluna à Esquerda',\n        delRow: 'Excluir linha',\n        delCol: 'Excluir coluna',\n        delTable: 'Excluir tabela',\n      },\n      hr: {\n        insert: 'Inserir linha horizontal',\n      },\n      style: {\n        style: 'Estilo',\n        p: 'Parágrafo',\n        blockquote: 'Citação',\n        pre: 'Código',\n        h1: 'Título 1',\n        h2: 'Título 2',\n        h3: 'Título 3',\n        h4: 'Título 4',\n        h5: 'Título 5',\n        h6: 'Título 6',\n      },\n      lists: {\n        unordered: 'Lista com marcadores',\n        ordered: 'Lista numerada',\n      },\n      options: {\n        help: 'Ajuda',\n        fullscreen: 'Janela Completa',\n        codeview: 'Ver código-fonte',\n      },\n      paragraph: {\n        paragraph: 'Parágrafo',\n        outdent: 'Menor tabulação',\n        indent: 'Maior tabulação',\n        left: 'Alinhar à esquerda',\n        center: 'Alinhar ao centro',\n        right: 'Alinha à direita',\n        justify: 'Justificado',\n      },\n      color: {\n        recent: 'Cor recente',\n        more: 'Mais cores',\n        background: 'Fundo',\n        foreground: 'Fonte',\n        transparent: 'Transparente',\n        setTransparent: 'Fundo transparente',\n        reset: 'Restaurar',\n        resetToDefault: 'Restaurar padrão',\n        cpSelect: 'Selecionar',\n      },\n      shortcut: {\n        shortcuts: 'Atalhos do teclado',\n        close: 'Fechar',\n        textFormatting: 'Formatação de texto',\n        action: 'Ação',\n        paragraphFormatting: 'Formatação de parágrafo',\n        documentStyle: 'Estilo de documento',\n      },\n      help: {\n        'insertParagraph': 'Inserir Parágrafo',\n        'undo': 'Desfazer o último comando',\n        'redo': 'Refazer o último comando',\n        'tab': 'Maior tabulação',\n        'untab': 'Menor tabulação',\n        'bold': 'Colocar em negrito',\n        'italic': 'Colocar em itálico',\n        'underline': 'Colocar em sublinhado',\n        'strikethrough': 'Colocar em riscado',\n        'removeFormat': 'Limpar o estilo',\n        'justifyLeft': 'Definir alinhado à esquerda',\n        'justifyCenter': 'Definir alinhado ao centro',\n        'justifyRight': 'Definir alinhado à direita',\n        'justifyFull': 'Definir justificado',\n        'insertUnorderedList': 'Alternar lista não ordenada',\n        'insertOrderedList': 'Alternar lista ordenada',\n        'outdent': 'Recuar parágrafo atual',\n        'indent': 'Avançar parágrafo atual',\n        'formatPara': 'Alterar formato do bloco para parágrafo',\n        'formatH1': 'Alterar formato do bloco para Título 1',\n        'formatH2': 'Alterar formato do bloco para Título 2',\n        'formatH3': 'Alterar formato do bloco para Título 3',\n        'formatH4': 'Alterar formato do bloco para Título 4',\n        'formatH5': 'Alterar formato do bloco para Título 5',\n        'formatH6': 'Alterar formato do bloco para Título 6',\n        'insertHorizontalRule': 'Inserir linha horizontal',\n        'linkDialog.show': 'Inserir uma ligração',\n      },\n      history: {\n        undo: 'Desfazer',\n        redo: 'Refazer',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}