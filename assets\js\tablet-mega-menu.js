/**
 * Tablet Mega Menu JavaScript for Nội Thất Bàng Vũ
 * Hybrid Approach: Modal for Portrait, Slide-out for Landscape
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Tablet mega menu script loaded');
    // Smart tablet detection - includes both portrait and landscape
    function isTablet() {
        const width = window.innerWidth;
        const height = window.innerHeight;

        // Check if it's in tablet range (either dimension between 768-1200)
        const isTabletSize = (width >= 768 && width <= 1200) || (height >= 768 && height <= 1200);

        // Additional check for touch capability
        const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

        return isTabletSize && isTouchDevice;
    }

    // Also check on resize to handle orientation changes
    function checkTabletStatus() {
        const result = isTablet();
        console.log('Tablet status check:', {
            width: window.innerWidth,
            height: window.innerHeight,
            isTabletSize: (window.innerWidth >= 768 && window.innerWidth <= 1200) || (window.innerHeight >= 768 && window.innerHeight <= 1200),
            isTouchDevice: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
            result: result
        });
        return result;
    }

    if (!checkTabletStatus()) {
        console.log('Not a tablet device, exiting tablet mega menu initialization');
        return;
    }

    console.log('Tablet mega menu initialized for touch device');

    // Mark body as touch device for CSS targeting
    document.body.classList.add('touch-device');

    // Get elements
    const productsNavItem = document.querySelector('.nav-item:has(.mega-menu)');
    const productsNavLink = productsNavItem?.querySelector('.nav-link');

    if (!productsNavItem || !productsNavLink) {
        console.log('Products nav item not found');
        return;
    }

    // Handle orientation changes
    window.addEventListener('orientationchange', function() {
        setTimeout(function() {
            if (!checkTabletStatus()) {
                // If no longer tablet, close any open mega menu
                closeTabletMegaMenu();
                return;
            }
            console.log('Orientation changed, tablet mega menu still active');
        }, 100);
    });

    // Handle resize events
    window.addEventListener('resize', function() {
        if (!checkTabletStatus()) {
            closeTabletMegaMenu();
            return;
        }
    });
    
    // Create tablet mega menu if it doesn't exist
    let tabletMegaMenu = document.getElementById('tablet-mega-menu');
    if (!tabletMegaMenu) {
        try {
            createTabletMegaMenu();
            tabletMegaMenu = document.getElementById('tablet-mega-menu');
            console.log('Tablet mega menu created successfully');
        } catch (error) {
            console.error('Error creating tablet mega menu:', error);
            return;
        }
    }
    
    // Function to create tablet mega menu HTML
    function createTabletMegaMenu() {
        // Get categories data from the existing mega menu
        const existingMegaMenu = document.querySelector('.mega-menu');
        const categories = [];

        if (existingMegaMenu) {
            const categoryElements = existingMegaMenu.querySelectorAll('.mega-menu-category');
            categoryElements.forEach(categoryEl => {
                const icon = categoryEl.querySelector('.mega-menu-category-icon i')?.className || 'fas fa-cube';
                const name = categoryEl.querySelector('.mega-menu-category-name')?.textContent || '';
                const categoryId = categoryEl.getAttribute('data-category-id') || '';

                // Get corresponding content element
                const contentId = `category-content-${categoryId}`;
                const contentEl = document.getElementById(contentId);
                let slug = '';
                let subcategories = [];
                let bestsellers = [];

                if (contentEl) {
                    // Get slug from "View All" link
                    const viewAllLink = contentEl.querySelector('.mega-menu-view-all');
                    if (viewAllLink) {
                        const href = viewAllLink.getAttribute('href');
                        const slugMatch = href?.match(/slug=([^&]+)/);
                        slug = slugMatch ? slugMatch[1] : '';
                    }

                    // Get subcategories
                    const subcategoryElements = contentEl.querySelectorAll('.mega-menu-subcategory');
                    subcategoryElements.forEach(subEl => {
                        const subName = subEl.querySelector('.mega-menu-subcategory-name')?.textContent || '';
                        const subHref = subEl.getAttribute('href') || '';
                        const subSlugMatch = subHref.match(/slug=([^&]+)/);
                        const subSlug = subSlugMatch ? subSlugMatch[1] : '';

                        // Get image or icon
                        const subImage = subEl.querySelector('.mega-menu-subcategory-image img');
                        const subIcon = subEl.querySelector('.mega-menu-subcategory-icon i');

                        if (subName) {
                            subcategories.push({
                                name: subName,
                                slug: subSlug,
                                image: subImage ? subImage.src : null,
                                icon: subIcon ? subIcon.className : 'fas fa-cube'
                            });
                        }
                    });

                    // Get bestseller products
                    const productElements = contentEl.querySelectorAll('.mega-menu-product');
                    productElements.forEach(prodEl => {
                        const prodName = prodEl.querySelector('.mega-menu-product-name')?.textContent || '';
                        const prodPrice = prodEl.querySelector('.mega-menu-product-price')?.textContent || '';
                        const prodHref = prodEl.getAttribute('href') || '';
                        const prodImage = prodEl.querySelector('.mega-menu-product-image img');

                        if (prodName) {
                            bestsellers.push({
                                name: prodName,
                                price: prodPrice,
                                href: prodHref,
                                image: prodImage ? prodImage.src : null
                            });
                        }
                    });
                }

                if (name) {
                    categories.push({
                        id: categoryId,
                        name: name,
                        icon: icon,
                        slug: slug || categoryId,
                        subcategories: subcategories,
                        bestsellers: bestsellers
                    });
                }
            });
        }

        // Fallback categories if none found
        if (categories.length === 0) {
            categories.push(
                {
                    id: 'phong-ngu',
                    name: 'Phòng ngủ',
                    icon: 'fas fa-bed',
                    slug: 'phong-ngu',
                    subcategories: [
                        { name: 'Giường ngủ', slug: 'giuong-ngu', icon: 'fas fa-bed' },
                        { name: 'Tủ quần áo', slug: 'tu-quan-ao', icon: 'fas fa-archive' }
                    ],
                    bestsellers: [
                        { name: 'Giường gỗ cao cấp', price: '15,000,000₫', href: BASE_URL + '/products.php' }
                    ]
                },
                {
                    id: 'phong-khach',
                    name: 'Phòng khách',
                    icon: 'fas fa-couch',
                    slug: 'phong-khach',
                    subcategories: [
                        { name: 'Sofa', slug: 'sofa', icon: 'fas fa-couch' },
                        { name: 'Bàn trà', slug: 'ban-tra', icon: 'fas fa-table' }
                    ],
                    bestsellers: [
                        { name: 'Sofa da thật', price: '25,000,000₫', href: BASE_URL + '/products.php' }
                    ]
                },
                {
                    id: 'phong-an',
                    name: 'Phòng ăn',
                    icon: 'fas fa-utensils',
                    slug: 'phong-an',
                    subcategories: [
                        { name: 'Bàn ăn', slug: 'ban-an', icon: 'fas fa-table' },
                        { name: 'Ghế ăn', slug: 'ghe-an', icon: 'fas fa-chair' }
                    ],
                    bestsellers: [
                        { name: 'Bộ bàn ăn 6 ghế', price: '12,000,000₫', href: BASE_URL + '/products.php' }
                    ]
                }
            );
        }

        // Create the tablet mega menu HTML with desktop-like 2-column layout
        const tabletMegaMenuHTML = `
            <div id="tablet-mega-menu" class="tablet-mega-menu" role="dialog" aria-modal="true" aria-labelledby="tablet-mega-menu-title" aria-hidden="true">
                <div class="tablet-mega-menu-content">
                    <div class="tablet-mega-menu-header">
                        <h3 id="tablet-mega-menu-title" class="tablet-mega-menu-title">Danh mục sản phẩm</h3>
                        <button class="tablet-mega-menu-close" aria-label="Đóng menu danh mục sản phẩm">
                            <i class="fas fa-times" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="tablet-mega-menu-body">
                        <!-- Desktop-like 2-column layout -->
                        <div class="tablet-mega-menu-main">
                            <!-- Left Column - Categories -->
                            <div class="tablet-mega-menu-categories">
                                ${categories.map((category, index) => `
                                    <div class="tablet-mega-category ${index === 0 ? 'active' : ''}"
                                         data-category-id="${category.id}"
                                         data-category-slug="${category.slug}">
                                        <div class="tablet-mega-category-icon">
                                            <i class="${category.icon}"></i>
                                        </div>
                                        <div class="tablet-mega-category-name">${category.name}</div>
                                    </div>
                                `).join('')}
                            </div>

                            <!-- Right Column - Content -->
                            <div class="tablet-mega-menu-content-area">
                                ${categories.map((category, index) => `
                                    <div class="tablet-mega-content ${index === 0 ? 'active' : ''}"
                                         id="tablet-content-${category.id}">
                                        <h3 class="tablet-mega-content-title">${category.name}</h3>

                                        ${category.subcategories.length > 0 ? `
                                            <div class="tablet-mega-subcategories">
                                                ${category.subcategories.map(sub => `
                                                    <a href="${BASE_URL}/category.php?slug=${sub.slug}"
                                                       class="tablet-mega-subcategory">
                                                        ${sub.image ?
                                                            `<div class="tablet-mega-subcategory-image">
                                                                <img data-src="${sub.image}" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48'%3E%3Crect width='48' height='48' fill='%23f3f4f6'/%3E%3C/svg%3E" alt="${sub.name}" loading="lazy">
                                                            </div>` :
                                                            `<div class="tablet-mega-subcategory-icon">
                                                                <i class="${sub.icon}"></i>
                                                            </div>`
                                                        }
                                                        <div class="tablet-mega-subcategory-name">${sub.name}</div>
                                                    </a>
                                                `).join('')}
                                            </div>
                                        ` : ''}

                                        ${category.bestsellers.length > 0 ? `
                                            <div class="tablet-mega-bestsellers">
                                                <h4 class="tablet-mega-bestsellers-title">
                                                    <i class="fas fa-fire"></i> Sản phẩm bán chạy
                                                </h4>
                                                <div class="tablet-mega-products">
                                                    ${category.bestsellers.slice(0, 4).map(product => `
                                                        <a href="${product.href}" class="tablet-mega-product">
                                                            <div class="tablet-mega-product-image">
                                                                ${product.image ?
                                                                    `<img data-src="${product.image}" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80'%3E%3Crect width='80' height='80' fill='%23f3f4f6'/%3E%3C/svg%3E" alt="${product.name}" loading="lazy">` :
                                                                    `<div class="tablet-mega-product-placeholder">
                                                                        <i class="fas fa-image"></i>
                                                                    </div>`
                                                                }
                                                            </div>
                                                            <div class="tablet-mega-product-name">${product.name}</div>
                                                            <div class="tablet-mega-product-price">${product.price}</div>
                                                        </a>
                                                    `).join('')}
                                                </div>
                                            </div>
                                        ` : ''}

                                        <a href="${BASE_URL}/category.php?slug=${category.slug}" class="tablet-mega-view-all">
                                            Xem tất cả <i class="fas fa-arrow-right"></i>
                                        </a>
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <!-- Fallback simple layout for very small tablets -->
                        <div class="tablet-mega-simple-layout" style="display: none;">
                            ${categories.map(category => `
                                <a href="${BASE_URL}/category.php?slug=${category.slug}" class="tablet-mega-simple-item">
                                    <div class="tablet-mega-simple-icon">
                                        <i class="${category.icon}"></i>
                                    </div>
                                    <div class="tablet-mega-simple-name">${category.name}</div>
                                </a>
                            `).join('')}
                        </div>

                        <!-- All Products Button -->
                        <div class="tablet-mega-all-products-section">
                            <a href="${BASE_URL}/products.php" class="tablet-mega-all-products-btn">
                                <i class="fas fa-th-large"></i>
                                <span>Xem tất cả sản phẩm</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Insert into DOM
        document.body.insertAdjacentHTML('beforeend', tabletMegaMenuHTML);

        // Setup category switching functionality
        setupCategorySwitching();



        // Setup scroll lock functionality
        setupScrollLock();
    }



    // Function to setup category switching functionality
    function setupCategorySwitching() {
        const categories = document.querySelectorAll('.tablet-mega-category');
        const contents = document.querySelectorAll('.tablet-mega-content');
        let animationTimeout = null;

        categories.forEach(category => {
            category.addEventListener('click', function(e) {
                e.preventDefault();

                const categoryId = this.getAttribute('data-category-id');

                // Don't do anything if already active
                if (this.classList.contains('active')) {
                    return;
                }

                // Clear any existing animation timeout
                if (animationTimeout) {
                    clearTimeout(animationTimeout);
                    animationTimeout = null;
                }

                // Remove active class from all contents without changing overflow
                contents.forEach(content => {
                    content.classList.remove('active', 'animation-complete');
                });

                // Remove active class from all categories and clear inline styles
                categories.forEach(cat => {
                    cat.classList.remove('active');
                    cat.style.backgroundColor = ''; // Clear inline hover style
                });

                // Add active class to clicked category and clear any inline styles
                this.classList.add('active');
                this.style.backgroundColor = ''; // Clear inline hover style

                // Show corresponding content immediately without delay
                const targetContent = document.getElementById(`tablet-content-${categoryId}`);
                if (targetContent) {
                    targetContent.classList.add('active');

                    // Reset scroll position to top for the content area
                    const contentArea = document.querySelector('.tablet-mega-menu-content-area');
                    if (contentArea) {
                        contentArea.scrollTop = 0;
                    }

                    // Set animation timeout for completion
                    animationTimeout = setTimeout(() => {
                        if (targetContent && targetContent.classList.contains('active')) {
                            targetContent.classList.add('animation-complete');
                        }
                    }, 500); // Reduced timeout for faster response
                }
            });

            // Add hover effect for better UX
            category.addEventListener('mouseenter', function() {
                if (!this.classList.contains('active')) {
                    this.style.backgroundColor = 'rgba(249, 115, 22, 0.05)';
                }
            });

            category.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    this.style.backgroundColor = '';
                }
            });
        });

        // Lazy load images when content becomes active
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const images = entry.target.querySelectorAll('img[loading="lazy"]');
                    images.forEach(img => {
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            img.addEventListener('load', function() {
                                this.style.opacity = '1';
                            });
                        }
                    });
                }
            });
        }, { threshold: 0.1 });

        contents.forEach(content => {
            observer.observe(content);
        });

        // Auto-activate first category if none is active
        const firstCategory = document.querySelector('.tablet-mega-category.active');
        if (!firstCategory) {
            const firstCat = document.querySelector('.tablet-mega-category');
            if (firstCat) {
                firstCat.classList.add('active');
                const firstContent = document.querySelector('.tablet-mega-content');
                if (firstContent) {
                    firstContent.classList.add('active');
                }
            }
        }
    }



    // Function to setup scroll lock functionality
    function setupScrollLock() {
        const tabletMegaMenu = document.getElementById('tablet-mega-menu');
        if (!tabletMegaMenu) return;

        // Prevent scroll on modal background
        tabletMegaMenu.addEventListener('wheel', function(e) {
            // Only prevent if scrolling on the background (not on content)
            if (e.target === tabletMegaMenu) {
                e.preventDefault();
                e.stopPropagation();
            }
        }, { passive: false });

        // Prevent touch scroll on modal background
        tabletMegaMenu.addEventListener('touchmove', function(e) {
            // Only prevent if touching the background (not content)
            if (e.target === tabletMegaMenu) {
                e.preventDefault();
                e.stopPropagation();
            }
        }, { passive: false });

        // Handle scroll within content areas
        const contentAreas = tabletMegaMenu.querySelectorAll('.tablet-mega-menu-categories, .tablet-mega-menu-content-area');
        contentAreas.forEach(area => {
            area.addEventListener('wheel', function(e) {
                // Check if we're at the scroll boundaries
                const atTop = area.scrollTop === 0;
                const atBottom = area.scrollTop >= (area.scrollHeight - area.clientHeight);

                // Prevent scroll propagation if we're at boundaries and trying to scroll further
                if ((atTop && e.deltaY < 0) || (atBottom && e.deltaY > 0)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            }, { passive: false });

            area.addEventListener('touchmove', function(e) {
                // Similar logic for touch
                const atTop = area.scrollTop === 0;
                const atBottom = area.scrollTop >= (area.scrollHeight - area.clientHeight);

                // Get touch delta
                const touch = e.touches[0];
                const deltaY = touch.clientY - (area.lastTouchY || touch.clientY);
                area.lastTouchY = touch.clientY;

                if ((atTop && deltaY > 0) || (atBottom && deltaY < 0)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            }, { passive: false });

            area.addEventListener('touchstart', function(e) {
                area.lastTouchY = e.touches[0].clientY;
            });
        });
    }

    // Function to open tablet mega menu
    function openTabletMegaMenu() {
        console.log('Opening tablet mega menu...');
        const tabletMegaMenu = document.getElementById('tablet-mega-menu');
        console.log('Tablet mega menu element:', tabletMegaMenu);
        if (tabletMegaMenu) {
            // Store current scroll position
            const scrollY = window.scrollY;

            // Show modal first (invisible)
            tabletMegaMenu.style.display = 'flex';

            // Force reflow to ensure display is applied
            tabletMegaMenu.offsetHeight;

            // Small delay to ensure proper animation trigger
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    // Add active class for animation
                    tabletMegaMenu.classList.add('active');
                    console.log('Active class added to tablet mega menu');

                    // Reset scroll position to top when opening menu
                    const contentArea = tabletMegaMenu.querySelector('.tablet-mega-menu-content-area');
                    if (contentArea) {
                        contentArea.scrollTop = 0;
                    }

                    // Debug: Check computed styles
                    const content = tabletMegaMenu.querySelector('.tablet-mega-menu-content');
                    if (content) {
                        const styles = window.getComputedStyle(content);
                        console.log('Content computed styles:', {
                            display: styles.display,
                            visibility: styles.visibility,
                            opacity: styles.opacity,
                            transform: styles.transform,
                            position: styles.position,
                            bottom: styles.bottom,
                            zIndex: styles.zIndex
                        });
                    }
                });
            });

            // Simple body scroll lock without position fixed
            document.body.style.overflow = 'hidden';
            document.body.style.touchAction = 'none';
            document.body.classList.add('tablet-mega-menu-open');

            // Store scroll position for restoration
            tabletMegaMenu.dataset.scrollY = scrollY;

            // Set ARIA attributes
            tabletMegaMenu.setAttribute('aria-hidden', 'false');
            if (productsNavLink) {
                productsNavLink.setAttribute('aria-expanded', 'true');
            }

            // Focus management
            const closeButton = tabletMegaMenu.querySelector('.tablet-mega-menu-close');
            if (closeButton) {
                setTimeout(() => closeButton.focus(), 300);
            }

            console.log('Tablet mega menu opened');
        }
    }
    
    // Function to close tablet mega menu
    function closeTabletMegaMenu() {
        const tabletMegaMenu = document.getElementById('tablet-mega-menu');
        if (tabletMegaMenu) {
            // Remove active class for animation
            tabletMegaMenu.classList.remove('active');

            // Hide modal after animation completes
            setTimeout(() => {
                tabletMegaMenu.style.display = 'none';
            }, 300); // Match CSS transition duration

            // FORCE HIDE ALL CSS EFFECTS on products nav item
            if (productsNavItem) {
                productsNavItem.classList.add('tablet-mega-closed');

                // Remove the class after a short delay to allow normal hover again
                setTimeout(() => {
                    productsNavItem.classList.remove('tablet-mega-closed');
                }, 100);
            }

            // Restore body scroll
            document.body.style.overflow = '';
            document.body.style.touchAction = '';
            document.body.classList.remove('tablet-mega-menu-open');

            // Restore scroll position if stored
            const scrollY = tabletMegaMenu.dataset.scrollY;
            if (scrollY) {
                window.scrollTo(0, parseInt(scrollY));
                delete tabletMegaMenu.dataset.scrollY;
            }

            // Set ARIA attributes
            tabletMegaMenu.setAttribute('aria-hidden', 'true');
            if (productsNavLink) {
                productsNavLink.setAttribute('aria-expanded', 'false');
            }

            // Return focus to trigger element but blur immediately to remove hover
            if (productsNavLink) {
                productsNavLink.focus();
                setTimeout(() => {
                    productsNavLink.blur();
                }, 50);
            }

            console.log('Tablet mega menu closed');
        }
    }
    
    // Event listeners
    
    // Click on products nav link
    productsNavLink.addEventListener('click', function(e) {
        console.log('Products nav link clicked on tablet device');
        e.preventDefault();
        e.stopPropagation();
        openTabletMegaMenu();
    });
    
    // Close button click
    document.addEventListener('click', function(e) {
        if (e.target.closest('.tablet-mega-menu-close')) {
            e.preventDefault();
            closeTabletMegaMenu();
        }
    });
    
    // Click outside to close
    document.addEventListener('click', function(e) {
        const tabletMegaMenu = document.getElementById('tablet-mega-menu');
        if (tabletMegaMenu &&
            tabletMegaMenu.classList.contains('active') &&
            e.target === tabletMegaMenu) {
            closeTabletMegaMenu();
        }

        // Force clear hover state when clicking anywhere outside products nav
        if (checkTabletStatus() && productsNavItem && !e.target.closest('.nav-item:has(.mega-menu)')) {
            productsNavItem.classList.add('tablet-mega-closed');
            setTimeout(() => {
                productsNavItem.classList.remove('tablet-mega-closed');
            }, 50);
        }
    });
    
    // Keyboard events
    document.addEventListener('keydown', function(e) {
        const tabletMegaMenu = document.getElementById('tablet-mega-menu');
        if (tabletMegaMenu && tabletMegaMenu.classList.contains('active')) {
            if (e.key === 'Escape') {
                e.preventDefault();
                closeTabletMegaMenu();
            }
        }
    });
    
    // Handle orientation change
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            const tabletMegaMenu = document.getElementById('tablet-mega-menu');
            if (tabletMegaMenu && tabletMegaMenu.classList.contains('active')) {
                // Re-focus after orientation change
                const closeButton = tabletMegaMenu.querySelector('.tablet-mega-menu-close');
                if (closeButton) {
                    closeButton.focus();
                }
            }
        }, 300);
    });
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (!isTablet()) {
            // Close menu if no longer tablet size
            closeTabletMegaMenu();
        }
    });
    
    // Prevent default hover behavior on products nav item for tablet
    productsNavItem.addEventListener('mouseenter', function(e) {
        if (isTablet()) {
            e.preventDefault();
            e.stopPropagation();
        }
    });
    
    // Touch events for better mobile experience
    let touchStartY = 0;
    
    document.addEventListener('touchstart', function(e) {
        const tabletMegaMenu = document.getElementById('tablet-mega-menu');
        if (tabletMegaMenu && tabletMegaMenu.classList.contains('active')) {
            const content = tabletMegaMenu.querySelector('.tablet-mega-menu-content');
            if (content && !content.contains(e.target)) {
                touchStartY = e.touches[0].clientY;
            }
        }
    }, { passive: true });
    
    document.addEventListener('touchmove', function(e) {
        const tabletMegaMenu = document.getElementById('tablet-mega-menu');
        if (tabletMegaMenu && tabletMegaMenu.classList.contains('active')) {
            const content = tabletMegaMenu.querySelector('.tablet-mega-menu-content');
            if (content && !content.contains(e.target)) {
                const touchY = e.touches[0].clientY;
                const deltaY = touchY - touchStartY;
                
                // Close on swipe down (portrait mode)
                if (window.innerHeight > window.innerWidth && deltaY > 100) {
                    closeTabletMegaMenu();
                }
            }
        }
    }, { passive: true });
    
    // Expose functions globally for external use
    window.openTabletMegaMenu = openTabletMegaMenu;
    window.closeTabletMegaMenu = closeTabletMegaMenu;

    console.log('Tablet mega menu setup complete with auto-scroll features');
});
