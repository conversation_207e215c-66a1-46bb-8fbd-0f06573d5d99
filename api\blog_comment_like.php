<?php
/**
 * API thích/bỏ thích bình luận blog
 */

// Include các file cần thiết
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/auth.php';

// Kiểm tra đăng nhập
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Bạn cần đăng nhập để thực hiện chức năng này.']);
    exit;
}

// Kiểm tra phương thức request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Phương thức không được hỗ trợ.']);
    exit;
}

// Lấy dữ liệu từ request
$comment_id = isset($_POST['comment_id']) ? (int)$_POST['comment_id'] : 0;
$action = isset($_POST['action']) ? $_POST['action'] : '';
$user_id = $_SESSION['user_id'];

// Kiểm tra dữ liệu
if (empty($comment_id) || empty($action)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Dữ liệu không hợp lệ.']);
    exit;
}

// Kiểm tra bình luận tồn tại
try {
    $stmt = $conn->prepare("SELECT id FROM blog_comments WHERE id = :comment_id");
    $stmt->bindParam(':comment_id', $comment_id, PDO::PARAM_INT);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Bình luận không tồn tại.']);
        exit;
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Lỗi hệ thống: ' . $e->getMessage()]);
    exit;
}

// Xử lý thích/bỏ thích
try {
    if ($action === 'like') {
        // Kiểm tra xem đã thích chưa
        $stmt = $conn->prepare("SELECT id FROM blog_comment_likes WHERE user_id = :user_id AND comment_id = :comment_id");
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':comment_id', $comment_id, PDO::PARAM_INT);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => 'Bạn đã thích bình luận này trước đó.']);
            exit;
        }
        
        // Thêm lượt thích mới
        $stmt = $conn->prepare("INSERT INTO blog_comment_likes (user_id, comment_id) VALUES (:user_id, :comment_id)");
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':comment_id', $comment_id, PDO::PARAM_INT);
        $stmt->execute();
        
        // Lấy số lượt thích mới
        $stmt = $conn->prepare("SELECT COUNT(*) as like_count FROM blog_comment_likes WHERE comment_id = :comment_id");
        $stmt->bindParam(':comment_id', $comment_id, PDO::PARAM_INT);
        $stmt->execute();
        $like_count = $stmt->fetch(PDO::FETCH_ASSOC)['like_count'];
        
        echo json_encode(['success' => true, 'message' => 'Đã thích bình luận.', 'like_count' => $like_count]);
    } elseif ($action === 'unlike') {
        // Xóa lượt thích
        $stmt = $conn->prepare("DELETE FROM blog_comment_likes WHERE user_id = :user_id AND comment_id = :comment_id");
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':comment_id', $comment_id, PDO::PARAM_INT);
        $stmt->execute();
        
        // Lấy số lượt thích mới
        $stmt = $conn->prepare("SELECT COUNT(*) as like_count FROM blog_comment_likes WHERE comment_id = :comment_id");
        $stmt->bindParam(':comment_id', $comment_id, PDO::PARAM_INT);
        $stmt->execute();
        $like_count = $stmt->fetch(PDO::FETCH_ASSOC)['like_count'];
        
        echo json_encode(['success' => true, 'message' => 'Đã bỏ thích bình luận.', 'like_count' => $like_count]);
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Hành động không hợp lệ.']);
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Lỗi hệ thống: ' . $e->getMessage()]);
}
