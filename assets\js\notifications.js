// /**
//  * <PERSON><PERSON> thống thông báo hiện đại
//  * File này đảm bảo tất cả các thông báo đều sử dụng showModernNotification
//  */

// document.addEventListener('DOMContentLoaded', function() {
//     console.log('Notifications system initialized');
    
//     // Đảm bảo container thông báo tồn tại
//     let notificationContainer = document.getElementById('notification-container');
//     if (!notificationContainer) {
//         notificationContainer = document.createElement('div');
//         notificationContainer.id = 'notification-container';
//         notificationContainer.className = 'notification-container';
//         document.body.appendChild(notificationContainer);
//         console.log('Created notification container');
//     }
    
//     // Đảm bảo hàm showModernNotification tồn tại
//     if (typeof window.showModernNotification !== 'function') {
//         console.warn('showModernNotification not found, creating fallback');
        
//         // Tạo hàm showModernNotification nếu chưa có
//         window.showModernNotification = function(message, type = 'success', duration = 3000) {
//             console.log('Using fallback showModernNotification', message, type);
            
//             // Tính toán chiều cao của header
//             const calculateHeaderHeight = () => {
//                 const header = document.querySelector('header');
//                 if (!header) return 20; // Giá trị mặc định nếu không tìm thấy header
                
//                 // Lấy chiều cao thực tế của header
//                 const headerHeight = header.offsetHeight;
                
//                 // Kiểm tra xem header có sticky không
//                 const isSticky = window.getComputedStyle(header).position === 'sticky' ||
//                                 window.getComputedStyle(header).position === 'fixed';
                
//                 // Thêm một khoảng cách nhỏ để đảm bảo thông báo không bị che khuất
//                 const padding = 20;
                
//                 return isSticky ? headerHeight + padding : padding;
//             };
            
//             // Tạo container thông báo nếu chưa tồn tại
//             let container = document.getElementById('notification-container');
//             if (!container) {
//                 container = document.createElement('div');
//                 container.id = 'notification-container';
//                 container.className = 'notification-container';
//                 document.body.appendChild(container);
//             }
            
//             // Điều chỉnh vị trí của container dựa trên chiều cao header
//             const headerHeight = calculateHeaderHeight();
//             container.style.top = `${headerHeight}px`;
            
//             // Tạo thông báo mới
//             const notification = document.createElement('div');
//             notification.className = `notification ${type}`;
            
//             // Xác định icon dựa vào loại thông báo
//             let icon = '';
//             let title = '';
            
//             switch (type) {
//                 case 'success':
//                     icon = 'fa-check-circle';
//                     title = 'Thành công';
//                     break;
//                 case 'error':
//                     icon = 'fa-exclamation-circle';
//                     title = 'Lỗi';
//                     break;
//                 case 'info':
//                     icon = 'fa-info-circle';
//                     title = 'Thông tin';
//                     break;
//                 case 'warning':
//                     icon = 'fa-exclamation-triangle';
//                     title = 'Cảnh báo';
//                     break;
//                 default:
//                     icon = 'fa-bell';
//                     title = 'Thông báo';
//             }
            
//             // Tùy chỉnh thông báo khi thêm vào giỏ hàng
//             if (type === 'success' && (message.includes('giỏ hàng') || message.includes('Đã thêm'))) {
//                 // Tạo nội dung HTML cho thông báo thêm vào giỏ hàng với thiết kế cải tiến
//                 notification.innerHTML = `
//                     <div class="notification-icon">
//                         <i class="fas ${icon}"></i>
//                     </div>
//                     <div class="notification-content">
//                         <div class="notification-message">
//                             <span class="font-medium">${message}</span>
//                             <a href="${BASE_URL}/cart.php" class="text-primary hover:underline ml-1">Xem giỏ hàng</a>
//                         </div>
//                     </div>
//                     <div class="notification-progress">
//                         <div class="notification-progress-bar"></div>
//                     </div>
//                 `;
                
//                 // Thêm class đặc biệt cho thông báo giỏ hàng
//                 notification.classList.add('cart-notification');
                
//                 // Thời gian hiển thị cho thông báo thêm vào giỏ hàng
//                 duration = 3000;
                
//                 console.log('Hiển thị thông báo giỏ hàng với class cart-notification');
//             } else {
//                 // Tạo nội dung HTML cho thông báo thông thường
//                 notification.innerHTML = `
//                     <div class="notification-icon">
//                         <i class="fas ${icon}"></i>
//                     </div>
//                     <div class="notification-content">
//                         <div class="notification-title">
//                             ${title}
//                             <span class="notification-close" onclick="closeNotification(this.parentElement.parentElement.parentElement)">
//                                 <i class="fas fa-times"></i>
//                             </span>
//                         </div>
//                         <div class="notification-message">${message}</div>
//                     </div>
//                     <div class="notification-progress">
//                         <div class="notification-progress-bar"></div>
//                     </div>
//                 `;
//             }
            
//             // Thêm thông báo vào container
//             container.appendChild(notification);
            
//             // Hiển thị thông báo với hiệu ứng
//             requestAnimationFrame(() => {
//                 notification.classList.add('show');
//             });
            
//             // Xóa thông báo sau thời gian đã định
//             setTimeout(() => {
//                 closeNotification(notification);
//             }, duration);
            
//             // Thêm sự kiện resize để điều chỉnh vị trí khi kích thước màn hình thay đổi
//             const handleResize = () => {
//                 const newHeaderHeight = calculateHeaderHeight();
//                 container.style.top = `${newHeaderHeight}px`;
//             };
            
//             // Đăng ký sự kiện resize nếu chưa đăng ký
//             if (!window.notificationResizeHandlerAdded) {
//                 window.addEventListener('resize', handleResize);
//                 window.notificationResizeHandlerAdded = true;
//             }
            
//             return notification;
//         };
//     }
    
//     // Đảm bảo hàm closeNotification tồn tại
//     if (typeof window.closeNotification !== 'function') {
//         window.closeNotification = function(notification) {
//             if (!notification) return;
            
//             // Thêm animation đóng
//             notification.classList.remove('show');
//             notification.style.animation = 'slide-out 0.3s ease-out forwards';
            
//             // Xóa thông báo sau khi animation kết thúc
//             setTimeout(() => {
//                 notification.remove();
//             }, 300);
//         };
//     }
    
//     // Ghi đè các hàm thông báo để sử dụng showModernNotification
//     window.showNotification = function(message, type) {
//         return window.showModernNotification(message, type);
//     };
    
//     window.showNotificationRealtime = function(message, type) {
//         return window.showModernNotification(message, type);
//     };
    
//     console.log('Notification system ready');
// });
