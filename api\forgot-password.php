<?php
/**
 * API xử lý quên mật khẩu
 */

// <PERSON><PERSON><PERSON> bảo trả về JSON ngay cả khi có lỗi PHP
header('Content-Type: application/json');

// Bắt lỗi PHP
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Lỗi máy chủ: ' . $errstr,
        'error_details' => [
            'file' => $errfile,
            'line' => $errline
        ]
    ]);
    exit;
});

// Bắt ngoại lệ không được xử lý
set_exception_handler(function($e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Lỗi máy chủ: ' . $e->getMessage(),
        'error_details' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
    exit;
});

// Nạp các file cần thiết
try {
    require_once '../includes/init.php';
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Lỗi khi nạp file: ' . $e->getMessage()
    ]);
    exit;
}

// Kiểm tra phương thức request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Phương thức không được hỗ trợ'
    ]);
    exit;
}

// Lấy dữ liệu từ request
$data = json_decode(file_get_contents('php://input'), true);

// Nếu không có dữ liệu JSON, thử lấy từ POST
if (!$data) {
    $data = $_POST;
}

// Xác định hành động
$action = $data['action'] ?? '';

// Xử lý các hành động
switch ($action) {
    case 'send_otp':
        // Gửi mã OTP đến email
        handleSendOTP($data);
        break;

    case 'verify_otp':
        // Xác thực mã OTP
        handleVerifyOTP($data);
        break;

    case 'reset_password':
        // Đặt lại mật khẩu
        handleResetPassword($data);
        break;

    default:
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Hành động không hợp lệ'
        ]);
        break;
}

/**
 * Xử lý gửi mã OTP
 */
function handleSendOTP($data) {
    try {
        // Kiểm tra email
        $email = $data['email'] ?? '';

        if (empty($email)) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Vui lòng nhập địa chỉ email'
            ]);
            return;
        }

        // Kiểm tra định dạng email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Địa chỉ email không hợp lệ'
            ]);
            return;
        }

        // Gửi mã OTP
        $result = send_otp_email($email);

        if ($result['success']) {
            echo json_encode([
                'success' => true,
                'message' => $result['message']
            ]);
        } else {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $result['message'],
                'error_details' => $result['error_details'] ?? null
            ]);
        }
    } catch (Exception $e) {
        error_log("Lỗi trong handleSendOTP: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Đã xảy ra lỗi khi xử lý yêu cầu: ' . $e->getMessage()
        ]);
    }
}

/**
 * Xử lý xác thực mã OTP
 */
function handleVerifyOTP($data) {
    // Kiểm tra email và mã OTP
    $email = $data['email'] ?? '';
    $otp_code = $data['otp_code'] ?? '';

    if (empty($email) || empty($otp_code)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Vui lòng nhập đầy đủ thông tin'
        ]);
        return;
    }

    // Xác thực mã OTP
    $result = verify_otp_code($email, $otp_code);

    if ($result['success']) {
        // Lưu user_id vào session để sử dụng khi đặt lại mật khẩu
        $_SESSION['reset_password_user_id'] = $result['user_id'];

        echo json_encode([
            'success' => true,
            'message' => $result['message']
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $result['message']
        ]);
    }
}

/**
 * Xử lý đặt lại mật khẩu
 */
function handleResetPassword($data) {
    // Kiểm tra mật khẩu mới
    $password = $data['password'] ?? '';
    $confirm_password = $data['confirm_password'] ?? '';

    if (empty($password) || empty($confirm_password)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Vui lòng nhập đầy đủ thông tin'
        ]);
        return;
    }

    if ($password !== $confirm_password) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Mật khẩu xác nhận không khớp'
        ]);
        return;
    }

    if (strlen($password) < 6) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Mật khẩu phải có ít nhất 6 ký tự'
        ]);
        return;
    }

    // Kiểm tra user_id trong session
    if (!isset($_SESSION['reset_password_user_id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Phiên làm việc đã hết hạn. Vui lòng thử lại.'
        ]);
        return;
    }

    $user_id = $_SESSION['reset_password_user_id'];

    // Đặt lại mật khẩu
    $result = reset_password_with_otp($user_id, $password);

    if ($result['success']) {
        // Xóa user_id khỏi session
        unset($_SESSION['reset_password_user_id']);

        echo json_encode([
            'success' => true,
            'message' => $result['message']
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $result['message']
        ]);
    }
}
