/*
 * Modern Thank You Page CSS for Nội Thất Bàng Vũ
 * Thiết kế trang cảm ơn hiện đại với hiệu ứng và UI/UX chuẩn
 */

:root {
    /* <PERSON><PERSON><PERSON> sắc */
    --thankyou-primary: #f97316;
    --thankyou-primary-dark: #ea580c;
    --thankyou-primary-light: #ffedd5;
    --thankyou-primary-ultra-light: #fff7ed;
    --thankyou-success: #10b981;
    --thankyou-success-dark: #059669;
    --thankyou-success-light: #d1fae5;
    --thankyou-text: #1e293b;
    --thankyou-text-light: #64748b;
    --thankyou-border: #e2e8f0;
    --thankyou-bg: #f8fafc;
    --thankyou-card-bg: #ffffff;
    --thankyou-info: #3b82f6;
    --thankyou-info-light: #dbeafe;

    /* <PERSON><PERSON><PERSON> thước và khoảng cách */
    --thankyou-border-radius: 0.75rem;
    --thankyou-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    --thankyou-box-shadow-hover: 0 8px 16px rgba(0, 0, 0, 0.1);
    --thankyou-transition: all 0.3s ease;
}

/* Container chính cho thank you page */
.modern-thankyou {
    background-color: var(--thankyou-bg);
    padding: 3rem 0;
}

/* Card chính */
.thankyou-card {
    background-color: var(--thankyou-card-bg);
    border-radius: var(--thankyou-border-radius);
    box-shadow: var(--thankyou-box-shadow);
    overflow: hidden;
    transition: var(--thankyou-transition);
    border: 1px solid var(--thankyou-border);
}

.thankyou-card:hover {
    box-shadow: var(--thankyou-box-shadow-hover);
}

/* Header thành công */
.thankyou-header {
    background-color: var(--thankyou-success);
    color: white;
    padding: 2.5rem 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.thankyou-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    z-index: 1;
}

.thankyou-header-content {
    position: relative;
    z-index: 2;
}

.thankyou-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: inline-block;
    animation: scaleIn 0.5s ease-out;
}

.thankyou-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    animation: fadeInUp 0.5s ease-out 0.2s both;
}

.thankyou-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    animation: fadeInUp 0.5s ease-out 0.4s both;
}

/* Nội dung chính */
.thankyou-content {
    padding: 2rem;
}

/* Tiêu đề phần */
.thankyou-section-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--thankyou-text);
    margin-bottom: 1rem;
    position: relative;
    padding-bottom: 0.75rem;
}

.thankyou-section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--thankyou-primary);
    border-radius: 3px;
}

/* Thông tin đơn hàng */
.order-info-card {
    background-color: var(--thankyou-primary-ultra-light);
    border: 1px solid var(--thankyou-primary-light);
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 2rem;
}

.order-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
}

.order-info-item:last-child {
    margin-bottom: 0;
}

.order-info-label {
    font-weight: 600;
    color: var(--thankyou-text);
}

.order-info-value {
    color: var(--thankyou-text-light);
}

.order-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.order-status-processing {
    background-color: var(--thankyou-info-light);
    color: var(--thankyou-info);
}

.order-status-success {
    background-color: var(--thankyou-success-light);
    color: var(--thankyou-success);
}

/* Bảng chi tiết đơn hàng */
.order-details-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 2rem;
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid var(--thankyou-border);
}

.order-details-table th {
    background-color: var(--thankyou-bg);
    padding: 0.75rem 1rem;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--thankyou-text-light);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.order-details-table td {
    padding: 1rem;
    border-top: 1px solid var(--thankyou-border);
    font-size: 0.875rem;
    color: var(--thankyou-text);
}

.order-details-table tbody tr:hover {
    background-color: var(--thankyou-bg);
}

.order-details-table tfoot {
    background-color: var(--thankyou-bg);
    font-weight: 600;
}

.order-details-table tfoot td {
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--thankyou-border);
}

.order-product-image {
    width: 3rem;
    height: 3rem;
    border-radius: 0.25rem;
    overflow: hidden;
    background-color: var(--thankyou-bg);
}

.order-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.order-product-name {
    font-weight: 500;
    color: var(--thankyou-text);
}

.order-total-price {
    color: var(--thankyou-primary);
    font-weight: 700;
}

/* Thông tin giao hàng */
.shipping-info-card {
    background-color: var(--thankyou-bg);
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 2rem;
}

.shipping-info-item {
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    color: var(--thankyou-text);
}

.shipping-info-item:last-child {
    margin-bottom: 0;
}

.shipping-info-label {
    font-weight: 600;
    display: inline-block;
    min-width: 120px;
}

/* Form đăng ký */
.register-card {
    background-color: var(--thankyou-info-light);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 2rem;
    text-align: left;
}

.register-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--thankyou-info);
    margin-bottom: 0.5rem;
}

.register-description {
    font-size: 0.875rem;
    color: var(--thankyou-text);
    margin-bottom: 1rem;
}

.register-form {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.25rem;
    border: 1px solid var(--thankyou-border);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--thankyou-text);
    margin-bottom: 0.5rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--thankyou-border);
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: var(--thankyou-transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--thankyou-primary);
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

.form-help-text {
    font-size: 0.75rem;
    color: var(--thankyou-text-light);
    margin-top: 0.25rem;
}

/* Nút */
.thankyou-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 600;
    transition: var(--thankyou-transition);
    cursor: pointer;
    gap: 0.5rem;
}

.thankyou-btn-primary {
    background-color: var(--thankyou-primary);
    color: white;
}

.thankyou-btn-primary:hover {
    background-color: var(--thankyou-primary-dark);
    transform: translateY(-2px);
}

.thankyou-btn-secondary {
    background-color: var(--thankyou-text-light);
    color: white;
}

.thankyou-btn-secondary:hover {
    background-color: var(--thankyou-text);
    transform: translateY(-2px);
}

.thankyou-btn-success {
    background-color: var(--thankyou-success);
    color: white;
}

.thankyou-btn-success:hover {
    background-color: var(--thankyou-success-dark);
    transform: translateY(-2px);
}

/* Animations */
@keyframes scaleIn {
    0% { transform: scale(0); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes fadeInUp {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Email Notification Card */
.email-notification-card {
    display: flex;
    align-items: flex-start;
    background: linear-gradient(to right, var(--thankyou-success-light), #f0fdfa);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(16, 185, 129, 0.2);
    text-align: left;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.5s ease-out;
}

.email-notification-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0) 70%);
    border-radius: 50%;
    z-index: 0;
}

.email-notification-icon {
    flex-shrink: 0;
    width: 3.5rem;
    height: 3.5rem;
    background-color: var(--thankyou-success);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1.25rem;
    position: relative;
    z-index: 1;
    box-shadow: 0 6px 12px rgba(16, 185, 129, 0.2);
    animation: pulse 2s infinite;
}

.email-notification-content {
    flex-grow: 1;
    position: relative;
    z-index: 1;
}

.email-notification-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--thankyou-success-dark);
    margin-bottom: 0.5rem;
}

.email-notification-text {
    font-size: 0.95rem;
    color: var(--thankyou-text);
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.email-notification-tip {
    font-size: 0.85rem;
    color: var(--thankyou-text-light);
    font-style: italic;
}

.email-highlight {
    color: var(--thankyou-success-dark);
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.email-highlight::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--thankyou-success);
    opacity: 0.3;
    border-radius: 1px;
}

/* Custom Checkbox */
.custom-checkbox-container {
    margin: 0.5rem 0;
}

.custom-checkbox-wrapper {
    display: flex;
    align-items: flex-start;
    position: relative;
}

.custom-checkbox {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.custom-checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    user-select: none;
    font-size: 0.875rem;
    color: var(--thankyou-text);
    line-height: 1.5;
}

.checkbox-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--thankyou-border);
    border-radius: 0.25rem;
    margin-right: 0.75rem;
    margin-top: 0.125rem;
    transition: all 0.2s ease;
    background-color: white;
    flex-shrink: 0;
}

.checkbox-icon i {
    color: white;
    font-size: 0.75rem;
    opacity: 0;
    transform: scale(0);
    transition: all 0.2s ease;
}

.custom-checkbox:checked + .custom-checkbox-label .checkbox-icon {
    background-color: var(--thankyou-primary);
    border-color: var(--thankyou-primary);
}

.custom-checkbox:checked + .custom-checkbox-label .checkbox-icon i {
    opacity: 1;
    transform: scale(1);
}

.custom-checkbox:focus + .custom-checkbox-label .checkbox-icon {
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
}

.custom-checkbox-label:hover .checkbox-icon {
    border-color: var(--thankyou-primary-light);
}

.checkbox-text {
    flex-grow: 1;
}

.terms-link {
    color: var(--thankyou-primary);
    font-weight: 500;
    text-decoration: none;
    position: relative;
    transition: all 0.2s ease;
}

.terms-link:hover {
    text-decoration: none;
    color: var(--thankyou-primary-dark);
}

.terms-link::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--thankyou-primary);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.terms-link:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

/* Responsive */
@media (max-width: 768px) {
    .thankyou-content {
        padding: 1.5rem;
    }

    .order-details-table {
        display: block;
        overflow-x: auto;
    }

    .thankyou-header {
        padding: 2rem 1rem;
    }

    .thankyou-icon {
        font-size: 3rem;
    }

    .thankyou-title {
        font-size: 1.5rem;
    }

    .thankyou-subtitle {
        font-size: 1rem;
    }

    .email-notification-card {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 1.25rem;
    }

    .email-notification-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .custom-checkbox-wrapper {
        flex-direction: column;
        align-items: center;
    }

    .checkbox-icon {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}
