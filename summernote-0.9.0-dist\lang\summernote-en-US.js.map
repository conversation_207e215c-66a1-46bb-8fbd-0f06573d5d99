{"version": 3, "file": "lang/summernote-en-US.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;ACVA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;ACNuB;AAEvBA,0DAAY,GAAGA,0DAAY,IAAI;EAC7BE,IAAI,EAAE,CAAC;AACT,CAAC;AAEDF,oDAAQ,CAAC,IAAI,EAAEA,0DAAY,CAACE,IAAI,EAAE;EAChC,OAAO,EAAE;IACPE,IAAI,EAAE;MACJC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE,WAAW;MACtBC,KAAK,EAAE,mBAAmB;MAC1BC,MAAM,EAAE,aAAa;MACrBC,IAAI,EAAE,aAAa;MACnBC,aAAa,EAAE,eAAe;MAC9BC,SAAS,EAAE,WAAW;MACtBC,WAAW,EAAE,aAAa;MAC1BC,IAAI,EAAE,WAAW;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDC,KAAK,EAAE;MACLA,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,cAAc;MACtBC,UAAU,EAAE,aAAa;MACzBC,UAAU,EAAE,aAAa;MACzBC,aAAa,EAAE,gBAAgB;MAC/BC,UAAU,EAAE,eAAe;MAC3BC,SAAS,EAAE,YAAY;MACvBC,UAAU,EAAE,aAAa;MACzBC,SAAS,EAAE,cAAc;MACzBC,YAAY,EAAE,gBAAgB;MAC9BC,WAAW,EAAE,eAAe;MAC5BC,cAAc,EAAE,kBAAkB;MAClCC,SAAS,EAAE,aAAa;MACxBC,aAAa,EAAE,yBAAyB;MACxCC,SAAS,EAAE,oBAAoB;MAC/BC,eAAe,EAAE,mBAAmB;MACpCC,eAAe,EAAE,mBAAmB;MACpCC,oBAAoB,EAAE,6BAA6B;MACnDC,GAAG,EAAE,WAAW;MAChBC,MAAM,EAAE,cAAc;MACtBC,QAAQ,EAAE;IACZ,CAAC;IACDC,KAAK,EAAE;MACLA,KAAK,EAAE,OAAO;MACdC,SAAS,EAAE,YAAY;MACvBrB,MAAM,EAAE,cAAc;MACtBiB,GAAG,EAAE,WAAW;MAChBK,SAAS,EAAE;IACb,CAAC;IACDC,IAAI,EAAE;MACJA,IAAI,EAAE,MAAM;MACZvB,MAAM,EAAE,aAAa;MACrBwB,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,MAAM;MACZC,aAAa,EAAE,iBAAiB;MAChCT,GAAG,EAAE,kCAAkC;MACvCU,eAAe,EAAE;IACnB,CAAC;IACDC,KAAK,EAAE;MACLA,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE,eAAe;MAC5BC,WAAW,EAAE,eAAe;MAC5BC,UAAU,EAAE,iBAAiB;MAC7BC,WAAW,EAAE,kBAAkB;MAC/BC,MAAM,EAAE,YAAY;MACpBC,MAAM,EAAE,eAAe;MACvBC,QAAQ,EAAE;IACZ,CAAC;IACDC,EAAE,EAAE;MACFpC,MAAM,EAAE;IACV,CAAC;IACDqC,KAAK,EAAE;MACLA,KAAK,EAAE,OAAO;MACdC,CAAC,EAAE,QAAQ;MACXC,UAAU,EAAE,OAAO;MACnBC,GAAG,EAAE,MAAM;MACXC,EAAE,EAAE,UAAU;MACdC,EAAE,EAAE,UAAU;MACdC,EAAE,EAAE,UAAU;MACdC,EAAE,EAAE,UAAU;MACdC,EAAE,EAAE,UAAU;MACdC,EAAE,EAAE;IACN,CAAC;IACDC,KAAK,EAAE;MACLC,SAAS,EAAE,gBAAgB;MAC3BC,OAAO,EAAE;IACX,CAAC;IACDC,OAAO,EAAE;MACPC,IAAI,EAAE,MAAM;MACZC,UAAU,EAAE,aAAa;MACzBC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACTA,SAAS,EAAE,WAAW;MACtBC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,YAAY;MAClBC,MAAM,EAAE,cAAc;MACtBC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLC,MAAM,EAAE,cAAc;MACtBC,IAAI,EAAE,YAAY;MAClBC,UAAU,EAAE,kBAAkB;MAC9BC,UAAU,EAAE,YAAY;MACxBC,WAAW,EAAE,aAAa;MAC1BC,cAAc,EAAE,iBAAiB;MACjCC,KAAK,EAAE,OAAO;MACdC,cAAc,EAAE,kBAAkB;MAClCC,QAAQ,EAAE;IACZ,CAAC;IACDC,QAAQ,EAAE;MACRC,SAAS,EAAE,oBAAoB;MAC/BC,KAAK,EAAE,OAAO;MACdC,cAAc,EAAE,iBAAiB;MACjCC,MAAM,EAAE,QAAQ;MAChBC,mBAAmB,EAAE,sBAAsB;MAC3CC,aAAa,EAAE,gBAAgB;MAC/BC,SAAS,EAAE;IACb,CAAC;IACD3B,IAAI,EAAE;MACJ,QAAQ,EAAE,QAAQ;MAClB,iBAAiB,EAAE,kBAAkB;MACrC,MAAM,EAAE,uBAAuB;MAC/B,MAAM,EAAE,uBAAuB;MAC/B,KAAK,EAAE,KAAK;MACZ,OAAO,EAAE,OAAO;MAChB,MAAM,EAAE,kBAAkB;MAC1B,QAAQ,EAAE,oBAAoB;MAC9B,WAAW,EAAE,uBAAuB;MACpC,eAAe,EAAE,2BAA2B;MAC5C,cAAc,EAAE,eAAe;MAC/B,aAAa,EAAE,gBAAgB;MAC/B,eAAe,EAAE,kBAAkB;MACnC,cAAc,EAAE,iBAAiB;MACjC,aAAa,EAAE,gBAAgB;MAC/B,qBAAqB,EAAE,uBAAuB;MAC9C,mBAAmB,EAAE,qBAAqB;MAC1C,SAAS,EAAE,8BAA8B;MACzC,QAAQ,EAAE,6BAA6B;MACvC,YAAY,EAAE,sDAAsD;MACpE,UAAU,EAAE,sCAAsC;MAClD,UAAU,EAAE,sCAAsC;MAClD,UAAU,EAAE,sCAAsC;MAClD,UAAU,EAAE,sCAAsC;MAClD,UAAU,EAAE,sCAAsC;MAClD,UAAU,EAAE,sCAAsC;MAClD,sBAAsB,EAAE,wBAAwB;MAChD,iBAAiB,EAAE;IACrB,CAAC;IACD4B,OAAO,EAAE;MACPC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE;IACR,CAAC;IACDC,WAAW,EAAE;MACXA,WAAW,EAAE,oBAAoB;MACjCC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNC,WAAW,EAAE;IACf;EACF;AACF,CAAC,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///external umd {\"root\":\"jQuery\",\"commonjs\":\"jquery\",\"commonjs2\":\"jquery\",\"amd\":\"jquery\"}", "webpack:///webpack/bootstrap", "webpack:///webpack/runtime/compat get default export", "webpack:///webpack/runtime/define property getters", "webpack:///webpack/runtime/hasOwnProperty shorthand", "webpack:///webpack/runtime/make namespace object", "webpack:///./src/lang/summernote-en-US.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"jquery\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"jquery\"], factory);\n\telse {\n\t\tvar a = typeof exports === 'object' ? factory(require(\"jquery\")) : factory(root[\"jQuery\"]);\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, (__WEBPACK_EXTERNAL_MODULE__8938__) => {\nreturn ", "module.exports = __WEBPACK_EXTERNAL_MODULE__8938__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import $ from 'jquery';\n\n$.summernote = $.summernote || {\n  lang: {},\n};\n\n$.extend(true, $.summernote.lang, {\n  'en-US': {\n    font: {\n      bold: 'Bold',\n      italic: 'Italic',\n      underline: 'Underline',\n      clear: 'Remove Font Style',\n      height: 'Line Height',\n      name: 'Font Family',\n      strikethrough: 'Strikethrough',\n      subscript: 'Subscript',\n      superscript: 'Superscript',\n      size: 'Font Size',\n      sizeunit: 'Font Size Unit',\n    },\n    image: {\n      image: 'Picture',\n      insert: 'Insert Image',\n      resizeFull: 'Resize full',\n      resizeHalf: 'Resize half',\n      resizeQuarter: 'Resize quarter',\n      resizeNone: 'Original size',\n      floatLeft: 'Float Left',\n      floatRight: 'Float Right',\n      floatNone: 'Remove float',\n      shapeRounded: 'Shape: Rounded',\n      shapeCircle: 'Shape: Circle',\n      shapeThumbnail: 'Shape: Thumbnail',\n      shapeNone: 'Shape: None',\n      dragImageHere: 'Drag image or text here',\n      dropImage: 'Drop image or Text',\n      selectFromFiles: 'Select from files',\n      maximumFileSize: 'Maximum file size',\n      maximumFileSizeError: 'Maximum file size exceeded.',\n      url: 'Image URL',\n      remove: 'Remove Image',\n      original: 'Original',\n    },\n    video: {\n      video: 'Video',\n      videoLink: 'Video Link',\n      insert: 'Insert Video',\n      url: 'Video URL',\n      providers: '(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion, Youku, Peertube)',\n    },\n    link: {\n      link: 'Link',\n      insert: 'Insert Link',\n      unlink: 'Unlink',\n      edit: 'Edit',\n      textToDisplay: 'Text to display',\n      url: 'To what URL should this link go?',\n      openInNewWindow: 'Open in new window',\n    },\n    table: {\n      table: 'Table',\n      addRowAbove: 'Add row above',\n      addRowBelow: 'Add row below',\n      addColLeft: 'Add column left',\n      addColRight: 'Add column right',\n      delRow: 'Delete row',\n      delCol: 'Delete column',\n      delTable: 'Delete table',\n    },\n    hr: {\n      insert: 'Insert Horizontal Rule',\n    },\n    style: {\n      style: 'Style',\n      p: 'Normal',\n      blockquote: 'Quote',\n      pre: 'Code',\n      h1: 'Header 1',\n      h2: 'Header 2',\n      h3: 'Header 3',\n      h4: 'Header 4',\n      h5: 'Header 5',\n      h6: 'Header 6',\n    },\n    lists: {\n      unordered: 'Unordered list',\n      ordered: 'Ordered list',\n    },\n    options: {\n      help: 'Help',\n      fullscreen: 'Full Screen',\n      codeview: 'Code View',\n    },\n    paragraph: {\n      paragraph: 'Paragraph',\n      outdent: 'Outdent',\n      indent: 'Indent',\n      left: 'Align left',\n      center: 'Align center',\n      right: 'Align right',\n      justify: 'Justify full',\n    },\n    color: {\n      recent: 'Recent Color',\n      more: 'More Color',\n      background: 'Background Color',\n      foreground: 'Text Color',\n      transparent: 'Transparent',\n      setTransparent: 'Set transparent',\n      reset: 'Reset',\n      resetToDefault: 'Reset to default',\n      cpSelect: 'Select',\n    },\n    shortcut: {\n      shortcuts: 'Keyboard shortcuts',\n      close: 'Close',\n      textFormatting: 'Text formatting',\n      action: 'Action',\n      paragraphFormatting: 'Paragraph formatting',\n      documentStyle: 'Document Style',\n      extraKeys: 'Extra keys',\n    },\n    help: {\n      'escape': 'Escape',\n      'insertParagraph': 'Insert Paragraph',\n      'undo': 'Undo the last command',\n      'redo': 'Redo the last command',\n      'tab': 'Tab',\n      'untab': 'Untab',\n      'bold': 'Set a bold style',\n      'italic': 'Set a italic style',\n      'underline': 'Set a underline style',\n      'strikethrough': 'Set a strikethrough style',\n      'removeFormat': 'Clean a style',\n      'justifyLeft': 'Set left align',\n      'justifyCenter': 'Set center align',\n      'justifyRight': 'Set right align',\n      'justifyFull': 'Set full align',\n      'insertUnorderedList': 'Toggle unordered list',\n      'insertOrderedList': 'Toggle ordered list',\n      'outdent': 'Outdent on current paragraph',\n      'indent': 'Indent on current paragraph',\n      'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n      'formatH1': 'Change current block\\'s format as H1',\n      'formatH2': 'Change current block\\'s format as H2',\n      'formatH3': 'Change current block\\'s format as H3',\n      'formatH4': 'Change current block\\'s format as H4',\n      'formatH5': 'Change current block\\'s format as H5',\n      'formatH6': 'Change current block\\'s format as H6',\n      'insertHorizontalRule': 'Insert horizontal rule',\n      'linkDialog.show': 'Show Link Dialog',\n    },\n    history: {\n      undo: 'Undo',\n      redo: 'Redo',\n    },\n    specialChar: {\n      specialChar: 'SPECIAL CHARACTERS',\n      select: 'Select Special characters',\n    },\n    output: {\n      noSelection: 'No Selection Made!',\n    },\n  },\n});\n"], "names": ["$", "summernote", "lang", "extend", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "sizeunit", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "resizeNone", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "output", "noSelection"], "sourceRoot": ""}