/**
 * Custom Scrollbar JavaScript for Nội Thất Băng Vũ
 * Tùy chỉnh thanh cuộn dọc với tông màu chủ đạo của website
 * Thiết kế tinh tế, mềm mại và hiện đại
 */

document.addEventListener('DOMContentLoaded', function() {
    // Khởi tạo tùy chỉnh thanh cuộn
    initCustomScrollbar();
});

/**
 * Khởi tạo tùy chỉnh thanh cuộn
 */
function initCustomScrollbar() {
    // Lấy màu chủ đạo từ biến CSS
    const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary').trim() || '#F37321';
    const primaryDarkColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-dark').trim() || '#D35400';
    const primaryLightColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-light').trim() || '#FF9D5C';
    
    // Tạo style element
    const style = document.createElement('style');
    
    // Định nghĩa CSS cho thanh cuộn tùy chỉnh
    style.textContent = `
        /* Tùy chỉnh thanh cuộn cho toàn bộ trang */
        ::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }
        
        /* Track (phần nền của thanh cuộn) */
        ::-webkit-scrollbar-track {
            background: rgba(243, 115, 33, 0.05);
            border-radius: 10px;
        }
        
        /* Thumb (phần thanh cuộn có thể kéo) */
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, ${primaryColor}, ${primaryDarkColor});
            border-radius: 10px;
            border: 3px solid transparent;
            background-clip: content-box;
            transition: all 0.3s ease;
        }
        
        /* Hover effect cho thumb */
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, ${primaryDarkColor}, ${primaryColor});
            border: 2px solid transparent;
            background-clip: content-box;
        }
        
        /* Tùy chỉnh thanh cuộn cho các phần tử có class .custom-scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(243, 115, 33, 0.03);
            border-radius: 8px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, ${primaryLightColor}, ${primaryColor});
            border-radius: 8px;
            border: 2px solid transparent;
            background-clip: content-box;
        }
        
        /* Tùy chỉnh cho Firefox */
        * {
            scrollbar-width: thin;
            scrollbar-color: ${primaryColor} rgba(243, 115, 33, 0.05);
        }
        
        /* Hiệu ứng mờ dần cho đầu và cuối thanh cuộn */
        .scroll-container {
            position: relative;
        }
        
        .scroll-container::before,
        .scroll-container::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            height: 30px;
            pointer-events: none;
            z-index: 1;
        }
        
        .scroll-container::before {
            top: 0;
            background: linear-gradient(to bottom, white, transparent);
        }
        
        .scroll-container::after {
            bottom: 0;
            background: linear-gradient(to top, white, transparent);
        }
        
        /* Tùy chỉnh cho các phần tử có thanh cuộn ngang */
        .horizontal-scroll {
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
        }
        
        .horizontal-scroll::-webkit-scrollbar {
            height: 6px;
        }
        
        .horizontal-scroll::-webkit-scrollbar-track {
            background: rgba(243, 115, 33, 0.03);
            border-radius: 3px;
        }
        
        .horizontal-scroll::-webkit-scrollbar-thumb {
            background: linear-gradient(to right, ${primaryLightColor}, ${primaryColor});
            border-radius: 3px;
        }
    `;
    
    // Thêm style vào head
    document.head.appendChild(style);
    
    // Thêm class custom-scrollbar cho các phần tử có overflow
    addCustomScrollbarClass();
    
    // Thêm hiệu ứng mờ dần cho các container có thanh cuộn
    addScrollFadeEffect();
}

/**
 * Thêm class custom-scrollbar cho các phần tử có overflow
 */
function addCustomScrollbarClass() {
    // Danh sách các selector cần thêm class custom-scrollbar
    const selectors = [
        '.product-description',
        '.product-details',
        '.blog-content',
        '.comment-section',
        '.modal-body',
        '.dropdown-menu',
        '.mega-menu-content',
        '.sidebar-content',
        '.tab-content',
        '.accordion-content',
        '.custom-editor-area',
        '.custom-editor-html-view'
    ];
    
    // Thêm class custom-scrollbar cho các phần tử
    selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (element) {
                element.classList.add('custom-scrollbar');
            }
        });
    });
}

/**
 * Thêm hiệu ứng mờ dần cho các container có thanh cuộn
 */
function addScrollFadeEffect() {
    // Danh sách các selector cần thêm hiệu ứng mờ dần
    const selectors = [
        '.product-gallery-thumbnails',
        '.related-products',
        '.category-list',
        '.tag-list',
        '.horizontal-scroll'
    ];
    
    // Thêm class scroll-container cho các phần tử
    selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (element) {
                element.classList.add('scroll-container');
            }
        });
    });
}
