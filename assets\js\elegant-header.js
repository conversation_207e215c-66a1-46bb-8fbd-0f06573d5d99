/**
 * Elegant Header JavaScript for Nội Thất Bàng Vũ
 * Handles interactive features of the elegant header
 */

document.addEventListener('DOMContentLoaded', function () {
  // Mobile menu toggle
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const mobileMenu = document.querySelector('.mobile-menu');
  const mobileMenuClose = document.querySelector('.mobile-menu-close');
  const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');

  if (mobileMenuToggle && mobileMenu) {
    mobileMenuToggle.addEventListener('click', function () {
      mobileMenu.classList.add('active');
      document.body.classList.add('overflow-hidden');
      if (mobileMenuOverlay) {
        mobileMenuOverlay.classList.add('active');
      }
    });
  }

  if (mobileMenuClose && mobileMenu) {
    mobileMenuClose.addEventListener('click', function () {
      mobileMenu.classList.remove('active');
      document.body.classList.remove('overflow-hidden');
      if (mobileMenuOverlay) {
        mobileMenuOverlay.classList.remove('active');
      }
    });
  }

  if (mobileMenuOverlay) {
    mobileMenuOverlay.addEventListener('click', function () {
      mobileMenu.classList.remove('active');
      document.body.classList.remove('overflow-hidden');
      this.classList.remove('active');
    });
  }

  // Mobile dropdown toggles
  const mobileDropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');

  mobileDropdownToggles.forEach(function (toggle) {
    toggle.addEventListener('click', function (e) {
      e.preventDefault();
      const parent = this.parentElement;
      const submenu = parent.querySelector('.mobile-submenu');

      if (parent.classList.contains('active')) {
        parent.classList.remove('active');
        submenu.style.maxHeight = '0px';
        this.querySelector('i').classList.remove('fa-chevron-up');
        this.querySelector('i').classList.add('fa-chevron-down');
      } else {
        parent.classList.add('active');
        submenu.style.maxHeight = submenu.scrollHeight + 'px';
        this.querySelector('i').classList.remove('fa-chevron-down');
        this.querySelector('i').classList.add('fa-chevron-up');
      }
    });
  });

  // Sticky header behavior
  const header = document.querySelector('.elegant-header');
  const topBar = document.querySelector('.top-bar');
  let lastScrollTop = 0;
  let isScrolled = false; // Biến theo dõi trạng thái hiện tại
  let scrollStateChangeTimer = null; // Timer để trì hoãn thay đổi trạng thái
  const scrollThreshold = 10; // Ngưỡng để thêm/xóa class scrolled
  const scrollHysteresis = 5; // Khoảng trễ để tránh nhấp nháy (hysteresis)

  if (header && topBar) {
    window.addEventListener('scroll', function () {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      // Xử lý trạng thái scrolled với hysteresis để tránh nhấp nháy
      // Nếu đang ở trạng thái không scrolled, chỉ chuyển sang scrolled khi vượt qua ngưỡng
      // Nếu đang ở trạng thái scrolled, chỉ chuyển về không scrolled khi thấp hơn (ngưỡng - hysteresis)
      if (!isScrolled && scrollTop > scrollThreshold) {
        // Chuyển từ không scrolled sang scrolled
        clearTimeout(scrollStateChangeTimer);
        scrollStateChangeTimer = setTimeout(() => {
          header.classList.add('scrolled');
          isScrolled = true;
        }, 50); // Trì hoãn nhỏ để tránh thay đổi quá nhanh
      } else if (isScrolled && scrollTop < (scrollThreshold - scrollHysteresis)) {
        // Chuyển từ scrolled sang không scrolled
        clearTimeout(scrollStateChangeTimer);
        scrollStateChangeTimer = setTimeout(() => {
          header.classList.remove('scrolled');
          isScrolled = false;
        }, 50); // Trì hoãn nhỏ để tránh thay đổi quá nhanh
      }

      // Hide/show top bar based on scroll direction
      if (scrollTop > lastScrollTop && scrollTop > topBar.offsetHeight) {
        // Scrolling down
        topBar.style.transform = 'translateY(-100%)';
      } else {
        // Scrolling up
        topBar.style.transform = 'translateY(0)';
      }

      lastScrollTop = scrollTop;
    });

    // Kiểm tra trạng thái ban đầu khi trang tải
    const initialScrollTop = window.pageYOffset || document.documentElement.scrollTop;
    if (initialScrollTop > scrollThreshold) {
      header.classList.add('scrolled');
      isScrolled = true;
    }
  }

  // Search input focus effect
  const searchInput = document.querySelector('.search-input');

  if (searchInput) {
    searchInput.addEventListener('focus', function() {
      this.parentElement.classList.add('focused');
    });

    searchInput.addEventListener('blur', function() {
      this.parentElement.classList.remove('focused');
    });
  }

  // Live search functionality
  if (searchInput) {
    searchInput.addEventListener('input', function () {
      // This would typically make an AJAX call to get search suggestions
      const searchValue = this.value.trim();

      if (searchValue.length > 0) {
        // Here you would make an AJAX call to get search suggestions
        // and update the UI accordingly
      }
    });
  }
});
