/*
 * Logo and Background Synchronization CSS
 * Smooth transitions for logo and background changes
 * Created: 2025-07-27
 * 
 * Features:
 * - Synchronized logo and background transitions
 * - Smooth color morphing
 * - Cross-fade logo transitions
 * - Coordinated timing
 */

/* ===== LOGO TRANSITION SYSTEM ===== */

/* Logo container optimization */
.premium-logo-image {
  /* Create stacking context for smooth transitions */
  position: relative;
  overflow: hidden;
  
  /* Hardware acceleration */
  will-change: transform;
  transform: translateZ(0);
  
  /* Optimized layer composition */
  contain: layout style paint;
}

/* Logo image base styles */
.premium-logo-image img {
  /* Smooth transition properties */
  will-change: opacity, filter, transform;
  transform: translateZ(0);
  
  /* Unified transition timing */
  transition:
    opacity 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94),
    filter 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  /* Prevent layout shifts */
  display: block;
  width: 100%;
  height: auto;
  
  /* Optimize image rendering */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* ===== CROSS-FADE LOGO TRANSITION ===== */

/* Create overlay system for smooth logo transitions */
.premium-logo-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  
  /* Background for dark logo */
  background-image: url('../images/logo/logo-chu-trang.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  
  /* Initially hidden */
  opacity: 0;
  
  /* Hardware acceleration */
  will-change: opacity;
  transform: translateZ(0);
  
  /* Smooth transition */
  transition: opacity 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  /* Layer above original logo */
  z-index: 2;
  
  /* Prevent interaction */
  pointer-events: none;
}

/* Show dark logo overlay when scrolled */
.premium-header.scrolled .premium-logo-image::before {
  opacity: 1;
}

/* Fade out original logo when scrolled */
.premium-header.scrolled .premium-logo-image img {
  opacity: 0.3;
  filter: brightness(0.7);
}

/* ===== BACKGROUND TRANSITION COORDINATION ===== */

/* Synchronized background transitions */
.mid-header-container,
.bottom-header-container {
  /* Position for overlay effects */
  position: relative;
  
  /* Hardware acceleration */
  will-change: background-color;
  transform: translateZ(0);
  
  /* Coordinated transition timing with logo */
  transition:
    background-color 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94),
    border-color 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94),
    box-shadow 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  /* Optimized layer composition */
  contain: layout style paint;
}

/* Create gradient overlay for smooth background transition */
.mid-header-container::before,
.bottom-header-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  
  /* Dark background gradient */
  background: linear-gradient(
    135deg,
    rgba(32, 40, 52, 0.95) 0%,
    rgba(42, 52, 65, 0.95) 50%,
    rgba(32, 40, 52, 0.95) 100%
  );
  
  /* Initially hidden */
  opacity: 0;
  
  /* Hardware acceleration */
  will-change: opacity;
  transform: translateZ(0);
  
  /* Synchronized transition with logo */
  transition: opacity 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  /* Behind content */
  z-index: -1;
  
  /* Prevent interaction */
  pointer-events: none;
}

/* Show background overlay when scrolled */
.premium-header.scrolled .mid-header-container::before,
.premium-header.scrolled .bottom-header-container::before {
  opacity: 1;
}

/* ===== TEXT COLOR SYNCHRONIZATION ===== */

/* Smooth text color transitions */
.premium-logo-title,
.premium-logo-tagline,
.nav-link,
.action-btn,
.search-input {
  /* Hardware acceleration */
  will-change: color;
  transform: translateZ(0);
  
  /* Synchronized color transition */
  transition: color 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Coordinated text color changes when scrolled */
.premium-header.scrolled .premium-logo-title {
  color: rgba(255, 255, 255, 0.95);
}

.premium-header.scrolled .premium-logo-tagline {
  color: rgba(255, 255, 255, 0.7);
}

.premium-header.scrolled .nav-link {
  color: rgba(255, 255, 255, 0.85);
}

.premium-header.scrolled .action-btn {
  color: rgba(255, 255, 255, 0.85);
}

/* ===== SEARCH FORM SYNCHRONIZATION ===== */

/* Search form background coordination */
.search-form {
  /* Position for overlay effects */
  position: relative;
  
  /* Hardware acceleration */
  will-change: background-color, border-color;
  transform: translateZ(0);
  
  /* Synchronized transition */
  transition:
    background-color 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94),
    border-color 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94),
    box-shadow 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Search form dark mode */
.premium-header.scrolled .search-form {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.premium-header.scrolled .search-input {
  color: rgba(255, 255, 255, 0.95);
}

.premium-header.scrolled .search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* ===== ENHANCED TRANSITION EFFECTS ===== */

/* Subtle glow effect during transition */
.premium-header.scrolled .premium-logo-image::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  
  /* Subtle glow */
  background: radial-gradient(
    circle at center,
    rgba(249, 115, 22, 0.1) 0%,
    transparent 70%
  );
  
  /* Initially hidden */
  opacity: 0;
  
  /* Hardware acceleration */
  will-change: opacity;
  transform: translateZ(0);
  
  /* Delayed transition for subtle effect */
  transition: opacity 600ms cubic-bezier(0.25, 0.46, 0.45, 0.94) 200ms;
  
  /* Behind logo */
  z-index: 1;
  
  /* Prevent interaction */
  pointer-events: none;
}

/* Show glow when scrolled */
.premium-header.scrolled .premium-logo-image::after {
  opacity: 1;
}

/* ===== MORPHING ANIMATION ===== */

/* Create morphing effect for smooth transitions */
@keyframes logo-morph-to-dark {
  0% {
    opacity: 0;
    transform: translateZ(0) scale(0.95);
    filter: brightness(1.2);
  }
  50% {
    opacity: 0.5;
    transform: translateZ(0) scale(1.02);
    filter: brightness(1.1);
  }
  100% {
    opacity: 1;
    transform: translateZ(0) scale(1);
    filter: brightness(1);
  }
}

@keyframes logo-morph-to-light {
  0% {
    opacity: 1;
    transform: translateZ(0) scale(1);
    filter: brightness(1);
  }
  50% {
    opacity: 0.5;
    transform: translateZ(0) scale(1.02);
    filter: brightness(1.1);
  }
  100% {
    opacity: 0;
    transform: translateZ(0) scale(0.95);
    filter: brightness(1.2);
  }
}

/* Apply morphing animations */
.premium-header.scrolled .premium-logo-image::before {
  animation: logo-morph-to-dark 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.premium-header:not(.scrolled) .premium-logo-image::before {
  animation: logo-morph-to-light 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* ===== BACKGROUND MORPHING ===== */

@keyframes background-morph-to-dark {
  0% {
    opacity: 0;
    transform: translateZ(0) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateZ(0) scale(1);
  }
}

@keyframes background-morph-to-light {
  0% {
    opacity: 1;
    transform: translateZ(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateZ(0) scale(1.02);
  }
}

/* Apply background morphing */
.premium-header.scrolled .mid-header-container::before,
.premium-header.scrolled .bottom-header-container::before {
  animation: background-morph-to-dark 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.premium-header:not(.scrolled) .mid-header-container::before,
.premium-header:not(.scrolled) .bottom-header-container::before {
  animation: background-morph-to-light 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* ===== ACCESSIBILITY ===== */

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .premium-logo-image::before,
  .mid-header-container::before,
  .bottom-header-container::before,
  .premium-logo-image::after {
    /* Disable animations for accessibility */
    animation: none !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .premium-header.scrolled .premium-logo-image::before {
    /* Ensure high contrast in accessibility mode */
    filter: contrast(1.5);
  }
}
