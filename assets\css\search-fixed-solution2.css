/**
 * <PERSON><PERSON><PERSON><PERSON> pháp 2: Position Fixed với JS Positioning
 * Tách search suggestions ra khỏi stacking context bằng position fixed
 */

/*
 * CSS Variables cho solution 2
 */
:root {
    --search-fixed-z-index: 9999;
    --search-transition-duration: 0.25s;
    --search-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

/*
 * Search container - giữ nguyên overflow hidden
 */
.search-container-solution2 {
    position: relative;
    z-index: 100;
}

/*
 * Search input styling
 */
.search-input-solution2 {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 16px;
    transition: all var(--search-transition-duration) var(--search-transition-timing);
    background: white;
    outline: none;
}

.search-input-solution2:focus {
    border-color: #f59e0b;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

/*
 * Search suggestions với position fixed
 * <PERSON><PERSON> tr<PERSON> sẽ được t<PERSON> toán bằng JavaScript
 */
.search-suggestions-solution2 {
    position: fixed !important;
    z-index: var(--search-fixed-z-index) !important;

    /* Style cơ bản */
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;

    /* Animation */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all var(--search-transition-duration) var(--search-transition-timing);

    /* Đảm bảo không bị clip */
    contain: none !important;

    /* Performance optimizations */
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
}

/*
 * Khi suggestions được hiển thị
 */
.search-suggestions-solution2.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/*
 * Suggestion items styling
 */
.search-suggestion-item-solution2 {
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-suggestion-item-solution2:last-child {
    border-bottom: none;
}

.search-suggestion-item-solution2:hover {
    background-color: #f9fafb;
    transform: translateX(4px);
}

.search-suggestion-item-solution2:active {
    background-color: #f3f4f6;
    transform: translateX(2px);
}

/*
 * Suggestion item content
 */
.search-suggestion-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
}

.search-suggestion-text {
    flex: 1;
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

.search-suggestion-category {
    font-size: 12px;
    color: #9ca3af;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
}

/*
 * Loading state
 */
.search-suggestions-solution2.loading {
    opacity: 0.7;
}

.search-loading-item {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
    border-radius: 4px;
}

.search-loading-icon {
    width: 20px;
    height: 20px;
}

.search-loading-text {
    height: 16px;
    flex: 1;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/*
 * Scrollbar styling
 */
.search-suggestions-solution2::-webkit-scrollbar {
    width: 6px;
}

.search-suggestions-solution2::-webkit-scrollbar-track {
    background: rgba(243, 115, 33, 0.05);
    border-radius: 3px;
}

.search-suggestions-solution2::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #FF9D5C, #F37321);
    border-radius: 3px;
    transition: all 0.3s ease;
}

.search-suggestions-solution2::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #F37321, #D35400);
}

/*
 * Responsive adjustments
 */
@media (max-width: 768px) {
    .search-suggestions-solution2 {
        max-height: 60vh;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        /* Trên mobile, đảm bảo không vượt quá viewport */
        max-width: calc(100vw - 32px);
    }

    .search-suggestion-item-solution2 {
        padding: 16px;
        font-size: 16px; /* Tránh zoom trên iOS */
    }
}

@media (max-width: 480px) {
    .search-suggestions-solution2 {
        max-height: 50vh;
        border-radius: 6px;
    }

    .search-suggestion-item-solution2 {
        padding: 14px 12px;
    }
}

/*
 * Accessibility improvements
 */
.search-suggestions-solution2[aria-hidden="false"] {
    opacity: 1 !important;
    visibility: visible !important;
}

.search-suggestion-item-solution2:focus {
    outline: 2px solid #f59e0b;
    outline-offset: 2px;
    background-color: #fef3c7;
}

.search-suggestion-item-solution2[aria-selected="true"] {
    background-color: #fef3c7;
    border-left: 3px solid #f59e0b;
}

/*
 * High contrast mode support
 */
@media (prefers-contrast: high) {
    .search-suggestions-solution2 {
        border: 2px solid #000;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .search-suggestion-item-solution2:hover {
        background-color: #000;
        color: #fff;
    }
}

/*
 * Reduced motion support
 */
@media (prefers-reduced-motion: reduce) {
    .search-suggestions-solution2 {
        transition: opacity 0.1s ease;
        transform: none !important;
    }

    .search-suggestion-item-solution2 {
        transition: background-color 0.1s ease;
        transform: none !important;
    }

    .search-loading-skeleton {
        animation: none;
        background: #e0e0e0;
    }
}

/*
 * Dark mode support (if needed)
 */
@media (prefers-color-scheme: dark) {
    .search-suggestions-solution2 {
        background: #1f2937;
        border-color: #374151;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .search-suggestion-item-solution2 {
        border-bottom-color: #374151;
        color: #f9fafb;
    }

    .search-suggestion-item-solution2:hover {
        background-color: #374151;
    }

    .search-suggestion-text {
        color: #f9fafb;
    }

    .search-suggestion-category {
        background: #374151;
        color: #d1d5db;
    }
}

/*
 * Print styles
 */
@media print {
    .search-suggestions-solution2 {
        display: none !important;
    }
}