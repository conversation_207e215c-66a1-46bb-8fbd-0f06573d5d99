/*
 * Typing Effect for Search Input Placeholder
 * Hiệu ứng gõ chữ cho placeholder của ô tìm kiếm
 */

(function() {
    'use strict';

    // C<PERSON>u hình hiệu ứng gõ chữ
    const config = {
        texts: [
            "Tìm kiếm không gian mơ ước...",
            "Bắt đầu với 'tủ quần áo hiện đại'..."
        ],
        typeSpeed: 100,        // Tốc độ gõ (ms)
        deleteSpeed: 50,       // Tốc độ xóa (ms)
        pauseTime: 2000,       // Thời gian dừng giữa các câu (ms)
        deleteDelay: 1500,     // Thời gian chờ trước khi xóa (ms)
        loop: true             // Lặp lại hiệu ứng
    };

    let currentTextIndex = 0;
    let currentCharIndex = 0;
    let isDeleting = false;
    let isTyping = false;
    let isPaused = false;
    let typingTimeout;
    let cursorBlinkInterval;
    let searchInput;
    let header;

    /**
     * Khởi tạo hiệu ứng gõ chữ
     */
    function initTypingEffect() {
        // Tìm ô tìm kiếm trong header
        searchInput = document.getElementById('header-search');
        
        if (!searchInput) {
            console.warn('Typing Effect: Không tìm thấy ô tìm kiếm với id "header-search"');
            return;
        }

        // Tìm header để theo dõi trạng thái scrolled
        header = document.querySelector('.premium-header') || document.querySelector('.mobile-header');

        // Lưu placeholder gốc
        const originalPlaceholder = searchInput.getAttribute('placeholder') || 'Tìm kiếm sản phẩm...';

        // Thêm placeholder gốc vào cuối danh sách nếu chưa có
        if (!config.texts.includes(originalPlaceholder)) {
            config.texts.push(originalPlaceholder);
        }

        // Thêm CSS cho hiệu ứng
        addTypingCSS();

        // Bắt đầu hiệu ứng
        startTypingEffect();

        // Dừng hiệu ứng khi user focus vào ô tìm kiếm
        searchInput.addEventListener('focus', stopTypingEffect);
        
        // Tiếp tục hiệu ứng khi user blur khỏi ô tìm kiếm (nếu ô trống)
        searchInput.addEventListener('blur', function() {
            if (!this.value.trim()) {
                setTimeout(startTypingEffect, 500);
            }
        });

        // Dừng hiệu ứng khi user bắt đầu gõ
        searchInput.addEventListener('input', function() {
            if (this.value.trim()) {
                stopTypingEffect();
            }
        });

        // Theo dõi thay đổi class của header để cập nhật màu sắc
        if (header) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        // Cập nhật màu sắc khi trạng thái header thay đổi
                        if (isTyping && searchInput) {
                            const currentPlaceholder = searchInput.getAttribute('placeholder') || '';
                            updatePlaceholderWithColors(currentPlaceholder);
                        }
                    }
                });
            });

            observer.observe(header, {
                attributes: true,
                attributeFilter: ['class']
            });
        }
    }

    /**
     * Bắt đầu hiệu ứng gõ chữ
     */
    function startTypingEffect() {
        if (!searchInput || searchInput.value.trim() || document.activeElement === searchInput) {
            return;
        }

        isTyping = true;
        isPaused = false;
        searchInput.classList.add('typing-active');

        // Reset về trạng thái ban đầu
        currentTextIndex = 0;
        currentCharIndex = 0;
        isDeleting = false;

        typeText();
    }

    /**
     * Dừng hiệu ứng gõ chữ
     */
    function stopTypingEffect() {
        isTyping = false;
        isPaused = false;

        if (searchInput) {
            searchInput.classList.remove('typing-active');
            // Khôi phục placeholder gốc
            searchInput.setAttribute('placeholder', 'Tìm kiếm sản phẩm...');
        }

        if (typingTimeout) {
            clearTimeout(typingTimeout);
            typingTimeout = null;
        }

        if (cursorBlinkInterval) {
            clearInterval(cursorBlinkInterval);
            cursorBlinkInterval = null;
        }
    }

    /**
     * Hiệu ứng gõ chữ chính
     */
    function typeText() {
        if (!isTyping || !searchInput) return;

        const currentText = config.texts[currentTextIndex];

        if (isDeleting) {
            // Xóa ký tự - cursor đứng yên khi đang xóa
            stopCursorBlink();
            const displayText = currentText.substring(0, currentCharIndex - 1);
            updatePlaceholderWithColors(displayText + '|');
            currentCharIndex--;

            if (currentCharIndex === 0) {
                isDeleting = false;
                currentTextIndex = (currentTextIndex + 1) % config.texts.length;
                // Dừng và cursor nhấp nháy
                startPauseWithBlinkingCursor('');
            } else {
                typingTimeout = setTimeout(typeText, config.deleteSpeed);
            }
        } else {
            // Gõ ký tự - cursor đứng yên khi đang gõ
            stopCursorBlink();
            const displayText = currentText.substring(0, currentCharIndex + 1);
            updatePlaceholderWithColors(displayText + '|');
            currentCharIndex++;

            if (currentCharIndex === currentText.length) {
                // Hoàn thành gõ - dừng và cursor nhấp nháy
                startPauseWithBlinkingCursor(currentText);
            } else {
                typingTimeout = setTimeout(typeText, config.typeSpeed);
            }
        }
    }

    /**
     * Bắt đầu dừng với cursor nhấp nháy
     */
    function startPauseWithBlinkingCursor(text) {
        if (!isTyping) return;

        isPaused = true;
        let showCursor = true;

        // Hiệu ứng nhấp nháy cursor
        cursorBlinkInterval = setInterval(() => {
            if (!isTyping || !isPaused) return;

            showCursor = !showCursor;
            const displayText = text + (showCursor ? '|' : '');
            updatePlaceholderWithColors(displayText);
        }, 530); // Tốc độ nhấp nháy cursor

        // Sau thời gian dừng, tiếp tục với hành động tiếp theo
        const delay = currentCharIndex === config.texts[currentTextIndex].length ? config.deleteDelay : config.pauseTime;

        typingTimeout = setTimeout(() => {
            stopCursorBlink();
            isPaused = false;

            if (currentCharIndex === config.texts[currentTextIndex].length) {
                // Bắt đầu xóa
                if (config.loop) {
                    isDeleting = true;
                    typeText();
                }
            } else {
                // Tiếp tục gõ
                typeText();
            }
        }, delay);
    }

    /**
     * Cập nhật placeholder với màu sắc phù hợp theo trạng thái header
     */
    function updatePlaceholderWithColors(text) {
        if (!searchInput) return;

        searchInput.setAttribute('placeholder', text);

        // Cập nhật màu sắc dựa trên trạng thái scrolled của header
        if (header && header.classList.contains('scrolled')) {
            // Header đã scrolled - sử dụng màu trắng
            searchInput.classList.add('header-scrolled');
        } else {
            // Header bình thường - sử dụng màu xám
            searchInput.classList.remove('header-scrolled');
        }
    }

    /**
     * Dừng hiệu ứng nhấp nháy cursor
     */
    function stopCursorBlink() {
        if (cursorBlinkInterval) {
            clearInterval(cursorBlinkInterval);
            cursorBlinkInterval = null;
        }
    }

    /**
     * Thêm CSS cho hiệu ứng cursor nhấp nháy
     */
    function addTypingCSS() {
        const style = document.createElement('style');
        style.textContent = `
            /* Hiệu ứng typing cho search input */
            .search-input.typing-active {
                position: relative;
            }

            /* Màu sắc mặc định (header bình thường) */
            .search-input.typing-active::placeholder {
                color: #666;
                font-style: normal;
                background: linear-gradient(
                    90deg,
                    #666 0%,
                    #666 calc(100% - 1ch),
                    #F37321 calc(100% - 1ch),
                    #F37321 100%
                );
                background-size: 100% 100%;
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            /* Màu sắc khi header scrolled (nền tối) */
            .search-input.typing-active.header-scrolled::placeholder {
                background: linear-gradient(
                    90deg,
                    rgba(255, 255, 255, 0.6) 0%,
                    rgba(255, 255, 255, 0.6) calc(100% - 1ch),
                    #F37321 calc(100% - 1ch),
                    #F37321 100%
                );
                background-size: 100% 100%;
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            /* Dừng hiệu ứng khi focus hoặc có nội dung */
            .search-input:focus::placeholder,
            .search-input:not(:placeholder-shown)::placeholder {
                background: #999;
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            /* Dừng hiệu ứng khi focus hoặc có nội dung (header scrolled) */
            .search-input.header-scrolled:focus::placeholder,
            .search-input.header-scrolled:not(:placeholder-shown)::placeholder {
                background: rgba(255, 255, 255, 0.6);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            /* Cải thiện hiệu ứng cho mobile */
            @media (max-width: 768px) {
                .search-input.typing-active::placeholder {
                    font-size: 14px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Khởi tạo khi DOM đã sẵn sàng
     */
    function init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                addTypingCSS();
                // Delay nhỏ để đảm bảo tất cả script khác đã load
                setTimeout(initTypingEffect, 100);
            });
        } else {
            addTypingCSS();
            setTimeout(initTypingEffect, 100);
        }
    }

    // Khởi tạo
    init();

    // Export cho debugging (nếu cần)
    window.TypingEffect = {
        start: startTypingEffect,
        stop: stopTypingEffect,
        config: config
    };

})();
