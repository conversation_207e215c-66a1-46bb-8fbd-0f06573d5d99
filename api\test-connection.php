<?php
/**
 * Test API connection và database
 */

// Đặt header JSON
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

try {
    // Include init để có các hàm cần thiết
    require_once '../includes/init.php';
    
    // Test database connection
    if (!isset($conn)) {
        throw new Exception('Database connection not found');
    }
    
    // Test basic query
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM products WHERE status = 1");
    $stmt->execute();
    $result = $stmt->fetch();
    $total_products = $result['total'];
    
    // Test get_products_with_filters function
    if (!function_exists('get_products_with_filters')) {
        throw new Exception('Function get_products_with_filters not found');
    }
    
    // Test basic call
    $products = get_products_with_filters([], [], 5, 0, '', 1);
    
    echo json_encode([
        'success' => true,
        'message' => 'API connection successful',
        'data' => [
            'total_products_in_db' => $total_products,
            'test_products_count' => count($products),
            'functions_available' => [
                'get_products_with_filters' => function_exists('get_products_with_filters'),
                'count_products_with_filters' => function_exists('count_products_with_filters'),
                'sanitize' => function_exists('sanitize'),
                'get_category_by_id' => function_exists('get_category_by_id'),
                'get_product_url' => function_exists('get_product_url'),
                'format_currency' => function_exists('format_currency')
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
