/**
 * Mobile Bottom Navigation JavaScript for Nội Thất Bàng Vũ
 * X<PERSON> lý các tính năng tương tác của thanh điều hướng dưới cùng trên điện thoại
 */

document.addEventListener('DOMContentLoaded', function () {
  console.log('Mobile Bottom Nav JS loaded');

  // <PERSON><PERSON><PERSON> phần tử DOM
  const mobileBottomNav = document.querySelector('.mobile-bottom-nav');
  const mobileNavItems = document.querySelectorAll('.mobile-nav-item');

  console.log('BASE_URL:', BASE_URL);
  console.log('Mobile nav items:', mobileNavItems.length);

  // Log các href của các mobile-nav-item
  mobileNavItems.forEach((item, index) => {
    console.log(`Item ${index} href:`, item.getAttribute('href'));
  });

  // Ki<PERSON>m tra xem các phần tử có tồn tại không
  if (!mobileBottomNav) {
    console.warn('Mobile bottom nav not found');
    return;
  }

  // Thêm hiệu ứng ripple khi nhấn vào các mục
  mobileNavItems.forEach(function (item) {
    item.addEventListener('click', function (e) {
      // Tạo hiệu ứng ripple
      const ripple = document.createElement('span');
      ripple.classList.add('nav-ripple-effect');

      // Lấy vị trí nhấn chuột
      const rect = item.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Thiết lập vị trí cho ripple
      ripple.style.left = x + 'px';
      ripple.style.top = y + 'px';

      // Thêm ripple vào item
      item.appendChild(ripple);

      // Xóa ripple sau khi hoàn thành animation
      setTimeout(function () {
        ripple.remove();
      }, 600);
    });
  });

  // Xử lý hiệu ứng khi cuộn trang
  window.addEventListener(
    'scroll',
    function () {
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;

      // Thêm hiệu ứng khi cuộn xuống
      if (scrollTop > 50) {
        mobileBottomNav.classList.add('scrolled');
      } else {
        mobileBottomNav.classList.remove('scrolled');
      }
    },
    { passive: true }
  );

  // Thiết lập active item dựa trên URL hiện tại
  function setActiveNavItem() {
    const currentPath = window.location.pathname;
    console.log('Current path:', currentPath);

    // Đảm bảo rằng các tab được active đúng
    // Xóa active class từ tất cả các tab
    mobileNavItems.forEach((item) => item.classList.remove('active'));

    // Thêm active class dựa trên URL hiện tại
    if (
      currentPath === '/' ||
      currentPath.endsWith('/index.php') ||
      currentPath === '/noithatbangvu/' ||
      currentPath === '/noithatbangvu/index.php'
    ) {
      // Trang chủ
      document
        .querySelector('.mobile-nav-item.home-nav-item')
        .classList.add('active');
    } else if (
      currentPath.includes('/products.php') ||
      currentPath.includes('/category.php') ||
      currentPath.includes('/product.php')
    ) {
      // Sản phẩm
      document
        .querySelector('.mobile-nav-item[href*="/products.php"]')
        .classList.add('active');
    } else if (currentPath.includes('/search.php')) {
      // Tìm kiếm
      document
        .querySelector('.mobile-nav-item[href*="/search.php"]')
        .classList.add('active');
    } else if (currentPath.includes('/cart.php')) {
      // Giỏ hàng
      document
        .querySelector('.mobile-nav-item[href*="/cart.php"]')
        .classList.add('active');
    } else if (
      currentPath.includes('/account/') ||
      currentPath.includes('/profile.php')
    ) {
      // Tài khoản
      const accountItem = document.querySelector(
        '.mobile-nav-item[href*="/account/"]'
      );
      if (accountItem) accountItem.classList.add('active');
    } else if (currentPath.includes('/login.php')) {
      // Đăng nhập
      const loginItem = document.querySelector(
        '.mobile-nav-item[href*="/login.php"]'
      );
      if (loginItem) loginItem.classList.add('active');
    }

    // Log số lượng active items để debug
    console.log(
      'Active items:',
      document.querySelectorAll('.mobile-nav-item.active').length
    );
  }

  // Thiết lập active item khi tải trang
  setActiveNavItem();
});
