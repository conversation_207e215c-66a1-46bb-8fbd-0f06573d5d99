<?php
// Include init
require_once '../includes/init.php';

// Ki<PERSON>m tra đăng nhập
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Bạn cần đăng nhập để thực hiện thao tác này'
    ]);
    exit;
}

// Lấy ID người dùng
$user_id = $_SESSION['user_id'];

// L<PERSON>y thời gian cập nhật cuối cùng
$last_update = isset($_GET['last_update']) ? (int)$_GET['last_update'] : 0;

// Lấy danh sách đơn hàng đã cập nhật sau thời gian đã cho
try {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT id, status, updated_at 
        FROM orders 
        WHERE user_id = :user_id 
        AND UNIX_TIMESTAMP(updated_at) > :last_update
        ORDER BY updated_at DESC
    ");
    
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':last_update', $last_update);
    $stmt->execute();
    
    $updated_orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Lấy thông tin chi tiết cho mỗi đơn hàng
    $orders = [];
    foreach ($updated_orders as $order) {
        $order_detail = get_order_by_id($order['id']);
        $status_info = get_order_status_info($order['status'], 'user');
        
        $orders[] = [
            'id' => $order['id'],
            'status' => $order['status'],
            'status_text' => $status_info['text'],
            'status_class' => $status_info['class'],
            'updated_at' => $order['updated_at'],
            'total' => $order_detail['total'],
            'items' => $order_detail['items']
        ];
    }
    
    // Lấy thời gian hiện tại
    $current_timestamp = time();
    
    // Trả về kết quả
    echo json_encode([
        'success' => true,
        'updates' => count($orders) > 0,
        'orders' => $orders,
        'timestamp' => $current_timestamp
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Lỗi: ' . $e->getMessage()
    ]);
}
?>
