{"version": 3, "file": "lang/summernote-tr-TR.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE,kBAAkB;QAC1BC,IAAI,EAAE,WAAW;QACjBC,aAAa,EAAE,aAAa;QAC5BC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,WAAW;QACxBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,YAAY;QACpBC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,WAAW;QACvBC,aAAa,EAAE,WAAW;QAC1BC,SAAS,EAAE,aAAa;QACxBC,UAAU,EAAE,aAAa;QACzBC,SAAS,EAAE,mBAAmB;QAC9BC,YAAY,EAAE,2BAA2B;QACzCC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,gBAAgB;QAChCC,SAAS,EAAE,YAAY;QACvBC,aAAa,EAAE,mBAAmB;QAClCC,SAAS,EAAE,0BAA0B;QACrCC,eAAe,EAAE,aAAa;QAC9BC,eAAe,EAAE,uBAAuB;QACxCC,oBAAoB,EAAE,+BAA+B;QACrDC,GAAG,EAAE,kBAAkB;QACvBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,kBAAkB;QAC7BpB,MAAM,EAAE,YAAY;QACpBgB,GAAG,EAAE,mBAAmB;QACxBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,UAAU;QAChBtB,MAAM,EAAE,eAAe;QACvBuB,MAAM,EAAE,mBAAmB;QAC3BC,IAAI,EAAE,oBAAoB;QAC1BC,aAAa,EAAE,mBAAmB;QAClCT,GAAG,EAAE,kBAAkB;QACvBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,mBAAmB;QAChCC,WAAW,EAAE,kBAAkB;QAC/BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,iBAAiB;QAC9BC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,GAAG;QACNC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,WAAW;QAChBC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,sBAAsB;QACjCC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,WAAW;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,aAAa;QACnBC,MAAM,EAAE,eAAe;QACvBC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,UAAU;QAClBC,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE,iBAAiB;QAC7BC,UAAU,EAAE,YAAY;QACxBC,WAAW,EAAE,WAAW;QACxBC,cAAc,EAAE,mBAAmB;QACnCC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE,wBAAwB;QACxCC,QAAQ,EAAE;MACZ,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,YAAY;QACvBC,KAAK,EAAE,OAAO;QACdC,cAAc,EAAE,oBAAoB;QACpCC,MAAM,EAAE,OAAO;QACfC,mBAAmB,EAAE,wBAAwB;QAC7CC,aAAa,EAAE,OAAO;QACtBC,SAAS,EAAE;MACb,CAAC;MACD3B,IAAI,EAAE;QACJ,iBAAiB,EAAE,gBAAgB;QACnC,MAAM,EAAE,sBAAsB;QAC9B,MAAM,EAAE,oBAAoB;QAC5B,KAAK,EAAE,mBAAmB;QAC1B,OAAO,EAAE,mBAAmB;QAC5B,MAAM,EAAE,6BAA6B;QACrC,QAAQ,EAAE,8BAA8B;QACxC,WAAW,EAAE,oCAAoC;QACjD,eAAe,EAAE,oCAAoC;QACrD,cAAc,EAAE,0BAA0B;QAC1C,aAAa,EAAE,qBAAqB;QACpC,eAAe,EAAE,gBAAgB;QACjC,cAAc,EAAE,qBAAqB;QACrC,aAAa,EAAE,8BAA8B;QAC7C,qBAAqB,EAAE,4BAA4B;QACnD,mBAAmB,EAAE,sBAAsB;QAC3C,SAAS,EAAE,sCAAsC;QACjD,QAAQ,EAAE,sCAAsC;QAChD,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,uDAAuD;QACnE,UAAU,EAAE,uDAAuD;QACnE,UAAU,EAAE,uDAAuD;QACnE,UAAU,EAAE,uDAAuD;QACnE,UAAU,EAAE,uDAAuD;QACnE,UAAU,EAAE,uDAAuD;QACnE,sBAAsB,EAAE,mBAAmB;QAC3C,iBAAiB,EAAE;MACrB,CAAC;MACD4B,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-tr-TR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'tr-TR': {\n      font: {\n        bold: 'Kalın',\n        italic: '<PERSON><PERSON><PERSON>',\n        underline: 'Altı çizili',\n        clear: '<PERSON><PERSON>zle',\n        height: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        name: '<PERSON><PERSON><PERSON>',\n        strikethrough: '<PERSON>st<PERSON> çizili',\n        subscript: 'Alt Simge',\n        superscript: 'Üst Simge',\n        size: 'Yazı tipi boyutu',\n      },\n      image: {\n        image: 'Resim',\n        insert: 'Resim ekle',\n        resizeFull: 'Orjinal boyut',\n        resizeHalf: '1/2 boyut',\n        resizeQuarter: '1/4 boyut',\n        floatLeft: 'Sola hizala',\n        floatRight: 'Sağa hizala',\n        floatNone: 'Hizalamayı kaldır',\n        shapeRounded: 'Şekil: Yuvarlatılmış Köşe',\n        shapeCircle: 'Şekil: Daire',\n        shapeThumbnail: 'Şekil: <PERSON><PERSON>',\n        shapeNone: 'Şekil: Yok',\n        dragImageHere: '<PERSON><PERSON><PERSON> sü<PERSON>',\n        dropImage: '<PERSON>si<PERSON> veya metni bırakın',\n        selectFromFiles: '<PERSON><PERSON><PERSON> seç<PERSON>',\n        maximumFileSize: 'Maksimum dosya boyutu',\n        maximumFileSizeError: 'Maksimum dosya boyutu aşıldı.',\n        url: 'Resim bağlantısı',\n        remove: 'Resimi Kaldır',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video bağlantısı',\n        insert: 'Video ekle',\n        url: 'Video bağlantısı?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion veya Youku)',\n      },\n      link: {\n        link: 'Bağlantı',\n        insert: 'Bağlantı ekle',\n        unlink: 'Bağlantıyı kaldır',\n        edit: 'Bağlantıyı düzenle',\n        textToDisplay: 'Görüntülemek için',\n        url: 'Bağlantı adresi?',\n        openInNewWindow: 'Yeni pencerede aç',\n      },\n      table: {\n        table: 'Tablo',\n        addRowAbove: 'Yukarı satır ekle',\n        addRowBelow: 'Aşağı satır ekle',\n        addColLeft: 'Sola sütun ekle',\n        addColRight: 'Sağa sütun ekle',\n        delRow: 'Satırı sil',\n        delCol: 'Sütunu sil',\n        delTable: 'Tabloyu sil',\n      },\n      hr: {\n        insert: 'Yatay çizgi ekle',\n      },\n      style: {\n        style: 'Biçim',\n        p: 'p',\n        blockquote: 'Alıntı',\n        pre: 'Önbiçimli',\n        h1: 'Başlık 1',\n        h2: 'Başlık 2',\n        h3: 'Başlık 3',\n        h4: 'Başlık 4',\n        h5: 'Başlık 5',\n        h6: 'Başlık 6',\n      },\n      lists: {\n        unordered: 'Madde işaretli liste',\n        ordered: 'Numaralı liste',\n      },\n      options: {\n        help: 'Yardım',\n        fullscreen: 'Tam ekran',\n        codeview: 'HTML Kodu',\n      },\n      paragraph: {\n        paragraph: 'Paragraf',\n        outdent: 'Girintiyi artır',\n        indent: 'Girintiyi azalt',\n        left: 'Sola hizala',\n        center: 'Ortaya hizala',\n        right: 'Sağa hizala',\n        justify: 'Yasla',\n      },\n      color: {\n        recent: 'Son renk',\n        more: 'Daha fazla renk',\n        background: 'Arka plan rengi',\n        foreground: 'Yazı rengi',\n        transparent: 'Seffaflık',\n        setTransparent: 'Şeffaflığı ayarla',\n        reset: 'Sıfırla',\n        resetToDefault: 'Varsayılanlara sıfırla',\n        cpSelect: 'Seç',\n      },\n      shortcut: {\n        shortcuts: 'Kısayollar',\n        close: 'Kapat',\n        textFormatting: 'Yazı biçimlendirme',\n        action: 'Eylem',\n        paragraphFormatting: 'Paragraf biçimlendirme',\n        documentStyle: 'Biçim',\n        extraKeys: 'İlave anahtarlar',\n      },\n      help: {\n        'insertParagraph': 'Paragraf ekler',\n        'undo': 'Son komudu geri alır',\n        'redo': 'Son komudu yineler',\n        'tab': 'Girintiyi artırır',\n        'untab': 'Girintiyi azaltır',\n        'bold': 'Kalın yazma stilini ayarlar',\n        'italic': 'İtalik yazma stilini ayarlar',\n        'underline': 'Altı çizgili yazma stilini ayarlar',\n        'strikethrough': 'Üstü çizgili yazma stilini ayarlar',\n        'removeFormat': 'Biçimlendirmeyi temizler',\n        'justifyLeft': 'Yazıyı sola hizalar',\n        'justifyCenter': 'Yazıyı ortalar',\n        'justifyRight': 'Yazıyı sağa hizalar',\n        'justifyFull': 'Yazıyı her iki tarafa yazlar',\n        'insertUnorderedList': 'Madde işaretli liste ekler',\n        'insertOrderedList': 'Numaralı liste ekler',\n        'outdent': 'Aktif paragrafın girintisini azaltır',\n        'indent': 'Aktif paragrafın girintisini artırır',\n        'formatPara': 'Aktif bloğun biçimini paragraf (p) olarak değiştirir',\n        'formatH1': 'Aktif bloğun biçimini başlık 1 (h1) olarak değiştirir',\n        'formatH2': 'Aktif bloğun biçimini başlık 2 (h2) olarak değiştirir',\n        'formatH3': 'Aktif bloğun biçimini başlık 3 (h3) olarak değiştirir',\n        'formatH4': 'Aktif bloğun biçimini başlık 4 (h4) olarak değiştirir',\n        'formatH5': 'Aktif bloğun biçimini başlık 5 (h5) olarak değiştirir',\n        'formatH6': 'Aktif bloğun biçimini başlık 6 (h6) olarak değiştirir',\n        'insertHorizontalRule': 'Yatay çizgi ekler',\n        'linkDialog.show': 'Bağlantı ayar kutusunu gösterir',\n      },\n      history: {\n        undo: 'Geri al',\n        redo: 'Yinele',\n      },\n      specialChar: {\n        specialChar: 'ÖZEL KARAKTERLER',\n        select: 'Özel Karakterleri seçin',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}