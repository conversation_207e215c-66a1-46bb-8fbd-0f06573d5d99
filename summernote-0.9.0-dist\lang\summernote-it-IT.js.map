{"version": 3, "file": "lang/summernote-it-IT.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,oBAAoB;QAC1BC,MAAM,EAAE,kBAAkB;QAC1BC,SAAS,EAAE,oBAAoB;QAC/BC,KAAK,EAAE,oCAAoC;QAC3CC,MAAM,EAAE,8BAA8B;QACtCC,IAAI,EAAE,eAAe;QACrBC,aAAa,EAAE,eAAe;QAC9BC,SAAS,EAAE,QAAQ;QACnBC,WAAW,EAAE,OAAO;QACpBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,UAAU;QACjBC,MAAM,EAAE,oBAAoB;QAC5BC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE,qBAAqB;QACjCC,aAAa,EAAE,qBAAqB;QACpCC,SAAS,EAAE,sBAAsB;QACjCC,UAAU,EAAE,oBAAoB;QAChCC,SAAS,EAAE,uBAAuB;QAClCC,YAAY,EAAE,oBAAoB;QAClCC,WAAW,EAAE,gBAAgB;QAC7BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,gBAAgB;QAC3BC,aAAa,EAAE,2BAA2B;QAC1CC,SAAS,EAAE,2BAA2B;QACtCC,eAAe,EAAE,iBAAiB;QAClCC,eAAe,EAAE,6BAA6B;QAC9CC,oBAAoB,EAAE,uCAAuC;QAC7DC,GAAG,EAAE,oBAAoB;QACzBC,MAAM,EAAE,kBAAkB;QAC1BC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,0BAA0B;QACrCpB,MAAM,EAAE,iBAAiB;QACzBgB,GAAG,EAAE,eAAe;QACpBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,cAAc;QACpBtB,MAAM,EAAE,wBAAwB;QAChCuB,MAAM,EAAE,sBAAsB;QAC9BC,IAAI,EAAE,uBAAuB;QAC7BC,aAAa,EAAE,wBAAwB;QACvCT,GAAG,EAAE,sBAAsB;QAC3BU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,WAAW,EAAE,qBAAqB;QAClCC,WAAW,EAAE,qBAAqB;QAClCC,UAAU,EAAE,2BAA2B;QACvCC,WAAW,EAAE,yBAAyB;QACtCC,MAAM,EAAE,cAAc;QACtBC,MAAM,EAAE,iBAAiB;QACzBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,SAAS;QACZC,UAAU,EAAE,WAAW;QACvBC,GAAG,EAAE,QAAQ;QACbC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,qBAAqB;QAChCC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,0BAA0B;QACtCC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,WAAW;QACtBC,OAAO,EAAE,kCAAkC;QAC3CC,MAAM,EAAE,+BAA+B;QACvCC,IAAI,EAAE,oBAAoB;QAC1BC,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,0BAA0B;QAClCC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE,kBAAkB;QAC9BC,UAAU,EAAE,QAAQ;QACpBC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,aAAa;QAC7BC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,yBAAyB;QACpCC,KAAK,EAAE,QAAQ;QACfC,cAAc,EAAE,qBAAqB;QACrCC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,yBAAyB;QAC9CC,aAAa,EAAE,OAAO;QACtBC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,qBAAqB;QACxC,MAAM,EAAE,2BAA2B;QACnC,MAAM,EAAE,8BAA8B;QACtC,KAAK,EAAE,aAAa;QACpB,OAAO,EAAE,oBAAoB;QAC7B,MAAM,EAAE,6BAA6B;QACrC,QAAQ,EAAE,2BAA2B;QACrC,WAAW,EAAE,qCAAqC;QAClD,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,mBAAmB;QACnC,aAAa,EAAE,oCAAoC;QACnD,eAAe,EAAE,mCAAmC;QACpD,cAAc,EAAE,mCAAmC;QACnD,aAAa,EAAE,sCAAsC;QACrD,qBAAqB,EAAE,sCAAsC;QAC7D,mBAAmB,EAAE,kCAAkC;QACvD,SAAS,EAAE,2BAA2B;QACtC,QAAQ,EAAE,mBAAmB;QAC7B,YAAY,EAAE,8DAA8D;QAC5E,UAAU,EAAE,+CAA+C;QAC3D,UAAU,EAAE,+CAA+C;QAC3D,UAAU,EAAE,+CAA+C;QAC3D,UAAU,EAAE,+CAA+C;QAC3D,UAAU,EAAE,+CAA+C;QAC3D,UAAU,EAAE,+CAA+C;QAC3D,sBAAsB,EAAE,6BAA6B;QACrD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-it-IT.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'it-IT': {\n      font: {\n        bold: 'Testo in grassetto',\n        italic: 'Testo in corsivo',\n        underline: 'Testo sottolineato',\n        clear: 'Elimina la formattazione del testo',\n        height: 'Altezza della linea di testo',\n        name: 'Famiglia Font',\n        strikethrough: 'Testo barrato',\n        subscript: 'Pedice',\n        superscript: 'Apice',\n        size: 'Dimensione del carattere',\n      },\n      image: {\n        image: 'Immagine',\n        insert: 'Inserisci immagine',\n        resizeFull: 'Dimensioni originali',\n        resizeHalf: 'Ridimensiona al 50%',\n        resizeQuarter: 'Ridimensiona al 25%',\n        floatLeft: 'Posiziona a sinistra',\n        floatRight: 'Posiziona a destra',\n        floatNone: 'Nessun posizionamento',\n        shapeRounded: 'Forma: arrotondata',\n        shapeCircle: 'Forma: cerchio',\n        shapeThumbnail: 'Forma: miniatura',\n        shapeNone: 'Forma: nessuna',\n        dragImageHere: 'Trascina qui un\\'immagine',\n        dropImage: 'Rilascia immagine o testo',\n        selectFromFiles: 'Scegli dai file',\n        maximumFileSize: 'Dimensione massima del file',\n        maximumFileSizeError: 'Dimensione massima del file superata.',\n        url: 'URL dell\\'immagine',\n        remove: 'Rimuovi immagine',\n        original: 'Originale',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Collegamento ad un video',\n        insert: 'Inserisci video',\n        url: 'URL del video',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion o Youku)',\n      },\n      link: {\n        link: 'Collegamento',\n        insert: 'Inserisci collegamento',\n        unlink: 'Elimina collegamento',\n        edit: 'Modifica collegamento',\n        textToDisplay: 'Testo del collegamento',\n        url: 'URL del collegamento',\n        openInNewWindow: 'Apri in una nuova finestra',\n      },\n      table: {\n        table: 'Tabella',\n        addRowAbove: 'Aggiungi riga sopra',\n        addRowBelow: 'Aggiungi riga sotto',\n        addColLeft: 'Aggiungi colonna sinistra',\n        addColRight: 'Aggiungi colonna destra',\n        delRow: 'Elimina riga',\n        delCol: 'Elimina colonna',\n        delTable: 'Elimina tabella',\n      },\n      hr: {\n        insert: 'Inserisce una linea di separazione',\n      },\n      style: {\n        style: 'Stili',\n        p: 'Normale',\n        blockquote: 'Citazione',\n        pre: 'Codice',\n        h1: 'Titolo 1',\n        h2: 'Titolo 2',\n        h3: 'Titolo 3',\n        h4: 'Titolo 4',\n        h5: 'Titolo 5',\n        h6: 'Titolo 6',\n      },\n      lists: {\n        unordered: 'Elenco non ordinato',\n        ordered: 'Elenco ordinato',\n      },\n      options: {\n        help: 'Aiuto',\n        fullscreen: 'Modalità a tutto schermo',\n        codeview: 'Visualizza codice',\n      },\n      paragraph: {\n        paragraph: 'Paragrafo',\n        outdent: 'Diminuisce il livello di rientro',\n        indent: 'Aumenta il livello di rientro',\n        left: 'Allinea a sinistra',\n        center: 'Centra',\n        right: 'Allinea a destra',\n        justify: 'Giustifica (allinea a destra e sinistra)',\n      },\n      color: {\n        recent: 'Ultimo colore utilizzato',\n        more: 'Altri colori',\n        background: 'Colore di sfondo',\n        foreground: 'Colore',\n        transparent: 'Trasparente',\n        setTransparent: 'Trasparente',\n        reset: 'Reimposta',\n        resetToDefault: 'Reimposta i colori',\n      },\n      shortcut: {\n        shortcuts: 'Scorciatoie da tastiera',\n        close: 'Chiudi',\n        textFormatting: 'Formattazione testo',\n        action: 'Azioni',\n        paragraphFormatting: 'Formattazione paragrafo',\n        documentStyle: 'Stili',\n        extraKeys: 'Tasti extra',\n      },\n      help: {\n        'insertParagraph': 'Inserisci paragrafo',\n        'undo': 'Annulla l\\'ultimo comando',\n        'redo': 'Ripristina l\\'ultimo comando',\n        'tab': 'Tabulazione',\n        'untab': 'Toglie tabulazione',\n        'bold': 'Imposta uno stile grassetto',\n        'italic': 'Imposta uno stile corsivo',\n        'underline': 'Imposta uno stile di sottolineatura',\n        'strikethrough': 'Imposta uno stile barrato',\n        'removeFormat': 'Rimuove uno stile',\n        'justifyLeft': 'Imposta l\\'allineamento a sinistra',\n        'justifyCenter': 'Imposta l\\'allineamento al centro',\n        'justifyRight': 'Imposta l\\'allineamento al destra',\n        'justifyFull': 'Imposta l\\'allineamento a pieno rigo',\n        'insertUnorderedList': 'Attiva/disattiva elenco non ordinato',\n        'insertOrderedList': 'Attiva/disattiva elenco ordinato',\n        'outdent': 'Annulla rientro paragrafo',\n        'indent': 'Rientro paragrafo',\n        'formatPara': 'Cambia il formato del blocco corrente come paragrafo (tag P)',\n        'formatH1': 'Cambia il formato del blocco corrente come H1',\n        'formatH2': 'Cambia il formato del blocco corrente come H2',\n        'formatH3': 'Cambia il formato del blocco corrente come H3',\n        'formatH4': 'Cambia il formato del blocco corrente come H4',\n        'formatH5': 'Cambia il formato del blocco corrente come H5',\n        'formatH6': 'Cambia il formato del blocco corrente come H6',\n        'insertHorizontalRule': 'Inserisci linea orizzontale',\n        'linkDialog.show': 'Mostra finestra di dialogo del collegamento',\n      },\n      history: {\n        undo: 'Annulla',\n        redo: 'Ripristina',\n      },\n      specialChar: {\n        specialChar: 'CARATTERI SPECIALI',\n        select: 'Selezione caratteri speciali',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}