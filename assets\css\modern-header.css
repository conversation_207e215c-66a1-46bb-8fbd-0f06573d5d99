/*
 * Modern Header CSS for Nội Thất Bàng Vũ
 * Designed with Be Vietnam Pro font and orange color scheme
 */

/* Import Be Vietnam Pro font */
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700;800&display=swap');

/* Color Variables */
:root {
    --primary: #F37321;
    /* <PERSON> logo */
    --primary-dark: #D35400;
    /* <PERSON> */
    --primary-light: #FFAB91;
    /* Cam nh<PERSON>t */
    --secondary: #2C3E50;
    /* Xanh đậm */
    --secondary-light: #34495E;
    /* Xanh nhạt */
    --neutral-dark: #333333;
    /* X<PERSON><PERSON> đậm cho text */
    --neutral-medium: #666666;
    /* Xám vừa */
    --neutral-light: #F5F5F5;
    /* Xám nhạt cho background */
    --white: #FFFFFF;
    --black: #000000;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition-normal: all 0.3s ease;
}

/* Base Typography */
body {
    font-family: 'Be Vietnam Pro', sans-serif;
    color: var(--neutral-dark);
    line-height: 1.6;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: 'Be Vietnam Pro', sans-serif;
    font-weight: 600;
}

/* Modern Header Styles */
.modern-header {
    position: sticky;
    top: 0;
    width: 100%;
    z-index: 1000;
    background-color: var(--white);
    box-shadow: var(--shadow-md);
    will-change: transform, background-color, box-shadow;

    /* Biến CSS cho hiệu ứng mượt mà */
    --header-opacity: 0;
    --header-scale: 1;
    --header-translate-y: 0px;
}

/* Thêm hiệu ứng transition mượt mà */
.modern-header.smooth-transition {
    transition:
        transform 0.3s cubic-bezier(0.33, 1, 0.68, 1),
        box-shadow 0.3s cubic-bezier(0.33, 1, 0.68, 1),
        background-color 0.3s cubic-bezier(0.33, 1, 0.68, 1);
}

/* Áp dụng hiệu ứng transform dựa trên biến CSS */
.modern-header.smooth-transition::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, var(--header-opacity));
    opacity: var(--header-opacity);
    pointer-events: none;
    z-index: -1;
    transition: opacity 0.3s cubic-bezier(0.33, 1, 0.68, 1);
}

/* Hiệu ứng transform mượt mà khi cuộn */
.modern-header.smooth-transition:not(.scrolled) {
    transform: translateY(var(--header-translate-y)) scale(var(--header-scale));
}

/* Top Bar */
.top-bar {
    background-color: var(--primary);
    color: var(--white);
    padding: 8px 0;
    font-size: 14px;
}

.top-bar a {
    color: var(--white);
    text-decoration: none;
    transition: var(--transition-normal);
}

.top-bar a:hover {
    color: var(--neutral-light);
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-bar-contact {
    display: flex;
    align-items: center;
    gap: 20px;
}

.top-bar-contact i {
    margin-right: 5px;
}

.top-bar-social {
    display: flex;
    gap: 15px;
}

/* Main Header */
.main-header {
    padding: 15px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo-container img {
    height: 50px;
    width: auto;
}

.logo-text {
    margin-left: 10px;
    font-weight: 700;
    font-size: 24px;
    color: var(--primary);
}

/* Search Bar */
.search-container {
    position: relative;
    max-width: 400px;
    width: 100%;
}

.search-form {
    display: flex;
    position: relative;
}

.search-input {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid var(--neutral-light);
    border-radius: 30px;
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: 14px;
    transition: var(--transition-normal);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.2);
}

.search-button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
}

.search-button:hover {
    background-color: var(--primary-dark);
}

/* User Actions */
.user-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.action-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--neutral-dark);
    text-decoration: none;
    transition: var(--transition-normal);
}

.action-item:hover {
    color: var(--primary);
}

.action-icon {
    font-size: 24px;
    margin-bottom: 5px;
}

.action-text {
    font-size: 12px;
    font-weight: 500;
}

.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
}

/* Navigation */
.main-nav {
    background-color: var(--white);
    border-top: 1px solid var(--neutral-light);
    box-shadow: var(--shadow-sm);
}

.nav-container {
    display: flex;
    justify-content: space-between;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 15px 20px;
    color: var(--neutral-dark);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-normal);
}

.nav-link:hover,
.nav-item.active .nav-link {
    color: var(--primary);
}

.nav-item.active .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    height: 3px;
    background-color: var(--primary);
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    min-width: 200px;
    box-shadow: var(--shadow-lg);
    border-radius: 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: var(--transition-normal);
    z-index: 100;
}

.nav-item:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 12px 20px;
    color: var(--neutral-dark);
    text-decoration: none;
    font-weight: 400;
    transition: var(--transition-normal);
}

.dropdown-item:hover {
    background-color: var(--neutral-light);
    color: var(--primary);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--neutral-dark);
    font-size: 24px;
    cursor: pointer;
    transition: var(--transition-normal);
}

.mobile-menu-toggle:hover {
    color: var(--primary);
}

/* Mobile Menu */
.mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.95);
    z-index: 2000;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow-y: auto;
    transition: all 0.3s ease;
}

.mobile-menu.hidden {
    display: none;
}

.mobile-menu-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    width: 40px;
    height: 40px;
    cursor: pointer;
    z-index: 2001;
}

.close-bar {
    display: block;
    width: 30px;
    height: 2px;
    background-color: var(--white);
    position: absolute;
    top: 50%;
    left: 50%;
    transition: var(--transition-normal);
}

.close-bar:first-child {
    transform: translate(-50%, -50%) rotate(45deg);
}

.close-bar:last-child {
    transform: translate(-50%, -50%) rotate(-45deg);
}

.mobile-menu-close:hover .close-bar {
    background-color: var(--primary);
}

.mobile-menu-links {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.mobile-menu-links li {
    margin-bottom: 15px;
}

.mobile-menu-links a {
    display: flex;
    align-items: center;
    color: var(--white);
    text-decoration: none;
    font-size: 18px;
    font-weight: 500;
    padding: 10px 0;
    transition: var(--transition-normal);
}

.mobile-menu-links a i {
    margin-right: 15px;
    width: 20px;
    text-align: center;
    color: var(--primary);
}

.mobile-menu-links a:hover,
.mobile-menu-links a.active {
    color: var(--primary);
    transform: translateX(5px);
}

.mobile-menu-divider {
    height: 1px;
    background-color: rgba(255, 255, 255, 0.1);
    margin: 20px 0;
}

.mobile-contact-info {
    margin-bottom: 30px;
}

.mobile-contact-info h3 {
    color: var(--white);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.mobile-contact-info h3::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--primary);
}

.mobile-contact-info p {
    color: var(--white);
    margin: 10px 0;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.mobile-contact-info p i {
    color: var(--primary);
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.mobile-contact-btn {
    margin-top: auto;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.mobile-contact-btn.visible {
    opacity: 1;
    transform: translateY(0);
}

.mobile-contact-btn .contact-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary);
    color: var(--white);
    padding: 12px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
}

.mobile-contact-btn .contact-btn i {
    margin-right: 10px;
}

.mobile-contact-btn .contact-btn:hover {
    background-color: var(--primary-dark);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .search-container {
        max-width: 300px;
    }

    .nav-link {
        padding: 15px 15px;
    }
}

@media (max-width: 768px) {
    .top-bar-content {
        flex-direction: column;
        gap: 5px;
    }

    .main-header {
        flex-wrap: wrap;
    }

    .search-container {
        order: 3;
        max-width: 100%;
        margin-top: 15px;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .main-nav {
        display: none;
    }

    .main-nav.active {
        display: block;
    }

    .nav-menu {
        flex-direction: column;
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .nav-item.active .dropdown-menu {
        max-height: 500px;
    }
}