{"version": 3, "file": "lang/summernote-ca-ES.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,YAAY;QACvBC,KAAK,EAAE,wBAAwB;QAC/BC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,MAAM;QACZC,aAAa,EAAE,SAAS;QACxBC,SAAS,EAAE,UAAU;QACrBC,WAAW,EAAE,YAAY;QACzBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE,gBAAgB;QACxBC,UAAU,EAAE,+BAA+B;QAC3CC,UAAU,EAAE,2BAA2B;QACvCC,aAAa,EAAE,0BAA0B;QACzCC,SAAS,EAAE,uBAAuB;QAClCC,UAAU,EAAE,oBAAoB;QAChCC,SAAS,EAAE,YAAY;QACvBC,YAAY,EAAE,kBAAkB;QAChCC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,aAAa;QAC7BC,SAAS,EAAE,YAAY;QACvBC,aAAa,EAAE,oCAAoC;QACnDC,SAAS,EAAE,sCAAsC;QACjDC,eAAe,EAAE,6BAA6B;QAC9CC,eAAe,EAAE,yBAAyB;QAC1CC,oBAAoB,EAAE,2CAA2C;QACjEC,GAAG,EAAE,kBAAkB;QACvBC,MAAM,EAAE,iBAAiB;QACzBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,kBAAkB;QAC7BpB,MAAM,EAAE,eAAe;QACvBgB,GAAG,EAAE,gBAAgB;QACrBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,QAAQ;QACdtB,MAAM,EAAE,gBAAgB;QACxBuB,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE,kBAAkB;QACjCT,GAAG,EAAE,kCAAkC;QACvCU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,eAAe;QAC5BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,GAAG;QACNC,UAAU,EAAE,MAAM;QAClBC,GAAG,EAAE,MAAM;QACXC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,qBAAqB;QAChCC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,MAAM;QACZC,UAAU,EAAE,kBAAkB;QAC9BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,uBAAuB;QAC7BC,MAAM,EAAE,gBAAgB;QACxBC,KAAK,EAAE,oBAAoB;QAC3BC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,sBAAsB;QACtCC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,oBAAoB;QAC/BC,KAAK,EAAE,QAAQ;QACfC,cAAc,EAAE,gBAAgB;QAChCC,MAAM,EAAE,OAAO;QACfC,mBAAmB,EAAE,oBAAoB;QACzCC,aAAa,EAAE,oBAAoB;QACnCC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,kBAAkB;QACrC,MAAM,EAAE,wBAAwB;QAChC,MAAM,EAAE,uBAAuB;QAC/B,KAAK,EAAE,SAAS;QAChB,OAAO,EAAE,oBAAoB;QAC7B,MAAM,EAAE,wBAAwB;QAChC,QAAQ,EAAE,wBAAwB;QAClC,WAAW,EAAE,2BAA2B;QACxC,eAAe,EAAE,wBAAwB;QACzC,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,uBAAuB;QACtC,eAAe,EAAE,mBAAmB;QACpC,cAAc,EAAE,oBAAoB;QACpC,aAAa,EAAE,YAAY;QAC3B,qBAAqB,EAAE,6BAA6B;QACpD,mBAAmB,EAAE,0BAA0B;QAC/C,SAAS,EAAE,+BAA+B;QAC1C,QAAQ,EAAE,kCAAkC;QAC5C,YAAY,EAAE,0DAA0D;QACxE,UAAU,EAAE,uCAAuC;QACnD,UAAU,EAAE,uCAAuC;QACnD,UAAU,EAAE,uCAAuC;QACnD,UAAU,EAAE,uCAAuC;QACnD,UAAU,EAAE,uCAAuC;QACnD,UAAU,EAAE,uCAAuC;QACnD,sBAAsB,EAAE,+BAA+B;QACvD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,qBAAqB;QAClCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ca-ES.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'ca-ES': {\n      font: {\n        bold: 'Negreta',\n        italic: 'Cursiva',\n        underline: 'Subratllat',\n        clear: 'Treure estil de lletra',\n        height: 'Alçada de línia',\n        name: 'Font',\n        strikethrough: 'Ratllat',\n        subscript: 'Subíndex',\n        superscript: 'Superíndex',\n        size: 'Mida de lletra',\n      },\n      image: {\n        image: 'Imatge',\n        insert: 'Inserir imatge',\n        resizeFull: 'Redimensionar a mida completa',\n        resizeHalf: 'Redimensionar a la meitat',\n        resizeQuarter: 'Redimensionar a un quart',\n        floatLeft: 'Alinear a l\\'esquerra',\n        floatRight: 'Alinear a la dreta',\n        floatNone: 'No alinear',\n        shapeRounded: 'Forma: Arrodonit',\n        shapeCircle: 'Forma: Cercle',\n        shapeThumbnail: 'Forma: Marc',\n        shapeNone: 'Forma: Cap',\n        dragImageHere: 'Arrossegueu una imatge o text aquí',\n        dropImage: 'Deixa anar aquí una imatge o un text',\n        selectFromFiles: 'Seleccioneu des dels arxius',\n        maximumFileSize: 'Mida màxima de l\\'arxiu',\n        maximumFileSizeError: 'La mida màxima de l\\'arxiu s\\'ha superat.',\n        url: 'URL de la imatge',\n        remove: 'Eliminar imatge',\n        original: 'Original',\n      },\n      video: {\n        video: 'Vídeo',\n        videoLink: 'Enllaç del vídeo',\n        insert: 'Inserir vídeo',\n        url: 'URL del vídeo?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion o Youku)',\n      },\n      link: {\n        link: 'Enllaç',\n        insert: 'Inserir enllaç',\n        unlink: 'Treure enllaç',\n        edit: 'Editar',\n        textToDisplay: 'Text per mostrar',\n        url: 'Cap a quina URL porta l\\'enllaç?',\n        openInNewWindow: 'Obrir en una finestra nova',\n      },\n      table: {\n        table: 'Taula',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Inserir línia horitzontal',\n      },\n      style: {\n        style: 'Estil',\n        p: 'p',\n        blockquote: 'Cita',\n        pre: 'Codi',\n        h1: 'Títol 1',\n        h2: 'Títol 2',\n        h3: 'Títol 3',\n        h4: 'Títol 4',\n        h5: 'Títol 5',\n        h6: 'Títol 6',\n      },\n      lists: {\n        unordered: 'Llista desendreçada',\n        ordered: 'Llista endreçada',\n      },\n      options: {\n        help: 'Ajut',\n        fullscreen: 'Pantalla sencera',\n        codeview: 'Veure codi font',\n      },\n      paragraph: {\n        paragraph: 'Paràgraf',\n        outdent: 'Menys tabulació',\n        indent: 'Més tabulació',\n        left: 'Alinear a l\\'esquerra',\n        center: 'Alinear al mig',\n        right: 'Alinear a la dreta',\n        justify: 'Justificar',\n      },\n      color: {\n        recent: 'Últim color',\n        more: 'Més colors',\n        background: 'Color de fons',\n        foreground: 'Color de lletra',\n        transparent: 'Transparent',\n        setTransparent: 'Establir transparent',\n        reset: 'Restablir',\n        resetToDefault: 'Restablir per defecte',\n      },\n      shortcut: {\n        shortcuts: 'Dreceres de teclat',\n        close: 'Tancar',\n        textFormatting: 'Format de text',\n        action: 'Acció',\n        paragraphFormatting: 'Format de paràgraf',\n        documentStyle: 'Estil del document',\n        extraKeys: 'Tecles adicionals',\n      },\n      help: {\n        'insertParagraph': 'Inserir paràgraf',\n        'undo': 'Desfer l\\'última acció',\n        'redo': 'Refer l\\'última acció',\n        'tab': 'Tabular',\n        'untab': 'Eliminar tabulació',\n        'bold': 'Establir estil negreta',\n        'italic': 'Establir estil cursiva',\n        'underline': 'Establir estil subratllat',\n        'strikethrough': 'Establir estil ratllat',\n        'removeFormat': 'Netejar estil',\n        'justifyLeft': 'Alinear a l\\'esquerra',\n        'justifyCenter': 'Alinear al centre',\n        'justifyRight': 'Alinear a la dreta',\n        'justifyFull': 'Justificar',\n        'insertUnorderedList': 'Inserir llista desendreçada',\n        'insertOrderedList': 'Inserir llista endreçada',\n        'outdent': 'Reduïr tabulació del paràgraf',\n        'indent': 'Augmentar tabulació del paràgraf',\n        'formatPara': 'Canviar l\\'estil del bloc com a un paràgraf (etiqueta P)',\n        'formatH1': 'Canviar l\\'estil del bloc com a un H1',\n        'formatH2': 'Canviar l\\'estil del bloc com a un H2',\n        'formatH3': 'Canviar l\\'estil del bloc com a un H3',\n        'formatH4': 'Canviar l\\'estil del bloc com a un H4',\n        'formatH5': 'Canviar l\\'estil del bloc com a un H5',\n        'formatH6': 'Canviar l\\'estil del bloc com a un H6',\n        'insertHorizontalRule': 'Inserir una línia horitzontal',\n        'linkDialog.show': 'Mostrar panel d\\'enllaços',\n      },\n      history: {\n        undo: 'Desfer',\n        redo: 'Refer',\n      },\n      specialChar: {\n        specialChar: 'CARÀCTERS ESPECIALS',\n        select: 'Selecciona caràcters especials',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}