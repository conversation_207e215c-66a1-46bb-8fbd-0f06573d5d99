<?php
/**
 * API endpoint để xử lý AJAX filter sản phẩm
 * <PERSON><PERSON><PERSON> về JSON data chứa danh sách sản phẩm đã được filter
 */

// Đặt header JSON
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Include init để có các hàm cần thiết
$init_path = dirname(__DIR__) . '/includes/init.php';
if (!file_exists($init_path)) {
    throw new Exception("Init file not found: $init_path");
}
require_once $init_path;

// Cho phép cả GET và POST request để test
if ($_SERVER['REQUEST_METHOD'] !== 'POST' && $_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Lấy dữ liệu từ request
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $raw_input = file_get_contents('php://input');
        $input = json_decode($raw_input, true);

        // Debug log
        error_log("AJAX Filter API - Raw input: " . $raw_input);
        error_log("AJAX Filter API - Parsed input: " . print_r($input, true));

        if (!$input) {
            // Nếu không có JSON data, tạo array rỗng để test
            $input = [];
        }
    } else {
        // GET request - sử dụng query parameters
        $input = $_GET;

        // Xử lý đặc biệt cho arrays trong GET request
        if (isset($_GET['categories']) && is_array($_GET['categories'])) {
            $input['categories'] = $_GET['categories'];
        }
        if (isset($_GET['promotions']) && is_array($_GET['promotions'])) {
            $input['promotions'] = $_GET['promotions'];
        }
    }
    
    // Xử lý các tham số filter giống như trong products.php
    $keyword = isset($input['keyword']) ? sanitize($input['keyword']) : '';
    
    // Xử lý multiple categories
    $category_ids = [];
    if (isset($input['categories']) && is_array($input['categories'])) {
        $category_ids = array_map('intval', $input['categories']);
        $category_ids = array_filter($category_ids, function ($id) {
            return $id > 0;
        });
    }
    
    // Xử lý price range
    $price_min = isset($input['price_min']) && $input['price_min'] !== '' ? (float) $input['price_min'] : null;
    $price_max = isset($input['price_max']) && $input['price_max'] !== '' ? (float) $input['price_max'] : null;
    
    // Xử lý sort
    $sort = isset($input['sort']) ? $input['sort'] : 'newest';
    
    // Xử lý multiple promotion filters
    $promotion_filters = [];
    if (isset($input['promotions']) && is_array($input['promotions'])) {
        $promotion_filters = array_map('sanitize', $input['promotions']);
    }
    
    // Phân trang
    $page = isset($input['page']) ? (int) $input['page'] : 1;
    $items_per_page = isset($input['items_per_page']) ? (int) $input['items_per_page'] : ITEMS_PER_PAGE;
    
    // Validate items per page
    $allowed_limits = [12, 24, 36, 48];
    $limit = in_array($items_per_page, $allowed_limits) ? $items_per_page : ITEMS_PER_PAGE;
    $offset = ($page - 1) * $limit;
    
    // Thiết lập tùy chọn sắp xếp
    $sort_options = [];
    $sort_label = '';
    switch ($sort) {
        case 'price_asc':
            $sort_options = ['by' => 'price', 'order' => 'ASC'];
            $sort_label = 'Giá tăng dần';
            break;
        case 'price_desc':
            $sort_options = ['by' => 'price', 'order' => 'DESC'];
            $sort_label = 'Giá giảm dần';
            break;
        case 'name_asc':
            $sort_options = ['by' => 'name', 'order' => 'ASC'];
            $sort_label = 'Tên A-Z';
            break;
        case 'name_desc':
            $sort_options = ['by' => 'name', 'order' => 'DESC'];
            $sort_label = 'Tên Z-A';
            break;
        case 'newest':
        default:
            $sort_options = ['by' => 'created_at', 'order' => 'DESC'];
            $sort_label = 'Mới nhất';
            break;
    }
    
    // Kiểm tra function tồn tại
    if (!function_exists('get_products_with_filters')) {
        throw new Exception('Function get_products_with_filters not found');
    }

    // Lấy danh sách sản phẩm với filters
    $products = get_products_with_filters(
        $category_ids,
        $promotion_filters,
        $limit,
        $offset,
        $keyword,
        1, // status - chỉ lấy sản phẩm đang hiển thị
        $price_min,
        $price_max,
        $sort_options
    );
    
    // Đếm tổng số sản phẩm
    $total_products = count_products_with_filters(
        $category_ids,
        $promotion_filters,
        $keyword,
        1, // status
        $price_min,
        $price_max
    );
    
    $total_pages = ceil($total_products / $limit);
    
    // Lấy thông tin categories nếu có
    $current_categories = [];
    if (!empty($category_ids)) {
        foreach ($category_ids as $cat_id) {
            $cat_info = get_category_by_id($cat_id);
            if ($cat_info) {
                $current_categories[] = $cat_info;
            }
        }
    }
    
    // Format dữ liệu sản phẩm để trả về
    $formatted_products = [];
    foreach ($products as $product) {
        $formatted_products[] = [
            'id' => $product['id'],
            'name' => $product['name'],
            'slug' => $product['slug'],
            'price' => $product['price'],
            'sale_price' => $product['sale_price'] ?? 0,
            'price_type' => $product['price_type'] ?? 'fixed',
            'image' => $product['image'],
            'rating' => $product['rating'] ?? 5,
            'sold' => $product['sold'] ?? 0,
            'category_name' => $product['category_name'],
            'url' => get_product_url($product['slug'])
        ];
    }

    // Tạo HTML cho Filter Results Header
    $filter_data = [
        'keyword' => $keyword,
        'category_ids' => $category_ids,
        'price_min' => $price_min,
        'price_max' => $price_max,
        'promotion_filters' => $promotion_filters
    ];

    $filter_results_header_html = generate_filter_results_header($filter_data, $formatted_products, $total_products, $sort_label);
    
    // Tạo response data
    $response = [
        'success' => true,
        'data' => [
            'products' => $formatted_products,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $total_pages,
                'total_products' => $total_products,
                'items_per_page' => $limit,
                'has_next' => $page < $total_pages,
                'has_prev' => $page > 1
            ],
            'filters' => [
                'keyword' => $keyword,
                'categories' => $current_categories,
                'price_min' => $price_min,
                'price_max' => $price_max,
                'promotions' => $promotion_filters,
                'sort' => $sort
            ],
            'filter_results_header_html' => $filter_results_header_html
        ]
    ];
    
    // Trả về JSON response
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // Log lỗi chi tiết
    error_log("AJAX Filter API Error: " . $e->getMessage());
    error_log("AJAX Filter API Stack trace: " . $e->getTraceAsString());

    // Xử lý lỗi
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Có lỗi xảy ra khi xử lý yêu cầu: ' . $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ], JSON_UNESCAPED_UNICODE);
}
