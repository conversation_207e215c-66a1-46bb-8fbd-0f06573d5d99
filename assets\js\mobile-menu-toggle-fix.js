/**
 * Mobile Menu Toggle Fix JavaScript for Nội Thất Bàng Vũ
 * X<PERSON> lý các tính năng tương tác của menu mobile
 */

document.addEventListener('DOMContentLoaded', function () {
  console.log('Mobile Menu Toggle Fix loaded');

  // Xử lý mở/đóng menu bằng nút toggle
  const mobileMenuToggle = document.querySelector('.mobile-header-menu-toggle');
  const mobileMenu = document.querySelector('.mobile-menu');
  const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');
  const contactButtonsContainer = document.querySelector('.contact-buttons-container');
  const scrollTopContainer = document.querySelector('.scroll-top-container');

  console.log('Mobile menu toggle:', mobileMenuToggle);
  console.log('Mobile menu:', mobileMenu);
  console.log('Mobile menu overlay:', mobileMenuOverlay);
  console.log('Contact buttons container:', contactButtonsContainer);
  console.log('Scroll top container:', scrollTopContainer);

  if (mobileMenuToggle && mobileMenu) {
    mobileMenuToggle.addEventListener('click', function () {
      console.log('Mobile menu toggle clicked');

      // Thêm hiệu ứng ripple khi nhấn
      const ripple = document.createElement('span');
      ripple.classList.add('menu-toggle-ripple');
      this.appendChild(ripple);

      // Xóa hiệu ứng ripple sau khi hoàn thành
      setTimeout(() => {
        ripple.remove();
      }, 600);

      // Toggle trạng thái active của nút
      this.classList.toggle('active');

      // Mở/đóng menu
      if (mobileMenu.classList.contains('active')) {
        // Đóng menu
        mobileMenu.classList.remove('active');
        document.body.classList.remove('overflow-hidden');

        // Cập nhật thuộc tính ARIA
        this.setAttribute('aria-expanded', 'false');
        mobileMenu.setAttribute('aria-hidden', 'true');

        // Xóa hiệu ứng mờ cho các nút liên hệ và nút cuộn lên đầu trang
        if (contactButtonsContainer) {
          contactButtonsContainer.classList.remove('menu-open-fade');
        }
        if (scrollTopContainer) {
          scrollTopContainer.classList.remove('menu-open-fade');
        }

        if (mobileMenuOverlay) {
          mobileMenuOverlay.classList.remove('active');
        }
      } else {
        // Mở menu
        mobileMenu.classList.add('active');
        document.body.classList.add('overflow-hidden');

        // Cập nhật thuộc tính ARIA
        this.setAttribute('aria-expanded', 'true');
        mobileMenu.setAttribute('aria-hidden', 'false');

        // Thêm hiệu ứng mờ cho các nút liên hệ và nút cuộn lên đầu trang
        if (contactButtonsContainer) {
          contactButtonsContainer.classList.add('menu-open-fade');
        }
        if (scrollTopContainer) {
          scrollTopContainer.classList.add('menu-open-fade');
        }

        if (mobileMenuOverlay) {
          mobileMenuOverlay.classList.add('active');
        }
      }
    });
  }

  // Xử lý đóng menu khi nhấp vào overlay
  if (mobileMenuOverlay) {
    mobileMenuOverlay.addEventListener('click', function () {
      console.log('Mobile menu overlay clicked');

      // Xóa class active khỏi nút toggle
      if (mobileMenuToggle) {
        mobileMenuToggle.classList.remove('active');
        // Cập nhật thuộc tính ARIA
        mobileMenuToggle.setAttribute('aria-expanded', 'false');
      }

      // Đóng menu
      mobileMenu.classList.remove('active');
      mobileMenu.setAttribute('aria-hidden', 'true');
      document.body.classList.remove('overflow-hidden');
      this.classList.remove('active');

      // Xóa hiệu ứng mờ cho các nút liên hệ và nút cuộn lên đầu trang
      if (contactButtonsContainer) {
        contactButtonsContainer.classList.remove('menu-open-fade');
      }
      if (scrollTopContainer) {
        scrollTopContainer.classList.remove('menu-open-fade');
      }
    });
  }

  // Thêm hỗ trợ keyboard navigation cho mobile menu
  function setupMobileKeyboardNavigation() {
    // Xử lý phím Escape để đóng menu
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && mobileMenu && mobileMenu.classList.contains('active')) {
        // Đóng menu
        mobileMenu.classList.remove('active');
        mobileMenu.setAttribute('aria-hidden', 'true');
        document.body.classList.remove('overflow-hidden');

        // Xóa hiệu ứng mờ cho các nút liên hệ và nút cuộn lên đầu trang
        if (contactButtonsContainer) {
          contactButtonsContainer.classList.remove('menu-open-fade');
        }
        if (scrollTopContainer) {
          scrollTopContainer.classList.remove('menu-open-fade');
        }

        if (mobileMenuToggle) {
          mobileMenuToggle.classList.remove('active');
          mobileMenuToggle.setAttribute('aria-expanded', 'false');
          // Focus lại vào nút toggle
          mobileMenuToggle.focus();
        }

        if (mobileMenuOverlay) {
          mobileMenuOverlay.classList.remove('active');
        }
      }
    });

    // Xử lý phím Enter và Space cho nút toggle
    if (mobileMenuToggle) {
      mobileMenuToggle.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          // Kích hoạt sự kiện click
          this.click();
        }
      });
    }

    // Xử lý focus trap trong mobile menu
    if (mobileMenu) {
      // Lấy tất cả các phần tử có thể focus trong menu
      const focusableElements = mobileMenu.querySelectorAll('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])');

      if (focusableElements.length > 0) {
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        // Xử lý Tab và Shift+Tab để giữ focus trong menu
        mobileMenu.addEventListener('keydown', function(e) {
          if (e.key === 'Tab') {
            if (e.shiftKey && document.activeElement === firstElement) {
              e.preventDefault();
              lastElement.focus();
            } else if (!e.shiftKey && document.activeElement === lastElement) {
              e.preventDefault();
              firstElement.focus();
            }
          }
        });
      }
    }
  }

  // Khởi chạy hàm hỗ trợ keyboard navigation
  setupMobileKeyboardNavigation();
});
