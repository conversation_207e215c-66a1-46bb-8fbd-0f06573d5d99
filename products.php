<?php


// Include init để có các hàm cần thiết
require_once 'includes/init.php';

// Lấy từ khóa tìm kiếm và các tham số filter
$keyword = isset($_GET['keyword']) ? sanitize($_GET['keyword']) : '';

// Xử lý multiple categories
$category_ids = [];

// Kiểm tra cả category[] (từ form) và category (từ URL trực tiếp)
if (isset($_GET['category']) && is_array($_GET['category'])) {
    // Từ form với category[]
    $category_ids = array_map('intval', $_GET['category']);
    $category_ids = array_filter($category_ids, function ($id) {
        return $id > 0; });
} elseif (isset($_GET['category']) && !is_array($_GET['category'])) {
    // Từ URL trực tiếp với category=X
    $single_id = (int) $_GET['category'];
    if ($single_id > 0) {
        $category_ids = [$single_id];
    }
}

// Debug: Log để kiểm tra (c<PERSON> thể comment out khi production)
// error_log("DEBUG: _GET = " . print_r($_GET, true));
// error_log("DEBUG: category_ids = " . print_r($category_ids, true));

// Để tương thích với code cũ, lấy category_id đầu tiên
$category_id = !empty($category_ids) ? $category_ids[0] : null;

$price_min = isset($_GET['price_min']) && $_GET['price_min'] !== '' ? (float) $_GET['price_min'] : null;
$price_max = isset($_GET['price_max']) && $_GET['price_max'] !== '' ? (float) $_GET['price_max'] : null;
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'newest';
// Xử lý multiple promotion filters
$promotion_filters = [];
if (isset($_GET['promotion'])) {
    if (is_array($_GET['promotion'])) {
        $promotion_filters = array_map('sanitize', $_GET['promotion']);
    } else {
        $promotion_filters = [sanitize($_GET['promotion'])];
    }
}

// Để tương thích với code cũ
$promotion_filter = !empty($promotion_filters) ? $promotion_filters[0] : '';

// Lấy thông tin categories nếu có
$current_categories = [];
if (!empty($category_ids)) {
    foreach ($category_ids as $cat_id) {
        $cat_info = get_category_by_id($cat_id);
        if ($cat_info) {
            $current_categories[] = $cat_info;
        }
    }
}

// Để tương thích với code cũ
$current_category = !empty($current_categories) ? $current_categories[0] : null;

// Thiết lập tiêu đề trang dựa trên có tìm kiếm hay không
if (!empty($keyword)) {
    $page_title = 'Tìm kiếm: ' . $keyword;
    $page_description = 'Kết quả tìm kiếm cho từ khóa "' . $keyword . '" tại Nội Thất Băng Vũ';
} else {
    $page_title = 'Tất cả sản phẩm';
    $page_description = 'Danh sách tất cả sản phẩm tại Nội Thất Băng Vũ';
}

// Include header
include_once 'partials/header.php';
?>

<!-- Giải pháp 1: Overflow Visible CSS và JS -->
<link rel="stylesheet" href="assets/css/search-overflow-solution1.css">
<script src="assets/js/search-overflow-solution1.js"></script>

<?php

// Phân trang
$page = isset($_GET['page']) ? (int) $_GET['page'] : 1;

// Xử lý items per page với cookie support
$allowed_limits = [12, 24, 36, 48];

// Ưu tiên: URL parameter > Cookie > Default
if (isset($_GET['items_per_page'])) {
    $items_per_page = (int) $_GET['items_per_page'];
} elseif (isset($_COOKIE['products_items_per_page']) && in_array((int) $_COOKIE['products_items_per_page'], $allowed_limits)) {
    $items_per_page = (int) $_COOKIE['products_items_per_page'];
} else {
    $items_per_page = ITEMS_PER_PAGE;
}

$limit = in_array($items_per_page, $allowed_limits) ? $items_per_page : ITEMS_PER_PAGE;

$offset = ($page - 1) * $limit;

// Thiết lập tùy chọn sắp xếp
$sort_options = [];
$sort_label = '';
switch ($sort) {
    case 'price_asc':
        $sort_options = ['by' => 'price', 'order' => 'ASC'];
        $sort_label = 'Giá tăng dần';
        break;
    case 'price_desc':
        $sort_options = ['by' => 'price', 'order' => 'DESC'];
        $sort_label = 'Giá giảm dần';
        break;
    case 'name_asc':
        $sort_options = ['by' => 'name', 'order' => 'ASC'];
        $sort_label = 'Tên A-Z';
        break;
    case 'name_desc':
        $sort_options = ['by' => 'name', 'order' => 'DESC'];
        $sort_label = 'Tên Z-A';
        break;
    case 'newest':
    default:
        $sort_options = ['by' => 'created_at', 'order' => 'DESC'];
        $sort_label = 'Mới nhất';
        break;
}




// Lấy danh sách sản phẩm với hỗ trợ multiple filters
$products = get_products_with_filters(
    $category_ids,
    $promotion_filters,
    $limit,
    $offset,
    $keyword,
    1, // status
    $price_min,
    $price_max,
    $sort_options
);

$total_products = count_products_with_filters(
    $category_ids,
    $promotion_filters,
    $keyword,
    1, // status
    $price_min,
    $price_max
);

$total_pages = ceil($total_products / $limit);
?>

<!-- Link CSS cho breadcrumb hiện đại -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-breadcrumb.css">

<!-- Professional Pagination CSS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-pagination.css">

<!-- Link CSS cho search page để sử dụng product card styles -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/search-page.css">

<!-- Mobile Filter Modal CSS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/mobile-filter-modal.css">

<!-- Enhanced Search JavaScript -->
<script src="<?php echo BASE_URL; ?>/assets/js/enhanced-search.js" defer></script>

<!-- Mobile Filter Modal JavaScript -->
<script src="<?php echo BASE_URL; ?>/assets/js/mobile-filter-modal.js" defer></script>

<!-- AJAX Filter JavaScript -->
<script src="<?php echo BASE_URL; ?>/assets/js/ajax-filter.js" defer></script>

<!-- Thêm BASE_URL cho JavaScript -->
<script>
    window.BASE_URL = '<?php echo BASE_URL; ?>';
</script>

<!-- Custom CSS for Orange Luxury Filter Results Header -->
<style>
    /* Enhanced responsive design for Filter Results Header */

    /* Tablet và màn hình nhỏ (768px trở xuống) */
    @media (max-width: 768px) {
        .filter-results-header {
            padding: 1.25rem !important;
            margin-bottom: 1.5rem !important;
            border-radius: 0.75rem !important;
        }

        /* Layout chính - chuyển sang dạng stack vertical */
        .filter-results-header > .relative > .flex.items-center.justify-between {
            flex-direction: column !important;
            align-items: stretch !important;
            gap: 1.25rem !important;
        }

        /* Phần header với icon và title */
        .filter-results-header .flex.items-center.space-x-3 {
            width: 100% !important;
            justify-content: flex-start !important;
            align-items: center !important;
            gap: 1rem !important;
        }

        .filter-results-header .icon-container {
            flex-shrink: 0 !important;
            width: 2.5rem !important;
            height: 2.5rem !important;
        }

        .filter-results-header .icon-container i {
            font-size: 1rem !important;
        }

        /* Content area */
        .filter-results-header .flex-1.min-w-0 {
            flex: 1 !important;
            min-width: 0 !important;
        }

        /* Title và badge */
        .filter-results-header .flex.items-center.gap-2.mb-1 {
            flex-wrap: wrap !important;
            gap: 0.75rem !important;
            margin-bottom: 0.5rem !important;
        }

        .filter-results-header h2 {
            font-size: 1.25rem !important;
            line-height: 1.3 !important;
            font-weight: 600 !important;
        }

        .filter-results-header .count-badge {
            font-size: 0.75rem !important;
            padding: 0.375rem 0.75rem !important;
            white-space: nowrap !important;
        }

        /* Description area */
        .filter-results-header .text-gray-600.text-sm {
            font-size: 0.875rem !important;
            line-height: 1.5 !important;
        }

        .filter-results-header .flex.flex-wrap.items-center.gap-2 {
            gap: 0.5rem !important;
            flex-wrap: wrap !important;
        }

        /* Quick actions - ẩn từ 768px trở xuống */
        .filter-results-header .quick-actions {
            display: none !important;
        }

        /* Active filters */
        .filter-results-header .active-filters-container {
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 0.75rem !important;
        }

        .filter-results-header .active-filters-container > p {
            margin-bottom: 0 !important;
            margin-right: 0 !important;
            font-weight: 600 !important;
        }
    }

    /* Mobile lớn (640px trở xuống) */
    @media (max-width: 640px) {
        .filter-results-header {
            padding: 1rem !important;
            margin-bottom: 1.25rem !important;
            border-radius: 0.75rem !important;
        }

        /* Icon và title area */
        .filter-results-header .icon-container {
            width: 2.25rem !important;
            height: 2.25rem !important;
        }

        .filter-results-header .icon-container i {
            font-size: 0.875rem !important;
        }

        .filter-results-header h2 {
            font-size: 1.125rem !important;
            line-height: 1.3 !important;
        }

        .filter-results-header .count-badge {
            font-size: 0.6875rem !important;
            padding: 0.25rem 0.625rem !important;
        }

        /* Description */
        .filter-results-header .text-gray-600.text-sm {
            font-size: 0.8125rem !important;
            line-height: 1.4 !important;
        }

        /* Quick actions - vẫn ẩn */
        .filter-results-header .quick-actions {
            display: none !important;
        }

        /* Filter tags */
        .filter-tag {
            width: 100% !important;
            justify-content: space-between !important;
            font-size: 0.75rem !important;
            padding: 0.5rem 0.75rem !important;
            border-radius: 0.5rem !important;
        }

        .filter-tag .remove-btn {
            width: 1.125rem !important;
            height: 1.125rem !important;
            flex-shrink: 0 !important;
        }

        .filter-tag .remove-btn i {
            font-size: 0.625rem !important;
        }

        /* Ẩn decorative elements */
        .filter-results-header .decorative-element {
            display: none !important;
        }
    }

    /* Mobile nhỏ (480px trở xuống) */
    @media (max-width: 480px) {
        .filter-results-header {
            padding: 0.875rem !important;
            margin-bottom: 1rem !important;
            border-radius: 0.625rem !important;
        }

        /* Header layout - icon và title cùng hàng */
        .filter-results-header .flex.items-center.space-x-3 {
            flex-direction: row !important;
            align-items: center !important;
            gap: 0.75rem !important;
            width: 100% !important;
        }

        /* Icon */
        .filter-results-header .icon-container {
            width: 2rem !important;
            height: 2rem !important;
            flex-shrink: 0 !important;
        }

        .filter-results-header .icon-container i {
            font-size: 0.75rem !important;
        }

        /* Title area - flex để chiếm hết không gian còn lại */
        .filter-results-header .flex-1.min-w-0 {
            flex: 1 !important;
            min-width: 0 !important;
        }

        /* Title và badge cùng hàng */
        .filter-results-header .flex.items-center.gap-2.mb-1 {
            flex-direction: row !important;
            align-items: center !important;
            gap: 0.5rem !important;
            margin-bottom: 0 !important;
            flex-wrap: wrap !important;
        }

        .filter-results-header h2 {
            font-size: 1rem !important;
            line-height: 1.25 !important;
            margin: 0 !important;
        }

        .filter-results-header .count-badge {
            font-size: 0.625rem !important;
            padding: 0.25rem 0.5rem !important;
            flex-shrink: 0 !important;
        }

        /* Ẩn description text */
        .filter-results-header .text-gray-600.text-sm.leading-relaxed {
            display: none !important;
        }

        /* Quick actions - vẫn ẩn */
        .filter-results-header .quick-actions {
            display: none !important;
        }

        /* Filter tags - compact */
        .filter-tag {
            font-size: 0.6875rem !important;
            padding: 0.375rem 0.625rem !important;
            border-radius: 0.375rem !important;
        }

        .filter-tag .remove-btn {
            width: 1rem !important;
            height: 1rem !important;
        }

        .filter-tag .remove-btn i {
            font-size: 0.5rem !important;
        }

        /* Spacing adjustments */
        .filter-results-header .active-filters-container {
            gap: 0.5rem !important;
        }

        .filter-results-header > .relative > .flex.items-center.justify-between {
            gap: 1rem !important;
        }
    }

    /* Subtle micro-interactions and animations */
    .filter-results-header {
        animation: fadeInUp 0.4s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .filter-tag {
        transition: all 0.2s ease;
        transform-origin: center;
    }

    .filter-tag:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(251, 146, 60, 0.08);
    }

    .filter-tag .remove-btn {
        transition: all 0.15s ease;
    }

    .filter-tag .remove-btn:hover {
        transform: scale(1.05);
        background: #fed7aa;
    }

    /* Subtle gradient animations */
    .gradient-bg {
        background-size: 150% 150%;
        animation: subtleGradientShift 12s ease infinite;
    }

    @keyframes subtleGradientShift {
        0% {
            background-position: 0% 50%;
        }

        50% {
            background-position: 100% 50%;
        }

        100% {
            background-position: 0% 50%;
        }
    }

    /* Gentle floating decorative elements animation */
    .decorative-element {
        animation: gentleFloat 8s ease-in-out infinite;
    }

    @keyframes gentleFloat {

        0%,
        100% {
            transform: translateY(0px);
        }

        50% {
            transform: translateY(-3px);
        }
    }

    /* Subtle button interactions */
    .luxury-button {
        position: relative;
        overflow: hidden;
    }

    .luxury-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.3s;
    }

    .luxury-button:hover::before {
        left: 100%;
    }

    /* Gentle pulse effect for count badge */
    .count-badge {
        animation: gentlePulse 3s infinite;
    }

    @keyframes gentlePulse {
        0% {
            box-shadow: 0 0 0 0 rgba(251, 146, 60, 0.3);
        }

        70% {
            box-shadow: 0 0 0 4px rgba(251, 146, 60, 0);
        }

        100% {
            box-shadow: 0 0 0 0 rgba(251, 146, 60, 0);
        }
    }

    /* Smooth transitions for interactive elements */
    .filter-tag,
    .luxury-button {
        transition: all 0.15s ease;
    }

    /* Focus states for accessibility */
    .filter-tag:focus-within,
    .luxury-button:focus {
        outline: 2px solid #fb923c;
        outline-offset: 2px;
    }

    /* Focus on content - reduce visual noise */
    .filter-results-header {
        background: linear-gradient(135deg, #fefbf3/60, #fef7ed/60);
        border: 1px solid #fed7aa/30;
        backdrop-filter: blur(8px);
    }

    .filter-results-header.empty-state {
        background: linear-gradient(135deg, #fef3c7/60, #fde68a/60);
        border: 1px solid #f59e0b/30;
    }

    /* Subtle quick search tags */
    .quick-search-tag {
        background: rgba(255, 255, 255, 0.7);
        border: 1px solid rgba(251, 146, 60, 0.2);
        color: #6b7280;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .quick-search-tag:hover {
        background: rgba(255, 247, 237, 0.9);
        border-color: rgba(251, 146, 60, 0.4);
        color: #ea580c;
        transform: translateY(-1px);
    }

    /* Typing effect styles */
    .typing-effect {
        background: linear-gradient(90deg, #fff7ed, #ffedd5, #fff7ed);
        background-size: 200% 100%;
        animation: typingGlow 2s ease-in-out infinite;
        border-color: #fb923c !important;
        box-shadow: 0 0 0 3px rgba(251, 146, 60, 0.15) !important;
        position: relative;
        transition: all 0.3s ease;
    }

    .typing-effect::after {
        content: '|';
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 18px;
        font-weight: 300;
        color: #f97316;
        animation: typingCursor 1.2s infinite;
        pointer-events: none;
        z-index: 10;
    }

    /* Smooth glow animation */
    @keyframes typingGlow {
        0%, 100% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
    }

    /* Realistic cursor blinking */
    @keyframes typingCursor {
        0%, 45% {
            opacity: 1;
        }
        46%, 100% {
            opacity: 0;
        }
    }

    /* Smooth transition when typing ends */
    .typing-effect.ending {
        animation: typingComplete 0.5s ease-out forwards;
    }

    @keyframes typingComplete {
        0% {
            background-position: 100% 50%;
            box-shadow: 0 0 0 3px rgba(251, 146, 60, 0.15);
        }
        100% {
            background-position: 0% 50%;
            box-shadow: 0 0 0 2px rgba(251, 146, 60, 0.1);
        }
    }

    /* Reduce sidebar visual weight */
    .sidebar-filters {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
    }

    /* ===== GPU OPTIMIZED DROPDOWN ANIMATIONS ===== */
    /* Tối ưu hiệu ứng dropdown với GPU acceleration */

    /* Filter Section Animations - Smooth & Fluid */
    .filter-section-visible {
        opacity: 1;
        max-height: 1000px;
        transform: translate3d(0, 0, 0);
        visibility: visible;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        will-change: transform, opacity, max-height;
        backface-visibility: hidden;
    }

    .filter-section-hidden {
        opacity: 0;
        max-height: 0;
        transform: translate3d(0, -10px, 0);
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        will-change: transform, opacity, max-height;
        overflow: hidden;
        backface-visibility: hidden;
    }

    /* Subcategory Animations - Smooth & Fluid */
    .subcategory-visible {
        opacity: 1;
        max-height: 500px;
        transform: translate3d(0, 0, 0) scale(1);
        visibility: visible;
        transition: all 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        will-change: transform, opacity, max-height;
        backface-visibility: hidden;
    }

    .subcategory-hidden {
        opacity: 0;
        max-height: 0;
        transform: translate3d(-5px, 0, 0) scale(0.98);
        visibility: hidden;
        transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        will-change: transform, opacity, max-height;
        overflow: hidden;
        backface-visibility: hidden;
    }

    /* Chevron Icon GPU Optimized */
    .fas.fa-chevron-right {
        transition: transform 0.2s ease, color 0.15s ease;
        will-change: transform;
        transform-origin: center;
        backface-visibility: hidden;
        transform: translateZ(0); /* GPU layer */
    }

    /* Enhanced Hover Effects - GPU Optimized */
    .cursor-pointer:hover .fas.fa-chevron-right:not(.rotate-90) {
        transform: translate3d(0, 0, 0) scale(1.1);
        color: #f97316;
    }

    /* Hover effect khi đã xoay xuống */
    .cursor-pointer:hover .fas.fa-chevron-right.rotate-90 {
        transform: translate3d(0, 0, 0) rotate(90deg) scale(1.1);
        color: #f97316;
    }

    /* Rotation state - GPU Optimized */
    .fas.fa-chevron-right.rotate-90 {
        transform: translate3d(0, 0, 0) rotate(90deg);
    }

    /* Subcategory chevron hover effects */
    .group:hover .fas.fa-chevron-right:not(.rotate-90) {
        transform: translate3d(0, 0, 0) scale(1.1);
        color: #f97316;
    }

    .group:hover .fas.fa-chevron-right.rotate-90 {
        transform: translate3d(0, 0, 0) rotate(90deg) scale(1.1);
        color: #f97316;
    }

    /* Sidebar specificity */
    .sidebar-filters .fas.fa-chevron-right.rotate-90 {
        transform: translate3d(0, 0, 0) rotate(90deg) !important;
    }

    .sidebar-filters .cursor-pointer:hover .fas.fa-chevron-right.rotate-90 {
        transform: translate3d(0, 0, 0) rotate(90deg) scale(1.1) !important;
        color: #f97316;
    }

    .sidebar-filters .group:hover .fas.fa-chevron-right.rotate-90 {
        transform: translate3d(0, 0, 0) rotate(90deg) scale(1.1) !important;
        color: #f97316;
    }

    /* Smooth Staggered Animation for Filter Items */
    .filter-section-visible > * {
        animation: slideInStagger 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        opacity: 0;
        transform: translate3d(0, 15px, 0);
    }

    .filter-section-visible > *:nth-child(1) { animation-delay: 0.08s; }
    .filter-section-visible > *:nth-child(2) { animation-delay: 0.12s; }
    .filter-section-visible > *:nth-child(3) { animation-delay: 0.16s; }
    .filter-section-visible > *:nth-child(4) { animation-delay: 0.2s; }
    .filter-section-visible > *:nth-child(5) { animation-delay: 0.24s; }
    .filter-section-visible > *:nth-child(6) { animation-delay: 0.28s; }

    @keyframes slideInStagger {
        0% {
            opacity: 0;
            transform: translate3d(0, 15px, 0);
        }
        100% {
            opacity: 1;
            transform: translate3d(0, 0, 0);
        }
    }

    /* Smooth Subcategory Staggered Animation */
    .subcategory-visible > * {
        animation: slideInSubcategory 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        opacity: 0;
        transform: translate3d(-12px, 0, 0);
    }

    .subcategory-visible > *:nth-child(1) { animation-delay: 0.06s; }
    .subcategory-visible > *:nth-child(2) { animation-delay: 0.1s; }
    .subcategory-visible > *:nth-child(3) { animation-delay: 0.14s; }
    .subcategory-visible > *:nth-child(4) { animation-delay: 0.18s; }
    .subcategory-visible > *:nth-child(5) { animation-delay: 0.22s; }

    @keyframes slideInSubcategory {
        0% {
            opacity: 0;
            transform: translate3d(-12px, 0, 0);
        }
        100% {
            opacity: 1;
            transform: translate3d(0, 0, 0);
        }
    }

    /* Performance Optimizations */
    .sidebar-filters * {
        backface-visibility: hidden;
        perspective: 1000px;
    }

    /* Checkbox Interactions - GPU Optimized */
    .custom-checkbox {
        transition: transform 0.15s ease, box-shadow 0.15s ease;
        will-change: transform;
        backface-visibility: hidden;
        transform: translateZ(0); /* GPU layer */
    }

    .custom-checkbox:hover {
        transform: translate3d(0, 0, 0) scale(1.05);
        box-shadow: 0 0 0 4px rgba(251, 146, 60, 0.1);
    }

    .custom-checkbox:checked {
        animation: checkboxBounce 0.25s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    @keyframes checkboxBounce {
        0% { transform: translate3d(0, 0, 0) scale(1); }
        50% { transform: translate3d(0, 0, 0) scale(1.15); }
        100% { transform: translate3d(0, 0, 0) scale(1); }
    }

    /* Price Preset Buttons - GPU Optimized với Outline Style */
    .price-preset {
        transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.3s ease;
        will-change: transform;
        backface-visibility: hidden;
        transform: translateZ(0); /* GPU layer */
        position: relative;
        overflow: visible;
    }

    /* Dấu tích ở góc phải trên - chỉ hiện khi active */
    .price-preset::after {
        content: '✓';
        position: absolute;
        top: -8px;
        right: -8px;
        width: 18px;
        height: 18px;
        background: #f97316;
        color: white;
        border-radius: 50%;
        font-size: 11px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transform: scale(0);
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        font-weight: bold;
        line-height: 1;
        border: 2px solid white;
        box-shadow: 0 2px 8px rgba(249, 115, 22, 0.3);
        z-index: 10;
    }

    .price-preset:hover {
        transform: translate3d(0, -2px, 0) scale(1.02);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .price-preset:active {
        transform: translate3d(0, 0, 0) scale(0.98);
        transition-duration: 0.1s;
    }

    /* Style cho trạng thái active - Outline cam thay vì background cam */
    .price-preset.from-orange-500 {
        background: white !important;
        color: #f97316 !important;
        border-color: #f97316 !important;
        box-shadow: 0 0 0 1px #f97316 !important;
    }

    .price-preset.from-orange-500::after {
        opacity: 1;
        transform: scale(1);
    }

    /* Override các class Tailwind để đảm bảo outline style */
    .price-preset.bg-gradient-to-r.from-orange-500.to-orange-600 {
        background: white !important;
        background-image: none !important;
    }

    /* ===== GPU PERFORMANCE OPTIMIZATIONS ===== */
    /* Tối ưu hiệu suất GPU rendering */

    /* Create GPU layers for main containers */
    .sidebar-filters {
        contain: layout style paint;
        transform: translateZ(0);
        backface-visibility: hidden;
    }

    /* Optimize animated elements */
    .filter-section-visible,
    .filter-section-hidden,
    .subcategory-visible,
    .subcategory-hidden {
        contain: layout style;
        transform: translateZ(0);
        backface-visibility: hidden;
    }

    /* Clean up will-change after animations */
    .filter-section-visible *,
    .subcategory-visible * {
        will-change: auto;
    }

    /* Optimize interactive elements */
    .custom-checkbox,
    .price-preset,
    .fas.fa-chevron-right {
        contain: layout style;
    }

    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
        .filter-section-visible,
        .filter-section-hidden,
        .subcategory-visible,
        .subcategory-hidden,
        .custom-checkbox,
        .price-preset,
        .fas.fa-chevron-right {
            transition: none !important;
            animation: none !important;
        }

        .filter-section-visible > *,
        .subcategory-visible > * {
            animation: none !important;
        }
    }

    /* Optimize for mobile devices - Keep smooth animations */
    @media (max-width: 768px) {
        .filter-section-visible,
        .filter-section-hidden {
            transition: all 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .subcategory-visible,
        .subcategory-hidden {
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Responsive cho dấu tích price-preset */
        .price-preset::after {
            width: 16px;
            height: 16px;
            font-size: 10px;
            top: -6px;
            right: -6px;
        }
    }

    @media (max-width: 480px) {
        .price-preset::after {
            width: 16px;
            height: 16px;
            font-size: 10px;
            top: -6px;
            right: -6px;
        }
    }

        /* Slightly faster stagger on mobile */
        .filter-section-visible > *:nth-child(1) { animation-delay: 0.05s; }
        .filter-section-visible > *:nth-child(2) { animation-delay: 0.08s; }
        .filter-section-visible > *:nth-child(3) { animation-delay: 0.11s; }
        .filter-section-visible > *:nth-child(4) { animation-delay: 0.14s; }
        .filter-section-visible > *:nth-child(5) { animation-delay: 0.17s; }
        .filter-section-visible > *:nth-child(6) { animation-delay: 0.2s; }

        .subcategory-visible > *:nth-child(1) { animation-delay: 0.04s; }
        .subcategory-visible > *:nth-child(2) { animation-delay: 0.07s; }
        .subcategory-visible > *:nth-child(3) { animation-delay: 0.1s; }
        .subcategory-visible > *:nth-child(4) { animation-delay: 0.13s; }
        .subcategory-visible > *:nth-child(5) { animation-delay: 0.16s; }
    }

    /* Keep animations on small devices for smooth experience */
    @media (max-width: 480px) {
        .filter-section-visible,
        .filter-section-hidden {
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .subcategory-visible,
        .subcategory-hidden {
            transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Faster stagger for small screens */
        .filter-section-visible > * {
            animation-duration: 0.4s;
        }

        .subcategory-visible > * {
            animation-duration: 0.3s;
        }
    }

    /* Focus on products grid */
    .products-grid {
        position: relative;
        z-index: 1;
    }

    .product-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .product-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border-color: rgba(251, 146, 60, 0.2);
    }

    /* Subtle pagination */
    .pagination-section {
        background: rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(8px);
        border-radius: 1rem;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* Reduce button prominence except primary actions */
    .luxury-button:not(.primary-action) {
        opacity: 0.8;
        font-weight: 500;
    }

    .luxury-button:not(.primary-action):hover {
        opacity: 1;
    }

    /* Clean empty state */
    .simple-empty-state {
        background: none;
        border: none;
        box-shadow: none;
    }

    /* Animated Background Pattern for Filter Results Header */
    .filter-bg-pattern {
        background-image:
            /* Geometric dots pattern */
            radial-gradient(circle at 20% 20%, #f97316 1.5px, transparent 1.5px),
            radial-gradient(circle at 80% 80%, #f59e0b 1px, transparent 1px),
            radial-gradient(circle at 40% 60%, #fb923c 1px, transparent 1px),
            radial-gradient(circle at 70% 30%, #f97316 0.8px, transparent 0.8px),
            /* Subtle lines */
            linear-gradient(45deg, transparent 48%, rgba(249, 115, 22, 0.1) 49%, rgba(249, 115, 22, 0.1) 51%, transparent 52%),
            linear-gradient(135deg, transparent 48%, rgba(251, 146, 60, 0.08) 49%, rgba(251, 146, 60, 0.08) 51%, transparent 52%);
        background-size:
            60px 60px,
            40px 40px,
            80px 80px,
            30px 30px,
            120px 120px,
            100px 100px;
        background-position:
            0 0,
            20px 20px,
            40px 10px,
            60px 40px,
            0 0,
            50px 50px;
        animation: filterPatternMove 25s linear infinite;
    }

    @keyframes filterPatternMove {
        0% {
            background-position:
                0 0,
                20px 20px,
                40px 10px,
                60px 40px,
                0 0,
                50px 50px;
        }

        100% {
            background-position:
                60px 60px,
                80px 80px,
                120px 70px,
                90px 70px,
                120px 120px,
                150px 150px;
        }
    }

    /* Loading Skeleton for Filter Results Header */
    .filter-results-header.loading {
        pointer-events: none;
        opacity: 0.7;
    }

    .filter-results-header.loading .icon-container {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
    }

    .filter-results-header.loading .count-badge {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        color: transparent;
    }

    .filter-results-header.loading h2,
    .filter-results-header.loading .text-gray-600 {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        color: transparent;
        border-radius: 4px;
    }

    .filter-results-header.loading .luxury-button {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        color: transparent;
        border-color: transparent;
    }

    @keyframes skeleton-loading {
        0% {
            background-position: 200% 0;
        }

        100% {
            background-position: -200% 0;
        }
    }

    /* Smooth transitions for count badge */
    .count-badge {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform-origin: center;
    }

    .count-badge.updating {
        animation: countUpdate 0.6s ease-in-out;
    }

    @keyframes countUpdate {
        0% {
            transform: scale(1);
            opacity: 1;
        }

        50% {
            transform: scale(1.1);
            opacity: 0.8;
        }

        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* Smooth transitions for header content */
    .filter-results-header h2 {
        transition: all 0.3s ease;
    }

    .filter-results-header .text-gray-600 {
        transition: all 0.3s ease;
    }

    .filter-results-header.empty-state {
        background: linear-gradient(135deg, #fef3c7/80, #fde68a/80);
        border-color: #f59e0b/40;
    }

    .filter-results-header.empty-state .icon-container.empty-icon {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    /* Visual Hierarchy for Empty States */
    .filter-results-header.empty-state {
        margin-bottom: 2rem;
        border-left: 4px solid #f59e0b;
    }

    /* Enhanced Products Grid Empty State */
    .products-grid-empty {
        background: linear-gradient(135deg, #fefbf3, #fef7ed);
        border: 2px dashed #fed7aa;
        border-radius: 1rem;
        margin: 1rem;
        position: relative;
        overflow: hidden;
    }

    .products-grid-empty::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #f97316, #ea580c, #f97316);
        background-size: 200% 100%;
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% {
            background-position: -200% 0;
        }

        100% {
            background-position: 200% 0;
        }
    }

    /* Empty state flow animation */
    .filter-results-header.empty-state+.products-content .products-grid-empty {
        animation: slideInFromTop 0.6s ease-out 0.3s both;
    }

    @keyframes slideInFromTop {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Improved empty state icon styling */
    .empty-state-icon-container {
        position: relative;
        display: inline-block;
    }

    .empty-state-icon-container::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 120%;
        height: 120%;
        border: 2px solid #fed7aa;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        animation: pulse-ring 2s ease-in-out infinite;
    }

    @keyframes pulse-ring {
        0% {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 1;
        }

        100% {
            transform: translate(-50%, -50%) scale(1.2);
            opacity: 0;
        }
    }

    /* Custom Checkbox Styling */
    .custom-checkbox {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 1rem;
        height: 1rem;
        border: 2px solid #d1d5db;
        border-radius: 0.375rem;
        background-color: white;
        cursor: pointer;
        position: relative;
        transition: all 0.2s ease-in-out;
    }

    .custom-checkbox:hover {
        border-color: #fb923c;
        box-shadow: 0 0 0 3px rgba(251, 146, 60, 0.1);
    }

    .custom-checkbox:focus {
        outline: none;
        border-color: #f97316;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
    }

    .custom-checkbox:checked {
        background-color: #f97316;
        border-color: #f97316;
        background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
        background-size: 0.75rem;
        background-position: center;
        background-repeat: no-repeat;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
    }

    .custom-checkbox:checked:hover {
        background-color: #ea580c;
        border-color: #ea580c;
    }

    .custom-checkbox:checked:focus {
        background-color: #f97316;
        border-color: #f97316;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.3);
    }

    /* Animation for checkbox */
    .custom-checkbox:checked {
        animation: checkboxPop 0.2s ease-in-out;
    }

    @keyframes checkboxPop {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.1);
        }

        100% {
            transform: scale(1);
        }
    }

    /* Custom Price Input Styling */
    .price-input {
        padding: 8px 16px 8px 16px !important;
    }

    /* Debug CSS cho Recently Viewed và Trending sections */
    .recently-viewed-section,
    .trending-section {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        position: relative !important;
        z-index: 10 !important;
        pointer-events: auto !important;
        background: white !important;
        border: 1px solid #e5e7eb !important;
        margin-top: 1.5rem !important;
    }

    .recently-viewed-section *,
    .trending-section * {
        pointer-events: auto !important;
    }

    /* Đảm bảo không có element nào che phủ */
    .recently-viewed-section::before,
    .trending-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: transparent;
        z-index: -1;
    }

    /* Responsive text cho sidebar titles từ 1330px trở xuống */
    @media (max-width: 1330px) {
        /* Ẩn text dài, hiện text ngắn */
        .sidebar-title-full {
            display: none !important;
        }

        .sidebar-title-short {
            display: inline !important;
        }

        .sidebar-title-extra-short {
            display: none !important;
        }
    }

    /* Breakpoint đặc biệt cho phần mô tả bộ lọc từ 1024px-1086px */
    @media (min-width: 1024px) and (max-width: 1086px) {
        .filter-header-desc .sidebar-title-short {
            display: none !important;
        }

        .filter-header-desc .sidebar-title-extra-short {
            display: inline !important;
        }
    }

    /* Mặc định: hiện text dài, ẩn text ngắn và extra short */
    .sidebar-title-full {
        display: inline;
    }

    .sidebar-title-short {
        display: none;
    }

    .sidebar-title-extra-short {
        display: none;
    }

    /* Responsive cho Price Input Range từ 1024px-1220px */
    @media (min-width: 1024px) and (max-width: 1220px) {
        /* Chuyển từ layout ngang sang layout dọc */
        .price-input-range {
            flex-direction: column !important;
            gap: 1rem !important;
        }

        /* Đảm bảo mỗi input chiếm full width */
        .price-input-range > div {
            flex: none !important;
            min-width: 100% !important;
        }
    }

    /* ===== RESPONSIVE LAYOUT FOR MOBILE FILTER ===== */
    /* Điều chỉnh layout khi có mobile filter modal */

    @media (max-width: 1023px) {
        /* Ẩn toàn bộ sidebar (bao gồm cả recently viewed và trending sections) */
        aside.lg\\:w-1\\/4 {
            display: none !important;
        }

        /* Ẩn hoàn toàn các elements desktop-only trên mobile */
        .desktop-only {
            display: none !important;
        }

        /* Điều chỉnh layout products content để chiếm full width */
        .flex.flex-col.lg\\:flex-row.lg\\:space-x-6 {
            flex-direction: column !important;
        }

        .lg\\:w-3\\/4 {
            width: 100% !important;
        }
    }

    /* ===== RESPONSIVE SUGGESTIONS GRID ===== */
    /* Tối ưu hiển thị grid suggestions cho mobile */

    /* Từ 768px trở xuống - hiển thị 2 cột để cân đối */
    @media (max-width: 767px) {
        .grid.grid-cols-2.sm\\:grid-cols-2.md\\:grid-cols-2.lg\\:grid-cols-2 {
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 0.75rem !important;
        }

        /* Điều chỉnh font size và spacing cho mobile */
        .grid.grid-cols-2.sm\\:grid-cols-2.md\\:grid-cols-2.lg\\:grid-cols-2 .flex.items-start.space-x-2 {
            font-size: 0.8125rem !important;
            line-height: 1.4 !important;
        }

        .grid.grid-cols-2.sm\\:grid-cols-2.md\\:grid-cols-2.lg\\:grid-cols-2 .flex.items-start.space-x-2 i {
            font-size: 0.6875rem !important;
            margin-top: 0.125rem !important;
            flex-shrink: 0 !important;
        }

        .grid.grid-cols-2.sm\\:grid-cols-2.md\\:grid-cols-2.lg\\:grid-cols-2 .flex.items-start.space-x-2 span {
            flex: 1 !important;
            word-break: break-word !important;
        }
    }

    /* Từ 500px trở xuống - mỗi gợi ý một hàng và căn trái - CSS với độ ưu tiên cao */
    @media (max-width: 500px) {
        /* Target chính xác class grid trong suggestions */
        .bg-white\\/60.rounded-xl.p-6.mb-6.border.border-orange-100\\/50 .grid {
            display: flex !important;
            flex-direction: column !important;
            gap: 1rem !important;
            align-items: flex-start !important;
            justify-content: flex-start !important;
            grid-template-columns: none !important;
        }

        /* Target chính xác các suggestion items */
        .bg-white\\/60.rounded-xl.p-6.mb-6.border.border-orange-100\\/50 .grid .flex.items-start.space-x-2 {
            display: flex !important;
            font-size: 0.8125rem !important;
            line-height: 1.4 !important;
            gap: 0.5rem !important;
            justify-content: flex-start !important;
            align-items: center !important;
            width: 100% !important;
            max-width: none !important;
            text-align: left !important;
            margin: 0 !important;
        }

        .bg-white\\/60.rounded-xl.p-6.mb-6.border.border-orange-100\\/50 .grid .flex.items-start.space-x-2 i {
            font-size: 0.6875rem !important;
            margin-top: 0 !important;
            flex-shrink: 0 !important;
        }

        .bg-white\\/60.rounded-xl.p-6.mb-6.border.border-orange-100\\/50 .grid .flex.items-start.space-x-2 span {
            flex: 1 !important;
            word-break: break-word !important;
        }

        /* Fallback với class selector khác */
        div[class*="grid"][class*="grid-cols-2"][class*="gap-3"][class*="text-sm"][class*="text-gray-600"] {
            display: flex !important;
            flex-direction: column !important;
            gap: 1rem !important;
            align-items: flex-start !important;
            justify-content: flex-start !important;
        }

        div[class*="grid"][class*="grid-cols-2"][class*="gap-3"][class*="text-sm"][class*="text-gray-600"] > div {
            display: flex !important;
            justify-content: flex-start !important;
            align-items: center !important;
            margin: 0 !important;
            width: 100% !important;
        }
    }

    /* ===== RESPONSIVE BUTTON TEXT ALIGNMENT ===== */
    /* Căn giữa text trong nút "Liên hệ tư vấn" và "Xem tất cả sản phẩm" từ 639px trở xuống */

    @media (max-width: 639px) {
        /* Target nút "Liên hệ tư vấn" */
        a[href*="contact.php"].inline-flex.items-center {
            justify-content: center !important;
            text-align: center !important;
        }

        /* Target nút "Xem tất cả sản phẩm" */
        a[href*="products.php"].inline-flex.items-center {
            justify-content: center !important;
            text-align: center !important;
        }

        /* Target container của 2 nút */
        .flex.flex-col.sm\\:flex-row.gap-2.justify-center {
            align-items: center !important;
        }

        .flex.flex-col.sm\\:flex-row.gap-2.justify-center > a {
            justify-content: center !important;
            text-align: center !important;
            width: 100% !important;
            max-width: 280px !important;
        }

        /* Đảm bảo icon và text căn giữa */
        .flex.flex-col.sm\\:flex-row.gap-2.justify-center > a i {
            margin-right: 0.5rem !important;
        }
    }

    /* ===== CUSTOM RESPONSIVE GRID FOR PRODUCTS ===== */
    /* Responsive grid theo yêu cầu cụ thể */

    /* Từ 576px đổ xuống: 1 cột */
    @media (max-width: 576px) {
        .products-grid {
            grid-template-columns: repeat(1, 1fr) !important;
        }
    }

    /* 577px đến 879px: 2 cột */
    @media (min-width: 577px) and (max-width: 879px) {
        .products-grid {
            grid-template-columns: repeat(2, 1fr) !important;
        }
    }

    /* 880px đến 1023px: 3 cột (sidebar ẩn) */
    @media (min-width: 880px) and (max-width: 1023px) {
        .products-grid {
            grid-template-columns: repeat(3, 1fr) !important;
        }
    }

    /* 1024px đến 1080px: 2 cột (sidebar hiện) */
    @media (min-width: 1024px) and (max-width: 1080px) {
        .products-grid {
            grid-template-columns: repeat(2, 1fr) !important;
        }
    }

    /* Từ 1081px đổ lên: 3 cột (sidebar hiện) */
    @media (min-width: 1081px) {
        .products-grid {
            grid-template-columns: repeat(3, 1fr) !important;
        }
    }

    /* List view - luôn 1 cột cho tất cả màn hình */
    .products-grid-list {
        grid-template-columns: repeat(1, 1fr) !important;
    }

    /* ===== SEARCH SUGGESTIONS HOVER EFFECT ===== */
    /* Đảm bảo hover effect có cùng màu cam với keyboard navigation */
    /* Sử dụng màu #fff0e8 giống như search header */
    .hover\:bg-orange-50:hover {
        background-color: #fff0e8 !important;
    }

    /* ===== KEYBOARD NAVIGATION ENHANCEMENTS ===== */
    /* Smooth scrolling cho suggestions container */
    .search-suggestions-container {
        scroll-behavior: smooth;
    }

    /* Visual feedback cho keyboard selection */
    [data-keyboard-selected="true"] {
        background-color: #fff0e8 !important;
        transition: all 0.2s ease;
    }

    /* Đảm bảo hover không override keyboard selection */
    [data-keyboard-selected="true"]:hover {
        background-color: #fff0e8 !important;
    }

    /* Smooth transition cho tất cả suggestion items */
    .search-suggestion-item {
        transition: all 0.2s ease;
    }

    /* ===== FILTER MICRO-INTERACTIONS ===== */

    /* Filter Notification Styles */
    .filter-notification {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        opacity: 1;
        animation: slideInNotification 0.4s ease-out;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 10;
    }

    .filter-notification.fade-out {
        opacity: 0;
        transform: translateY(-10px);
    }

    /* Highlight effect when scrolled to */
    .filter-notification.highlight {
        animation: highlightNotification 1s ease-out;
    }

    @keyframes slideInNotification {
        0% {
            opacity: 0;
            transform: translateY(10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes highlightNotification {
        0% {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        50% {
            box-shadow: 0 8px 24px rgba(251, 146, 60, 0.3);
            transform: scale(1.02);
        }
        100% {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: scale(1);
        }
    }

    /* Reset Sync Icon Centering */
    .reset-sync-icon-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
    }

    .reset-sync-icon {
        font-size: 12px;
        line-height: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.5s ease;
        transform-origin: center center;
    }

    .group:hover .reset-sync-icon {
        transform: rotate(180deg);
    }

    /* Apply Filters Button - Clean Base Styles */
    .apply-filters-btn {
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .apply-filters-btn .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        position: relative;
    }

    /* Clean button styles - no loading states */
    .apply-filters-btn .btn-text {
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .apply-filters-btn .btn-filter-icon {
        transition: all 0.3s ease;
    }

    /* Button states và animations */
    .apply-filters-btn:hover {
        transform: translateY(-1px) scale(1.02);
        box-shadow: 0 8px 25px rgba(249, 115, 22, 0.3);
    }

    .apply-filters-btn:active {
        transform: translateY(0) scale(0.98);
        transition: all 0.1s ease;
    }

    .apply-filters-btn.loading {
        cursor: not-allowed;
        transform: scale(0.98);
        background: linear-gradient(135deg, #f97316, #ea580c) !important;
    }

    .apply-filters-btn.loading:hover {
        transform: scale(0.98);
        box-shadow: 0 4px 15px rgba(249, 115, 22, 0.2);
    }

    /* Success State - Màu xanh lá cây sang trọng */
    .apply-filters-btn.success {
        background: linear-gradient(135deg, #059669, #10b981, #34d399) !important;
        border-color: #10b981 !important;
        transform: scale(1.02);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4) !important;
        animation: successPulse 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .apply-filters-btn.success::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(135deg, #059669, #10b981, #34d399);
        border-radius: inherit;
        z-index: -1;
        opacity: 0.7;
        animation: successGlow 1.2s ease-in-out;
    }

    .apply-filters-btn.success:hover {
        transform: scale(1.02);
        box-shadow: 0 10px 30px rgba(16, 185, 129, 0.5) !important;
    }

    /* Icon states - đảm bảo căn giữa hoàn hảo */
    .apply-filters-btn .btn-spinner {
        display: none;
        align-items: center;
        justify-content: center;
    }

    .apply-filters-btn .btn-success {
        display: none;
        color: white;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* Success state text styling */
    .apply-filters-btn.success .btn-text {
        color: white !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        letter-spacing: 0.025em;
    }

    /* Đảm bảo btn-content luôn căn giữa */
    .apply-filters-btn .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        gap: 0;
    }

    /* Khi loading hoặc success, loại bỏ gap giữa các elements */
    .apply-filters-btn.loading .btn-content {
        gap: 0;
    }

    /* Pulse animation cho overlay */
    @keyframes pulse {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.7;
            transform: scale(1.1);
        }
    }

    /* ===== SEARCH SUBMIT BUTTON LOADING EFFECTS ===== */
    /* Giống y hệt nút "Đặt bộ lọc" */

    .search-submit-btn {
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        /* Cố định kích thước để tránh layout jump */
        min-width: 140px;
        max-width: 220px; /* Tăng để có đủ chỗ cho text success */
        width: auto;
        white-space: nowrap;
        /* Thêm padding để thoải mái hơn */
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }

    /* Loading State - Cải thiện transitions */
    .search-submit-btn.loading {
        background: linear-gradient(135deg, #f97316, #ea580c, #dc2626) !important;
        border-color: #ea580c !important;
        transform: scale(0.98);
        box-shadow: 0 4px 15px rgba(249, 115, 22, 0.2);
        pointer-events: none;
        /* Đảm bảo kích thước không thay đổi */
        min-width: 140px;
        max-width: 220px;
        /* Thêm padding để thoải mái hơn */
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }

    /* Success State - Cải thiện transitions */
    .search-submit-btn.success {
        background: linear-gradient(135deg, #059669, #10b981, #34d399) !important;
        border-color: #10b981 !important;
        transform: scale(1.02);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4) !important;
        animation: successPulse 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        /* Đảm bảo kích thước không thay đổi và có đủ chỗ cho text success */
        min-width: 140px;
        max-width: 220px;
        /* Thêm padding để text success thoải mái hơn */
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .search-submit-btn.success::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(135deg, #059669, #10b981, #34d399);
        border-radius: inherit;
        z-index: -1;
        opacity: 0.7;
        animation: successGlow 1.2s ease-in-out;
    }

    .search-submit-btn.success:hover {
        transform: scale(1.02);
        box-shadow: 0 10px 30px rgba(16, 185, 129, 0.5) !important;
    }

    /* Button content layout - Cải thiện để tránh layout jump */
    .search-submit-btn .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        gap: 0.5rem;
        /* Đảm bảo layout ổn định */
        min-height: 1.5rem;
        position: relative;
    }

    .search-submit-btn.loading .btn-content,
    .search-submit-btn.success .btn-content {
        gap: 0.5rem;
        /* Giữ nguyên kích thước khi chuyển state */
        min-height: 1.5rem;
    }

    /* Spinner styles - Cải thiện để tránh layout shift */
    .search-submit-btn .btn-spinner {
        display: none;
        align-items: center;
        justify-content: center;
        /* Cố định kích thước để tránh jump */
        width: 1rem;
        height: 1rem;
        flex-shrink: 0;
    }

    .search-submit-btn .btn-success {
        display: none;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        /* Cố định kích thước giống spinner */
        width: 1rem;
        height: 1rem;
        flex-shrink: 0;
    }

    /* Search icon styles - Cố định kích thước */
    .search-submit-btn .btn-search-icon {
        width: 1rem;
        height: 1rem;
        flex-shrink: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Text styles - Đảm bảo không wrap */
    .search-submit-btn .btn-text {
        white-space: nowrap;
        flex-shrink: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Text styling */
    .search-submit-btn.success .btn-text {
        color: white !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        letter-spacing: 0.025em;
    }

    .search-submit-btn.loading .btn-text,
    .search-submit-btn.success .btn-text {
        animation: textSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Smooth state transitions - Ngăn chặn layout jump */
    .search-submit-btn * {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Đảm bảo spinner có kích thước cố định */
    .search-submit-btn .btn-spinner .spinner-border {
        width: 1rem !important;
        height: 1rem !important;
        border-width: 2px !important;
    }

    /* Spin animation cho search button spinner */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Success animations */
    @keyframes successPulse {
        0% {
            transform: scale(0.98);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 12px 35px rgba(16, 185, 129, 0.6);
        }
        100% {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }
    }

    @keyframes successGlow {
        0% {
            opacity: 0.7;
            transform: scale(1);
        }
        50% {
            opacity: 0.9;
            transform: scale(1.02);
        }
        100% {
            opacity: 0.7;
            transform: scale(1);
        }
    }

    /* Success icon animation */
    @keyframes successCheckmark {
        0% {
            transform: scale(0) rotate(-45deg);
            opacity: 0;
        }
        50% {
            transform: scale(1.2) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: scale(1) rotate(0deg);
            opacity: 1;
        }
    }

    /* Success text animation */
    @keyframes successTextSlide {
        0% {
            opacity: 0;
            transform: translateX(-10px);
        }
        100% {
            opacity: 1;
            transform: translateX(0);
        }
    }


        border-radius: inherit;
        z-index: -1;
        opacity: 0.7;
        animation: loadingGlow 2s ease-in-out infinite;
    }

    @keyframes loadingGlow {
        0%, 100% { opacity: 0.7; transform: scale(1); }
        50% { opacity: 0.9; transform: scale(1.02); }
    }

    /* Success state enhancements */
    .apply-filters-btn.success::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: radial-gradient(circle, rgba(16, 185, 129, 0.3) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        animation: successExpand 0.8s ease-out;
        z-index: -1;
    }

    @keyframes successExpand {
        0% { width: 0; height: 0; opacity: 1; }
        100% { width: 200px; height: 200px; opacity: 0; }
    }

    /* Smooth text transitions */
    .apply-filters-btn .btn-text {
        display: inline-block;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .apply-filters-btn.loading .btn-text,
    .apply-filters-btn.success .btn-text {
        animation: textSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes textSlideIn {
        0% { opacity: 0; transform: translateY(10px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    /* Success checkmark animation - Smooth scale without rotation */
    @keyframes successCheckmark {
        0% {
            opacity: 0;
            transform: scale(0.5);
        }
        50% {
            opacity: 1;
            transform: scale(1.1);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* Success text slide animation */
    @keyframes successTextSlide {
        0% {
            opacity: 0;
            transform: translateX(-10px);
        }
        100% {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Đảm bảo animations được reset khi cần */
    .apply-filters-btn * {
        animation-fill-mode: forwards;
    }

    /* Force immediate state changes */
    .apply-filters-btn .immediate-hide {
        display: none !important;
        opacity: 0 !important;
        transform: scale(0) !important;
        transition: none !important;
        animation: none !important;
    }

    .apply-filters-btn .immediate-show {
        display: inline-flex !important;
        transition: none !important;
        animation: none !important;
    }

    /* Reset Filters Button Animation */
    .reset-filters-btn {
        position: relative;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateZ(0);
        backface-visibility: hidden;
    }

    .reset-filters-btn .broom-icon {
        transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        transform-origin: center;
    }

    .reset-filters-btn:hover .broom-icon {
        transform: rotate(15deg) scale(1.1);
    }

    .reset-filters-btn.resetting .broom-icon {
        animation: broomSweep 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    @keyframes broomSweep {
        0% { transform: rotate(0deg) scale(1); }
        25% { transform: rotate(-15deg) scale(1.1); }
        50% { transform: rotate(15deg) scale(1.1); }
        75% { transform: rotate(-10deg) scale(1.05); }
        100% { transform: rotate(0deg) scale(1); }
    }

    /* Loading state cho nút reset - giống nút "Áp dụng bộ lọc" */
    .reset-filters-btn.loading {
        pointer-events: none;
    }

    /* Filter Tags Fade Animation */
    .filter-tag {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateZ(0);
        backface-visibility: hidden;
    }

    .filter-tag.fade-out {
        opacity: 0;
        transform: scale(0.8) translateY(-10px);
        pointer-events: none;
    }

    .filter-tag.fade-in {
        animation: filterTagFadeIn 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    @keyframes filterTagFadeIn {
        0% {
            opacity: 0;
            transform: scale(0.8) translateY(10px);
        }
        100% {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    /* Custom Checkbox Bounce & Ripple Effects */
    .custom-checkbox {
        position: relative;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateZ(0);
        backface-visibility: hidden;
    }

    .custom-checkbox::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(249, 115, 22, 0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        pointer-events: none;
        z-index: -1;
    }

    .custom-checkbox:active::before {
        width: 40px;
        height: 40px;
        opacity: 1;
    }

    .custom-checkbox.bounce {
        animation: checkboxBounce 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    @keyframes checkboxBounce {
        0% { transform: scale(1); }
        30% { transform: scale(0.9); }
        60% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .custom-checkbox.ripple::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(249, 115, 22, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        animation: rippleEffect 0.6s ease-out;
        pointer-events: none;
    }

    @keyframes rippleEffect {
        0% {
            width: 0;
            height: 0;
            opacity: 1;
        }
        100% {
            width: 48px;
            height: 48px;
            opacity: 0;
        }
    }

    /* ===== RESPONSIVE PADDING-TOP FOR PRODUCTS SECTION ===== */
    /* Điều chỉnh padding-top cho products-section theo breakpoint */

    /* Từ 769px đến 1023px: padding-top = 0px */
    @media (min-width: 769px) and (max-width: 1023px) {
        .products-section-responsive {
            padding-top: 0px !important;
        }
    }

    /* Từ 1024px trở lên: padding-top = 2.5rem (40px) */
    @media (min-width: 1024px) {
        .products-section-responsive {
            padding-top: 2.5rem !important;
        }
    }
</style>

<!-- Breadcrumb - Thiết kế hiện đại -->
<div class="modern-breadcrumb">
    <div class="breadcrumb-wrapper">
        <div class="breadcrumb-item">
            <a href="<?php echo BASE_URL; ?>" class="breadcrumb-link">
                <span class="breadcrumb-icon">
                    <i class="fas fa-home"></i>
                </span>
                <span>Trang chủ</span>
            </a>
        </div>
        <div class="breadcrumb-divider">
            <i class="fas fa-chevron-right"></i>
        </div>
        <div class="breadcrumb-item active">
            <span class="breadcrumb-link">
                <span class="breadcrumb-icon">
                    <i class="fas fa-th-large"></i>
                </span>
                <span>Tất cả sản phẩm</span>
            </span>
        </div>
    </div>
</div>

<div class="relative overflow-hidden header-section-with-search">
    <!-- Animated Background Pattern -->
    <div class="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-orange-50/30"></div>
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(249,115,22,0.05),transparent_50%)]"></div>

    <!-- Floating Particles Animation -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-orange-300/30 rounded-full animate-bounce"
            style="animation-delay: 0s; animation-duration: 3s;"></div>
        <div class="absolute top-3/4 right-1/3 w-1 h-1 bg-amber-400/40 rounded-full animate-bounce"
            style="animation-delay: 1s; animation-duration: 4s;"></div>
        <div class="absolute top-1/2 right-1/4 w-1.5 h-1.5 bg-orange-400/25 rounded-full animate-bounce"
            style="animation-delay: 2s; animation-duration: 5s;"></div>
    </div>

    <!-- Content Container -->
    <div class="relative z-10 text-center py-10 px-6">
        <!-- Luxury Badge with Animation -->
        <div
            class="inline-flex items-center bg-gradient-to-r from-orange-500/10 via-orange-400/10 to-amber-500/10 backdrop-blur-sm border border-orange-200/50 text-orange-700 text-sm font-semibold px-6 py-2.5 rounded-full mb-5 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-300 group">
            <div
                class="w-2 h-2 bg-gradient-to-r from-orange-500 to-amber-500 rounded-full mr-3 animate-pulse shadow-sm group-hover:animate-spin">
            </div>
            <span class="tracking-wide">LUXURY COLLECTION</span>
            <div class="ml-3 opacity-100 group-hover:animate-bounce transition-all duration-300 flex items-center">
                <i class="fas fa-crown text-amber-500 text-sm"></i>
            </div>
        </div>

        <!-- Main Title with Enhanced Animation -->
        <div class="space-y-3">
            <h1
                class="text-2xl md:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 bg-clip-text text-transparent leading-tight hover:from-orange-600 hover:via-orange-500 hover:to-amber-600 transition-all duration-500">
                Shop Nội Thất Bàng Vũ
            </h1>

            <!-- Enhanced Decorative Divider -->
            <div class="flex items-center justify-center space-x-4">
                <div class="h-px bg-gradient-to-r from-transparent via-orange-300 to-transparent flex-1 max-w-20"></div>
                <div class="flex space-x-1">
                    <div class="w-1.5 h-1.5 bg-orange-400 rounded-full animate-pulse"></div>
                    <div class="w-2 h-2 bg-gradient-to-r from-orange-500 to-amber-500 rounded-full"></div>
                    <div class="w-1.5 h-1.5 bg-orange-400 rounded-full animate-pulse" style="animation-delay: 0.5s;">
                    </div>
                </div>
                <div class="h-px bg-gradient-to-r from-transparent via-orange-300 to-transparent flex-1 max-w-20"></div>
            </div>

            <p class="text-base md:text-lg text-slate-600 font-medium tracking-wide">
                Nội Thất Chất Lượng Cao Cấp
            </p>
        </div>

        <!-- Enhanced Stats Section -->
        <div class="mt-6 flex flex-wrap justify-center gap-6 text-sm">
            <div
                class="flex items-center space-x-2 text-slate-600 hover:text-orange-600 transition-colors duration-300">
                <div
                    class="w-8 h-8 bg-gradient-to-br from-orange-100 to-amber-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-star text-orange-500 text-xs"></i>
                </div>
                <span class="font-medium">Chất lượng chính hãng</span>
            </div>
            <div
                class="flex items-center space-x-2 text-slate-600 hover:text-orange-600 transition-colors duration-300">
                <div
                    class="w-8 h-8 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-shield-alt text-green-500 text-xs"></i>
                </div>
                <span class="font-medium">Bảo hành 10 năm</span>
            </div>
            <div
                class="flex items-center space-x-2 text-slate-600 hover:text-orange-600 transition-colors duration-300">
                <div
                    class="w-8 h-8 bg-gradient-to-br from-blue-100 to-cyan-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-truck text-blue-500 text-xs"></i>
                </div>
                <span class="font-medium">Lắp đặt toàn quốc</span>
            </div>
        </div>


    </div>

    <!-- Enhanced Decorative Elements -->
    <div
        class="absolute top-6 left-6 w-24 h-24 bg-gradient-to-br from-orange-200/20 to-amber-200/20 rounded-full blur-xl animate-pulse">
    </div>
    <div class="absolute bottom-6 right-6 w-20 h-20 bg-gradient-to-br from-slate-200/20 to-orange-200/20 rounded-full blur-xl animate-pulse"
        style="animation-delay: 1s;"></div>
    <div class="absolute top-1/2 left-8 w-16 h-16 bg-gradient-to-br from-amber-200/15 to-orange-200/15 rounded-full blur-2xl animate-pulse"
        style="animation-delay: 2s;"></div>

    <!-- Subtle Border Bottom -->
    <div
        class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-orange-200/50 to-transparent">
    </div>

    <!-- Luxury Search Section - Moved inside overflow-hidden container -->
    <div class="relative" style="z-index: 100;">
        <div class="relative py-8 px-6" style="z-index: 101;">
            <div class="max-w-4xl mx-auto">
                <!-- Enhanced Search Form -->
                <form action="<?php echo BASE_URL; ?>/products.php" method="GET"
                    class="search-container-solution1 relative" style="z-index: 1000;">
                    <!-- Main Search Input -->
                    <div class="relative">
                        <div class="search-input-container">
                            <input type="text" name="keyword" value="<?php echo htmlspecialchars($keyword); ?>"
                                placeholder="Tìm kiếm sản phẩm nội thất..."
                                class="js-search-input-solution1 search-input-enhanced w-full px-4 py-3 pl-12 pr-20 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all"
                                autocomplete="off" id="main-search-input">

                            <!-- Search Icon (Left) -->
                            <div class="search-icon-left">
                                <i class="fas fa-search search-icon"></i>
                                <i class="fas fa-keyboard typing-icon"></i>
                            </div>

                            <!-- Action Buttons (Right) -->
                            <div class="search-actions-right">
                                <!-- Clear Button -->
                                <button type="button" class="search-clear-btn" id="search-clear-btn">
                                    <i class="fas fa-times"></i>
                                </button>

                                <!-- Quick Search Button -->
                                <button type="submit" class="search-submit-btn" id="search-submit-btn">
                                    <div class="btn-content">
                                        <!-- Spinner cho loading state -->
                                        <div class="btn-spinner" style="display: none;"></div>

                                        <!-- Success icon -->
                                        <div class="btn-success" style="display: none;">
                                            <i class="fas fa-check"></i>
                                        </div>

                                        <!-- Search icon -->
                                        <i class="fas fa-search btn-search-icon"></i>

                                        <!-- Text -->
                                        <span class="btn-text">Tìm kiếm</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                        <!-- Search Suggestions Container -->
                        <div id="search-suggestions"
                            class="search-suggestions-solution1 search-suggestions-container hidden absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 z-50">
                            <!-- Suggestions will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Hidden inputs để giữ nguyên các filter hiện tại khi search -->
                    <!-- Sử dụng category[] để hỗ trợ multiple categories (additive filtering) -->
                    <?php if (!empty($category_ids)): ?>
                        <?php foreach ($category_ids as $cat_id): ?>
                            <input type="hidden" name="category[]" value="<?php echo $cat_id; ?>">
                        <?php endforeach; ?>
                    <?php endif; ?>
                    <?php if ($price_min): ?>
                        <input type="hidden" name="price_min" value="<?php echo $price_min; ?>">
                    <?php endif; ?>
                    <?php if ($price_max): ?>
                        <input type="hidden" name="price_max" value="<?php echo $price_max; ?>">
                    <?php endif; ?>
                    <?php if ($sort && $sort !== 'newest'): ?>
                        <input type="hidden" name="sort" value="<?php echo htmlspecialchars($sort); ?>">
                    <?php endif; ?>
                    <!-- Sử dụng promotion[] để hỗ trợ multiple promotion filters -->
                    <?php if (!empty($promotion_filters)): ?>
                        <?php foreach ($promotion_filters as $promo_filter): ?>
                            <input type="hidden" name="promotion[]" value="<?php echo htmlspecialchars($promo_filter); ?>">
                        <?php endforeach; ?>
                    <?php endif; ?>

                    <!-- Quick Search Tags -->
                    <div class="mt-6 text-center">
                        <div class="flex flex-wrap justify-center items-center gap-3">
                            <span class="text-sm text-slate-500 font-medium">Tìm kiếm phổ biến:</span>
                            <?php
                            // Lấy danh mục được thiết lập hiển thị trong tìm kiếm phổ biến
                            try {
                                $stmt = $conn->prepare("
                                        SELECT c.id, c.name, c.slug, c.popular_search_order, COUNT(p.id) as product_count
                                        FROM categories c
                                        LEFT JOIN products p ON c.id = p.category_id AND p.status = 1
                                        WHERE c.status = 1 AND c.show_in_popular_search = 1
                                        GROUP BY c.id, c.name, c.slug, c.popular_search_order
                                        ORDER BY c.popular_search_order ASC, c.name ASC
                                        LIMIT 5
                                    ");
                                $stmt->execute();
                                $popular_categories = $stmt->fetchAll();
                            } catch (PDOException $e) {
                                $popular_categories = [];
                            }

                            // Nếu không có danh mục nào được thiết lập, sử dụng dữ liệu mặc định
                            if (empty($popular_categories)) {
                                $popular_categories = [
                                    ['name' => 'Sofa', 'slug' => 'sofa', 'product_count' => 0, 'popular_search_order' => 1],
                                    ['name' => 'Bàn ăn', 'slug' => 'ban-an', 'product_count' => 0, 'popular_search_order' => 2],
                                    ['name' => 'Giường ngủ', 'slug' => 'giuong-ngu', 'product_count' => 0, 'popular_search_order' => 3],
                                    ['name' => 'Tủ quần áo', 'slug' => 'tu-quan-ao', 'product_count' => 0, 'popular_search_order' => 4],
                                    ['name' => 'Bàn làm việc', 'slug' => 'ban-lam-viec', 'product_count' => 0, 'popular_search_order' => 5]
                                ];
                            }

                            foreach ($popular_categories as $category): ?>
                                <button type="button"
                                    class="quick-search-tag px-4 py-2 bg-white/80 hover:bg-orange-50 border border-slate-200/80 hover:border-orange-200 text-slate-600 hover:text-orange-600 text-sm font-medium rounded-full transition-all duration-200 hover:shadow-md transform hover:-translate-y-0.5 group"
                                    data-keyword="<?php echo htmlspecialchars($category['name']); ?>">
                                    <i
                                        class="fas fa-tag text-xs mr-1.5 opacity-60 group-hover:opacity-100 transition-opacity duration-200"></i>
                                    <span><?php echo htmlspecialchars($category['name']); ?></span>
                                </button>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div
            class="absolute top-4 right-8 w-16 h-16 bg-gradient-to-br from-blue-200/20 to-indigo-200/20 rounded-full blur-xl animate-pulse">
        </div>
        <div class="absolute bottom-4 left-8 w-12 h-12 bg-gradient-to-br from-orange-200/20 to-amber-200/20 rounded-full blur-xl animate-pulse"
            style="animation-delay: 1s;"></div>
    </div>
</div>



<!-- Products -->
<div id="products-section" class="py-10 bg-gradient-to-b from-white to-gray-50 relative products-section-responsive" style="z-index: 1;">
    <div class="container mx-auto px-4">

        <div class="flex flex-col lg:flex-row lg:space-x-6">
            <!-- Sidebar Filters (25%) -->
            <aside class="lg:w-1/4 mb-6 lg:mb-0">
                <div class="sidebar-filters bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <!-- Professional Elegant Header -->
                    <div class="px-8 py-6 bg-white border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-1 h-8 bg-slate-800 rounded-full"></div>
                                <div>
                                    <h2 class="text-xl font-semibold text-slate-800 tracking-tight">
                                        <span class="sidebar-title-full">Bộ lọc sản phẩm</span>
                                        <span class="sidebar-title-short">Bộ lọc</span>
                                    </h2>
                                    <p class="text-sm text-slate-500 mt-0.5 filter-header-desc">
                                        <span class="sidebar-title-full">Tùy chỉnh tìm kiếm của bạn</span>
                                        <span class="sidebar-title-short">Tùy chỉnh tìm kiếm</span>
                                        <span class="sidebar-title-extra-short">Tùy chỉnh</span>
                                    </p>
                                </div>
                            </div>
                            <?php
                            // Đếm số lượng filters active
                            $active_filters_count = 0;
                            $filter_details = [];

                            if (!empty($keyword)) {
                                $active_filters_count++;
                                $filter_details[] = 'từ khóa';
                            }
                            if (!empty($category_ids)) {
                                $active_filters_count++;
                                $filter_details[] = 'danh mục';
                            }
                            if ($price_min || $price_max) {
                                $active_filters_count++;
                                $filter_details[] = 'khoảng giá';
                            }
                            if (!empty($promotion_filters)) {
                                $active_filters_count++;
                                $filter_details[] = 'khuyến mãi';
                            }

                            $has_filters = $active_filters_count > 0;
                            $tooltip_text = $has_filters ?
                                "Xóa tất cả bộ lọc ({$active_filters_count} bộ lọc: " . implode(', ', $filter_details) . ")" :
                                "Đặt lại bộ lọc";

                            $button_classes = $has_filters ?
                                "group relative text-orange-500 hover:text-orange-600 bg-orange-50 hover:bg-orange-100 border-orange-200 hover:border-orange-300" :
                                "group relative text-slate-400 hover:text-orange-500 hover:bg-orange-50 border-transparent hover:border-orange-200";
                            ?>
                            <button
                                class="reset-filters-btn <?php echo $button_classes; ?> transition-all duration-300 w-12 h-12 rounded-lg border transform hover:scale-105 flex items-center justify-center flex-shrink-0 relative"
                                id="resetFilters" title="<?php echo htmlspecialchars($tooltip_text); ?>">

                                <!-- Spinner cho loading state (ẩn mặc định) -->
                                <span class="btn-spinner" style="display: none;"></span>

                                <!-- Icon chính -->
                                <i class="btn-reset-icon fas fa-sync-alt text-base group-hover:rotate-180 transition-transform duration-500"></i>

                                <?php if ($has_filters): ?>
                                    <div
                                        class="absolute -top-1 -right-1 min-w-[18px] h-[18px] bg-orange-500 text-white text-xs font-bold rounded-full flex items-center justify-center animate-pulse">
                                        <?php echo $active_filters_count; ?>
                                    </div>
                                <?php endif; ?>
                            </button>
                        </div>
                    </div>

                    <!-- Filter content -->
                    <div class="p-6 space-y-6">
                        <!-- Category Filter -->
                        <div class="border-b border-gray-100 pb-6">
                            <div class="flex items-center justify-between mb-4 cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2 transition-colors duration-200"
                                onclick="toggleFilterSection('category')">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-th-large text-orange-500 text-sm"></i>
                                    </div>
                                    Danh mục
                                </h3>
                                <div class="text-gray-400 hover:text-orange-500 transition-colors duration-200">
                                    <i class="fas fa-chevron-right transform transition-transform duration-200 rotate-90"
                                        id="category-chevron"></i>
                                </div>
                            </div>
                            <div class="space-y-3 filter-section-visible" id="category-content">
                                <?php
                                $parent_categories = get_categories(null, 1);
                                foreach ($parent_categories as $parent_cat):
                                    if ($parent_cat['parent_id'] == null):
                                        $subcategories = get_categories($parent_cat['id'], 1);
                                        ?>
                                        <div class="group">
                                            <!-- Parent Category -->
                                            <div class="flex items-center justify-between p-3 bg-gray-50 hover:bg-orange-50 rounded-lg transition-colors duration-200 cursor-pointer"
                                                <?php if (!empty($subcategories)): ?>onclick="toggleSubcategories(<?php echo $parent_cat['id']; ?>)" <?php endif; ?>>
                                                <div class="flex items-center flex-1">
                                                    <input type="checkbox" id="cat_<?php echo $parent_cat['id']; ?>"
                                                        name="category[]" value="<?php echo $parent_cat['id']; ?>"
                                                        class="custom-checkbox w-4 h-4 text-orange-500 bg-white border-2 border-gray-300 rounded-md focus:ring-orange-500 focus:ring-2 focus:ring-offset-0"
                                                        <?php echo (in_array($parent_cat['id'], $category_ids)) ? 'checked' : ''; ?> onclick="event.stopPropagation()">
                                                    <span
                                                        class="ml-3 text-sm font-medium text-gray-700 group-hover:text-orange-600 cursor-pointer hover:text-orange-600"
                                                        onclick="event.stopPropagation(); toggleCategoryCheckbox('cat_<?php echo $parent_cat['id']; ?>')">
                                                        <?php echo htmlspecialchars($parent_cat['name']); ?>
                                                    </span>
                                                </div>
                                                <?php if (!empty($subcategories)): ?>
                                                    <div
                                                        class="text-gray-400 hover:text-orange-500 transition-colors duration-200 ml-2">
                                                        <i class="fas fa-chevron-right text-xs transform transition-transform duration-200"
                                                            id="sub-chevron-<?php echo $parent_cat['id']; ?>"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Subcategories -->
                                            <?php if (!empty($subcategories)): ?>
                                                <div class="ml-6 mt-2 space-y-2 subcategory-hidden"
                                                    id="subcategories-<?php echo $parent_cat['id']; ?>">
                                                    <?php foreach ($subcategories as $subcat): ?>
                                                        <label
                                                            class="flex items-center p-2 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors duration-200">
                                                            <input type="checkbox" id="subcat_<?php echo $subcat['id']; ?>"
                                                                name="category[]" value="<?php echo $subcat['id']; ?>"
                                                                class="custom-checkbox w-4 h-4 text-orange-500 bg-white border-2 border-gray-300 rounded-md focus:ring-orange-500 focus:ring-2 focus:ring-offset-0"
                                                                <?php echo (in_array($subcat['id'], $category_ids)) ? 'checked' : ''; ?>>
                                                            <span class="ml-3 text-sm text-gray-600 hover:text-orange-600">
                                                                <?php echo htmlspecialchars($subcat['name']); ?>
                                                            </span>
                                                        </label>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <?php
                                    endif;
                                endforeach;
                                ?>
                            </div>
                        </div>

                        <!-- Price Range Filter -->
                        <div class="border-b border-gray-100 pb-6">
                            <div class="flex items-center justify-between mb-4 cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2 transition-colors duration-200"
                                onclick="toggleFilterSection('price')">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-dollar-sign text-green-500 text-sm"></i>
                                    </div>
                                    Khoảng giá
                                </h3>
                                <div class="text-gray-400 hover:text-orange-500 transition-colors duration-200">
                                    <i class="fas fa-chevron-right transform transition-transform duration-200 rotate-90"
                                        id="price-chevron"></i>
                                </div>
                            </div>
                            <div class="space-y-4 filter-section-visible" id="price-content">
                                <!-- Price Input Range -->
                                <div class="flex gap-2 price-input-range">
                                    <div class="flex-1 min-w-0">
                                        <label class="block text-xs font-medium text-gray-500 mb-1.5">Từ (VNĐ)</label>
                                        <div class="relative">
                                            <input type="text" id="price-min" name="price_min" placeholder="0"
                                                class="price-input w-full border border-gray-300 rounded-md focus:border-orange-500 focus:ring-1 focus:ring-orange-500/20 transition-all duration-200"
                                                data-price-field="min" style="font-size: 14px;">
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <label class="block text-xs font-medium text-gray-500 mb-1.5">Đến (VNĐ)</label>
                                        <div class="relative">
                                            <input type="text" id="price-max" name="price_max" placeholder="10.000.000"
                                                class="price-input w-full border border-gray-300 rounded-md focus:border-orange-500 focus:ring-1 focus:ring-orange-500/20 transition-all duration-200"
                                                data-price-field="max" style="font-size: 14px;">
                                        </div>
                                    </div>
                                </div>

                                <!-- Price Presets -->
                                <div class="space-y-2">
                                    <label class="block text-xs font-medium text-gray-500 mb-2">Khoảng giá phổ
                                        biến</label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <?php
                                        $price_presets = [
                                            ['min' => 0, 'max' => 5000000, 'label' => 'Dưới 5 triệu'],
                                            ['min' => 5000000, 'max' => 10000000, 'label' => '5 - 10 triệu'],
                                            ['min' => 10000000, 'max' => 20000000, 'label' => '10 - 20 triệu'],
                                            ['min' => 20000000, 'max' => 30000000, 'label' => '20 - 30 triệu'],
                                            ['min' => 30000000, 'max' => 50000000, 'label' => '30 - 50 triệu'],
                                            ['min' => 50000000, 'max' => null, 'label' => 'Trên 50 triệu']
                                        ];

                                        foreach ($price_presets as $preset):
                                            // Check if this preset matches current price filter
                                            $is_active = false;
                                            if ($preset['max'] === null) {
                                                // "Trên X triệu" case
                                                $is_active = ($price_min == $preset['min'] && !$price_max);
                                            } else {
                                                // Range case
                                                $is_active = ($price_min == $preset['min'] && $price_max == $preset['max']);
                                            }

                                            $active_classes = $is_active ? 'from-orange-500 bg-white text-gray-700 border-gray-200' : 'bg-white text-gray-700 border-gray-200';
                                            ?>
                                            <button type="button"
                                                class="price-preset px-3 py-2 text-xs font-medium rounded-lg border transform hover:scale-105 hover:-translate-y-0.5 <?php echo $active_classes; ?>"
                                                data-min="<?php echo $preset['min']; ?>"
                                                data-max="<?php echo $preset['max'] ?? ''; ?>"
                                                style="transition: transform 0.4s ease-in-out;">
                                                <?php echo $preset['label']; ?>
                                            </button>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Promotion Filter -->
                        <div class="border-b border-gray-100 pb-6">
                            <div class="flex items-center justify-between mb-4 cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2 transition-colors duration-200"
                                onclick="toggleFilterSection('promotion')">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-tags text-red-500 text-sm"></i>
                                    </div>
                                    Khuyến mãi
                                </h3>
                                <div class="text-gray-400 hover:text-orange-500 transition-colors duration-200">
                                    <i class="fas fa-chevron-right transform transition-transform duration-200 rotate-90"
                                        id="promotion-chevron"></i>
                                </div>
                            </div>
                            <div class="space-y-3 filter-section-visible" id="promotion-content">
                                <label
                                    class="flex items-center p-3 bg-gray-50 hover:bg-orange-50 rounded-lg cursor-pointer transition-colors duration-200 group">
                                    <input type="checkbox" name="promotion[]" value="sale"
                                        class="custom-checkbox w-4 h-4 text-orange-500 bg-white border-2 border-gray-300 rounded-md focus:ring-orange-500 focus:ring-2 focus:ring-offset-0"
                                        <?php echo (in_array('sale', $promotion_filters)) ? 'checked' : ''; ?>>
                                    <div class="ml-3 flex items-center">
                                        <div
                                            class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-2 group-hover:bg-red-200">
                                            <i class="fas fa-percentage text-red-500 text-xs"></i>
                                        </div>
                                        <span class="text-sm font-medium text-gray-700 group-hover:text-orange-600">Đang
                                            giảm giá</span>
                                    </div>
                                </label>
                                <label
                                    class="flex items-center p-3 bg-gray-50 hover:bg-orange-50 rounded-lg cursor-pointer transition-colors duration-200 group">
                                    <input type="checkbox" name="promotion[]" value="flash_sale"
                                        class="custom-checkbox w-4 h-4 text-orange-500 bg-white border-2 border-gray-300 rounded-md focus:ring-orange-500 focus:ring-2 focus:ring-offset-0"
                                        <?php echo (in_array('flash_sale', $promotion_filters)) ? 'checked' : ''; ?>>
                                    <div class="ml-3 flex items-center">
                                        <div
                                            class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-2 group-hover:bg-purple-200">
                                            <i class="fas fa-bolt text-purple-500 text-xs"></i>
                                        </div>
                                        <span
                                            class="text-sm font-medium text-gray-700 group-hover:text-orange-600">Flash
                                            Sale</span>
                                    </div>
                                </label>
                                <label
                                    class="flex items-center p-3 bg-gray-50 hover:bg-orange-50 rounded-lg cursor-pointer transition-colors duration-200 group">
                                    <input type="checkbox" name="promotion[]" value="featured"
                                        class="custom-checkbox w-4 h-4 text-orange-500 bg-white border-2 border-gray-300 rounded-md focus:ring-orange-500 focus:ring-2 focus:ring-offset-0"
                                        <?php echo (in_array('featured', $promotion_filters)) ? 'checked' : ''; ?>>
                                    <div class="ml-3 flex items-center">
                                        <div
                                            class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mr-2 group-hover:bg-yellow-200">
                                            <i class="fas fa-star text-yellow-500 text-xs"></i>
                                        </div>
                                        <span class="text-sm font-medium text-gray-700 group-hover:text-orange-600">Sản
                                            phẩm nổi bật</span>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Apply Filters Button -->
                        <div class="pt-3 space-y-2.5">
                            <button type="button" id="applyFilters"
                                class="apply-filters-btn group w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 flex items-center justify-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5 hover:scale-[1.02] relative overflow-hidden">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700">
                                </div>
                                <div class="btn-content relative z-10">
                                    <div class="btn-spinner"></div>
                                    <i class="btn-success fas fa-check"></i>
                                    <i class="btn-filter-icon fas fa-filter mr-2 text-xs group-hover:scale-110 transition-transform duration-300"></i>
                                    <span class="btn-text text-sm">Áp dụng bộ lọc</span>
                                    <div class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                        <i class="fas fa-arrow-right text-xs"></i>
                                    </div>
                                </div>
                            </button>
                            <button type="button" id="resetFiltersBtn"
                                class="reset-filters-btn group w-full bg-white border border-gray-200 hover:border-orange-300 text-gray-600 hover:text-orange-600 font-medium py-2.5 px-4 rounded-lg transition-all duration-300 flex items-center justify-center hover:bg-gradient-to-r hover:from-orange-50 hover:to-orange-100 transform hover:scale-[1.02] relative overflow-hidden">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-orange-100/0 via-orange-100/50 to-orange-100/0 -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700">
                                </div>
                                <i
                                    class="broom-icon fas fa-broom mr-2 text-xs group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"></i>
                                <span class="text-sm relative z-10">Đặt lại bộ lọc</span>
                                <div class="reset-sync-icon-container ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <i class="reset-sync-icon fas fa-sync-alt"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>

                <?php
                // Hàm chuyển đổi thời gian thành dạng thân thiện
                function time_ago($datetime) {
                    $time = time() - strtotime($datetime);

                    if ($time < 60) {
                        return 'Vừa xem';
                    } elseif ($time < 3600) {
                        $minutes = floor($time / 60);
                        return $minutes . ' phút trước';
                    } elseif ($time < 86400) {
                        $hours = floor($time / 3600);
                        return $hours . ' giờ trước';
                    } elseif ($time < 172800) { // 2 days
                        return 'Hôm qua lúc ' . date('H:i', strtotime($datetime));
                    } elseif ($time < 604800) { // 7 days
                        $days = floor($time / 86400);
                        return $days . ' ngày trước';
                    } else {
                        return date('d/m/Y', strtotime($datetime));
                    }
                }

                // Lấy sản phẩm xem gần đây (CHỈ từ dữ liệu thật)
                $recently_viewed = get_recently_viewed_products(4);

                // Lấy sản phẩm trending
                $trending_products = get_trending_products(4);

                // Nếu không có trending products thật, tạo dữ liệu test cho trending
                if (empty($trending_products)) {
                    $trending_products = get_products(4, 0, null, 1, null, 1); // Lấy 4 sản phẩm featured
                    // Thêm recent_views và total_views giả
                    foreach ($trending_products as &$product) {
                        $product['recent_views'] = rand(10, 50);
                        $product['total_views'] = $product['views'] ?? rand(100, 500);
                    }
                }
                ?>

                <!-- Recently Viewed Products Section (chỉ hiển thị trên desktop) -->
                <?php if (!empty($recently_viewed)): ?>
                <div class="recently-viewed-section desktop-only bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mt-6">
                    <div class="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-history text-blue-500 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800">
                                    <span class="sidebar-title-full">Sản phẩm xem gần đây</span>
                                    <span class="sidebar-title-short">Xem gần đây</span>
                                </h3>
                                <p class="text-sm text-gray-500">
                                    <span class="sidebar-title-full">Những sản phẩm bạn đã xem</span>
                                    <span class="sidebar-title-short">Đã xem</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 space-y-3">
                        <?php foreach ($recently_viewed as $product): ?>
                        <div class="group flex items-center space-x-3 p-3 bg-gray-50 hover:bg-blue-50 rounded-lg transition-all duration-200 cursor-pointer">
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
                                <?php if (!empty($product['image'])): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo htmlspecialchars($product['image']); ?>"
                                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-br from-gray-300 to-gray-400 flex items-center justify-center">
                                        <i class="fas fa-image text-gray-500 text-xs"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1 min-w-0">
                                <a href="<?php echo BASE_URL; ?>/san-pham/<?php echo htmlspecialchars($product['slug']); ?>"
                                   class="block">
                                    <h4 class="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors duration-200 truncate">
                                        <?php echo htmlspecialchars($product['name']); ?>
                                    </h4>
                                    <div class="mt-1">
                                        <?php if ($product['price_type'] == 'contact'): ?>
                                            <span class="text-sm font-semibold text-blue-600">Liên hệ báo giá</span>
                                        <?php else: ?>
                                            <span class="text-sm font-semibold text-orange-600">
                                                <?php
                                                if (!empty($product['sale_price']) && $product['sale_price'] > 0 && $product['sale_price'] < $product['price']) {
                                                    echo number_format($product['sale_price']) . 'đ';
                                                } else {
                                                    echo number_format($product['price']) . 'đ';
                                                }
                                                ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex items-center text-xs text-gray-400 mt-1">
                                        <i class="fas fa-clock mr-1"></i>
                                        <span><?php echo time_ago($product['view_date']); ?></span>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Trending Products Section (chỉ hiển thị trên desktop) -->
                <?php if (!empty($trending_products)): ?>
                <div class="trending-section desktop-only bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mt-6">
                    <div class="px-6 py-4 bg-gradient-to-r from-orange-50 to-red-50 border-b border-gray-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-fire text-orange-500 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800">
                                    <span class="sidebar-title-full">Sản phẩm được quan tâm</span>
                                    <span class="sidebar-title-short">Được quan tâm</span>
                                </h3>
                                <p class="text-sm text-gray-500">
                                    <span class="sidebar-title-full">Trending trong tuần</span>
                                    <span class="sidebar-title-short">Trending</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 space-y-3">
                        <?php foreach ($trending_products as $index => $product): ?>
                        <div class="group flex items-center space-x-3 p-3 bg-gray-50 hover:bg-orange-50 rounded-lg transition-all duration-200 cursor-pointer">
                            <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs font-bold"><?php echo $index + 1; ?></span>
                            </div>
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
                                <?php if (!empty($product['image'])): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo htmlspecialchars($product['image']); ?>"
                                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-br from-gray-300 to-gray-400 flex items-center justify-center">
                                        <i class="fas fa-image text-gray-500 text-xs"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1 min-w-0">
                                <a href="<?php echo BASE_URL; ?>/san-pham/<?php echo htmlspecialchars($product['slug']); ?>"
                                   class="block">
                                    <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-600 transition-colors duration-200 truncate">
                                        <?php echo htmlspecialchars($product['name']); ?>
                                    </h4>
                                    <div class="mt-1">
                                        <?php if ($product['price_type'] == 'contact'): ?>
                                            <span class="text-sm font-semibold text-blue-600">Liên hệ báo giá</span>
                                        <?php else: ?>
                                            <span class="text-sm font-semibold text-orange-600">
                                                <?php
                                                if (!empty($product['sale_price']) && $product['sale_price'] > 0 && $product['sale_price'] < $product['price']) {
                                                    echo number_format($product['sale_price']) . 'đ';
                                                } else {
                                                    echo number_format($product['price']) . 'đ';
                                                }
                                                ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex items-center text-xs text-gray-400 mt-1">
                                        <i class="fas fa-eye mr-1"></i>
                                        <span><?php echo number_format($product['total_views']); ?> lượt xem</span>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </aside>

            <!-- Main Products Area (75%) -->
            <main class="lg:w-3/4 products-content">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <!-- Professional Elegant Header -->
                    <div class="px-8 py-6 bg-white border-b border-gray-200">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-1 h-8 bg-slate-800 rounded-full"></div>
                                <div>
                                    <h2 class="text-xl font-semibold text-slate-800 tracking-tight">Danh sách sản phẩm
                                    </h2>
                                    <div id="products-stats" class="flex items-center space-x-1 text-sm text-slate-500 mt-0.5">
                                        <span id="products-showing"><?php echo number_format(min($limit, $total_products)); ?></span>
                                        <span class="text-slate-400">/</span>
                                        <span id="products-total"><?php echo number_format($total_products); ?></span>
                                        <span class="text-slate-400 ml-1">sản phẩm</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Controls -->
                            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                                <!-- Sort -->
                                <div class="flex items-center gap-2">
                                    <label for="sort-select"
                                        class="text-sm text-gray-600 whitespace-nowrap font-medium">Sắp xếp:</label>
                                    <select id="sort-select" name="sort"
                                        class="px-3 py-2 border border-gray-300 rounded-lg focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20 transition-all duration-200 text-sm bg-white">
                                        <option value="newest" <?php echo ($sort == 'newest') ? 'selected' : ''; ?>>Mới
                                            nhất</option>
                                        <option value="price_asc" <?php echo ($sort == 'price_asc') ? 'selected' : ''; ?>>
                                            Giá thấp đến cao</option>
                                        <option value="price_desc" <?php echo ($sort == 'price_desc') ? 'selected' : ''; ?>>Giá cao đến thấp</option>
                                        <option value="name_asc" <?php echo ($sort == 'name_asc') ? 'selected' : ''; ?>>
                                            Tên A-Z</option>
                                        <option value="popular" <?php echo ($sort == 'popular') ? 'selected' : ''; ?>>Phổ
                                            biến nhất</option>
                                    </select>
                                </div>

                                <!-- Items per page -->
                                <div class="flex items-center gap-2">
                                    <label for="items-per-page"
                                        class="text-sm text-gray-600 whitespace-nowrap font-medium">Hiển thị:</label>
                                    <select id="items-per-page" name="items_per_page"
                                        class="px-3 py-2 border border-gray-300 rounded-lg focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20 transition-all duration-200 text-sm bg-white">
                                        <option value="12" <?php echo ($limit == 12) ? 'selected' : ''; ?>>12</option>
                                        <option value="24" <?php echo ($limit == 24) ? 'selected' : ''; ?>>24</option>
                                        <option value="36" <?php echo ($limit == 36) ? 'selected' : ''; ?>>36</option>
                                        <option value="48" <?php echo ($limit == 48) ? 'selected' : ''; ?>>48</option>
                                    </select>
                                </div>


                            </div>
                        </div>
                    </div>

                    <!-- Products Content -->
                    <div class="p-6 products-content">
                        <!-- Filter Results Header (hiển thị khi có tìm kiếm hoặc filter) -->
                        <?php
                        // Sử dụng function helper để generate Filter Results Header
                        $filter_data = [
                            'keyword' => $keyword,
                            'category_ids' => $category_ids,
                            'price_min' => $price_min,
                            'price_max' => $price_max,
                            'promotion_filters' => $promotion_filters
                        ];

                        echo generate_filter_results_header($filter_data, $products, $total_products, $sort_label);
                        ?>


                        <!-- Products Grid -->
                        <div class="products-grid grid grid-cols-1 gap-6"
                            id="productsGrid">
                            <?php if (!empty($products)): ?>
                                <?php foreach ($products as $product): ?>
                                    <div
                                        class="group h-full flex flex-col bg-white rounded-2xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-blue-200 hover:-translate-y-2">
                                        <div class="product-image-wrapper relative">
                                            <a href="<?php echo get_product_url($product['slug']); ?>"
                                                class="block product-image">
                                                <?php if ($product['image']): ?>
                                                    <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $product['image']; ?>"
                                                        alt="<?php echo $product['name']; ?>"
                                                        class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                                                <?php else: ?>
                                                    <div
                                                        class="w-full h-full bg-gray-300 flex items-center justify-center absolute top-0 left-0">
                                                        <i class="fas fa-image text-gray-500 text-4xl"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </a>

                                            <!-- Premium Sale Badge -->
                                            <?php if (isset($product['sale_price']) && $product['sale_price'] > 0 && $product['price'] > $product['sale_price']): ?>
                                                <?php $discount_percent = round(($product['price'] - $product['sale_price']) / $product['price'] * 100); ?>
                                                <div class="premium-sale-badge">
                                                    <div class="badge-content">
                                                        <span class="discount-percent">-<?php echo $discount_percent; ?>%</span>
                                                        <span class="sale-text">SALE</span>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="product-info-wrapper flex flex-col flex-grow">
                                            <!-- Product Title - Max 2 lines -->
                                            <div class="product-title mb-3">
                                                <a href="<?php echo get_product_url($product['slug']); ?>" class="block">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-800 hover:text-blue-500 transition duration-200 line-clamp-2 leading-tight">
                                                        <?php echo $product['name']; ?>
                                                    </h3>
                                                </a>
                                            </div>

                                            <!-- Premium Price Section -->
                                            <div class="premium-price-section">
                                                <?php if (isset($product['price_type']) && $product['price_type'] === 'contact'): ?>
                                                    <!-- Liên hệ báo giá - Giống regular price container -->
                                                    <div class="contact-price-container">
                                                        <div class="contact-price-main">
                                                            GỌI NGAY
                                                        </div>
                                                        <div class="contact-price-subtitle">
                                                            Liên hệ báo giá
                                                        </div>
                                                    </div>
                                                <?php elseif ($product['sale_price'] > 0): ?>
                                                    <!-- Sản phẩm có giá sale -->
                                                    <div class="price-container">
                                                        <div class="original-price">
                                                            <?php echo format_currency($product['price']); ?></div>
                                                        <div class="sale-price">
                                                            <?php echo format_currency($product['sale_price']); ?></div>
                                                    </div>
                                                <?php else: ?>
                                                    <!-- Sản phẩm giá thường -->
                                                    <div class="regular-price-container">
                                                        <div class="price-label">Giá bán</div>
                                                        <div class="main-price"><?php echo format_currency($product['price']); ?>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Rating and Sales Section -->
                                            <div class="product-rating-sales">
                                                <div class="rating-section">
                                                    <div class="stars">
                                                        <?php
                                                        // Hiển thị rating từ database
                                                        $rating = isset($product['rating']) ? (int)$product['rating'] : 5;
                                                        for ($i = 1; $i <= 5; $i++):
                                                        ?>
                                                            <i class="fas fa-star<?php echo $i <= $rating ? '' : '-o'; ?>"></i>
                                                        <?php endfor; ?>
                                                    </div>
                                                    <span class="rating-text"><?php echo number_format($rating, 1); ?></span>
                                                </div>
                                                <div class="sales-section">
                                                    <i class="fas fa-shopping-cart"></i>
                                                    <span><?php echo number_format(isset($product['sold']) ? (int)$product['sold'] : 0); ?> đã bán</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="col-span-full py-16 text-center">
                                    <div class="max-w-2xl mx-auto">
                                        <!-- Enhanced Empty State -->
                                        <div
                                            class="bg-gradient-to-br from-orange-50/50 to-amber-50/50 rounded-2xl p-8 border border-orange-100/50 shadow-sm">
                                            <!-- Animated Icon -->
                                            <div class="relative mb-6">
                                                <div
                                                    class="w-20 h-20 mx-auto bg-gradient-to-br from-orange-100 to-amber-100 rounded-full flex items-center justify-center shadow-lg">
                                                    <?php if (!empty($keyword)): ?>
                                                        <i class="fas fa-search-minus text-orange-400 text-2xl"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-box-open text-orange-400 text-2xl"></i>
                                                    <?php endif; ?>
                                                </div>
                                                <!-- Pulse animation -->
                                                <div
                                                    class="absolute inset-0 w-20 h-20 mx-auto bg-orange-200/30 rounded-full animate-ping">
                                                </div>
                                            </div>

                                            <!-- Main Message -->
                                            <h3 class="text-xl font-bold text-gray-800 mb-3">
                                                <?php if (!empty($keyword)): ?>
                                                    Không tìm thấy sản phẩm phù hợp
                                                <?php else: ?>
                                                    Không có sản phẩm trong bộ lọc này
                                                <?php endif; ?>
                                            </h3>

                                            <!-- Detailed Description -->
                                            <p class="text-gray-600 mb-6 leading-relaxed">
                                                <?php if (!empty($keyword)): ?>
                                                    Không có sản phẩm nào khớp với từ khóa <span
                                                        class="inline-flex items-center px-2 py-1 bg-orange-100 text-orange-700 rounded font-medium">"<?php echo htmlspecialchars($keyword); ?>"</span>
                                                <?php else: ?>
                                                    Không có sản phẩm nào phù hợp với các tiêu chí lọc hiện tại
                                                <?php endif; ?>
                                            </p>

                                            <!-- Helpful Suggestions -->
                                            <div class="bg-white/60 rounded-xl p-6 mb-6 border border-orange-100/50">
                                                <h4 class="text-sm font-semibold text-gray-700 mb-4 flex items-center">
                                                    <i class="fas fa-lightbulb text-amber-500 mr-2"></i>
                                                    Gợi ý để tìm thấy sản phẩm:
                                                </h4>
                                                <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-3 text-sm text-gray-600">
                                                    <?php if (!empty($keyword)): ?>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Thử từ khóa ngắn gọn hơn</span>
                                                        </div>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Kiểm tra chính tả từ khóa</span>
                                                        </div>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Sử dụng từ đồng nghĩa</span>
                                                        </div>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Thử bỏ bớt bộ lọc</span>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Mở rộng khoảng giá</span>
                                                        </div>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Thử danh mục khác</span>
                                                        </div>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Bỏ bớt tiêu chí lọc</span>
                                                        </div>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Xem tất cả sản phẩm</span>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <!-- Popular Categories -->
                                            <div class="mb-6">
                                                <h4 class="text-sm font-semibold text-gray-700 mb-3">Danh mục phổ biến:</h4>
                                                <div class="flex flex-wrap justify-center gap-2">
                                                    <?php
                                                    // Lấy 6 danh mục phổ biến
                                                    try {
                                                        $stmt = $conn->prepare("
                                                            SELECT c.id, c.name, c.slug, COUNT(p.id) as product_count
                                                            FROM categories c
                                                            LEFT JOIN products p ON c.id = p.category_id AND p.status = 1
                                                            WHERE c.status = 1
                                                            GROUP BY c.id, c.name, c.slug
                                                            HAVING product_count > 0
                                                            ORDER BY product_count DESC, c.name ASC
                                                            LIMIT 6
                                                        ");
                                                        $stmt->execute();
                                                        $popular_cats = $stmt->fetchAll();

                                                        foreach ($popular_cats as $cat):
                                                            ?>
                                                            <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo $cat['id']; ?>"
                                                                class="inline-flex items-center px-3 py-2 bg-white border border-orange-200 hover:border-orange-300 text-orange-600 hover:text-orange-700 rounded-lg text-xs font-medium transition-all duration-200 hover:bg-orange-50 hover:shadow-sm">
                                                                <i class="fas fa-tag mr-1.5 text-xs"></i>
                                                                <?php echo htmlspecialchars($cat['name']); ?>
                                                                <span
                                                                    class="ml-1 text-gray-400">(<?php echo $cat['product_count']; ?>)</span>
                                                            </a>
                                                            <?php
                                                        endforeach;
                                                    } catch (Exception $e) {
                                                        // Fallback nếu có lỗi
                                                    }
                                                    ?>
                                                </div>
                                            </div>

                                            <!-- Contact Support -->
                                            <div class="text-center">
                                                <p class="text-sm text-gray-500 mb-3">Không tìm thấy sản phẩm bạn cần?</p>
                                                <div class="flex flex-col sm:flex-row gap-2 justify-center">
                                                    <a href="<?php echo BASE_URL; ?>/contact.php"
                                                        class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg text-sm">
                                                        <i class="fas fa-headset mr-2"></i>
                                                        Liên hệ tư vấn
                                                    </a>
                                                    <a href="<?php echo BASE_URL; ?>/products.php"
                                                        class="inline-flex items-center px-4 py-2 bg-white border-2 border-orange-200 hover:border-orange-300 text-orange-600 hover:text-orange-700 font-medium rounded-lg transition-all duration-200 hover:bg-orange-50 text-sm">
                                                        <i class="fas fa-th-large mr-2"></i>
                                                        Xem tất cả sản phẩm
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Professional Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <div class="pagination-section mt-12">
                            <!-- Results Summary -->
                            <div class="pagination-summary">
                                <div class="results-info">
                                    <span class="results-text">
                                        Hiển thị <strong><?php echo (($page - 1) * $limit) + 1; ?></strong> -
                                        <strong><?php echo min($page * $limit, $total_products); ?></strong>
                                        trong tổng số <strong><?php echo number_format($total_products); ?></strong> sản
                                        phẩm
                                    </span>
                                </div>
                            </div>

                            <!-- Professional Pagination Navigation -->
                            <nav class="pagination-nav" aria-label="Điều hướng trang">
                                <ul class="pagination-list">
                                    <?php
                                    // Build URL parameters for pagination
                                    $url_params = [];

                                    // Preserve search keyword
                                    if (!empty($keyword))
                                        $url_params['keyword'] = $keyword;

                                    // Preserve category filters (support multiple categories)
                                    if (!empty($category_ids)) {
                                        if (count($category_ids) == 1) {
                                            $url_params['category'] = $category_ids[0];
                                        } else {
                                            foreach ($category_ids as $index => $cat_id) {
                                                $url_params['category[' . $index . ']'] = $cat_id;
                                            }
                                        }
                                    }

                                    // Preserve promotion filters
                                    if (!empty($promotion_filters)) {
                                        if (count($promotion_filters) == 1) {
                                            $url_params['promotion'] = $promotion_filters[0];
                                        } else {
                                            foreach ($promotion_filters as $index => $promo) {
                                                $url_params['promotion[' . $index . ']'] = $promo;
                                            }
                                        }
                                    }

                                    // Preserve price filters
                                    if ($price_min)
                                        $url_params['price_min'] = $price_min;
                                    if ($price_max)
                                        $url_params['price_max'] = $price_max;

                                    // Preserve sort
                                    if ($sort && $sort !== 'newest')
                                        $url_params['sort'] = $sort;

                                    // Preserve items per page
                                    if (isset($_GET['items_per_page']))
                                        $url_params['items_per_page'] = $_GET['items_per_page'];

                                    function build_products_pagination_url($page_num, $params)
                                    {
                                        $params['page'] = $page_num;
                                        $params['scroll'] = '1'; // Thêm scroll parameter để tự động cuộn
                                        return BASE_URL . '/products.php?' . http_build_query($params);
                                    }
                                    ?>

                                    <!-- Previous Button -->
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link"
                                                href="<?php echo build_products_pagination_url($page - 1, $url_params); ?>"
                                                aria-label="Trang trước">
                                                <i class="fas fa-chevron-left"></i>
                                                <span class="page-text">Trước</span>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link" aria-disabled="true">
                                                <i class="fas fa-chevron-left"></i>
                                                <span class="page-text">Trước</span>
                                            </span>
                                        </li>
                                    <?php endif; ?>

                                    <?php
                                    // Smart pagination logic
                                    $start_page = max(1, $page - 2);
                                    $end_page = min($total_pages, $page + 2);
                                    ?>

                                    <!-- First page + ellipsis if needed -->
                                    <?php if ($start_page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link"
                                                href="<?php echo build_products_pagination_url(1, $url_params); ?>"
                                                aria-label="Trang 1">1</a>
                                        </li>
                                        <?php if ($start_page > 2): ?>
                                            <li class="page-item disabled">
                                                <span class="page-link ellipsis">...</span>
                                            </li>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <!-- Visible page range -->
                                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                        <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                            <a class="page-link"
                                                href="<?php echo build_products_pagination_url($i, $url_params); ?>"
                                                aria-label="Trang <?php echo $i; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <!-- Last page + ellipsis if needed -->
                                    <?php if ($end_page < $total_pages): ?>
                                        <?php if ($end_page < $total_pages - 1): ?>
                                            <li class="page-item disabled">
                                                <span class="page-link ellipsis">...</span>
                                            </li>
                                        <?php endif; ?>
                                        <li class="page-item">
                                            <a class="page-link"
                                                href="<?php echo build_products_pagination_url($total_pages, $url_params); ?>"
                                                aria-label="Trang <?php echo $total_pages; ?>"><?php echo $total_pages; ?></a>
                                        </li>
                                    <?php endif; ?>

                                    <!-- Next Button -->
                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link"
                                                href="<?php echo build_products_pagination_url($page + 1, $url_params); ?>"
                                                aria-label="Trang sau">
                                                <span class="page-text">Sau</span>
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link" aria-disabled="true">
                                                <span class="page-text">Sau</span>
                                                <i class="fas fa-chevron-right"></i>
                                            </span>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>

                        </div>
                    <?php endif; ?>
                </div>
        </div>
    </div>



    <!-- JavaScript cho trang sản phẩm -->
    <script>
        // Không sử dụng JavaScript redirect nữa để tránh load 2 lần

        // Quick search tags functionality for products page
        document.addEventListener('DOMContentLoaded', function () {
            // Lưu items_per_page hiện tại vào Local Storage nếu có
            const urlParams = new URLSearchParams(window.location.search);
            const currentItemsPerPage = urlParams.get('items_per_page');
            if (currentItemsPerPage && ['12', '24', '36', '48'].includes(currentItemsPerPage)) {
                localStorage.setItem('products_items_per_page', currentItemsPerPage);
            }
            const quickSearchTags = document.querySelectorAll('.quick-search-tag');
            const searchInput = document.getElementById('main-search-input');

            // Set and format current price values from PHP
            const priceMinInput = document.getElementById('price-min');
            const priceMaxInput = document.getElementById('price-max');

            <?php if ($price_min): ?>
                priceMinInput.value = '<?php echo $price_min; ?>';
                handlePriceInput(priceMinInput);
            <?php endif; ?>

            <?php if ($price_max): ?>
                priceMaxInput.value = '<?php echo $price_max; ?>';
                handlePriceInput(priceMaxInput);
            <?php endif; ?>

            // Global variable to track current typing timer
            let currentTypingTimer = null;

            // Enhanced typing effect function
            function typewriterEffect(element, text, speed = 80) {
                // Clear any existing typing timer
                if (currentTypingTimer) {
                    clearInterval(currentTypingTimer);
                    element.classList.remove('typing-effect');
                }

                // Clear current value and focus
                element.value = '';
                element.focus();

                // Add typing class for visual feedback
                element.classList.add('typing-effect');

                let i = 0;
                currentTypingTimer = setInterval(() => {
                    if (i < text.length) {
                        element.value += text.charAt(i);
                        i++;

                        // Trigger input event periodically (not every character for better performance)
                        if (i % 2 === 0 || i === text.length) {
                            const inputEvent = new Event('input', { bubbles: true });
                            element.dispatchEvent(inputEvent);
                        }
                    } else {
                        clearInterval(currentTypingTimer);
                        currentTypingTimer = null;

                        // Remove typing class when done
                        element.classList.remove('typing-effect');

                        // Final input event when typing is complete
                        const finalEvent = new Event('input', { bubbles: true });
                        element.dispatchEvent(finalEvent);
                    }
                }, speed);

                return currentTypingTimer;
            }

            // Quick search tags functionality - Fill search input with typing effect
            quickSearchTags.forEach(tag => {
                tag.addEventListener('click', function () {
                    const keyword = this.getAttribute('data-keyword');

                    if (keyword && searchInput) {
                        // Prevent multiple clicks during typing
                        if (currentTypingTimer) {
                            return;
                        }

                        // Add visual feedback to the tag
                        this.style.transform = 'translateY(-2px) scale(0.95)';
                        this.style.pointerEvents = 'none'; // Disable during animation

                        setTimeout(() => {
                            this.style.transform = '';
                            this.style.pointerEvents = 'auto'; // Re-enable after animation
                        }, 150);

                        // Calculate typing speed based on text length (shorter = slower for readability)
                        const baseSpeed = 60;
                        const adjustedSpeed = Math.max(40, Math.min(100, baseSpeed + (10 - keyword.length) * 5));

                        // Use typing effect to fill the search input
                        typewriterEffect(searchInput, keyword, adjustedSpeed);
                    }
                });
            });
        });
    </script>

    <script>
        // Toggle filter sections
        function toggleFilterSection(sectionName) {
            const content = document.getElementById(sectionName + '-content');
            const chevron = document.getElementById(sectionName + '-chevron');

            if (!content || !chevron) return;

            // Debounce để tránh spam click
            if (content.dataset.animating === 'true') return;
            content.dataset.animating = 'true';

            // Sử dụng requestAnimationFrame để tối ưu hiệu suất
            requestAnimationFrame(() => {
                const isHidden = content.classList.contains('filter-section-hidden');

                if (isHidden) {
                    // Hiển thị content với animation mượt mà
                    content.classList.remove('filter-section-hidden');
                    content.classList.add('filter-section-visible');
                    chevron.classList.add('rotate-90');

                    // Trigger reflow để animation hoạt động
                    content.offsetHeight;
                } else {
                    // Ẩn content với animation mượt mà
                    content.classList.remove('filter-section-visible');
                    content.classList.add('filter-section-hidden');
                    chevron.classList.remove('rotate-90');
                }

                // Reset animation flag sau khi animation hoàn thành
                setTimeout(() => {
                    content.dataset.animating = 'false';
                }, isHidden ? 400 : 300);
            });
        }

        // Toggle category checkbox with animations
        function toggleCategoryCheckbox(checkboxId) {
            const checkbox = document.getElementById(checkboxId);
            if (checkbox) {
                checkbox.checked = !checkbox.checked;

                // Trigger animations if the function exists
                if (typeof triggerCheckboxAnimations === 'function') {
                    triggerCheckboxAnimations(checkbox);
                }

                // Trigger change event for any other listeners
                const changeEvent = new Event('change', { bubbles: true });
                checkbox.dispatchEvent(changeEvent);
            }
        }

        // Format number with dots (Vietnamese currency format)
        function formatNumberWithDots(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        }

        // Remove dots from formatted number
        function removeDotsFromNumber(str) {
            return str.replace(/\./g, '');
        }

        // Handle price input formatting
        function handlePriceInput(input) {
            let value = input.value;

            // Remove all non-digit characters except dots
            value = value.replace(/[^\d.]/g, '');

            // Remove existing dots
            value = removeDotsFromNumber(value);

            // Only keep digits
            value = value.replace(/\D/g, '');

            // Format with dots if there's a value
            if (value) {
                value = formatNumberWithDots(value);
            }

            // Update input value
            input.value = value;
        }

        // Get numeric value from formatted price input
        function getPriceNumericValue(input) {
            const value = input.value;
            if (!value) return '';
            return removeDotsFromNumber(value);
        }

        // Toggle subcategories với hiệu ứng mượt mà và tối ưu GPU
        function toggleSubcategories(parentId) {
            const subcategories = document.getElementById('subcategories-' + parentId);
            const chevron = document.getElementById('sub-chevron-' + parentId);

            if (!subcategories || !chevron) return;

            // Debounce để tránh spam click
            if (subcategories.dataset.animating === 'true') return;
            subcategories.dataset.animating = 'true';

            // Sử dụng requestAnimationFrame để tối ưu hiệu suất
            requestAnimationFrame(() => {
                const isHidden = subcategories.classList.contains('subcategory-hidden');

                if (isHidden) {
                    // Hiển thị subcategories với animation mượt mà
                    subcategories.classList.remove('subcategory-hidden');
                    subcategories.classList.add('subcategory-visible');
                    chevron.classList.add('rotate-90');

                    // Trigger reflow để animation hoạt động
                    subcategories.offsetHeight;
                } else {
                    // Ẩn subcategories với animation mượt mà
                    subcategories.classList.remove('subcategory-visible');
                    subcategories.classList.add('subcategory-hidden');
                    chevron.classList.remove('rotate-90');
                }

                // Reset animation flag sau khi animation hoàn thành
                setTimeout(() => {
                    subcategories.dataset.animating = 'false';
                }, isHidden ? 350 : 250);
            });
        }

        // Price input formatting
        document.querySelectorAll('.price-input').forEach(input => {
            // Format on input
            input.addEventListener('input', function () {
                handlePriceInput(this);
            });

            // Format on paste
            input.addEventListener('paste', function () {
                setTimeout(() => {
                    handlePriceInput(this);
                }, 10);
            });

            // Format existing value on page load
            if (input.value) {
                handlePriceInput(input);
            }
        });

        // Price preset functionality
        document.querySelectorAll('.price-preset').forEach(button => {
            button.addEventListener('click', function () {
                const minPrice = this.getAttribute('data-min');
                const maxPrice = this.getAttribute('data-max');
                const priceMinInput = document.getElementById('price-min');
                const priceMaxInput = document.getElementById('price-max');

                // Check if this button is currently active
                const isCurrentlyActive = this.classList.contains('from-orange-500');

                if (isCurrentlyActive) {
                    // If currently active, deactivate it (clear inputs)
                    priceMinInput.value = '';
                    priceMaxInput.value = '';

                    // Remove active class from this preset
                    this.className = this.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
                    this.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
                } else {
                    // If not active, activate it (set values)
                    priceMinInput.value = minPrice || '';
                    priceMaxInput.value = maxPrice || '';

                    if (minPrice) handlePriceInput(priceMinInput);
                    if (maxPrice) handlePriceInput(priceMaxInput);

                    // Remove active class from all presets first
                    document.querySelectorAll('.price-preset').forEach(btn => {
                        btn.className = btn.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
                        btn.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
                    });

                    // Add active class to clicked preset - chỉ thêm from-orange-500 để trigger outline style
                    this.className = this.className.replace(/bg-white|text-gray-700|border-gray-200/g, '');
                    this.classList.add('from-orange-500', 'bg-white', 'text-gray-700', 'border-gray-200');
                }
            });
        });

        // View toggle functionality
        document.addEventListener('DOMContentLoaded', function () {
            const gridViewBtn = document.getElementById('grid-view');
            const listViewBtn = document.getElementById('list-view');
            const productsGrid = document.getElementById('productsGrid');

            if (gridViewBtn && listViewBtn && productsGrid) {
                // Grid view (default) - sử dụng CSS responsive tùy chỉnh
                gridViewBtn.addEventListener('click', function () {
                    // Remove list view class và thêm custom grid class
                    productsGrid.classList.remove('products-grid-list');
                    productsGrid.classList.add('products-grid');

                    // Update buttons
                    gridViewBtn.classList.remove('bg-white', 'text-gray-600');
                    gridViewBtn.classList.add('bg-orange-500', 'text-white');

                    listViewBtn.classList.remove('bg-orange-500', 'text-white');
                    listViewBtn.classList.add('bg-white', 'text-gray-600');

                    // Save preference
                    localStorage.setItem('products-view', 'grid');
                });

                // List view - force 1 column cho tất cả màn hình
                listViewBtn.addEventListener('click', function () {
                    // Remove grid class và thêm list class
                    productsGrid.classList.remove('products-grid');
                    productsGrid.classList.add('products-grid-list');

                    // Update buttons
                    listViewBtn.classList.remove('bg-white', 'text-gray-600');
                    listViewBtn.classList.add('bg-orange-500', 'text-white');

                    gridViewBtn.classList.remove('bg-orange-500', 'text-white');
                    gridViewBtn.classList.add('bg-white', 'text-gray-600');

                    // Save preference
                    localStorage.setItem('products-view', 'list');
                });

                // Load saved preference
                const savedView = localStorage.getItem('products-view') || 'grid';
                if (savedView === 'list') {
                    // Set list view
                    productsGrid.classList.remove('products-grid');
                    productsGrid.classList.add('products-grid-list');
                    listViewBtn.classList.remove('bg-white', 'text-gray-600');
                    listViewBtn.classList.add('bg-orange-500', 'text-white');
                    gridViewBtn.classList.remove('bg-orange-500', 'text-white');
                    gridViewBtn.classList.add('bg-white', 'text-gray-600');
                } else {
                    // Set grid view (default)
                    productsGrid.classList.remove('products-grid-list');
                    productsGrid.classList.add('products-grid');
                    gridViewBtn.classList.remove('bg-white', 'text-gray-600');
                    gridViewBtn.classList.add('bg-orange-500', 'text-white');
                    listViewBtn.classList.remove('bg-orange-500', 'text-white');
                    listViewBtn.classList.add('bg-white', 'text-gray-600');
                }
            }

            // Sort functionality
            const sortSelect = document.getElementById('sort-select');
            const itemsPerPageSelect = document.getElementById('items-per-page');

            if (sortSelect) {
                sortSelect.addEventListener('change', function () {
                    // Skip if AJAX filter is active
                    if (window.ajaxFilterActive) {
                        console.log('AJAX Filter: Blocking sort redirect');
                        return;
                    }
                    window.location.href = updateUrlParameter(window.location.href, 'sort', this.value);
                });
            }

            if (itemsPerPageSelect) {
                itemsPerPageSelect.addEventListener('change', function () {
                    // Skip if AJAX filter is active
                    if (window.ajaxFilterActive) {
                        console.log('AJAX Filter: Blocking items per page redirect');
                        return;
                    }

                    // Lưu giá trị vào cả Local Storage và Cookie
                    localStorage.setItem('products_items_per_page', this.value);

                    // Lưu vào cookie với thời hạn 30 ngày
                    const expiryDate = new Date();
                    expiryDate.setDate(expiryDate.getDate() + 30);
                    document.cookie = `products_items_per_page=${this.value}; expires=${expiryDate.toUTCString()}; path=/`;

                    // Reset về trang 1 khi thay đổi items per page và thêm scroll parameter
                    let url = updateUrlParameter(window.location.href, 'items_per_page', this.value);
                    url = updateUrlParameter(url, 'page', '1');
                    url = updateUrlParameter(url, 'scroll', '1');
                    window.location.href = url;
                });
            }

            // Reset filters
            const resetFiltersBtn = document.getElementById('resetFilters');
            const resetFiltersBtnSidebar = document.getElementById('resetFiltersBtn');
            const resetFiltersNoProductsBtn = document.getElementById('resetFiltersNoProducts');

            if (resetFiltersBtn) {
                resetFiltersBtn.addEventListener('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Smart reset logic với FilterStateManager
                    const debugInfo = filterStateManager.getDebugInfo();
                    console.log('Reset button clicked - Debug info:', debugInfo);

                    // Kiểm tra filter active với logging chi tiết
                    const hasFilters = filterStateManager.hasActiveFilters();
                    console.log('Reset button - hasActiveFilters result:', hasFilters);

                    if (!hasFilters) {
                        // This is the small reset button (w-12 h-12) - should scroll to notification
                        const isSmallButton = resetFiltersBtn.classList.contains('w-12');
                        console.log('No active filters detected, showing notification');
                        showFilterNotification('Không có bộ lọc nào để đặt lại!', 'warning', isSmallButton);
                        return false;
                    }

                    console.log('Active filters detected, proceeding with reset');

                    // Clear any existing notifications trước khi reset
                    const existingNotification = document.getElementById('filter-notification');
                    if (existingNotification) {
                        console.log('Removing existing notification before reset');
                        existingNotification.remove();
                    }

                    // Có filters active - thực hiện reset với AJAX
                    console.log('Resetting filters with AJAX');
                    if (window.ajaxFilter) {
                        // Show loading state
                        window.ajaxFilter.showResetLoadingState(resetFiltersBtn);

                        // Override handleResetFilters để remove loading state sau khi hoàn thành
                        const originalHandleResetFilters = window.ajaxFilter.handleResetFilters;
                        window.ajaxFilter.handleResetFilters = function() {
                            originalHandleResetFilters.call(this);
                            // Remove loading state sau khi reset hoàn thành
                            setTimeout(() => {
                                window.ajaxFilter.hideResetLoadingState(resetFiltersBtn);
                            }, 800); // Đợi animation hoàn thành
                        };
                        window.ajaxFilter.handleResetFilters();
                    } else {
                        // Fallback: reload trang
                        window.location.href = 'products.php?scroll=1';
                    }
                });
            }

            if (resetFiltersBtnSidebar) {
                resetFiltersBtnSidebar.addEventListener('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Smart reset logic với FilterStateManager
                    const debugInfo = filterStateManager.getDebugInfo();
                    console.log('Reset sidebar button clicked - Debug info:', debugInfo);

                    // Kiểm tra filter active với logging chi tiết
                    const hasFilters = filterStateManager.hasActiveFilters();
                    console.log('Reset sidebar button - hasActiveFilters result:', hasFilters);

                    if (!hasFilters) {
                        // This is the large reset button (w-full) - should NOT scroll to notification
                        const isSmallButton = resetFiltersBtnSidebar.classList.contains('w-12');
                        console.log('No active filters detected, showing notification');
                        showFilterNotification('Không có bộ lọc nào để đặt lại!', 'warning', isSmallButton);
                        return false;
                    }

                    console.log('Active filters detected, proceeding with reset');

                    // Clear any existing notifications trước khi reset
                    const existingNotification = document.getElementById('filter-notification');
                    if (existingNotification) {
                        console.log('Removing existing notification before reset');
                        existingNotification.remove();
                    }

                    // Có filters active - thực hiện reset với AJAX
                    console.log('Resetting filters from sidebar with AJAX');
                    if (window.ajaxFilter) {
                        // Không có loading state cho nút reset lớn - chỉ thực hiện reset
                        window.ajaxFilter.handleResetFilters();
                    } else {
                        // Fallback: reload trang
                        window.location.href = 'products.php?scroll=1';
                    }
                });
            }

            if (resetFiltersNoProductsBtn) {
                resetFiltersNoProductsBtn.addEventListener('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Check if any filters are selected
                    const hasFilters = hasActiveFilters();
                    console.log('Reset no products button - hasActiveFilters result:', hasFilters);

                    if (!hasFilters) {
                        console.log('No active filters detected, showing notification');
                        showFilterNotification('Không có bộ lọc nào để đặt lại!', 'warning', true);
                        return false;
                    }

                    console.log('Active filters detected, proceeding with reset');

                    // Clear any existing notifications trước khi reset
                    const existingNotification = document.getElementById('filter-notification');
                    if (existingNotification) {
                        console.log('Removing existing notification before reset');
                        existingNotification.remove();
                    }

                    // Có filters active - thực hiện reset với AJAX
                    console.log('Resetting filters from no products section with AJAX');
                    if (window.ajaxFilter) {
                        window.ajaxFilter.handleResetFilters();
                    } else {
                        // Fallback: reload trang
                        window.location.href = 'products.php?scroll=1';
                    }
                });
            }

            // Pagination Loading Effect with Delay - Giống y hệt trang tìm kiếm
            const paginationLinks = document.querySelectorAll('.page-link[href]');

            paginationLinks.forEach(link => {
                link.addEventListener('click', function (e) {
                    // Prevent immediate navigation
                    e.preventDefault();

                    // Store the target URL
                    const targetUrl = this.href;

                    // Apply loading state immediately
                    this.style.opacity = '0.6';
                    this.style.pointerEvents = 'none';

                    // Show loading spinner with text
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Đang tải...';

                    // Navigate after 1.5 seconds delay
                    setTimeout(() => {
                        window.location.href = targetUrl;
                    }, 1500);

                    // Fallback restore (in case something goes wrong)
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.style.opacity = '';
                        this.style.pointerEvents = '';
                    }, 5000);
                });
            });



            // Apply filters - DISABLED: Now handled by AJAX Filter module
            // const applyFiltersBtn = document.getElementById('applyFilters');
            // if (applyFiltersBtn) {
            //     applyFiltersBtn.addEventListener('click', function (e) {
            //         // Check if any filters are selected
            //         if (!hasActiveFilters()) {
            //             // Prevent any default behavior and auto-scroll
            //             e.preventDefault();
            //             e.stopPropagation();
            //             showFilterNotification('Vui lòng chọn ít nhất một bộ lọc trước khi áp dụng!', 'warning', false);
            //             return false;
            //         }

            //         // Collect filter data
            //         let url = 'products.php?';

            //         // Preserve current keyword if exists
            //         const urlParams = new URLSearchParams(window.location.search);
            //         const keyword = urlParams.get('keyword');
            //         if (keyword && keyword.trim() !== '') {
            //             url += `keyword=${encodeURIComponent(keyword)}&`;
            //         }

            //         // Categories
            //         const selectedCategories = Array.from(document.querySelectorAll('input[name="category[]"]:checked')).map(input => input.value);
            //         if (selectedCategories.length > 0) {
            //             selectedCategories.forEach(cat => {
            //                 url += `category[]=${cat}&`;
            //             });
            //         }

            //         // Price range
            //         const priceMinInput = document.getElementById('price-min');
            //         const priceMaxInput = document.getElementById('price-max');
            //         const minPrice = getPriceNumericValue(priceMinInput);
            //         const maxPrice = getPriceNumericValue(priceMaxInput);
            //         if (minPrice) url += `price_min=${minPrice}&`;
            //         if (maxPrice) url += `price_max=${maxPrice}&`;

            //         // Promotions
            //         const selectedPromotions = Array.from(document.querySelectorAll('input[name="promotion[]"]:checked')).map(input => input.value);
            //         if (selectedPromotions.length > 0) {
            //             selectedPromotions.forEach(promo => {
            //                 url += `promotion[]=${promo}&`;
            //             });
            //         }

            //         // Remove trailing &
            //         if (url.endsWith('&')) {
            //             url = url.slice(0, -1);
            //         }

            //         window.location.href = url;
            //     });
            // }
        });

        // Helper function to update URL parameters
        function updateUrlParameter(url, key, value) {
            const re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
            const separator = url.indexOf('?') !== -1 ? "&" : "?";

            if (url.match(re)) {
                return url.replace(re, '$1' + key + "=" + value + '$2');
            } else {
                return url + separator + key + "=" + value;
            }
        }

        // Product interaction functions
        function quickView(productId) {
            console.log('Quick view for product ID:', productId);
            // Implement quick view modal functionality
            alert('Xem nhanh sản phẩm ID: ' + productId);
        }

        function toggleWishlist(productId) {
            console.log('Toggle wishlist for product ID:', productId);
            // Implement wishlist toggle functionality
            alert('Đã thêm/xóa sản phẩm ID: ' + productId + ' khỏi danh sách yêu thích');
        }

        function toggleCompare(productId) {
            console.log('Toggle compare for product ID:', productId);
            // Implement compare toggle functionality
            alert('Đã thêm/xóa sản phẩm ID: ' + productId + ' khỏi danh sách so sánh');
        }

        function addToCart(productId) {
            console.log('Add to cart product ID:', productId);
            // Implement add to cart functionality
            alert('Đã thêm sản phẩm ID: ' + productId + ' vào giỏ hàng');
        }

        function buyNow(productId) {
            console.log('Buy now product ID:', productId);
            // Implement buy now functionality
            window.location.href = 'checkout.php?product_id=' + productId;
        }

        function contactProduct(productId) {
            console.log('Contact about product ID:', productId);
            // Implement contact functionality
            alert('Liên hệ về sản phẩm ID: ' + productId);
        }
    </script>



    <!-- Tích hợp Giải pháp 1: Overflow Visible với search logic hiện tại -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Đảm bảo solution1 đã được khởi tạo
            if (typeof SearchOverflowSolution1 !== 'undefined') {
                SearchOverflowSolution1.init();

                // Tích hợp với search input hiện tại
                const searchInput = document.getElementById('main-search-input');
                const searchSuggestions = document.getElementById('search-suggestions');

                if (searchInput && searchSuggestions) {
                    let searchTimeout = null;
                    let lastQuery = '';
                    let isSearching = false;
                    let searchCache = new Map(); // Cache để lưu kết quả tìm kiếm
                    let lastApiCall = 0; // Timestamp của lần gọi API cuối
                    const API_DEBOUNCE_TIME = 500; // 500ms debounce
                    let currentRequestId = 0; // ID để track request hiện tại

                    // Hàm xử lý tìm kiếm thống nhất
                    function handleSearch(query, forceSearch = false) {
                        const now = Date.now();

                        // Tránh tìm kiếm trùng lặp
                        if (query === lastQuery && !forceSearch && (now - lastApiCall) < API_DEBOUNCE_TIME) {
                            return;
                        }

                        // Xóa timeout cũ
                        if (searchTimeout) {
                            clearTimeout(searchTimeout);
                        }

                        if (query.length > 0) {
                            // Clear error state khi user bắt đầu nhập
                            searchSuggestions.classList.remove('error-state');

                            // Kiểm tra cache trước
                            if (searchCache.has(query)) {
                                const cachedResults = searchCache.get(query);
                                displayRealSuggestions(cachedResults);
                                SearchOverflowSolution1.showSuggestions();
                                lastQuery = query;
                                return;
                            }

                            // Nếu đang tìm kiếm cùng query, không thực hiện tìm kiếm mới
                            if (isSearching && query === lastQuery) {
                                return;
                            }

                            // Hiển thị loading state chỉ khi cần thiết
                            if (searchSuggestions.innerHTML.trim() === '' || query !== lastQuery) {
                                showLoadingState();
                                SearchOverflowSolution1.showSuggestions();
                            }

                            // Debounce search để tránh gọi API quá nhiều
                            searchTimeout = setTimeout(() => {
                                const currentQuery = searchInput.value.trim();
                                if (query === currentQuery && (Date.now() - lastApiCall) >= API_DEBOUNCE_TIME) {
                                    fetchRealSuggestions(query);
                                    lastQuery = query;
                                    lastApiCall = Date.now();
                                }
                            }, 300);
                        } else {
                            lastQuery = '';
                            SearchOverflowSolution1.hideSuggestions();
                        }
                    }

                    // Đánh dấu để tránh đăng ký event listener trùng lặp
                    if (!searchInput.hasAttribute('data-search-initialized')) {
                        searchInput.setAttribute('data-search-initialized', 'true');

                        // Lắng nghe sự kiện input để hiển thị suggestions
                        searchInput.addEventListener('input', function () {
                            const query = this.value.trim();
                            handleSearch(query);
                        });

                        // Lắng nghe sự kiện focus - chỉ hiển thị lại nếu đã có kết quả
                        searchInput.addEventListener('focus', function () {
                            const query = this.value.trim();
                            if (query.length > 0 && searchSuggestions.innerHTML.trim() !== '') {
                                // Chỉ hiển thị lại kết quả đã có, không gọi API mới
                                SearchOverflowSolution1.showSuggestions();
                            }
                        });

                        // Lắng nghe sự kiện blur
                        searchInput.addEventListener('blur', function () {
                            // Delay để cho phép click vào suggestions
                            setTimeout(() => {
                                SearchOverflowSolution1.hideSuggestions();
                            }, 200);
                        });
                    }
                }

                // Hàm hiển thị trạng thái loading
                function showLoadingState() {
                    searchSuggestions.innerHTML = `
                <div style="padding: 1.5rem; text-align: center; background: linear-gradient(135deg, #fefbf3, #fef7ed); border-radius: 0.75rem; margin: 0.5rem;">
                    <div style="display: flex !important; align-items: center !important; justify-content: center !important; gap: 0.75rem !important; line-height: 1 !important;">
                        <div style="position: relative; display: inline-block !important; width: 1.25rem !important; height: 1.25rem !important;">
                            <div style="position: absolute; width: 100%; height: 100%; border: 2px solid #fed7aa; border-radius: 50%; animation: luxuryPulse 2s ease-in-out infinite;"></div>
                            <div style="position: absolute; width: 100%; height: 100%; border: 2px solid #f97316; border-top: 2px solid transparent; border-radius: 50%; animation: luxurySpin 1s linear infinite;"></div>
                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 4px; height: 4px; background: linear-gradient(45deg, #f97316, #ea580c); border-radius: 50%; animation: luxuryGlow 1.5s ease-in-out infinite alternate;"></div>
                        </div>
                        <span style="display: inline-block !important; color: #92400e !important; font-size: 0.875rem !important; font-weight: 500 !important; line-height: 1 !important; vertical-align: middle !important; letter-spacing: 0.025em;">Đang tìm kiếm sản phẩm...</span>
                    </div>
                </div>
                <style>
                    @keyframes luxurySpin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                    @keyframes luxuryPulse {
                        0%, 100% {
                            transform: scale(1);
                            opacity: 0.3;
                        }
                        50% {
                            transform: scale(1.1);
                            opacity: 0.6;
                        }
                    }
                    @keyframes luxuryGlow {
                        0% {
                            opacity: 0.8;
                            box-shadow: 0 0 4px #f97316;
                        }
                        100% {
                            opacity: 1;
                            box-shadow: 0 0 8px #f97316, 0 0 12px #f97316;
                        }
                    }
                </style>
            `;
                }

                // Hàm gọi API tìm kiếm thực tế
                function fetchRealSuggestions(query) {
                    // Tạo request ID duy nhất
                    const requestId = ++currentRequestId;
                    console.log(`🚀 Starting search request #${requestId} for query: "${query}"`);

                    // Đánh dấu đang tìm kiếm
                    isSearching = true;

                    // Tạo AbortController để có thể cancel request
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout

                    fetch(`<?php echo BASE_URL; ?>/api/search_suggestions.php?keyword=${encodeURIComponent(query)}`, {
                        signal: controller.signal
                    })
                        .then(response => {
                            // Kiểm tra HTTP status code
                            if (!response.ok) {
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            // Kiểm tra cấu trúc response
                            if (!data || typeof data !== 'object') {
                                throw new Error('Invalid response format');
                            }

                            // Đảm bảo có field suggestions
                            if (!Array.isArray(data.suggestions)) {
                                throw new Error('Missing or invalid suggestions field');
                            }

                            console.log(`✅ Request #${requestId} completed successfully. Results: ${data.suggestions.length} items`);
                            console.log(`Current request ID: ${currentRequestId}, Current query: "${searchInput.value.trim()}"`);

                            // Chỉ hiển thị kết quả nếu đây là request mới nhất và query vẫn còn hiện tại
                            if (requestId === currentRequestId && query === searchInput.value.trim()) {
                                console.log(`📋 Displaying results for request #${requestId}`);
                                // Lưu vào cache
                                searchCache.set(query, data.suggestions);

                                // Giới hạn cache size (tối đa 20 queries)
                                if (searchCache.size > 20) {
                                    const firstKey = searchCache.keys().next().value;
                                    searchCache.delete(firstKey);
                                }

                                displayRealSuggestions(data.suggestions);
                            } else {
                                console.log(`🚫 Ignoring results for request #${requestId} (outdated)`);
                            }
                        })
                        .catch(error => {
                            console.error(`❌ Request #${requestId} failed:`, error);

                            // Không hiển thị lỗi nếu request bị abort (do user thay đổi query nhanh)
                            if (error.name === 'AbortError') {
                                console.log(`⏹️ Request #${requestId} was aborted`);
                                return;
                            }

                            // Chỉ hiển thị lỗi nếu đây là request mới nhất và query vẫn còn hiện tại
                            if (requestId === currentRequestId && query === searchInput.value.trim()) {
                                console.log(`💥 Showing error state for request #${requestId}`);
                                showErrorState();
                            } else {
                                console.log(`🤐 Suppressing error for outdated request #${requestId}`);
                            }
                        })
                        .finally(() => {
                            // Clear timeout
                            clearTimeout(timeoutId);
                            // Đánh dấu kết thúc tìm kiếm
                            isSearching = false;
                        });
                }

                // Hàm hiển thị kết quả tìm kiếm thực tế
                function displayRealSuggestions(suggestions) {
                    // Clear any previous error states
                    searchSuggestions.classList.remove('error-state');

                    if (!suggestions || suggestions.length === 0) {
                        showNoResultsState();
                        return;
                    }

                    const html = suggestions.map(product => `
                <div class="p-3 hover:bg-orange-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-200" onclick="selectProduct('${product.url}')">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                            <img src="${product.image}" alt="${product.name}" class="w-full h-full object-cover" onerror="this.src='<?php echo BASE_URL; ?>/assets/img/no-image.jpg'">
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="text-sm font-medium text-gray-900 truncate">${product.name}</div>
                            <div class="text-xs text-gray-500">${product.category}</div>
                        </div>
                        <div class="text-right flex-shrink-0">
                            <div class="text-sm font-semibold text-orange-600">${product.price}</div>
                            <div class="text-xs text-gray-400">⭐ ${product.rating}</div>
                        </div>
                    </div>
                </div>
            `).join('');

                    searchSuggestions.innerHTML = html;
                }

                // Hàm hiển thị trạng thái không có kết quả
                function showNoResultsState() {
                    searchSuggestions.innerHTML = `
                <div class="p-4 text-center">
                    <div class="text-gray-400 mb-2">
                        <i class="fas fa-search text-2xl"></i>
                    </div>
                    <div class="text-gray-500 text-sm">Không tìm thấy sản phẩm phù hợp</div>
                    <div class="text-gray-400 text-xs mt-1">Thử tìm kiếm với từ khóa khác</div>
                </div>
            `;
                }

                // Hàm hiển thị trạng thái lỗi (chỉ hiển thị sau một khoảng thời gian)
                function showErrorState() {
                    // Delay hiển thị lỗi để tránh flash error khi user đang nhập nhanh
                    setTimeout(() => {
                        // Chỉ hiển thị lỗi nếu vẫn không có kết quả và user không đang nhập
                        if (searchSuggestions.innerHTML.includes('Đang tìm kiếm...') ||
                            searchSuggestions.classList.contains('error-state')) {

                            searchSuggestions.classList.add('error-state');
                            searchSuggestions.innerHTML = `
                        <div class="p-4 text-center">
                            <div class="text-red-400 mb-2">
                                <i class="fas fa-exclamation-triangle text-2xl"></i>
                            </div>
                            <div class="text-gray-500 text-sm">Có lỗi xảy ra khi tìm kiếm</div>
                            <div class="text-gray-400 text-xs mt-1">Vui lòng thử lại sau</div>
                        </div>
                    `;
                        }
                    }, 1000); // Delay 1 giây
                }

                // Hàm chọn sản phẩm từ suggestions
                window.selectProduct = function (productUrl) {
                    window.location.href = productUrl;
                };

                console.log('✅ Giải pháp 1: Overflow Visible đã được tích hợp thành công!');
            } else {
                console.warn('⚠️ SearchOverflowSolution1 chưa được load');
            }
        });

        // Auto-scroll to Products section when filtering/searching
        function autoScrollToProducts() {
            // Tìm phần Products
            const productsSection = document.getElementById('products-section');
            if (!productsSection) {
                console.warn('Products section not found');
                return;
            }

            // Tính toán chiều cao header sau khi scroll (khi top bar ẩn)
            let finalHeaderHeight = 0;

            // Kiểm tra xem đang ở desktop hay mobile
            const isMobile = window.innerWidth <= 768;

            if (isMobile) {
                // Màn hình mobile (≤768px): sử dụng mobile-header
                const mobileHeader = document.querySelector('.mobile-header');
                if (mobileHeader && window.getComputedStyle(mobileHeader).display !== 'none') {
                    // Trên mobile (cả điện thoại và tablet), cần cuộn thêm 60px nữa để đến đúng vị trí danh sách sản phẩm
                    finalHeaderHeight = -60; // Giá trị âm để cuộn thêm 60px
                    console.log('Mobile header calculation:', {
                        mobileHeaderHeight: mobileHeader.offsetHeight,
                        finalHeaderHeight: finalHeaderHeight,
                        screenWidth: window.innerWidth,
                        note: 'Scroll additional 60px for mobile (phone and tablet)'
                    });
                } else {
                    // Fallback cho mobile
                    finalHeaderHeight = -60;
                }
            } else {
                // Desktop: tính header height sau khi top bar ẩn
                const premiumHeader = document.querySelector('.premium-header');
                if (premiumHeader && window.getComputedStyle(premiumHeader).display !== 'none') {
                    // Tìm top bar - sẽ ẩn khi scroll
                    const topBar = premiumHeader.querySelector('.top-bar');

                    if (topBar) {
                        // Header height = total height - top bar height (vì top bar sẽ ẩn)
                        const totalHeight = premiumHeader.offsetHeight;
                        const topBarHeight = topBar.offsetHeight;
                        finalHeaderHeight = totalHeight - topBarHeight;

                        console.log('Desktop header calculation:', {
                            totalHeight,
                            topBarHeight,
                            finalHeaderHeight
                        });
                    } else {
                        // Fallback: ước tính
                        const totalHeight = premiumHeader.offsetHeight;
                        const estimatedTopBarHeight = 40; // Ước tính top bar height
                        finalHeaderHeight = Math.max(60, totalHeight - estimatedTopBarHeight);
                    }
                }
            }

            // Fallback nếu không tìm thấy header
            if (finalHeaderHeight === 0) {
                const anyHeader = document.querySelector('header, .header, [class*="header"]');
                if (anyHeader) {
                    finalHeaderHeight = isMobile ? anyHeader.offsetHeight : Math.max(70, anyHeader.offsetHeight - 45);
                } else {
                    finalHeaderHeight = isMobile ? 60 : 70; // Default values
                }
            }

            // Tính vị trí cuộn: mép trên của Products section trừ đi chiều cao header
            // Để mép dưới header chạm vào mép trên của Products
            const targetPosition = productsSection.offsetTop - finalHeaderHeight;

            // Smooth scroll
            window.scrollTo({
                top: Math.max(0, targetPosition),
                behavior: 'smooth'
            });

            console.log('Auto-scroll to Products:', {
                isMobile,
                finalHeaderHeight,
                targetPosition,
                productsOffset: productsSection.offsetTop,
                calculation: `${productsSection.offsetTop} - ${finalHeaderHeight} = ${targetPosition}`
            });
        }

        // Auto-scroll khi trang load với filter/search parameters
        document.addEventListener('DOMContentLoaded', function () {
            // Kiểm tra xem có filter/search parameters không
            const urlParams = new URLSearchParams(window.location.search);
            const hasKeyword = urlParams.has('keyword') && urlParams.get('keyword').trim() !== '';

            // Kiểm tra category (hỗ trợ cả category và category[])
            const hasCategory = urlParams.has('category') || urlParams.has('category[]') ||
                Array.from(urlParams.keys()).some(key => key.startsWith('category['));

            // Kiểm tra promotion filter
            const hasPromotion = urlParams.has('promotion') || urlParams.has('promotion[]') ||
                Array.from(urlParams.keys()).some(key => key.startsWith('promotion['));

            const hasPriceMin = urlParams.has('price_min') && urlParams.get('price_min') !== '';
            const hasPriceMax = urlParams.has('price_max') && urlParams.get('price_max') !== '';
            const hasSort = urlParams.has('sort') && urlParams.get('sort') !== 'newest';

            // Kiểm tra items per page parameter
            const hasItemsPerPage = urlParams.has('items_per_page');

            // Kiểm tra page parameter (pagination)
            const hasPageParam = urlParams.has('page') && urlParams.get('page') !== '1';

            // Kiểm tra scroll parameter cho reset actions
            const hasScrollParam = urlParams.has('scroll') && urlParams.get('scroll') === '1';

            // Nếu có bất kỳ filter nào hoặc scroll parameter, auto-scroll
            if (hasKeyword || hasCategory || hasPromotion || hasPriceMin || hasPriceMax || hasSort || hasItemsPerPage || hasPageParam || hasScrollParam) {
                // Delay một chút để đảm bảo page đã render xong
                setTimeout(autoScrollToProducts, 300);
            }
        });

        // Auto-scroll khi submit form tìm kiếm
        const searchForms = document.querySelectorAll('form[action*="products.php"]');
        searchForms.forEach(form => {
            form.addEventListener('submit', function (e) {
                // Không prevent default, để form submit bình thường
                // Sau khi page reload, DOMContentLoaded sẽ handle auto-scroll
            });
        });

        // Auto-scroll khi click vào filter buttons
        document.addEventListener('click', function (e) {

            // Apply filters button - only scroll if filters are active and AJAX filter is not active
            if (e.target.id === 'applyFilters' || e.target.closest('#applyFilters')) {
                // Skip if AJAX filter is handling this
                if (window.ajaxFilterActive) {
                    return;
                }

                if (hasActiveFilters()) {
                    setTimeout(autoScrollToProducts, 100);
                }
            }

            // Category filter links
            if (e.target.closest('a[href*="category="]')) {
                // Sẽ auto-scroll sau khi page load
            }

            // Search suggestion links
            if (e.target.closest('a[href*="keyword="]')) {
                // Sẽ auto-scroll sau khi page load
            }
        });

        // Auto-scroll khi thay đổi sort
        const sortSelect = document.querySelector('select[name="sort"]');
        if (sortSelect) {
            sortSelect.addEventListener('change', function () {
                setTimeout(autoScrollToProducts, 100);
            });
        }

        // Auto-scroll khi thay đổi items per page
        const limitSelect = document.querySelector('select[name="items_per_page"]');
        if (limitSelect) {
            limitSelect.addEventListener('change', function () {
                setTimeout(autoScrollToProducts, 100);
            });
        }

        // Auto-scroll khi click pagination
        document.addEventListener('click', function (e) {
            if (e.target.closest('.pagination a')) {
                // Sẽ auto-scroll sau khi page load
            }
        });

        // Expose function globally for manual use
        window.autoScrollToProducts = autoScrollToProducts;

        // Initialize smooth dropdown animations
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize filter sections state
            const filterSections = ['category', 'price', 'promotion'];

            filterSections.forEach(sectionName => {
                const content = document.getElementById(sectionName + '-content');
                const chevron = document.getElementById(sectionName + '-chevron');

                if (content && chevron) {
                    // Set initial state - all sections visible by default
                    if (!content.classList.contains('filter-section-hidden') &&
                        !content.classList.contains('filter-section-visible')) {
                        content.classList.add('filter-section-visible');
                        chevron.classList.add('rotate-90');
                    }
                }
            });

            // Initialize subcategories state - all hidden by default
            const subcategoryContainers = document.querySelectorAll('[id^="subcategories-"]');
            subcategoryContainers.forEach(container => {
                if (!container.classList.contains('subcategory-hidden') &&
                    !container.classList.contains('subcategory-visible')) {
                    container.classList.add('subcategory-hidden');
                }
            });

            // Add smooth scroll behavior to filter interactions
            const filterHeaders = document.querySelectorAll('[onclick*="toggleFilterSection"]');
            filterHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    // Add a subtle bounce effect
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Add smooth interactions to subcategory toggles
            const subcategoryHeaders = document.querySelectorAll('[onclick*="toggleSubcategories"]');
            subcategoryHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    // Add a subtle bounce effect
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            console.log('✅ Smooth dropdown animations initialized successfully!');
        });

        // Function to scroll to products search input
        function scrollToProductsSearch() {
            const searchInput = document.getElementById('main-search-input');
            if (searchInput) {
                // Smooth scroll to search input
                searchInput.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Focus and select text after scroll
                setTimeout(() => {
                    searchInput.focus();
                    searchInput.select();
                }, 500);
            }
        }



        // Loading Skeleton for Filter Results Header
        function showFilterHeaderLoading() {
            const filterHeader = document.querySelector('.filter-results-header');
            if (filterHeader) {
                filterHeader.classList.add('loading');
            }
        }

        function hideFilterHeaderLoading() {
            const filterHeader = document.querySelector('.filter-results-header');
            if (filterHeader) {
                filterHeader.classList.remove('loading');
            }
        }

        // Smooth count badge animation
        function animateCountBadge() {
            const countBadge = document.getElementById('results-count');
            if (countBadge) {
                countBadge.classList.add('updating');
                setTimeout(() => {
                    countBadge.classList.remove('updating');
                }, 600);
            }
        }

        // Observer to detect count changes (for future AJAX updates)
        function observeCountChanges() {
            const countBadge = document.getElementById('results-count');
            if (countBadge && window.MutationObserver) {
                const observer = new MutationObserver(function (mutations) {
                    mutations.forEach(function (mutation) {
                        if (mutation.type === 'childList' || mutation.type === 'characterData') {
                            animateCountBadge();
                        }
                    });
                });

                observer.observe(countBadge, {
                    childList: true,
                    subtree: true,
                    characterData: true
                });
            }
        }

        // Add loading state to form submissions and filter changes
        document.addEventListener('DOMContentLoaded', function () {
            // Loading for search form submission
            const searchForms = document.querySelectorAll('form[action*="products.php"]');
            searchForms.forEach(form => {
                form.addEventListener('submit', function () {
                    showFilterHeaderLoading();
                });
            });

            // Loading for filter button clicks - DISABLED: Now handled by AJAX Filter module
            // const applyFiltersBtn = document.getElementById('applyFilters');
            // if (applyFiltersBtn) {
            //     applyFiltersBtn.addEventListener('click', function () {
            //         showFilterHeaderLoading();
            //     });
            // }



            // Loading for pagination links
            const paginationLinks = document.querySelectorAll('.page-link[href]');
            paginationLinks.forEach(link => {
                link.addEventListener('click', function () {
                    showFilterHeaderLoading();
                });
            });

            // Loading for sort/limit changes - DISABLED: Skeleton loading removed per user request
            // const sortSelect = document.querySelector('select[name="sort"]');
            // const limitSelect = document.querySelector('select[name="limit"]');

            // if (sortSelect) {
            //     sortSelect.addEventListener('change', function () {
            //         showFilterHeaderLoading();
            //     });
            // }

            // if (limitSelect) {
            //     limitSelect.addEventListener('change', function () {
            //         showFilterHeaderLoading();
            //     });
            // }

            // Initialize count change observer
            observeCountChanges();
        });

    </script>

    <!-- Filter Micro-interactions Implementation -->
    <script>
    // Global Filter State Manager
    class FilterStateManager {
        constructor() {
            this.urlParams = new URLSearchParams(window.location.search);
        }

        // Cập nhật URL params khi cần
        updateUrlParams() {
            this.urlParams = new URLSearchParams(window.location.search);
        }

        // Kiểm tra filters từ URL (actual applied filters)
        hasUrlFilters() {
            this.updateUrlParams();

            // Check keyword
            const keyword = this.urlParams.get('keyword');
            if (keyword && keyword.trim() !== '') return true;

            // Check categories từ URL
            const urlCategories = this.urlParams.getAll('category[]');
            if (urlCategories.length > 0) return true;

            // Check price từ URL
            const urlPriceMin = this.urlParams.get('price_min');
            const urlPriceMax = this.urlParams.get('price_max');
            if (urlPriceMin || urlPriceMax) return true;

            // Check promotions từ URL
            const urlPromotions = this.urlParams.getAll('promotion[]');
            if (urlPromotions.length > 0) return true;

            return false;
        }

        // Kiểm tra filters từ UI (sidebar checkboxes)
        hasUIFilters() {
            // Check categories
            const selectedCategories = document.querySelectorAll('input[name="category[]"]:checked');
            if (selectedCategories.length > 0) return true;

            // Check price range
            const priceMinInput = document.getElementById('price-min');
            const priceMaxInput = document.getElementById('price-max');
            const minPrice = getPriceNumericValue(priceMinInput);
            const maxPrice = getPriceNumericValue(priceMaxInput);
            if (minPrice || maxPrice) return true;

            // Check promotions
            const selectedPromotions = document.querySelectorAll('input[name="promotion[]"]:checked');
            if (selectedPromotions.length > 0) return true;

            return false;
        }

        // Kiểm tra có filters nào đang active (UI hoặc URL)
        hasActiveFilters() {
            return this.hasUrlFilters() || this.hasUIFilters();
        }

        // Đồng bộ UI với URL state
        syncUIWithURL() {
            this.updateUrlParams();

            // Sync categories
            const urlCategories = this.urlParams.getAll('category[]');
            const categoryInputs = document.querySelectorAll('input[name="category[]"]');
            categoryInputs.forEach(input => {
                input.checked = urlCategories.includes(input.value);
            });

            // Sync price
            const urlPriceMin = this.urlParams.get('price_min');
            const urlPriceMax = this.urlParams.get('price_max');
            const priceMinInput = document.getElementById('price-min');
            const priceMaxInput = document.getElementById('price-max');

            if (priceMinInput && urlPriceMin) {
                priceMinInput.value = formatCurrency(urlPriceMin);
            }
            if (priceMaxInput && urlPriceMax) {
                priceMaxInput.value = formatCurrency(urlPriceMax);
            }

            // Sync promotions
            const urlPromotions = this.urlParams.getAll('promotion[]');
            const promotionInputs = document.querySelectorAll('input[name="promotion[]"]');
            promotionInputs.forEach(input => {
                input.checked = urlPromotions.includes(input.value);
            });
        }

        // Debug info
        getDebugInfo() {
            return {
                hasUrlFilters: this.hasUrlFilters(),
                hasUIFilters: this.hasUIFilters(),
                hasActiveFilters: this.hasActiveFilters(),
                urlParams: Object.fromEntries(this.urlParams.entries()),
                currentUrl: window.location.href
            };
        }
    }

    // Global instance
    const filterStateManager = new FilterStateManager();

    // Make it available globally for AJAX Filter
    window.filterStateManager = filterStateManager;

    // Helper function for currency formatting
    function formatCurrency(value) {
        if (!value) return '';
        const numValue = parseInt(value);
        return numValue.toLocaleString('vi-VN') + ' đ';
    }

    // Legacy function for backward compatibility
    function hasActiveFilters() {
        return filterStateManager.hasActiveFilters();
    }

    // Function to show filter notification
    function showFilterNotification(message, type = 'warning', shouldScrollToNotification = false) {
        // Remove existing notification if any
        const existingNotification = document.getElementById('filter-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.id = 'filter-notification';
        notification.className = `filter-notification ${type === 'warning' ? 'bg-yellow-50 border-yellow-200 text-yellow-800' : 'bg-red-50 border-red-200 text-red-800'} border rounded-lg p-3 mt-3 flex items-center space-x-2 animate-pulse`;

        notification.innerHTML = `
            <i class="fas ${type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'} text-sm"></i>
            <span class="text-sm font-medium">${message}</span>
        `;

        // Insert notification after the buttons
        const buttonsContainer = document.querySelector('.pt-3.space-y-2\\.5');
        if (buttonsContainer) {
            buttonsContainer.appendChild(notification);

            // Always add highlight effect, but scroll only if requested
            setTimeout(() => {
                if (shouldScrollToNotification) {
                    notification.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'nearest'
                    });

                    // Add highlight effect after scrolling
                    setTimeout(() => {
                        notification.classList.add('highlight');
                    }, 500); // Wait for scroll to complete
                } else {
                    // Add highlight effect immediately without scrolling
                    notification.classList.add('highlight');
                }
            }, 100); // Small delay to ensure element is rendered

            // Auto remove after 4 seconds (increased time for better UX)
            setTimeout(() => {
                if (notification && notification.parentNode) {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }
            }, 4000);
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        console.log('Initializing filter micro-interactions...');

        // ===== APPLY FILTERS BUTTON MICRO-INTERACTIONS =====
        // DISABLED: Now handled by AJAX Filter module
        // const applyFiltersBtn = document.getElementById('applyFilters');
        // if (applyFiltersBtn) {
        //     // Store original click handler
        //     const originalHandler = applyFiltersBtn.onclick;

        //     // Override click handler with micro-interactions
        //     applyFiltersBtn.onclick = function(e) {
        //         e.preventDefault();
        //         e.stopPropagation();

        //         // Check if any filters are selected first
        //         if (!hasActiveFilters()) {
        //             showFilterNotification('Vui lòng chọn ít nhất một bộ lọc trước khi áp dụng!', 'warning', false);
        //             return false;
        //         }

        //         // Start loading state with enhanced UX
        //         applyFiltersBtn.classList.add('loading');
        //         const btnText = applyFiltersBtn.querySelector('.btn-text');
        //         const originalText = btnText.textContent;

        //         // Enhanced loading text with animation
        //         btnText.textContent = 'Đang xử lý';

        //         // Add dots animation
        //         let dotCount = 0;
        //         const loadingInterval = setInterval(() => {
        //             dotCount = (dotCount + 1) % 4;
        //             btnText.textContent = 'Đang xử lý' + '.'.repeat(dotCount);
        //         }, 400);

        //         // Simulate processing time then show success
        //         setTimeout(function() {
        //             clearInterval(loadingInterval);

        //             // Smooth transition to success state
        //             applyFiltersBtn.classList.remove('loading');
        //             applyFiltersBtn.classList.add('success');
        //             btnText.textContent = '✓ Áp dụng thành công!';

        //             // Collect filter data and redirect after success animation
        //             setTimeout(function() {
        //                 try {
        //                     // Collect filter data (same as original logic)
        //                     let url = 'products.php?';

        //                     // Preserve current keyword if exists
        //                     const urlParams = new URLSearchParams(window.location.search);
        //                     const keyword = urlParams.get('keyword');
        //                     if (keyword && keyword.trim() !== '') {
        //                         url += 'keyword=' + encodeURIComponent(keyword) + '&';
        //                     }

        //                     // Categories
        //                     const selectedCategories = Array.from(document.querySelectorAll('input[name="category[]"]:checked')).map(input => input.value);
        //                     if (selectedCategories.length > 0) {
        //                         url += 'category=' + selectedCategories.join(',') + '&';
        //                     }

        //                     // Price range
        //                     const priceMin = document.getElementById('priceMin')?.value;
        //                     const priceMax = document.getElementById('priceMax')?.value;
        //                     if (priceMin) url += 'price_min=' + priceMin + '&';
        //                     if (priceMax) url += 'price_max=' + priceMax + '&';

        //                     // Promotions
        //                     const selectedPromotions = Array.from(document.querySelectorAll('input[name="promotion[]"]:checked')).map(input => input.value);
        //                     if (selectedPromotions.length > 0) {
        //                         url += 'promotion=' + selectedPromotions.join(',') + '&';
        //                     }

        //                     // Sort
        //                     const sortBy = document.getElementById('sortBy')?.value;
        //                     if (sortBy) url += 'sort=' + sortBy + '&';

        //                     // Items per page
        //                     const itemsPerPage = document.getElementById('itemsPerPage')?.value;
        //                     if (itemsPerPage) url += 'limit=' + itemsPerPage + '&';

        //                     // Add scroll parameter
        //                     url += 'scroll=1';

        //                     // Navigate to new URL
        //                     window.location.href = url;
        //                 } catch (error) {
        //                     // Error handling - reset button state
        //                     console.error('Filter application error:', error);
        //                     applyFiltersBtn.classList.remove('loading', 'success');
        //                     btnText.textContent = originalText;
        //                     showFilterNotification('Có lỗi xảy ra. Vui lòng thử lại!', 'error', false);
        //                 }
        //             }, 1000); // Wait for success animation to complete
        //         }, 1200); // Realistic processing time
        //     };
        // }

        // ===== RESET FILTERS BUTTON MICRO-INTERACTIONS =====
        // DISABLED: This was causing duplicate event handlers and incorrect notifications
        // The main reset logic is handled in the individual button event listeners above
        /*
        const resetButtons = document.querySelectorAll('.reset-filters-btn');
        resetButtons.forEach(function(btn) {
            // Store original click handler
            const originalHandler = btn.onclick;

            btn.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Check if any filters are selected first
                if (!hasActiveFilters()) {
                    // Check if this is the small reset button (w-12 h-12) or large button (w-full)
                    const isSmallButton = btn.classList.contains('w-12');
                    showFilterNotification('Không có bộ lọc nào để đặt lại!', 'warning', isSmallButton);
                    return false;
                }
        */

                /*
                // Start reset animation
                btn.classList.add('resetting');

                // Fade out filter tags
                const filterTags = document.querySelectorAll('.filter-tag');
                filterTags.forEach(function(tag, index) {
                    setTimeout(function() {
                        tag.classList.add('fade-out');
                    }, index * 100); // Stagger the fade out
                });

                // Navigate after animation
                setTimeout(function() {
                    window.location.href = 'products.php?scroll=1';
                }, 800);
            };
        });
        */

        // ===== CHECKBOX BOUNCE & RIPPLE EFFECTS =====
        const checkboxes = document.querySelectorAll('.custom-checkbox');

        // Function to trigger checkbox animations (make it global)
        window.triggerCheckboxAnimations = function(checkbox) {
            // Add bounce effect
            checkbox.classList.add('bounce');

            // Remove bounce class after animation
            setTimeout(function() {
                checkbox.classList.remove('bounce');
            }, 400);

            // Add ripple effect
            checkbox.classList.add('ripple');

            // Remove ripple class after animation
            setTimeout(function() {
                checkbox.classList.remove('ripple');
            }, 600);
        };

        checkboxes.forEach(function(checkbox) {
            // Trigger animations on checkbox change
            checkbox.addEventListener('change', function() {
                window.triggerCheckboxAnimations(checkbox);
            });

            // Add click ripple effect (for visual feedback on click)
            checkbox.addEventListener('mousedown', function() {
                checkbox.classList.add('ripple');
                setTimeout(function() {
                    checkbox.classList.remove('ripple');
                }, 600);
            });
        });

        // ===== LABEL CLICK ANIMATIONS FOR CHECKBOXES =====
        // Handle clicks on labels that contain checkboxes (for promotion checkboxes)
        const checkboxLabels = document.querySelectorAll('label');
        checkboxLabels.forEach(function(label) {
            const checkbox = label.querySelector('.custom-checkbox');
            if (checkbox) {
                label.addEventListener('click', function(e) {
                    // Only trigger if clicking on the label text, not the checkbox itself
                    if (e.target !== checkbox) {
                        // Trigger animations immediately for better UX
                        window.triggerCheckboxAnimations(checkbox);
                    }
                });
            }
        });

        // Note: Category checkbox text clicks are handled by existing toggleCategoryCheckbox() function

        console.log('Filter micro-interactions initialized successfully!');
    });
    </script>

    <?php
    // Thêm script giỏ hàng thời gian thực
    echo '<script src="' . BASE_URL . '/assets/js/cart-realtime.js"></script>';

    // Thêm script cho recently viewed và trending products
    echo '<script src="' . BASE_URL . '/assets/js/recently-viewed-trending.js"></script>';
    ?>

    <!-- Initialize AJAX Filter -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing AJAX Filter...');

            // Khởi tạo AJAX Filter
            if (typeof AjaxFilter !== 'undefined') {
                window.ajaxFilter = new AjaxFilter();
                console.log('AJAX Filter initialized successfully');
            } else {
                console.warn('AjaxFilter class not found');
            }
        });
    </script>

    <?php
    // Include footer
    include_once 'partials/footer.php';
    ?>