/*
 * Tablet Mega Menu CSS for Nội Thất Bàng Vũ
 * Hybrid Approach: Modal for Portrait, Slide-out for Landscape
 */

/* TABLET MEGA MENU - SMART DETECTION - HIGH PRIORITY */
@media (min-width: 768px) and (max-width: 1200px) {

    /* FORCE ALL TABLET MEGA MENU STYLES - HIGHEST PRIORITY */
    .tablet-mega-menu,
    .tablet-mega-menu *,
    .tablet-mega-menu *::before,
    .tablet-mega-menu *::after {
        box-sizing: border-box !important;
    }

    /* Ensure all tablet mega menu elements are visible and styled ONLY when active */
    .tablet-mega-menu.active .tablet-mega-menu-content,
    .tablet-mega-menu.active .tablet-mega-menu-header,
    .tablet-mega-menu.active .tablet-mega-menu-body,
    .tablet-mega-menu.active .tablet-mega-menu-main,
    .tablet-mega-menu.active .tablet-mega-menu-categories,
    .tablet-mega-menu.active .tablet-mega-menu-content-area,
    .tablet-mega-menu.active .tablet-mega-category,
    .tablet-mega-menu.active .tablet-mega-subcategories,
    .tablet-mega-menu.active .tablet-mega-subcategory {
        visibility: visible !important;
    }

    /* Override specific display types ONLY when active */
    .tablet-mega-menu.active .tablet-mega-menu-main {
        display: flex !important;
    }

    .tablet-mega-menu.active .tablet-mega-menu-categories {
        display: block !important;
    }

    .tablet-mega-menu.active .tablet-mega-subcategories {
        display: grid !important;
    }

    .tablet-mega-menu.active .tablet-mega-category {
        display: flex !important;
    }

    .tablet-mega-menu.active .tablet-mega-content:not(.active) {
        display: none !important;
    }

    .tablet-mega-menu.active .tablet-mega-content.active {
        display: block !important;
    }

    /* Override the hidden mega menu from tablet-navigation-scroll.css */
    .mega-menu {
        display: none !important;
    }

    /* Hide desktop mega menu on touch devices in tablet range */
    @media (pointer: coarse) {
        .mega-menu {
            display: none !important;
        }

        .nav-item:hover .mega-menu {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }
    }

    /* Additional targeting for JavaScript-detected touch devices */
    body.touch-device .mega-menu {
        display: none !important;
    }

    body.touch-device .nav-item:hover .mega-menu {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }

    /* FORCE HIDE ALL CSS EFFECTS when mega menu is closed */
    .nav-item.tablet-mega-closed .nav-link::before,
    .nav-item.tablet-mega-closed .nav-link::after {
        display: none !important;
        opacity: 0 !important;
        width: 0 !important;
        height: 0 !important;
        transform: scale(0) !important;
        visibility: hidden !important;
    }

    .nav-item.tablet-mega-closed .nav-link {
        background: none !important;
        box-shadow: none !important;
        border: none !important;
        outline: none !important;
    }


    
    /* Tablet Mega Menu Container */
    .tablet-mega-menu {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        display: none; /* Hidden by default */
        align-items: flex-end; /* Align content to bottom */
        justify-content: center;
        transition: opacity 0.3s cubic-bezier(0.25, 0.8, 0.25, 1), visibility 0.3s;
        /* Remove heavy blur effects for better performance */
        /* backdrop-filter: blur(4px); */
        /* -webkit-backdrop-filter: blur(4px); */
        /* Ensure it's above everything */
        isolation: isolate;
        transform: translateZ(0);
        will-change: transform, opacity;
    }

    /* Force z-index to be higher than any other element */
    .tablet-mega-menu {
        z-index: 10000 !important;
    }

    .tablet-mega-menu.active {
        opacity: 1;
        visibility: visible;
        display: flex; /* Show modal */
        z-index: 10000; /* Higher than header but reasonable */
    }

    /* Simple body scroll lock without position fixed */
    body.tablet-mega-menu-open {
        overflow: hidden !important;
        touch-action: none !important;
    }

    /* DEBUG: Force visibility for troubleshooting */
    .tablet-mega-menu.active {
        display: flex !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .tablet-mega-menu.active .tablet-mega-menu-content {
        display: block !important;
        visibility: visible !important;
    }
    
    /* Mega Menu Content Container - HIGH PRIORITY */
    .tablet-mega-menu-content {
        position: absolute !important;
        background: white !important;
        border-radius: 16px 16px 0 0 !important;
        box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.15) !important;
        overflow: hidden !important;
        transform: translateY(100%) !important;
        transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
        z-index: 10001 !important;
        isolation: isolate !important;
    }
    
    .tablet-mega-menu.active .tablet-mega-menu-content {
        transform: translateY(0) !important;
    }
    
    /* Modal Style for ALL tablet orientations */
        .tablet-mega-menu-content {
            bottom: 0;
            left: 0;
            right: 0;
            max-height: 75vh;
            min-height: 50vh;
        }

        /* Adjust for smaller tablets */
        @media (max-height: 800px) {
            .tablet-mega-menu-content {
                max-height: 80vh;
            }
        }
        
        .tablet-mega-menu-header {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            padding: 20px 24px 16px !important;
            border-bottom: 1px solid #e5e7eb !important;
            background: linear-gradient(135deg, #f97316 0%, #fb923c 100%) !important;
            color: white !important;
        }
        
        .tablet-mega-menu-title {
            font-size: 18px !important;
            font-weight: 600 !important;
            margin: 0 !important;
        }

        .tablet-mega-menu-close {
            background: rgba(255, 255, 255, 0.2) !important;
            border: none !important;
            border-radius: 50% !important;
            width: 36px !important;
            height: 36px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            color: white !important;
            font-size: 16px !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
        }
        
        .tablet-mega-menu-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
        
        .tablet-mega-menu-body {
            padding: 0 !important;
            max-height: calc(75vh - 80px) !important;
            overflow: hidden !important;
            display: flex !important;
            flex-direction: column !important;
            background: white !important;
            min-height: 400px !important;
        }

        /* Ensure content area can scroll on small screens */
        @media (max-height: 700px) {
            .tablet-mega-menu-body {
                max-height: calc(85vh - 80px);
                min-height: 350px;
            }
        }

        @media (max-height: 600px) {
            .tablet-mega-menu-body {
                max-height: calc(90vh - 80px);
                min-height: 300px;
            }
        }

        /* Desktop-like 2-column layout */
        .tablet-mega-menu-main {
            display: flex !important;
            height: 100% !important;
            min-height: 400px !important;
            flex: 1 !important;
            position: relative !important;
            contain: layout style !important;
        }

        /* Left Column - Categories */
        .tablet-mega-menu-categories {
            width: 30% !important;
            background-color: #f8fafc !important;
            border-right: 1px solid #e5e7eb !important;
            overflow-y: auto !important;
            padding: 0 !important;
        }

        .tablet-mega-category {
            display: flex !important;
            align-items: center !important;
            padding: 16px 20px !important;
            cursor: pointer !important;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
            border-left: 3px solid transparent !important;
            border-bottom: 1px solid rgba(229, 231, 235, 0.5) !important;
            position: relative !important;
            overflow: hidden !important;
        }

        /* Ripple effect for categories */
        .tablet-mega-category::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s ease, height 0.6s ease;
        }

        .tablet-mega-category:active::before {
            width: 300px;
            height: 300px;
        }

        .tablet-mega-category:hover,
        .tablet-mega-category.active {
            background-color: white;
            border-left-color: #f97316;
        }

        .tablet-mega-category-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #3b82f6; /* Blue accent thay vì cam */
        }

        .tablet-mega-category-name {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .tablet-mega-category:hover .tablet-mega-category-name,
        .tablet-mega-category.active .tablet-mega-category-name {
            color: #f97316;
        }

        /* Right Column - Content */
        .tablet-mega-menu-content-area {
            width: 70% !important;
            padding: 20px !important;
            overflow-y: auto !important;
            background: white !important;
            position: relative !important;
            /* Ensure scrolling works on small screens */
            max-height: 100% !important;
            flex: 1 !important;
            /* Prevent layout shifts during tab switching */
            min-height: 400px !important;
            contain: layout style !important;
        }

        .tablet-mega-content {
            display: none !important;
            opacity: 0 !important;
            transition: opacity 0.3s ease !important;
            position: relative !important;
        }

        .tablet-mega-content.active {
            display: block !important;
            opacity: 1 !important;
            /* Overflow will be controlled by JavaScript to prevent scrollbar flicker */
        }

        /* Simplified animation for content elements */
        .tablet-mega-content.active .tablet-mega-content-title {
            animation: slideInFade 0.3s ease-out both;
        }

        .tablet-mega-content.active .tablet-mega-subcategories {
            animation: slideInFade 0.3s ease-out 0.05s both;
        }

        .tablet-mega-content.active .tablet-mega-bestsellers {
            animation: slideInFade 0.3s ease-out 0.1s both;
        }

        .tablet-mega-content.active .tablet-mega-view-all {
            animation: slideInFade 0.3s ease-out 0.15s both;
        }

        /* Keyframes for animations */
        @keyframes slideInFade {
            0% {
                opacity: 0;
                transform: scale(0.95);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .tablet-mega-content-title {
            font-size: 18px !important;
            font-weight: 600 !important;
            color: #374151 !important;
            margin-bottom: 16px !important;
            padding-bottom: 8px !important;
            border-bottom: 1px solid #e5e7eb !important;
        }

        /* Subcategories */
        .tablet-mega-subcategories {
            display: grid !important;
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 12px !important;
            margin-bottom: 20px !important;
        }

        .tablet-mega-subcategory {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            padding: 12px;
            border-radius: 8px;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            text-align: center;
            background: #f8fafc;
            position: relative;
            overflow: hidden;
        }

        /* Subtle scale and glow effect */
        .tablet-mega-subcategory::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 8px;
        }

        .tablet-mega-subcategory:hover {
            background-color: rgba(59, 130, 246, 0.1); /* Blue hover */
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }

        .tablet-mega-subcategory:hover::after {
            opacity: 1;
        }

        .tablet-mega-subcategory-icon {
            width: 32px;
            height: 32px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #3b82f6; /* Blue accent */
            background-color: rgba(59, 130, 246, 0.1); /* Blue background */
            border-radius: 50%;
        }

        .tablet-mega-subcategory-image {
            width: 80px;
            height: 80px;
            margin-bottom: 10px;
            border-radius: 10px;
            overflow: hidden;
        }

        .tablet-mega-subcategory-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .tablet-mega-subcategory-image img[src]:not([src=""]) {
            opacity: 1;
        }

        .tablet-mega-subcategory-name {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
        }

        .tablet-mega-subcategory:hover .tablet-mega-subcategory-name {
            color: #3b82f6; /* Blue text on hover */
        }

        /* Bestsellers */
        .tablet-mega-bestsellers {
            margin-top: 20px;
        }

        .tablet-mega-bestsellers-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .tablet-mega-bestsellers-title i {
            color: #10b981; /* Green cho fire icon */
            margin-right: 8px;
        }

        .tablet-mega-products {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .tablet-mega-product {
            text-decoration: none;
            display: block;
            background: #f8fafc;
            border-radius: 8px;
            padding: 8px;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            overflow: hidden;
        }

        /* Product hover glow effect */
        .tablet-mega-product::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(16, 185, 129, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 8px;
        }

        .tablet-mega-product:hover {
            background: rgba(59, 130, 246, 0.05); /* Subtle blue hover */
            transform: translateY(-2px) scale(1.01);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.1);
        }

        .tablet-mega-product:hover::before {
            opacity: 1;
        }

        .tablet-mega-product-image {
            width: 100%;
            height: 0;
            padding-bottom: 100%; /* Tạo tỷ lệ 1:1 (hình vuông) */
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 8px;
            background: #e5e7eb;
            position: relative;
        }

        .tablet-mega-product-image img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .tablet-mega-product-image img[src]:not([src=""]) {
            opacity: 1;
        }

        .tablet-mega-product-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
        }

        .tablet-mega-product-name {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .tablet-mega-product-price {
            font-size: 12px;
            font-weight: 600;
            color: #f97316;
        }

        .tablet-mega-view-all {
            display: inline-block;
            margin-top: 16px;
            margin-bottom: 16px; /* Ensure space at bottom */
            font-size: 14px;
            font-weight: 500;
            color: #3b82f6; /* Blue color */
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px; /* Bo góc consistent với design system */
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            border: 1px solid rgba(59, 130, 246, 0.3); /* Blue border */
            background-color: rgba(59, 130, 246, 0.05); /* Subtle blue background */
            position: relative;
            overflow: hidden;
        }

        /* Button shine effect */
        .tablet-mega-view-all::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .tablet-mega-view-all:hover::before {
            left: 100%;
        }

        .tablet-mega-view-all:hover {
            background-color: #3b82f6; /* Blue background on hover */
            color: white;
            border-color: #3b82f6;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        /* All Products Button Section */
        .tablet-mega-all-products-section {
            padding: 16px 20px;
            border-top: 1px solid #e5e7eb;
            background: #f8fafc;
        }

        .tablet-mega-all-products-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 12px 16px;
            background: linear-gradient(135deg, #f97316, #fb923c);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(249, 115, 22, 0.2);
        }

        .tablet-mega-all-products-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(249, 115, 22, 0.3);
            background: linear-gradient(135deg, #ea580c, #f97316);
        }

        .tablet-mega-all-products-btn i {
            margin-right: 8px;
            font-size: 16px;
        }

        .tablet-mega-all-products-btn span {
            font-weight: 600;
        }

        /* Hide old layouts */
        .tablet-mega-categories-grid,
        .tablet-mega-categories-list,
        .tablet-mega-all-products {
            display: none !important;
        }

        /* Ensure main layout is visible */
        .tablet-mega-menu-main {
            display: flex !important;
        }



        /* Simple layout for very small tablets */
        .tablet-mega-simple-layout {
            padding: 20px;
            display: none;
        }

        .tablet-mega-simple-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            text-decoration: none;
            margin-bottom: 12px;
            transition: all 0.2s ease;
        }

        .tablet-mega-simple-item:hover {
            background: rgba(59, 130, 246, 0.1); /* Blue hover */
            transform: translateX(4px);
        }

        .tablet-mega-simple-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #3b82f6; /* Blue icons */
        }

        .tablet-mega-simple-name {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .tablet-mega-simple-item:hover .tablet-mega-simple-name {
            color: #3b82f6; /* Blue text on hover */
        }

        /* Responsive adjustments for different tablet sizes */

        /* Large tablets (1000px+) - More columns and larger content */
        @media (min-width: 1000px) {
            .tablet-mega-menu-categories {
                width: 25%; /* Narrower sidebar for more content space */
            }

            .tablet-mega-menu-content-area {
                width: 75%;
                padding: 24px;
            }

            .tablet-mega-subcategories {
                grid-template-columns: repeat(4, 1fr); /* 4 columns for large tablets */
                gap: 16px;
            }

            .tablet-mega-products {
                grid-template-columns: repeat(4, 1fr); /* 4 columns for products */
                gap: 16px;
            }

            .tablet-mega-content-title {
                font-size: 20px;
                margin-bottom: 20px;
            }

            .tablet-mega-subcategory-image {
                width: 90px;
                height: 90px;
            }
        }

        /* Medium tablets (900px - 999px) - 3 columns */
        @media (min-width: 900px) and (max-width: 999px) {
            .tablet-mega-menu-categories {
                width: 30%;
            }

            .tablet-mega-menu-content-area {
                width: 70%;
                padding: 20px;
            }

            .tablet-mega-subcategories {
                grid-template-columns: repeat(3, 1fr);
                gap: 14px;
            }

            .tablet-mega-products {
                grid-template-columns: repeat(3, 1fr);
                gap: 14px;
            }

            .tablet-mega-subcategory-image {
                width: 85px;
                height: 85px;
            }
        }

        /* Small-medium tablets (800px - 899px) - 2 columns with better spacing */
        @media (min-width: 800px) and (max-width: 899px) {
            .tablet-mega-menu-categories {
                width: 35%;
            }

            .tablet-mega-menu-content-area {
                width: 65%;
                padding: 18px;
            }

            .tablet-mega-subcategories {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .tablet-mega-products {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }
        }

        /* Small tablets (768px - 799px) - Compact layout */
        @media (max-width: 799px) {
            .tablet-mega-menu-categories {
                width: 35%;
            }

            .tablet-mega-menu-content-area {
                width: 65%;
                padding: 16px;
            }

            .tablet-mega-subcategories {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .tablet-mega-products {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }
        }

        /* Switch to simple layout for very small tablets */
        @media (max-width: 850px) {
            .tablet-mega-menu-main {
                display: none;
            }

            .tablet-mega-simple-layout {
                display: block;
            }
        }

        /* Adjustments for small tablets */
        @media (max-width: 800px) {
            .tablet-mega-menu-content {
                max-height: 80vh; /* Increase height for small tablets */
            }

            .tablet-mega-menu-body {
                max-height: calc(80vh - 80px);
            }

            .tablet-mega-menu-categories {
                width: 40%;
            }

            .tablet-mega-menu-content-area {
                width: 60%;
                padding: 12px;
                /* Ensure proper scrolling */
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
            }

            .tablet-mega-category {
                padding: 12px 16px;
            }

            .tablet-mega-category-name {
                font-size: 13px;
            }

            /* Reduce spacing to fit more content */
            .tablet-mega-subcategories {
                margin-bottom: 16px;
                gap: 8px;
            }

            .tablet-mega-bestsellers {
                margin-top: 16px;
            }

            .tablet-mega-view-all {
                margin-top: 12px;
                margin-bottom: 12px;
                padding: 6px 12px;
                font-size: 13px;
            }
        }

        /* Extra small tablets and short screens */
        @media (max-height: 650px) {
            .tablet-mega-menu-content {
                max-height: 85vh;
            }

            .tablet-mega-menu-body {
                max-height: calc(85vh - 70px);
            }

            .tablet-mega-menu-content-area {
                padding: 10px;
            }

            .tablet-mega-subcategories {
                margin-bottom: 12px;
                gap: 6px;
            }

            .tablet-mega-bestsellers {
                margin-top: 12px;
            }

            .tablet-mega-bestsellers-title {
                font-size: 13px;
                margin-bottom: 8px;
            }
        }

        .tablet-mega-category-card:nth-child(1) { animation-delay: 0.1s; }
        .tablet-mega-category-card:nth-child(2) { animation-delay: 0.15s; }
        .tablet-mega-category-card:nth-child(3) { animation-delay: 0.2s; }
        .tablet-mega-category-card:nth-child(4) { animation-delay: 0.25s; }
        .tablet-mega-category-card:nth-child(5) { animation-delay: 0.3s; }
        .tablet-mega-category-card:nth-child(6) { animation-delay: 0.35s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .tablet-mega-category-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 16px;
            background: #f8fafc;
            border-radius: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .tablet-mega-category-card:hover {
            background: #f1f5f9;
            border-color: #f97316;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(249, 115, 22, 0.15);
        }
        
        .tablet-mega-category-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #f97316, #fb923c);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
        }
        
        .tablet-mega-category-card:hover .tablet-mega-category-icon {
            transform: scale(1.1);
            box-shadow: 0 4px 16px rgba(249, 115, 22, 0.3);
        }
        
        .tablet-mega-category-name {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            text-align: center;
            line-height: 1.4;
        }
        
        .tablet-mega-all-products {
            background: linear-gradient(135deg, #f97316, #fb923c);
            color: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 8px;
        }
        
        .tablet-mega-all-products:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(249, 115, 22, 0.3);
        }

    /* Responsive adjustments for landscape - just more columns */
    @media (orientation: landscape) {
        /* More columns for wider screens */
        .tablet-mega-subcategories {
            grid-template-columns: repeat(4, 1fr) !important;
        }

        .tablet-mega-products {
            grid-template-columns: repeat(4, 1fr) !important;
        }
    }




    /* Smooth scrolling and performance optimizations */
    .tablet-mega-menu-categories,
    .tablet-mega-menu-content-area {
        -webkit-overflow-scrolling: touch;
        scrollbar-width: thin;
        scrollbar-color: rgba(249, 115, 22, 0.3) transparent;
        /* Performance optimizations */
        will-change: scroll-position;
        transform: translateZ(0);
    }

    .tablet-mega-menu-categories::-webkit-scrollbar,
    .tablet-mega-menu-content-area::-webkit-scrollbar {
        width: 4px;
    }

    .tablet-mega-menu-categories::-webkit-scrollbar-track,
    .tablet-mega-menu-content-area::-webkit-scrollbar-track {
        background: transparent;
    }

    .tablet-mega-menu-categories::-webkit-scrollbar-thumb,
    .tablet-mega-menu-content-area::-webkit-scrollbar-thumb {
        background: rgba(249, 115, 22, 0.3);
        border-radius: 2px;
    }

    .tablet-mega-menu-categories::-webkit-scrollbar-thumb:hover,
    .tablet-mega-menu-content-area::-webkit-scrollbar-thumb:hover {
        background: rgba(249, 115, 22, 0.5);
    }

    /* Performance optimizations for animations */
    .tablet-mega-category,
    .tablet-mega-subcategory,
    .tablet-mega-product,
    .tablet-mega-content {
        will-change: transform, opacity;
        transform: translateZ(0);
    }

    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
        .tablet-mega-content,
        .tablet-mega-category,
        .tablet-mega-subcategory,
        .tablet-mega-product {
            transition: none;
        }
    }
}



/* DESKTOP - Ensure no interference */
@media (min-width: 1025px) {
    .tablet-mega-menu {
        display: none !important;
    }
}

/* MOBILE - Ensure no interference */
@media (max-width: 767px) {
    .tablet-mega-menu {
        display: none !important;
    }
}
