/* CSS cho phần hiển thị thông tin tác giả */
.blog-author-box {
    margin-top: 3.5rem;
    margin-bottom: 3.5rem;
    border-radius: 12px;
    background-color: #fff;
    border: none;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
}

/* <PERSON>h trang trí phía trên */
.blog-author-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #F37321, #ff9a5a);
}

.blog-author-box-header {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1.8rem;
    padding: 1.5rem 2rem 0;
    color: #333;
    position: relative;
    display: inline-block;
}

.blog-author-box-header::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 2rem;
    width: 40px;
    height: 3px;
    background-color: #F37321;
    border-radius: 3px;
}

.blog-author-content {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    padding: 0 2rem 2rem;
}

.blog-author-avatar {
    flex-shrink: 0;
    width: 140px;
    height: 140px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-author-avatar:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.blog-author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.blog-author-avatar:hover img {
    transform: scale(1.05);
}

.blog-author-info {
    flex-grow: 1;
    position: relative;
}

.blog-author-name {
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 0.4rem;
    color: #222;
    position: relative;
    display: inline-block;
}

.blog-author-position {
    font-size: 1.05rem;
    font-weight: 500;
    margin-bottom: 1.2rem;
    color: #F37321;
    font-style: italic;
    letter-spacing: 0.5px;
}

.blog-author-bio {
    margin-bottom: 1.5rem;
    line-height: 1.7;
    color: #444;
    font-size: 1.02rem;
    padding-bottom: 1.2rem;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

.blog-author-section {
    margin-bottom: 1.5rem;
    padding-top: 1rem;
    position: relative;
}

.blog-author-section-title {
    font-size: 1.05rem;
    font-weight: 600;
    margin-bottom: 0.7rem;
    color: #333;
    display: flex;
    align-items: center;
}

.blog-author-section-title::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #F37321;
    border-radius: 50%;
    margin-right: 8px;
}

.blog-author-section-content {
    font-size: 0.98rem;
    line-height: 1.7;
    color: #555;
    padding-left: 14px;
    border-left: 2px solid rgba(243, 115, 33, 0.2);
}

.blog-author-social {
    display: flex;
    gap: 0.9rem;
    margin-top: 1.5rem;
}

.blog-author-social a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f8f8f8;
    color: #555;
    border: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.blog-author-social a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(0);
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.blog-author-social a:hover::before {
    transform: scale(1);
}

.blog-author-social a i {
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
}

.blog-author-social a:hover {
    transform: translateY(-3px);
    color: #fff;
}

/* Màu khác nhau cho các mạng xã hội */
.blog-author-social a[aria-label="Facebook"]:hover {
    background-color: #1877F2;
    box-shadow: 0 5px 15px rgba(24, 119, 242, 0.3);
}

.blog-author-social a[aria-label="Twitter"]:hover {
    background-color: #1DA1F2;
    box-shadow: 0 5px 15px rgba(29, 161, 242, 0.3);
}

.blog-author-social a[aria-label="LinkedIn"]:hover {
    background-color: #0A66C2;
    box-shadow: 0 5px 15px rgba(10, 102, 194, 0.3);
}

.blog-author-social a[aria-label="Instagram"]:hover {
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    box-shadow: 0 5px 15px rgba(225, 48, 108, 0.3);
}

.blog-author-social a[aria-label="Zalo"]:hover {
    background-color: #0068FF;
    box-shadow: 0 5px 15px rgba(0, 104, 255, 0.3);
}

.blog-author-website {
    display: inline-flex;
    align-items: center;
    margin-top: 1rem;
    margin-bottom: 1rem;
    color: #F37321;
    font-size: 0.95rem;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background-color: rgba(243, 115, 33, 0.08);
}

.blog-author-website:hover {
    color: #fff;
    background-color: #F37321;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.25);
}

.blog-author-website i {
    margin-right: 0.6rem;
}

/* Responsive */
@media (max-width: 768px) {
    .blog-author-content {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 0 1.5rem 1.5rem;
    }

    .blog-author-box-header {
        padding: 1.2rem 1.5rem 0;
        margin-bottom: 1.5rem;
    }

    .blog-author-box-header::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .blog-author-avatar {
        margin-bottom: 1.5rem;
        width: 120px;
        height: 120px;
    }

    .blog-author-social {
        justify-content: center;
    }

    .blog-author-website {
        justify-content: center;
        margin-left: auto;
        margin-right: auto;
        display: flex;
    }

    .blog-author-section-content {
        border-left: none;
        padding-left: 0;
        text-align: center;
    }

    .blog-author-section-title {
        justify-content: center;
    }

    .blog-author-section-title::before {
        display: none;
    }
}

@media (max-width: 480px) {
    .blog-author-avatar {
        width: 100px;
        height: 100px;
    }

    .blog-author-name {
        font-size: 1.4rem;
    }

    .blog-author-position {
        font-size: 0.95rem;
    }

    .blog-author-box {
        margin-top: 2.5rem;
        margin-bottom: 2.5rem;
    }
}
