/*
 * Search Page CSS for Nội Thất Băng Vũ
 * Thiết kế trang tìm kiếm hiện đại với design system nhất quán
 */

/* Import CSS Variables từ design system */
:root {
    /* Primary Colors - Cam chủ đạo */
    --primary: #F37321;
    --primary-dark: #D35400;
    --primary-light: #FF9D5C;
    --primary-ultra-light: #FFF0E8;

    /* Neutral Colors */
    --dark: #1A1A1A;
    --dark-gray: #333333;
    --medium-gray: #666666;
    --light-gray: #CCCCCC;
    --ultra-light-gray: #F8F8F8;
    --white: #FFFFFF;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* Section Title Badge - Giống profile.php */
.section-title-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(243, 115, 33, 0.1);
    color: var(--primary);
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    margin-bottom: 1rem;
    transition: var(--transition-normal);
}

.section-title-badge:hover {
    background-color: rgba(243, 115, 33, 0.15);
    transform: translateY(-1px);
}

/* Animate pulse dot */
.section-title-badge .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

/* Page Title với accent line */
.search-page-title {
    position: relative;
    display: inline-block;
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 1rem;
}

.search-page-title .accent-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background-color: rgba(243, 115, 33, 0.3);
    border-radius: 2px;
}

/* Search Header Description */
.search-description {
    color: var(--medium-gray);
    font-size: 1rem;
    line-height: 1.6;
    margin-top: 0.5rem;
}

.search-description strong {
    color: var(--primary);
    font-weight: 600;
}

/* Enhanced Search Form */
.search-input-container {
    position: relative;
}

.search-input-enhanced {
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: 1rem;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
    /* Đảm bảo text không tràn ra icon */
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.search-input-enhanced:focus {
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1), var(--shadow-md);
    transform: translateY(-1px);
}

.clear-search-btn {
    transition: all 0.2s ease;
    border-radius: 50%;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    z-index: 10;
    /* Đảm bảo icon luôn hiển thị trên text */
    background-color: rgba(255, 255, 255, 0.9);
    /* Thêm background để tách biệt với text */
    backdrop-filter: blur(2px);
}

.clear-search-btn:hover {
    transform: translateY(-50%) scale(1.1);
    background-color: rgba(107, 114, 128, 0.1);
    color: #374151 !important;
}

.clear-search-btn:active {
    transform: translateY(-50%) scale(0.95);
}

/* Responsive adjustments cho search input */
@media (max-width: 768px) {
    .search-input-enhanced {
        padding-right: 3.5rem !important;
        /* Tăng padding-right trên tablet */
        font-size: 0.9rem;
    }

    .clear-search-btn {
        right: 0.75rem;
        /* Điều chỉnh vị trí icon */
        width: 28px;
        height: 28px;
    }
}

@media (max-width: 480px) {
    .search-input-enhanced {
        padding-right: 4rem !important;
        /* Tăng padding-right trên mobile */
        font-size: 0.85rem;
        padding-left: 2.5rem !important;
        /* Giảm padding-left để có thêm không gian */
    }

    .clear-search-btn {
        right: 0.5rem;
        /* Điều chỉnh vị trí icon */
        width: 32px;
        height: 32px;
    }

    /* Điều chỉnh search icon */
    .search-input-container .absolute.left-4 {
        left: 0.5rem;
    }
}

/* Đảm bảo search input container có position relative */
.search-input-container {
    position: relative;
    display: block;
}

/* Đảm bảo search icon có z-index thấp hơn clear button */
.search-input-container .absolute.left-4 {
    z-index: 5;
}

/* Cải thiện hiển thị trên các thiết bị có màn hình nhỏ */
@media (max-width: 360px) {
    .search-input-enhanced {
        padding-right: 4.5rem !important;
        padding-left: 2rem !important;
        font-size: 0.8rem;
    }

    .clear-search-btn {
        right: 0.25rem;
        width: 36px;
        height: 36px;
    }

    .search-input-container .absolute.left-4 {
        left: 0.25rem;
    }
}

/* Enhanced Search Suggestions */
.search-suggestions-container {
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: opacity 0.3s ease, transform 0.2s ease;
    opacity: 1;
}

.search-suggestion-item {
    cursor: pointer;
    transition: var(--transition-fast);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.search-suggestion-item:last-child {
    border-bottom: none;
}

/* Product Suggestions - Rich Layout */
.product-suggestion {
    padding: 0;
}

.product-suggestion:hover {
    background-color: rgba(243, 115, 33, 0.02);
}

.product-suggestion:hover .fa-arrow-right {
    transform: translateX(2px);
    color: var(--primary);
}

.product-suggestion img {
    transition: var(--transition-fast);
}

.product-suggestion:hover img {
    transform: scale(1.05);
}

/* Simple Text Suggestions */
.simple-suggestion {
    padding: 0;
}

.simple-suggestion:hover {
    background-color: rgba(243, 115, 33, 0.05);
}

/* No Results Suggestion */
.no-results {
    padding: 0;
    border-bottom: none;
}

.no-results button {
    transition: var(--transition-fast);
}

.no-results button:hover {
    color: var(--primary-dark);
}

/* Loading Skeleton Animations */
.loading-item {
    padding: 0;
    pointer-events: none;
}

.loading-skeleton-image {
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: 0.5rem;
    animation: skeleton-loading 1.5s infinite;
}

.loading-skeleton-text {
    height: 1rem;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: 0.25rem;
    animation: skeleton-loading 1.5s infinite;
    width: 80%;
}

.loading-skeleton-text-small {
    height: 0.75rem;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: 0.25rem;
    animation: skeleton-loading 1.5s infinite;
    width: 60%;
}

.loading-skeleton-text-small:last-child {
    width: 40%;
}

@keyframes skeleton-loading {
    0% {
        background-position: -200% 0;
    }

    100% {
        background-position: 200% 0;
    }
}

/* Loading Message */
.search-loading-message {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important;
    gap: 0.5rem !important;
    border-bottom: none;
    background-color: rgba(243, 115, 33, 0.02);
    color: #6b7280;
    font-size: 0.875rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.search-loading-message i {
    color: #f37321 !important;
    animation: pulse-text 2s infinite;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    vertical-align: middle !important;
}

.search-loading-message span {
    animation: pulse-text 2s infinite;
    display: inline-flex !important;
    align-items: center !important;
}

.search-loading-message .text-gray-600 {
    animation: pulse-text 2s infinite;
}

@keyframes pulse-text {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.6;
    }
}

/* Enhanced Loading States */
.loading-item:nth-child(1) .loading-skeleton-image,
.loading-item:nth-child(1) .loading-skeleton-text,
.loading-item:nth-child(1) .loading-skeleton-text-small {
    animation-delay: 0s;
}

.loading-item:nth-child(2) .loading-skeleton-image,
.loading-item:nth-child(2) .loading-skeleton-text,
.loading-item:nth-child(2) .loading-skeleton-text-small {
    animation-delay: 0.2s;
}

.loading-item:nth-child(3) .loading-skeleton-image,
.loading-item:nth-child(3) .loading-skeleton-text,
.loading-item:nth-child(3) .loading-skeleton-text-small {
    animation-delay: 0.4s;
}

/* Filter Groups */
.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark-gray);
}

.filter-group label i {
    color: var(--primary);
    margin-right: 0.25rem;
}

/* Enhanced Select Styling - Focused and Clean */
.filter-select {
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--dark-gray);
    background-color: var(--white);
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    cursor: pointer;
    appearance: none;
    transition: all 0.2s ease;

    /* Custom dropdown arrow */
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1rem 1rem;
}

.filter-select:hover {
    border-color: #9ca3af;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23F37321' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Enhanced Select Options - Limited browser support */
.filter-select option {
    padding: 0.75rem 1rem;
    background-color: var(--white);
    color: var(--dark-gray);
    font-weight: 500;
    font-family: 'Be Vietnam Pro', sans-serif;
    border: none;
    border-radius: 0.25rem;
    margin: 0.125rem 0;
    line-height: 1.5;
}

.filter-select option:hover {
    background-color: rgba(243, 115, 33, 0.1);
    color: var(--primary);
}

.filter-select option:checked,
.filter-select option:selected {
    background-color: var(--primary) !important;
    color: var(--white) !important;
    font-weight: 600;
}

.filter-select option:focus {
    background-color: rgba(243, 115, 33, 0.1);
    outline: none;
}

/* Input Fields */
.filter-input {
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--dark-gray);
    background-color: var(--white);
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    padding: 0.75rem;
    transition: all 0.2s ease;
}

.filter-input:hover {
    border-color: #9ca3af;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
}

.filter-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

/* Remove number input spinners */
.filter-input[type="number"] {
    -moz-appearance: textfield;
}

.filter-input[type="number"]::-webkit-outer-spin-button,
.filter-input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Filter Container - Simple and Clean */
.filter-container {
    background-color: var(--white);
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Filter Group */
.filter-group {
    display: flex;
    flex-direction: column;
}

/* Action Buttons - Simple and Effective */
.btn-search {
    background-color: var(--primary);
    color: var(--white);
    border: none;
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: background-color 0.2s ease;
}

.btn-search:hover {
    background-color: var(--primary-dark);
}

.btn-reset {
    background-color: #f3f4f6;
    color: var(--dark-gray);
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-reset:hover {
    background-color: #e5e7eb;
    border-color: #9ca3af;
}

/* ===== CUSTOM BREAKPOINTS ===== */

/* XS: <576px (1 column) - Default mobile */
@media (max-width: 575px) {
    /* XS specific styles will be added here */
}

/* SM: 576px+ (2 columns) - Large mobile */
@media (min-width: 576px) {
    /* SM specific styles will be added here */
}

/* MD: 768px+ (2 columns) - Tablet portrait */
@media (min-width: 768px) {
    /* MD specific styles will be added here */
}

/* MD+: 900px+ (3 columns) - Large tablet */
@media (min-width: 900px) {
    /* MD+ specific styles will be added here */
}

/* LG: 992px+ (3 columns) - Small laptop */
@media (min-width: 992px) {
    /* LG specific styles will be added here */
}

/* LG+: 1024px+ (4 columns) - Standard laptop */
@media (min-width: 1024px) {
    /* LG+ specific styles will be added here */
}

/* XL: 1200px+ (4 columns) - Desktop */
@media (min-width: 1200px) {
    /* XL specific styles will be added here */
}

/* XXL: 1400px+ (4 columns) - Large desktop */
@media (min-width: 1400px) {
    /* XXL specific styles will be added here */
}

/* Ultra: 1600px+ (4 columns) - Ultra-wide desktop */
@media (min-width: 1600px) {
    /* Ultra specific styles will be added here */
}

/* ===== LEGACY RESPONSIVE DESIGN (TO BE UPDATED) ===== */

/* Responsive Design */
@media (max-width: 768px) {
    .search-page-title {
        font-size: 1.875rem;
    }

    .search-description {
        font-size: 0.875rem;
    }

    /* Mobile filter layout */
    .filter-group {
        margin-bottom: 1rem;
    }

    .search-input-enhanced {
        font-size: 1rem;
        padding: 0.875rem 1rem 0.875rem 3rem;
    }

    .btn-search,
    .btn-reset {
        padding: 0.875rem 1.5rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 640px) {
    .section-title-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .search-page-title {
        font-size: 1.5rem;
    }

    .search-description {
        font-size: 0.8rem;
        text-align: center;
    }

    /* Stack filters vertically on small screens */
    .filter-group label {
        font-size: 0.875rem;
    }

    .filter-select,
    .filter-input {
        font-size: 0.875rem;
        padding: 0.75rem;
        padding-right: 2.25rem;
        /* Adjust for mobile dropdown arrow */
    }

    /* Mobile filter container */
    .filter-container {
        padding: 1rem;
        border-radius: 0.5rem;
    }

    .filter-group::before {
        display: none;
        /* Hide accent line on mobile */
    }

    /* Adjust search suggestions for mobile */
    .search-suggestions-container {
        max-height: 300px;
        border-radius: 0.5rem;
    }

    /* Fix search suggestion items layout on tablet */
    .search-suggestion-item .flex.items-center {
        padding: 0.75rem 0.5rem;
        /* Giảm padding từ p-3 xuống */
        flex-wrap: nowrap;
        /* Đảm bảo không wrap */
        align-items: center;
        /* Căn giữa theo chiều dọc */
    }

    .search-suggestion-item .flex-shrink-0 {
        width: 3rem;
        /* Tăng kích thước image lên để dễ nhìn hơn */
        height: 3rem;
        margin-right: 0.75rem;
        /* Tăng margin để cân đối */
        flex-shrink: 0;
    }

    .search-suggestion-item .flex-1 {
        min-width: 0;
        /* Cho phép flex item co lại */
        overflow: hidden;
        /* Ẩn nội dung tràn */
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    /* Responsive text trong suggestions */
    .search-suggestion-item .text-sm {
        font-size: 0.8rem;
        line-height: 1.2;
    }

    .search-suggestion-item .text-xs {
        font-size: 0.7rem;
    }

    /* Truncate long text */
    .search-suggestion-item .truncate {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }

    /* Đảm bảo price section căn trái */
    .search-suggestion-item .flex.items-center.mt-1 {
        margin-top: 0.25rem;
        margin-left: 0;
        padding-left: 0;
        justify-content: flex-start;
    }

    .simple-suggestion .flex {
        padding: 0.75rem !important;
        font-size: 0.875rem;
    }

    .no-results .flex {
        padding: 1rem !important;
    }

    /* Mobile button adjustments */
    .btn-search,
    .btn-reset {
        padding: 1rem 1.5rem;
        font-size: 0.875rem;
        border-radius: 0.5rem;
    }

    /* Mobile filter badges */
    .filter-badge,
    .clear-all-badge {
        font-size: 0.7rem;
        padding: 0.375rem 0.5rem;
        max-width: 150px;
        height: 34px;
        /* Chiều cao cố định cho mobile */
    }

    .clear-all-filters {
        font-size: 0.7rem;
        padding: 0.375rem 0.5rem;
    }
}

/* Mobile responsive cho search suggestions - Cải thiện layout */
@media (max-width: 480px) {
    .search-suggestions-container {
        max-height: 250px;
        margin-top: 0.25rem;
    }

    /* Reset và tối ưu layout cho mobile */
    .search-suggestion-item .flex.items-center {
        padding: 0.5rem 0.375rem !important;
        align-items: center !important;
        /* Căn giữa để cân bằng với image */
        gap: 0.5rem;
    }

    .search-suggestion-item .flex-shrink-0 {
        width: 2.75rem !important;
        height: 2.75rem !important;
        margin-right: 0 !important;
        /* Xóa margin vì đã có gap */
        flex-shrink: 0;
    }

    .search-suggestion-item .flex-1 {
        min-width: 0 !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        /* Căn giữa content với image */
        padding: 0 !important;
        margin: 0 !important;
    }

    /* Đảm bảo tất cả text căn trái và không có padding/margin thừa */
    .search-suggestion-item .font-medium {
        font-size: 0.75rem !important;
        line-height: 1.2;
        margin: 0 !important;
        padding: 0 !important;
        text-align: left;
    }

    .search-suggestion-item .text-sm {
        font-size: 0.7rem !important;
        line-height: 1.1;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Ẩn category trên mobile */
    .search-suggestion-item:not(.no-results) .text-gray-500:not(.text-primary):not(.font-semibold) {
        display: none;
    }

    /* Đảm bảo no-results text luôn hiển thị đúng */
    .search-suggestion-item.no-results .text-gray-500 {
        display: block !important;
        font-size: 0.8rem !important;
        margin-bottom: 0.5rem !important;
        color: #6b7280 !important;
        text-align: center;
        line-height: 1.4;
    }

    /* Đảm bảo no-results container hiển thị đúng */
    .search-suggestion-item.no-results .flex.flex-col {
        padding: 1rem 0.5rem !important;
    }

    /* Icon trong no-results */
    .search-suggestion-item.no-results i {
        font-size: 1.5rem !important;
        margin-bottom: 0.5rem !important;
        color: #d1d5db !important;
    }

    /* Button trong no-results */
    .search-suggestion-item.no-results button {
        font-size: 0.8rem !important;
        padding: 0.25rem 0.5rem;
        margin-top: 0.25rem;
    }

    /* Ẩn rating section trên mobile */
    .search-suggestion-item .flex.items-center.text-xs.text-gray-400 {
        display: none !important;
    }

    /* Ẩn arrow icon trên mobile */
    .search-suggestion-item .flex-shrink-0.ml-2 {
        display: none !important;
    }

    /* Price section - đảm bảo căn trái và thẳng hàng với title */
    .search-suggestion-item .flex.items-center.mt-1 {
        margin: 0.25rem 0 0 0 !important;
        /* Reset tất cả margin */
        padding: 0 !important;
        /* Reset tất cả padding */
        justify-content: flex-start !important;
        align-items: flex-start !important;
    }

    .search-suggestion-item .text-primary {
        font-size: 0.8rem !important;
        font-weight: 600;
        margin: 0 !important;
        /* Reset margin */
        padding: 0 !important;
        /* Reset padding */
    }
}

/* Mobile rất nhỏ - Tối ưu tối đa */
@media (max-width: 360px) {
    .search-suggestions-container {
        max-height: 200px;
    }

    .search-suggestion-item .flex.items-center {
        padding: 0.375rem 0.25rem !important;
    }

    .search-suggestion-item .flex-shrink-0 {
        width: 2.25rem !important;
        /* Tăng kích thước image trên mobile nhỏ */
        height: 2.25rem !important;
    }

    .search-suggestion-item .text-sm {
        font-size: 0.7rem !important;
    }

    .search-suggestion-item .text-xs {
        font-size: 0.6rem !important;
    }

    /* Rating đã được ẩn ở breakpoint 480px, không cần lặp lại */

    /* Đơn giản hóa hiển thị chỉ còn tên và giá */
    .search-suggestion-item .flex-1 {
        display: flex;
        flex-direction: column;
        gap: 0.125rem;
        justify-content: center;
        /* Căn giữa theo chiều dọc */
    }

    /* Đảm bảo arrow icon cũng bị ẩn trên mobile nhỏ */
    .search-suggestion-item .flex-shrink-0.ml-2 {
        display: none !important;
    }
}

/* Responsive cho loading và no-results states */
@media (max-width: 640px) {

    /* Loading skeleton responsive */
    .loading-skeleton-image {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 0.5rem;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    .loading-skeleton-text {
        height: 0.75rem;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 0.25rem;
    }

    /* Search loading message responsive */
    .search-loading-message .flex {
        padding: 0.75rem 0.5rem !important;
        font-size: 0.8rem;
    }

    /* No results message responsive */
    .search-no-results {
        padding: 1rem 0.5rem;
        text-align: center;
        font-size: 0.8rem;
        color: #6b7280;
    }

    .search-no-results i {
        font-size: 1.25rem !important;
        margin-bottom: 0.375rem !important;
    }
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }

    100% {
        background-position: -200% 0;
    }
}

/* CSS Variables Support for Tailwind Classes */
.bg-primary\/10 {
    background-color: rgba(243, 115, 33, 0.1) !important;
}

.text-primary {
    color: var(--primary) !important;
}

.bg-primary\/30 {
    background-color: rgba(243, 115, 33, 0.3) !important;
}

.bg-primary {
    background-color: var(--primary) !important;
}

.hover\:bg-primary-dark:hover {
    background-color: var(--primary-dark) !important;
}

/* Animation cho page load */
.search-results-container {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Gradient Background */
.search-gradient-bg {
    background: linear-gradient(to bottom, #ffffff, #f9fafb);
}

/* Focus styles for better accessibility */
.section-title-badge:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Custom Dropdown - Alternative to Select */
.custom-dropdown {
    position: relative;
    display: inline-block;
    width: 100%;
}

.custom-dropdown-trigger {
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--dark-gray);
    background-color: var(--white);
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    cursor: pointer;
    width: 100%;
    text-align: left;
    transition: all 0.2s ease;

    /* Text overflow handling */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    /* Custom dropdown arrow */
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1rem 1rem;
}

.custom-dropdown-trigger:hover {
    border-color: #9ca3af;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.custom-dropdown-trigger:focus,
.custom-dropdown.open .custom-dropdown-trigger {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23F37321' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

.custom-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--white);
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    margin-top: 0.25rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.custom-dropdown.open .custom-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.custom-dropdown-option {
    padding: 0.75rem 1rem;
    cursor: pointer;
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--dark-gray);
    transition: all 0.15s ease;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
}

.custom-dropdown-option:last-child {
    border-bottom: none;
}

.custom-dropdown-option:hover {
    background-color: rgba(243, 115, 33, 0.1);
    color: var(--primary);
}

.custom-dropdown-option:hover i {
    transform: scale(1.1);
}

.custom-dropdown-option.selected {
    background-color: var(--primary);
    color: var(--white);
    font-weight: 600;
}

.custom-dropdown-option.selected:hover {
    background-color: var(--primary-dark);
}

.custom-dropdown-option.selected i {
    color: var(--white);
}

/* Icon styling in dropdown options */
.custom-dropdown-option i {
    transition: all 0.15s ease;
    min-width: 1rem;
}

/* Custom Price Option Styling */
.custom-price-option {
    border-top: 2px solid #f3f4f6 !important;
    margin-top: 0.25rem;
    font-weight: 600;
    color: var(--primary) !important;
    position: relative;
}

.custom-price-option::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.custom-price-option:hover {
    background-color: rgba(243, 115, 33, 0.15) !important;
    color: var(--primary-dark) !important;
}

.custom-price-option:hover::before {
    opacity: 1;
}

/* Price Range Modal Styles - Sử dụng z-index cao nhất như các modal khác */
#custom-price-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 2147483647 !important;
    /* Z-index tối đa giống delete-confirmation-modal */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: rgba(0, 0, 0, 0.75) !important;
    /* Tăng opacity cho mobile */
    padding: 20px !important;
    box-sizing: border-box !important;
    overflow-y: auto !important;

    /* Hiệu ứng mượt mà khi hiển thị và ẩn */
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Khi modal ẩn */
#custom-price-modal.hidden {
    display: none !important;
}

#custom-price-modal.active {
    opacity: 1;
    visibility: visible;
}

/* Modern Modal Content Box */
.modal-content-box {
    position: relative !important;
    z-index: 2147483647 !important;
    max-width: 520px !important;
    width: 92% !important;
    margin: 0 auto !important;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
    border-radius: 24px !important;
    overflow: hidden !important;
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.8),
        inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;

    /* Hiệu ứng cho modal box */
    transform: scale(0.92) translateY(30px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

#custom-price-modal.active .modal-content-box {
    transform: scale(1) translateY(0);
    opacity: 1;
}

/* Ngăn cuộn trang khi modal hiển thị */
body.modal-active {
    overflow: hidden !important;
}

/* Đảm bảo modal không bị ảnh hưởng bởi các thuộc tính CSS khác */
#custom-price-modal {
    transform: none !important;
    filter: none !important;
    perspective: none !important;
    contain: none !important;
    isolation: isolate !important;
    will-change: auto !important;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-20px);
    }

    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Modal Header Styling */
.modal-header {
    position: relative;
    padding: 32px 32px 24px 32px;
    text-align: center;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modal-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px auto;
    background: linear-gradient(135deg, var(--primary) 0%, #e67e22 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(243, 115, 33, 0.3);
    animation: modalIconPulse 3s ease-in-out infinite;
}

.modal-icon i {
    font-size: 24px;
    color: white;
}

.modal-title {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 8px 0;
    letter-spacing: -0.02em;
}

.modal-subtitle {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
    line-height: 1.5;
}

.modal-close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 36px;
    height: 36px;
    border-radius: 12px;
    background: rgba(0, 0, 0, 0.05);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    transition: all 0.2s ease;
    cursor: pointer;
}

.modal-close-btn:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    transform: scale(1.1);
}

#apply-custom-price:hover {
    background-color: var(--primary-dark);
}

#cancel-custom-price:hover {
    background-color: #e5e7eb;
}

/* Price range specific dropdown styling - Giữ nguyên màu sắc đa dạng */
.filter-group .custom-dropdown .custom-dropdown-option[data-price-min] i {
    /* Màu sắc được định nghĩa trực tiếp trong HTML với Tailwind classes */
    transition: all 0.15s ease;
}

.filter-group .custom-dropdown .custom-dropdown-option[data-price-min]:hover i {
    transform: scale(1.1);
    /* Giữ nguyên màu gốc khi hover, chỉ scale */
}

.filter-group .custom-dropdown .custom-dropdown-option.selected[data-price-min] i {
    color: var(--white) !important;
    /* Override màu Tailwind khi được selected */
}

/* Mobile responsive for price modal */
@media (max-width: 640px) {
    #custom-price-modal .bg-white {
        margin: 1rem;
        width: calc(100% - 2rem);
        max-width: none;
    }

    #custom-price-modal h3 {
        font-size: 1rem;
    }

    #custom-price-modal input {
        font-size: 1rem;
        padding: 0.875rem;
    }

    #custom-price-modal button {
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
    }

    .custom-dropdown-menu {
        max-height: 300px;
        overflow-y: auto;
    }

    .custom-dropdown-option {
        padding: 1rem;
        font-size: 0.875rem;
    }

    .custom-dropdown-option i {
        font-size: 0.875rem;
        min-width: 1.25rem;
    }
}

/* Filter Badges - Beautiful and Interactive */
.filter-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: linear-gradient(135deg, rgba(243, 115, 33, 0.1), rgba(243, 115, 33, 0.05));
    border: 1px solid rgba(243, 115, 33, 0.2);
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--primary);
    transition: all 0.2s ease;
    animation: fadeInScale 0.3s ease-out;
    max-width: 300px;
    /* Tăng max-width để có thể hiển thị nhiều text hơn */
    min-width: 0;
    /* Cho phép flex item co lại */
    height: 40px;
    /* Đặt chiều cao cố định */
    box-sizing: border-box;
    /* Đảm bảo padding và border được tính trong chiều cao */
}

.filter-badge:hover {
    background: linear-gradient(135deg, rgba(243, 115, 33, 0.15), rgba(243, 115, 33, 0.08));
    border-color: rgba(243, 115, 33, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(243, 115, 33, 0.2);
}

/* Specific Badge Types */
.keyword-badge {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
    border-color: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    max-width: 350px;
    /* Tăng max-width cho keyword badge */
}

.keyword-badge:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.08));
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

/* Text wrapper để xử lý text truncation cho tất cả badge */
.keyword-text,
.badge-text {
    flex: 1;
    min-width: 0;
    /* Cho phép flex item co lại */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 250px;
    /* Giới hạn chiều rộng của text */
}

/* Keyword text có thể dài hơn */
.keyword-text {
    max-width: 250px;
}

/* Responsive Design cho Filter Badges */
@media (max-width: 768px) {

    .filter-badge,
    .clear-all-badge {
        max-width: 250px;
        /* Giảm max-width trên tablet */
        font-size: 0.7rem;
        padding: 0.4rem 0.6rem;
        height: 36px;
        /* Giảm chiều cao trên tablet */
    }

    .keyword-badge {
        max-width: 280px;
    }

    .keyword-text,
    .badge-text {
        max-width: 180px;
        /* Giảm max-width cho text trên tablet */
    }
}

@media (max-width: 480px) {

    .filter-badge,
    .clear-all-badge {
        max-width: 200px;
        /* Giảm max-width trên mobile */
        font-size: 0.65rem;
        padding: 0.35rem 0.5rem;
        height: 32px;
        /* Giảm chiều cao trên mobile */
    }

    .keyword-badge {
        max-width: 220px;
    }

    .keyword-text,
    .badge-text {
        max-width: 130px;
        /* Giảm max-width cho text trên mobile */
    }

    /* Giảm kích thước icon và button */
    .filter-badge i,
    .clear-all-badge i {
        font-size: 0.6rem;
    }

    .remove-filter {
        padding: 0.2rem;
        margin-left: 0.3rem;
    }
}

.category-badge {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
    border-color: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.category-badge:hover {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(16, 185, 129, 0.08));
    border-color: rgba(16, 185, 129, 0.3);
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.price-badge {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(168, 85, 247, 0.05));
    border-color: rgba(168, 85, 247, 0.2);
    color: #a855f7;
}

.price-badge:hover {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.15), rgba(168, 85, 247, 0.08));
    border-color: rgba(168, 85, 247, 0.3);
    box-shadow: 0 2px 4px rgba(168, 85, 247, 0.2);
}

.promotion-badge {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    border-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.promotion-badge:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.08));
    border-color: rgba(239, 68, 68, 0.3);
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

/* Remove Filter Button */
.remove-filter {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0.125rem;
    border-radius: 50%;
    transition: all 0.15s ease;
    opacity: 0.7;
}

.remove-filter:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}



/* Animation for badges */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Simple Disabled State */
.filter-select:disabled,
.filter-input:disabled {
    background-color: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed;
    opacity: 0.6;
}

/* No Results Section - Thiết kế tối giản và hiện đại */
.no-results-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    padding: 2rem 1rem;
}

.no-results-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 1.5rem;
    padding: 3rem 2rem;
    text-align: center;
    max-width: 500px;
    width: 100%;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: fadeInUp 0.6s ease-out;
}

.no-results-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

/* Icon Section */
.no-results-icon {
    margin-bottom: 1.5rem;
}

.icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, rgba(243, 115, 33, 0.1), rgba(243, 115, 33, 0.05));
    border: 2px solid rgba(243, 115, 33, 0.2);
    border-radius: 50%;
    margin: 0 auto;
    transition: all 0.3s ease;
}

.icon-wrapper i {
    font-size: 2rem;
    color: var(--primary);
    transition: all 0.3s ease;
}

.no-results-card:hover .icon-wrapper {
    transform: scale(1.05);
    background: linear-gradient(135deg, rgba(243, 115, 33, 0.15), rgba(243, 115, 33, 0.08));
    border-color: rgba(243, 115, 33, 0.3);
}

.no-results-card:hover .icon-wrapper i {
    transform: scale(1.1);
}

/* Content Section */
.no-results-content {
    margin-bottom: 2rem;
}

.no-results-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 0.75rem;
    line-height: 1.2;
}

.no-results-message {
    font-size: 1rem;
    color: var(--medium-gray);
    line-height: 1.6;
    margin-bottom: 0;
}

.keyword-highlight {
    color: var(--primary);
    font-weight: 600;
    background: linear-gradient(135deg, rgba(243, 115, 33, 0.1), rgba(243, 115, 33, 0.05));
    padding: 0.125rem 0.375rem;
    border-radius: 0.375rem;
    border: 1px solid rgba(243, 115, 33, 0.2);
    /* Khắc phục text overflow */
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    display: inline-block;
    text-align: center;
    /* Responsive margin-top */
    margin-top: 1rem;
    /* Desktop - khoảng cách vừa phải */
}

/* Simple Suggestions */
.search-suggestions-simple {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    color: var(--medium-gray);
    transition: all 0.2s ease;
}

.suggestion-item:hover {
    background-color: rgba(243, 115, 33, 0.05);
    border-color: rgba(243, 115, 33, 0.2);
    color: var(--primary);
    transform: translateY(-1px);
}

.suggestion-item i {
    color: var(--primary);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.suggestion-item:hover i {
    transform: scale(1.1);
}

/* Action Buttons */
.no-results-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-home,
.btn-new-search {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    min-width: 140px;
    justify-content: center;
}

.btn-home {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: var(--white);
    box-shadow: 0 2px 4px rgba(243, 115, 33, 0.3);
}

.btn-home:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(243, 115, 33, 0.4);
    background: linear-gradient(135deg, var(--primary-dark), #B8460E);
}

.btn-new-search {
    background-color: #f8fafc;
    color: var(--dark-gray);
    border: 1px solid #e2e8f0;
}

.btn-new-search:hover {
    background-color: #e2e8f0;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive Design for No Results */
@media (max-width: 768px) {
    .no-results-container {
        min-height: 300px;
        padding: 1.5rem 1rem;
    }

    .no-results-card {
        padding: 2rem 1.5rem;
        border-radius: 1rem;
    }

    .icon-wrapper {
        width: 60px;
        height: 60px;
    }

    .icon-wrapper i {
        font-size: 1.5rem;
    }

    .no-results-title {
        font-size: 1.5rem;
    }

    .no-results-message {
        font-size: 0.875rem;
    }

    .keyword-highlight {
        margin-top: 0.875rem;
        /* Tablet - khoảng cách trung bình */
    }

    .search-suggestions-simple {
        flex-direction: column;
        gap: 0.75rem;
        align-items: center;
    }

    .suggestion-item {
        padding: 0.625rem 0.875rem;
        font-size: 0.8rem;
    }

    .no-results-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .btn-home,
    .btn-new-search {
        padding: 0.75rem 1.25rem;
        font-size: 0.8rem;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    .no-results-card {
        padding: 1.5rem 1rem;
        margin: 0 0.5rem;
    }

    .no-results-title {
        font-size: 1.25rem;
    }

    .search-suggestions-simple {
        gap: 0.5rem;
    }

    .suggestion-item {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        width: 100%;
        max-width: 200px;
    }
}

/* Performance-Optimized Product Card */
.modern-product-card {
    background: linear-gradient(145deg, #ffffff 0%, #fefefe 100%);
    border-radius: 1.25rem;
    overflow: hidden;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 1px 3px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    /* Optimized transition - only essential properties */
    transition:
        transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    /* Performance optimizations */
    will-change: transform;
    transform: translateZ(0);
    /* Force hardware acceleration */
    backface-visibility: hidden;
    /* Prevent flickering */
}

.modern-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
    opacity: 0;
    transition: opacity 0.25s ease;
    will-change: opacity;
}

.modern-product-card:hover {
    /* Match homepage hover effect - only translateY */
    transform: translateY(-5px) !important;
    box-shadow:
        0 12px 24px rgba(0, 0, 0, 0.1),
        0 4px 8px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(59, 130, 246, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 1) !important;
}

.modern-product-card:hover::before {
    opacity: 1;
}

/* Force override for product cards with both classes */
.product-card.modern-product-card:hover {
    transform: translateY(-5px) !important;
    box-shadow:
        0 12px 24px rgba(0, 0, 0, 0.1),
        0 4px 8px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(59, 130, 246, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 1) !important;
}

/* Product Image Wrapper - 1:1 Aspect Ratio */
.product-image-wrapper {
    position: relative;
    overflow: hidden;
    width: 100%;
    aspect-ratio: 1 / 1;
    /* Modern browsers */
    /* Fallback for older browsers */
    padding-bottom: 100%;
    height: 0;
}

.product-image-wrapper img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* Optimized image transition */
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
}

/* Image hover effect now handled by Tailwind group-hover:scale-110 */

/* Stock Status Badge */
.stock-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.35rem 0.85rem;
    border-radius: 9999px;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 0.35rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(4px);
}

.modern-product-card:hover .stock-status {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.in-stock {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
    color: white;
    font-weight: 600;
    box-shadow: 0 3px 6px rgba(16, 185, 129, 0.3);
}

.out-of-stock {
    background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
    color: white;
    font-weight: 600;
    box-shadow: 0 3px 6px rgba(239, 68, 68, 0.3);
}

/* Optimized Product Info Wrapper */
.product-info-wrapper {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding: 1.25rem 1.25rem 1rem 1.25rem;
    /* Top, Right, Bottom, Left */
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    position: relative;
}

.product-info-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 1.5rem;
    right: 1.5rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
}

/* Product Title - Enhanced Line Clamp */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.5;
    /* Better readability */
    max-height: 3em;
    /* 2 lines * 1.5 line-height */
    word-break: break-word;
    /* Handle long words */
    hyphens: auto;
    /* Better text wrapping */
}

/* Luxury Category Link */
.product-info-wrapper a[href*="category"] {
    color: var(--primary);
    font-weight: 500;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
}

.product-info-wrapper a[href*="category"]:hover {
    color: var(--primary-dark);
    transform: translateY(-1px);
}

.product-info-wrapper a[href*="category"]::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--primary-dark));
    transition: width 0.3s ease;
}

.product-info-wrapper a[href*="category"]:hover::after {
    width: 100%;
}

/* Luxury Product Title */
.product-title h3 {
    font-weight: 600;
    color: #1e293b;
    transition: color 0.3s ease;
    line-height: 1.4;
}

.product-title a:hover h3 {
    color: #D65A0F;
}

/* Optimized Spacing for Product Info Sections */
.product-title {
    margin-bottom: 0 !important;
    /* Remove gap between title and price */
}

.premium-price-section {
    margin-top: 0;
    margin-bottom: 0;
    /* No gap before rating section */
}

.product-rating-sales {
    margin-top: 0.5rem;
    /* Consistent reduced spacing */
}

/* Luxury Price Section */
.price-section {
    margin-top: auto;
    padding: 1rem 0 0.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.price-section .text-gray-500 {
    font-size: 0.875rem;
    color: #64748b !important;
    font-weight: 500;
}

.price-section .text-red-500 {
    color: #dc2626 !important;
    font-weight: 700;
    font-size: 1.25rem;
    text-shadow: 0 1px 2px rgba(220, 38, 38, 0.1);
}

.price-section .text-gray-800 {
    color: #1e293b !important;
    font-weight: 700;
    font-size: 1.25rem;
    text-shadow: 0 1px 2px rgba(30, 41, 59, 0.1);
}

/* Luxury Product Actions */
.product-actions {
    margin-top: auto;
    gap: 0.75rem;
}

.product-actions button,
.product-actions a {
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    font-weight: 600;
    letter-spacing: 0.025em;
    position: relative;
    overflow: hidden;
}

.product-actions button {
    background: linear-gradient(135deg, var(--primary) 0%, #e67e22 100%);
    border: none;
    box-shadow:
        0 4px 12px rgba(243, 115, 33, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.product-actions button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.product-actions button:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 20px rgba(243, 115, 33, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.product-actions button:hover::before {
    left: 100%;
}

.product-actions a {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.product-actions a:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 100%);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 1);
}

/* Primary Button Colors */
.bg-primary {
    background-color: var(--primary);
}

.bg-primary:hover,
.hover\:bg-primary-dark:hover {
    background-color: var(--primary-dark);
}

/* Responsive Design for Optimized Product Cards */
@media (max-width: 768px) {
    .modern-product-card {
        border-radius: 1rem;
        /* Optimize for touch devices */
        will-change: auto;
    }

    /* Reduced hover effects for tablet - match homepage */
    .modern-product-card:hover {
        transform: translateY(-3px) !important;
        box-shadow:
            0 8px 16px rgba(0, 0, 0, 0.08),
            0 2px 4px rgba(0, 0, 0, 0.04),
            inset 0 1px 0 rgba(255, 255, 255, 1) !important;
    }

    /* Image hover effect now handled by Tailwind group-hover:scale-110 */
}

.stock-status {
    font-size: 0.65rem;
    padding: 0.25rem 0.6rem;
    top: 0.75rem;
    right: 0.75rem;
    gap: 0.25rem;
}

.product-info-wrapper {
    padding: 1rem 1rem 0.75rem 1rem;
}

.product-info-wrapper::before {
    left: 1rem;
    right: 1rem;
}

.product-title {
    margin-bottom: 0 !important;
    /* Remove gap between title and price */
}

.product-title h3 {
    font-size: 1rem;
    line-height: 1.4;
    /* Consistent with line-clamp */
}

.premium-price-section {
    margin-top: 0;
    margin-bottom: 0;
    /* No gap before rating section */
}

.product-rating-sales {
    margin-top: 0.5rem;
    /* Consistent reduced spacing */
}

.price-section {
    margin-bottom: 0.75rem;
    padding: 0.75rem 0 0.5rem;
}

.product-actions {
    gap: 0.5rem;
}

.product-actions button,
.product-actions a {
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.75rem;
}


@media (max-width: 480px) {

    /* Disable hover effects on mobile for better performance */
    .modern-product-card {
        will-change: auto;
    }

    .modern-product-card:hover {
        transform: none !important;
        box-shadow:
            0 4px 20px rgba(0, 0, 0, 0.08),
            0 1px 3px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
    }

    /* Image hover effect disabled on mobile */

    .modern-product-card:hover::before {
        opacity: 0;
    }

    .stock-status {
        font-size: 0.6rem;
        padding: 0.2rem 0.5rem;
        top: 0.5rem;
        right: 0.5rem;
    }

    .product-title {
        margin-bottom: 0 !important;
        /* Remove gap between title and price */
    }

    .product-title h3 {
        font-size: 0.9rem;
        line-height: 1.4;
        /* Consistent line-height */
    }

    .premium-price-section {
        margin-top: 0;
        margin-bottom: 0;
        /* No gap for mobile */
    }

    .product-rating-sales {
        margin-top: 0.5rem;
        /* Consistent reduced spacing */
    }

    .product-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .product-actions button,
    .product-actions a {
        width: 100%;
        justify-content: center;
    }
}

/* Premium Sale Badge - Modern & Sophisticated với tông màu cam chính */
.premium-sale-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    background: linear-gradient(135deg, #F37321 0%, #D65A0F 50%, #D35400 100%);
    border-radius: 0.75rem;
    padding: 0.5rem 0.75rem;
    box-shadow:
        0 4px 12px rgba(243, 115, 33, 0.4),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: rotate(-3deg);
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: premium-badge-pulse 2.5s infinite;
}

.premium-sale-badge:hover,
.modern-product-card:hover .premium-sale-badge,
.product-card:hover .premium-sale-badge,
.group:hover .premium-sale-badge {
    transform: rotate(0deg) scale(1.05);
    box-shadow:
        0 6px 16px rgba(243, 115, 33, 0.5),
        0 3px 6px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, #FF8A3D 0%, #F37321 50%, #D65A0F 100%);
    animation: none;
    /* Stop pulse animation on hover */
}

/* Hiệu ứng pulse cho premium sale badge */
@keyframes premium-badge-pulse {
    0% {
        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.4), 0 0 0 0 rgba(243, 115, 33, 0.3);
    }

    70% {
        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.4), 0 0 0 8px rgba(243, 115, 33, 0);
    }

    100% {
        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.4), 0 0 0 0 rgba(243, 115, 33, 0);
    }
}



.badge-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.125rem;
    transition: all 0.3s ease;
}

.discount-percent {
    font-size: 0.875rem;
    font-weight: 800;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    line-height: 1;
    transition: all 0.3s ease;
}

.sale-text {
    font-size: 0.625rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1;
    transition: all 0.3s ease;
}

/* Enhanced hover effects for badge content */
.modern-product-card:hover .badge-content,
.product-card:hover .badge-content,
.group:hover .badge-content {
    transform: scale(1.02);
}

.modern-product-card:hover .discount-percent,
.product-card:hover .discount-percent,
.group:hover .discount-percent {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    font-weight: 900;
}

.modern-product-card:hover .sale-text,
.product-card:hover .sale-text,
.group:hover .sale-text {
    color: rgba(255, 255, 255, 1);
    letter-spacing: 0.1em;
}

/* Premium Price Section - Clean Layout without boxes */
.premium-price-section {
    /* Remove all background and border styling */
    background: none;
    border: none;
    border-radius: 0;
    padding: 0;
    /* Remove all padding for optimal height */
    position: relative;
    margin-top: auto;
    min-height: 3.5rem;
    /* Đảm bảo chiều cao tối thiểu nhất quán */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Remove the decorative line */
.premium-price-section::before {
    display: none;
}

.price-container {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    width: 100%;
    justify-content: center;
    align-items: flex-start;
    /* Căn trái cho sale products */
}

.original-price {
    font-size: clamp(0.65rem, 1.5vw, 0.875rem);
    /* Default size */
    color: #64748b;
    text-decoration: line-through;
    font-weight: 500;
    opacity: 0.8;
    align-self: flex-start;
    /* Căn trái */
}

.sale-price {
    font-size: clamp(1rem, 2.8vw, 1.5rem);
    /* Default size */
    font-weight: 800;
    color: #D65A0F;
    text-shadow: 0 1px 2px rgba(213, 84, 15, 0.1);
    line-height: 1.2;
    align-self: flex-start;
    /* Căn trái */
}

.savings-amount {
    font-size: clamp(0.6rem, 1.3vw, 0.75rem);
    /* Optimized for 3-col tablet */
    color: #059669;
    font-weight: 600;
    /* Remove background box styling */
    background: none;
    padding: 0.25rem 0;
    /* Only vertical padding */
    border: none;
    border-radius: 0;
    text-align: left;
    /* Align with other price elements */
    text-transform: uppercase;
    letter-spacing: 0.025em;
    align-self: flex-start;
    /* Align left like other elements */
    width: auto;
    /* Auto width */
}

/* Regular Price Container - Consistent Layout */
.regular-price-container {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    width: 100%;
    justify-content: center;
    align-items: flex-start;
    /* Căn trái giống sale products */
}

.price-label {
    font-size: clamp(0.65rem, 1.5vw, 0.875rem);
    /* Optimized for 3-col tablet */
    color: #64748b;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    align-self: flex-start;
    position: relative;
    transition: color 0.3s ease;
}

/* Subtle blue accent on hover */
.modern-product-card:hover .price-label {
    color: #3b82f6;
}

.main-price {
    font-size: clamp(1rem, 2.8vw, 1.5rem);
    /* Default size */
    font-weight: 800;
    color: #D65A0F;
    /* Đổi thành màu cam giống sale-price */
    text-shadow: 0 1px 2px rgba(213, 84, 15, 0.1);
    line-height: 1.2;
    align-self: flex-start;
    position: relative;
    transition: color 0.3s ease;
}

/* Subtle hover effect for main price */
.modern-product-card:hover .main-price {
    color: #B8450C;
    /* Darker orange on hover */
}

.price-note {
    font-size: clamp(0.6rem, 1.3vw, 0.75rem);
    /* Optimized for 3-col tablet */
    color: #64748b;
    /* Lighter color for subtle note */
    font-weight: 500;
    /* Lighter weight */
    /* Remove background box styling */
    background: none;
    padding: 0.25rem 0;
    /* Only vertical padding */
    border: none;
    border-radius: 0;
    text-align: left;
    /* Align with other price elements */
    text-transform: none;
    /* Remove uppercase for softer look */
    letter-spacing: 0;
    align-self: flex-start;
    /* Align left */
    width: auto;
    position: relative;
    overflow: visible;
}

/* Remove decorative line */
.price-note::before {
    display: none;
}

/* ===== RESPONSIVE FONT SIZE OPTIMIZATION FOR LG+ (1024px+) ===== */
@media (min-width: 1024px) {

    /* Optimize font sizes for 4-column layout on 1024px+ screens */
    .main-price {
        font-size: clamp(0.9rem, 2.2vw, 1.25rem) !important;
    }

    .sale-price {
        font-size: clamp(0.9rem, 2.2vw, 1.25rem) !important;
    }

    .contact-price-subtitle {
        font-size: clamp(0.9rem, 2.2vw, 1.25rem) !important;
    }

    .original-price {
        font-size: clamp(0.6rem, 1.2vw, 0.75rem) !important;
    }

    .price-label {
        font-size: clamp(0.6rem, 1.2vw, 0.75rem) !important;
    }

    .contact-price-main {
        font-size: clamp(0.6rem, 1.2vw, 0.75rem) !important;
    }

    .price-note {
        font-size: clamp(0.55rem, 1.1vw, 0.65rem) !important;
    }

    .savings-amount {
        font-size: clamp(0.55rem, 1.1vw, 0.65rem) !important;
    }

    /* Product Rating and Sales Section - Optimize for 4 columns */
    .rating-section .stars i {
        font-size: 0.625rem !important;
        /* Giảm từ 0.75rem */
    }

    .rating-section .rating-text {
        font-size: 0.625rem !important;
        /* Giảm từ 0.75rem */
    }

    .sales-section i {
        font-size: 0.625rem !important;
        /* Giảm từ 0.75rem */
        color: #3b82f6;
    }

    .sales-section span {
        font-size: 0.625rem !important;
        /* Giảm từ 0.75rem */
    }

    /* Premium Price Section - Reduce min-height for 4-column layout */
    .premium-price-section {
        min-height: 3.5rem !important;
        /* Giảm từ 4.5rem */
    }
}

/* ===== RESTORE ORIGINAL FONT SIZES FOR XL+ (1200px+) ===== */
@media (min-width: 1200px) {

    /* Restore original font sizes for larger screens */
    .main-price {
        font-size: clamp(1rem, 2.8vw, 1.5rem) !important;
    }

    .sale-price {
        font-size: clamp(1rem, 2.8vw, 1.5rem) !important;
    }

    .contact-price-subtitle {
        font-size: clamp(1rem, 2.8vw, 1.5rem) !important;
    }

    .original-price {
        font-size: clamp(0.65rem, 1.5vw, 0.875rem) !important;
    }

    .price-label {
        font-size: clamp(0.65rem, 1.5vw, 0.875rem) !important;
    }

    .contact-price-main {
        font-size: clamp(0.65rem, 1.5vw, 0.875rem) !important;
    }

    .price-note {
        font-size: clamp(0.6rem, 1.3vw, 0.75rem) !important;
    }

    .savings-amount {
        font-size: clamp(0.6rem, 1.3vw, 0.75rem) !important;
    }

    /* Product Rating and Sales Section - Restore to original sizes */
    .rating-section .stars i {
        font-size: 0.75rem !important;
        /* Back to original */
    }

    .rating-section .rating-text {
        font-size: 0.75rem !important;
        /* Back to original */
    }

    .sales-section i {
        font-size: 0.75rem !important;
        /* Back to original */
        color: #3b82f6;
    }

    .sales-section span {
        font-size: 0.75rem !important;
        /* Back to original */
    }

    /* Premium Price Section - Restore original min-height */
    .premium-price-section {
        min-height: 4.5rem !important;
        /* Back to original */
    }
}

/* Contact Price Styling - Giống Regular Price Container */
.contact-price-container {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    width: 100%;
    justify-content: center;
    align-items: flex-start;
    /* Căn trái giống regular products */
    /* Remove background box styling */
    background: none;
    border: none;
    border-radius: 0;
    position: relative;
    overflow: visible;
}

/* Remove decorative line */
.contact-price-container::before {
    display: none;
}

.contact-price-main {
    font-size: clamp(0.65rem, 1.5vw, 0.875rem);
    color: #64748b;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    align-self: flex-start;
    position: relative;
    transition: color 0.3s ease;
}

/* Hover effect giống price-label */
.modern-product-card:hover .contact-price-main {
    color: #3b82f6;
}

.contact-price-subtitle {
    font-size: clamp(1rem, 2.8vw, 1.5rem);
    font-weight: 800;
    color: #1e40af;
    text-shadow: 0 1px 2px rgba(30, 64, 175, 0.1);
    line-height: 1.2;
    align-self: flex-start;
    position: relative;
    transition: color 0.3s ease;
}

/* Pulse animation for phone icon */
@keyframes pulse {

    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

/* Responsive Design for Premium Elements */
@media (max-width: 768px) {
    .premium-sale-badge {
        top: 0.75rem;
        right: 0.75rem;
        padding: 0.4rem 0.7rem;
        /* Slightly larger for tablet */
        border-radius: 0.625rem;
    }

    .discount-percent {
        font-size: 0.8rem;
        /* Slightly larger for tablet */
    }

    .sale-text {
        font-size: 0.55rem;
        /* Slightly larger for tablet */
    }

    .premium-price-section {
        padding: 0;
        /* Remove all padding */
        min-height: 4rem;
        /* Chiều cao tối thiểu cho tablet */
    }

    .sale-price {
        font-size: 1.25rem;
    }

    .main-price {
        font-size: 1.25rem;
    }

    .price-label {
        font-size: 0.75rem;
    }

    .original-price {
        font-size: 0.75rem;
    }

    .price-note {
        font-size: 0.65rem;
    }

    /* Contact Price Tablet Responsive */
    .contact-price-main {
        font-size: 0.75rem;
    }

    .contact-price-main i {
        font-size: 0.875rem;
        /* Tablet icon size */
    }

    .contact-price-subtitle {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .premium-sale-badge {
        top: 0.5rem;
        right: 0.5rem;
        padding: 0.375rem 0.625rem;
        /* Increased padding for better visibility */
        border-radius: 0.5rem;
    }

    .discount-percent {
        font-size: 0.75rem;
        /* Increased from 0.625rem */
    }

    .sale-text {
        font-size: 0.5rem;
        /* Increased from 0.4rem */
    }

    .premium-price-section {
        padding: 0;
        /* Remove all padding */
        min-height: 3.5rem;
        /* Chiều cao tối thiểu cho mobile */
    }

    .sale-price {
        font-size: 1.125rem;
    }

    .main-price {
        font-size: 1.125rem;
    }

    .price-label {
        font-size: 0.65rem;
    }

    .original-price {
        font-size: 0.65rem;
    }

    .price-note {
        font-size: 0.6rem;
    }

    .savings-amount {
        font-size: 0.625rem;
        padding: 0.1875rem 0.375rem;
    }

    /* Contact Price Mobile Responsive */
    .contact-price-main {
        font-size: 0.65rem;
    }

    /* Keyword Highlight Responsive */
    .keyword-highlight {
        margin-top: 0.75rem;
        /* Mobile - giảm margin để phù hợp màn hình nhỏ */
    }

    .contact-price-main i {
        font-size: 0.875rem;
        /* Mobile icon size */
    }

    .contact-price-subtitle {
        font-size: 1.125rem;
    }
}

/* ===== MODERN MODAL DESIGN ===== */

/* Modal Body Styling */
.modal-body {
    padding: 24px 32px;
}

/* Info Card Styling */
.info-card {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border: 1px solid #bfdbfe;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 24px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

.info-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.info-icon i {
    font-size: 16px;
    color: white;
}

.info-content {
    flex: 1;
}

.info-title {
    font-size: 14px;
    font-weight: 600;
    color: #1e40af;
    margin: 0 0 8px 0;
}

.info-list {
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 13px;
    color: #1e40af;
    line-height: 1.6;
}

.info-list li {
    position: relative;
    padding-left: 16px;
    margin-bottom: 4px;
}

.info-list li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #3b82f6;
    font-weight: bold;
}

/* Input Group Styling */
.input-group {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.input-field {
    position: relative;
}

.input-label {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.input-wrapper {
    position: relative;
}

.price-input {
    width: 100%;
    padding: 16px 48px 16px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    color: #1f2937;
    background: #ffffff;
    transition: all 0.3s ease;
    font-family: 'Be Vietnam Pro', sans-serif;
}

.price-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
    background: #fefefe;
}

.price-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

.input-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 14px;
    pointer-events: none;
}

/* Modal Footer Styling */
.modal-footer {
    padding: 24px 32px 32px 32px;
    background: #f9fafb;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, #e67e22 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(243, 115, 33, 0.4);
}

.btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #e5e7eb;
}

.btn-secondary:hover {
    background: #e5e7eb;
    color: #374151;
    transform: translateY(-1px);
}

/* Animation Keyframes */
@keyframes modalIconPulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

/* Mobile Responsive */
@media (max-width: 640px) {
    #custom-price-modal {
        padding: 10px !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .modal-content-box {
        width: 100% !important;
        max-width: none !important;
        margin: 0 auto !important;
        max-height: calc(100vh - 20px) !important;
        overflow-y: auto !important;
        border-radius: 20px !important;
    }

    .modal-header {
        padding: 20px 16px 16px 16px;
    }

    .modal-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 12px;
        border-radius: 16px;
    }

    .modal-icon i {
        font-size: 20px;
    }

    .modal-title {
        font-size: 20px;
        margin-bottom: 6px;
    }

    .modal-subtitle {
        font-size: 13px;
    }

    .modal-close-btn {
        top: 12px;
        right: 12px;
        width: 32px;
        height: 32px;
    }

    .modal-body {
        padding: 16px;
    }

    .info-card {
        padding: 14px;
        margin-bottom: 16px;
        border-radius: 12px;
    }

    .info-icon {
        width: 32px;
        height: 32px;
        border-radius: 10px;
    }

    .info-icon i {
        font-size: 14px;
    }

    .info-title {
        font-size: 13px;
        margin-bottom: 6px;
    }

    .info-list {
        font-size: 12px;
    }

    .input-group {
        gap: 14px;
    }

    .input-label {
        font-size: 13px;
        margin-bottom: 6px;
    }

    .price-input {
        padding: 12px 40px 12px 12px;
        font-size: 16px;
        border-radius: 10px;
    }

    .input-icon {
        right: 12px;
        font-size: 12px;
    }

    .modal-footer {
        padding: 16px;
        gap: 8px;
    }

    .btn-primary,
    .btn-secondary {
        padding: 12px 16px;
        font-size: 14px;
        border-radius: 10px;
        flex: 1;
    }
}

/* Extra Small Mobile (Height < 600px) */
@media (max-width: 640px) and (max-height: 600px) {
    #custom-price-modal {
        padding: 5px !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .modal-content-box {
        max-height: calc(100vh - 10px) !important;
        margin: 0 auto !important;
    }

    .modal-header {
        padding: 16px 12px 12px 12px;
    }

    .modal-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 8px;
        border-radius: 12px;
    }

    .modal-icon i {
        font-size: 18px;
    }

    .modal-title {
        font-size: 18px;
        margin-bottom: 4px;
    }

    .modal-subtitle {
        font-size: 12px;
    }

    .modal-body {
        padding: 12px;
    }

    .info-card {
        padding: 12px;
        margin-bottom: 12px;
    }

    .input-group {
        gap: 12px;
    }

    .price-input {
        padding: 10px 36px 10px 10px;
        font-size: 16px;
    }

    .modal-footer {
        padding: 12px;
    }

    .btn-primary,
    .btn-secondary {
        padding: 10px 14px;
        font-size: 13px;
    }
}

/* Landscape Mobile */
@media (max-width: 640px) and (orientation: landscape) and (max-height: 500px) {
    #custom-price-modal {
        align-items: center !important;
        justify-content: center !important;
        padding: 5px !important;
    }

    .modal-content-box {
        max-height: calc(100vh - 10px) !important;
        margin: 0 auto !important;
        display: flex;
        flex-direction: column;
    }

    .modal-header {
        padding: 12px;
        flex-shrink: 0;
    }

    .modal-icon {
        width: 36px;
        height: 36px;
        margin-bottom: 6px;
    }

    .modal-icon i {
        font-size: 16px;
    }

    .modal-title {
        font-size: 16px;
        margin-bottom: 2px;
    }

    .modal-subtitle {
        font-size: 11px;
    }

    .modal-body {
        padding: 8px 12px;
        flex: 1;
        overflow-y: auto;
    }

    .info-card {
        padding: 10px;
        margin-bottom: 10px;
        flex-direction: row;
        gap: 8px;
    }

    .info-icon {
        width: 28px;
        height: 28px;
    }

    .info-title {
        font-size: 12px;
        margin-bottom: 4px;
    }

    .info-list {
        font-size: 11px;
    }

    .input-group {
        gap: 10px;
    }

    .price-input {
        padding: 8px 32px 8px 8px;
        font-size: 14px;
    }

    .modal-footer {
        padding: 8px 12px;
        flex-shrink: 0;
        flex-direction: row;
    }

    .btn-primary,
    .btn-secondary {
        padding: 8px 12px;
        font-size: 12px;
    }
}

/* ===== PREMIUM ACTION BUTTONS ===== */

.action-buttons-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 24px 0;
    border-top: 1px solid #e5e7eb;
    margin-top: 16px;
}

@media (min-width: 640px) {
    .action-buttons-container {
        flex-direction: row;
        gap: 20px;
    }
}

/* Premium Search Button */
.btn-search-premium {
    position: relative;
    flex: 1;
    min-height: 48px;
    background: linear-gradient(135deg, #f37321 0%, #e67e22 50%, #d35400 100%);
    border: none;
    border-radius: 12px;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 6px 20px rgba(243, 115, 33, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    font-family: 'Be Vietnam Pro', sans-serif;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (min-width: 640px) {
    .btn-search-premium {
        flex: none;
        min-width: 140px;
    }
}

.btn-search-premium:hover {
    transform: translateY(-1px);
    box-shadow:
        0 8px 25px rgba(243, 115, 33, 0.35),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset;
    background: linear-gradient(135deg, #e67e22 0%, #d35400 50%, #c0392b 100%);
}

.btn-search-premium:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

/* Premium Reset Button */
.btn-reset-premium {
    position: relative;
    flex: 1;
    min-height: 48px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 3px 12px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(255, 255, 255, 0.8) inset;
    font-family: 'Be Vietnam Pro', sans-serif;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (min-width: 640px) {
    .btn-reset-premium {
        flex: none;
        min-width: 120px;
    }
}

.btn-reset-premium:hover {
    transform: translateY(-1px);
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.9) inset;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
    border-color: #cbd5e1;
}

.btn-reset-premium:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

/* Button Content */
.btn-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.02em;
}

.btn-search-premium .btn-content {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-reset-premium .btn-content {
    color: #475569;
}

.btn-content i {
    font-size: 16px;
    transition: transform 0.2s ease;
}

.btn-search-premium:hover .btn-content i {
    transform: scale(1.1);
}

.btn-reset-premium:hover .btn-content i {
    transform: rotate(180deg);
}

/* Shine Effect */
.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent);
    transition: left 0.6s ease;
    z-index: 1;
}

.btn-search-premium:hover .btn-shine,
.btn-reset-premium:hover .btn-shine {
    left: 100%;
}

/* Loading State */
.btn-search-premium.loading {
    pointer-events: none;
}

.btn-search-premium.loading .btn-content span {
    opacity: 0.7;
}

.btn-search-premium.loading .btn-content i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* Mobile Responsive */
@media (max-width: 639px) {
    .action-buttons-container {
        gap: 12px;
        padding: 20px 0;
    }

    .btn-search-premium,
    .btn-reset-premium {
        min-height: 44px;
    }

    .btn-content {
        font-size: 14px;
        gap: 6px;
    }

    .btn-content i {
        font-size: 14px;
    }
}

/* ===== ENHANCED SEARCH INPUT ===== */

.search-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

/* Search Icon (Left) */
.search-icon-left {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    color: #9ca3af;
    z-index: 2;
    pointer-events: none;
    transition: color 0.2s ease;
}

.search-input-enhanced:focus+.search-icon-left,
.search-input-container.focused .search-icon-left {
    color: var(--primary);
}

/* Icon Switching Logic */
.search-icon-left .search-icon,
.search-icon-left .typing-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Default state: Show search icon, hide typing icon */
.search-icon-left .search-icon {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.search-icon-left .typing-icon {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
}

/* When typing: Hide search icon, show typing icon */
.search-input-container.has-content .search-icon-left .search-icon {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
}

.search-input-container.has-content .search-icon-left .typing-icon {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
    color: var(--primary);
}

/* Action Buttons Container (Right) */
.search-actions-right {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 2;
    opacity: 1;
    visibility: visible;
    transition: all 0.3s ease;
}

.search-actions-right.show {
    opacity: 1;
    visibility: visible;
}

/* Clear Button */
.search-clear-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: rgba(239, 68, 68, 0.1);
    border: none;
    color: #ef4444;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
}

.search-clear-btn:hover {
    background: rgba(239, 68, 68, 0.15);
    transform: scale(1.05);
}

.search-clear-btn:active {
    transform: scale(0.95);
}

/* Show Clear Button when input has text */
.search-actions-right.show .search-clear-btn {
    opacity: 1;
    visibility: visible;
}

/* Quick Search Button */
.search-submit-btn {
    min-width: 90px;
    height: 32px;
    padding: 0 12px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--primary) 0%, #e67e22 100%);
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.2s ease;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(243, 115, 33, 0.3);
    white-space: nowrap;
}

.search-submit-btn:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.4);
}

.search-submit-btn:active {
    transform: scale(0.95);
}

/* Input Adjustments */
.search-input-enhanced {
    padding-right: 140px !important;
}

.search-input-enhanced:focus {
    padding-right: 140px !important;
}

/* Animation for buttons appearance */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(10px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}



/* Mobile Responsive */
@media (max-width: 640px) {
    .search-clear-btn {
        width: 30px;
        height: 30px;
        font-size: 11px;
    }

    .search-submit-btn {
        min-width: 80px;
        height: 30px;
        padding: 0 10px;
        font-size: 11px;
        gap: 4px;
    }

    .search-input-enhanced {
        padding-right: 120px !important;
    }

    .search-actions-right {
        right: 6px;
        gap: 3px;
    }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
    .search-submit-btn {
        min-width: 70px;
        padding: 0 8px;
        font-size: 10px;
    }

    .search-submit-btn span {
        display: none;
    }

    .search-submit-btn {
        width: 30px;
        min-width: 30px;
    }

    .search-input-enhanced {
        padding-right: 70px !important;
    }
}

/* ===== CLEAR ALL BADGE ===== */

.clear-all-badge {
    /* Explicitly inherit all sizing and layout properties from .filter-badge */
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
    animation: fadeInScale 0.3s ease-out;
    max-width: 300px;
    min-width: 0;
    height: 40px;
    /* Đặt chiều cao cố định giống filter-badge */
    box-sizing: border-box;
    /* Đảm bảo padding và border được tính trong chiều cao */

    /* Custom colors for clear-all badge */
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #64748b;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    user-select: none;
}

.clear-all-badge:hover {
    /* Keep default hover behavior from .filter-badge, only override colors */
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    color: #475569;
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.clear-all-badge:active {
    transform: translateY(0);
}

.clear-all-badge .badge-text {
    color: inherit !important;
    font-weight: 500;
}

.clear-all-badge i {
    color: inherit !important;
    transition: transform 0.2s ease;
}

.clear-all-badge:hover i.fa-broom {
    transform: rotate(-15deg);
}

.clear-all-badge:hover i.fa-times {
    transform: scale(1.1);
}

/* Subtle shine effect */
.clear-all-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.4),
            transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.clear-all-badge:hover::before {
    left: 100%;
}

.clear-all-badge .badge-text,
.clear-all-badge i {
    position: relative;
    z-index: 2;
}

/* Product Rating and Sales Section */
.product-rating-sales {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    /* Reduced separation from price section */
    padding: 0;
    border-top: none;
    /* Remove border for cleaner look */
    width: 100%;
}

.rating-section {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    flex-shrink: 0;
}

.rating-section .stars {
    display: flex;
    gap: 0.125rem;
    align-items: center;
}

.rating-section .stars i {
    font-size: 0.75rem;
    color: #fbbf24;
    transition: color 0.2s ease;
}

.rating-section .stars i.far {
    color: #d1d5db;
}

.rating-section .rating-text {
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    margin-left: 0.25rem;
}

.sales-section {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex-shrink: 0;
    margin-left: auto;
}

.sales-section i {
    font-size: 0.75rem;
    color: #3b82f6;
}

.sales-section span {
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    white-space: nowrap;
}

/* Responsive adjustments for rating and sales */
@media (max-width: 768px) {
    .product-rating-sales {
        margin-top: 0.5rem;
        /* Reduced space on mobile */
        gap: 0.5rem;
        padding: 0;
    }

    .rating-section .stars i {
        font-size: 0.625rem;
    }

    .rating-section .rating-text,
    .sales-section span {
        font-size: 0.625rem;
    }

    .sales-section i {
        font-size: 0.625rem;
        color: #3b82f6;
    }
}

/* ===== CUSTOM GRID UTILITY CLASSES FOR NEW BREAKPOINTS ===== */

/* MD+: 900px+ (3 columns) */
@media (min-width: 900px) {
    .md-plus\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

/* LG+: 1024px+ (4 columns) */
@media (min-width: 1024px) {
    .lg-plus\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

/* XXL: 1400px+ (4 columns) */
@media (min-width: 1400px) {
    .xxl\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

/* Ultra: 1600px+ (4 columns) */
@media (min-width: 1600px) {
    .ultra\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}