<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra phương thức request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Ph<PERSON>ơng thức không được hỗ trợ']);
    exit;
}

// Kiểm tra CSRF token
if (!check_csrf_token($_POST['csrf_token'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'CSRF token không hợp lệ']);
    exit;
}

// L<PERSON>y dữ liệu từ request
$product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
$review_title = isset($_POST['review_title']) ? sanitize($_POST['review_title']) : '';
$review_content = isset($_POST['review_content']) ? sanitize($_POST['review_content']) : '';
$rating = isset($_POST['rating']) ? intval($_POST['rating']) : null;
$is_anonymous = isset($_POST['is_anonymous']) ? true : false;

// Kiểm tra dữ liệu
if (empty($product_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID sản phẩm không hợp lệ']);
    exit;
}

if (empty($review_content)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Vui lòng nhập nội dung đánh giá']);
    exit;
}

// Chuẩn bị dữ liệu đánh giá
$review_data = [
    'review_title' => $review_title,
    'review_content' => $review_content,
    'is_anonymous' => $is_anonymous ? 1 : 0
];

// Nếu người dùng đã đăng nhập
if (is_logged_in()) {
    $review_data['user_id'] = $_SESSION['user_id'];

    // Kiểm tra xem người dùng đã mua sản phẩm chưa
    $has_purchased = has_purchased_product($_SESSION['user_id'], $product_id);

    // Nếu đã mua sản phẩm thì mới cho phép đánh giá sao
    if ($has_purchased && $rating !== null) {
        $review_data['rating'] = $rating;
    }
} else {
    // Nếu là khách, yêu cầu thông tin
    $guest_name = isset($_POST['guest_name']) ? sanitize($_POST['guest_name']) : '';
    $guest_email = isset($_POST['guest_email']) ? sanitize($_POST['guest_email']) : '';

    if (empty($guest_name) || empty($guest_email)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Vui lòng nhập tên và email']);
        exit;
    }

    // Kiểm tra định dạng email
    if (!filter_var($guest_email, FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Email không hợp lệ']);
        exit;
    }

    $review_data['guest_name'] = $guest_name;
    $review_data['guest_email'] = $guest_email;
}

// Xử lý upload media
$media_files = [];
if (isset($_FILES['media']) && is_array($_FILES['media']['name'])) {
    for ($i = 0; $i < count($_FILES['media']['name']); $i++) {
        if ($_FILES['media']['error'][$i] === UPLOAD_ERR_OK) {
            $media_files[] = [
                'name' => $_FILES['media']['name'][$i],
                'type' => $_FILES['media']['type'][$i],
                'tmp_name' => $_FILES['media']['tmp_name'][$i],
                'error' => $_FILES['media']['error'][$i],
                'size' => $_FILES['media']['size'][$i]
            ];
        }
    }
}

// Thêm đánh giá
$result = add_product_review($product_id, $review_data, $media_files);

// Trả về kết quả
header('Content-Type: application/json');
echo json_encode($result);
?>
