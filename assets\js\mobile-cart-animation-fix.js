/**
 * Fix cho hiệu ứng bay vào giỏ hàng trên mobile
 * File này hoàn toàn thay thế hàm animateAddToCart để đảm bảo hiệu ứng bay vào giỏ hàng luôn hoạt động đúng
 */

document.addEventListener('DOMContentLoaded', function () {
  console.log('Mobile cart animation fix loaded');

  // Hoàn toàn thay thế hàm animateAddToCart với phiên bản mới
  window.animateAddToCart = function (button) {
    console.log('Using new animateAddToCart implementation');

    // Ki<PERSON><PERSON> tra xem đang ở giao diện desktop hay mobile
    const isMobile = window.innerWidth <= 576;
    console.log('Device detection:', {
      isMobile,
      windowWidth: window.innerWidth,
    });

    // Tạo phần tử ảo để hiệu ứng
    const element = document.createElement('div');
    element.className = 'cart-animation';

    // Lấy vị trí của nút thêm vào giỏ
    const buttonRect = button.getBoundingClientRect();
    console.log('Button rect:', buttonRect);

    // Thiết lập vị trí ban đầu
    element.style.top = buttonRect.top + 'px';
    element.style.left = buttonRect.left + 'px';
    element.style.width = buttonRect.width + 'px';
    element.style.height = buttonRect.height + 'px';
    element.style.position = 'fixed';
    element.style.zIndex = '9999';
    element.style.backgroundColor = 'rgba(249, 115, 22, 0.2)';
    element.style.borderRadius = '50%';
    element.style.transition = isMobile
      ? 'all 0.8s cubic-bezier(0.16, 1, 0.3, 1)'
      : 'all 0.6s cubic-bezier(0.16, 1, 0.3, 1)';
    element.style.pointerEvents = 'none';
    element.style.willChange = 'transform, opacity, top, left, width, height';

    // Thêm vào body
    document.body.appendChild(element);

    // Xác định vị trí đích dựa trên giao diện
    let targetTop, targetLeft;

    if (isMobile) {
      // Trên mobile, nhắm đến thanh navigation phía dưới
      // Lấy vị trí của mobile-bottom-nav
      const mobileNav = document.querySelector('.mobile-bottom-nav');

      if (mobileNav) {
        const mobileNavRect = mobileNav.getBoundingClientRect();
        console.log('Mobile nav rect:', mobileNavRect);

        // Tìm nút giỏ hàng trong mobile-bottom-nav
        const cartButton = mobileNav.querySelector(
          '.mobile-nav-item[href*="/cart.php"]'
        );

        if (cartButton) {
          const cartButtonRect = cartButton.getBoundingClientRect();
          console.log('Cart button rect:', cartButtonRect);

          // Tìm icon giỏ hàng
          const cartIcon = cartButton.querySelector('i.fa-shopping-cart');

          if (cartIcon) {
            const iconRect = cartIcon.getBoundingClientRect();
            console.log('Cart icon rect:', iconRect);

            // Nhắm đến giữa icon giỏ hàng
            targetTop = iconRect.top + iconRect.height / 2;
            targetLeft = iconRect.left + iconRect.width / 2;
          } else {
            // Fallback: nhắm đến giữa nút giỏ hàng
            targetTop = cartButtonRect.top + cartButtonRect.height / 2;
            targetLeft = cartButtonRect.left + cartButtonRect.width / 2;
          }
        } else {
          // Fallback: nhắm đến giữa thanh navigation
          targetTop = mobileNavRect.top + mobileNavRect.height / 2;
          targetLeft = mobileNavRect.left + mobileNavRect.width / 2;
        }
      } else {
        // Fallback: nhắm đến góc dưới bên phải màn hình
        targetTop = window.innerHeight - 30;
        targetLeft = window.innerWidth - 30;
      }

      console.log('Target position for mobile:', { targetTop, targetLeft });
    } else {
      // Trên desktop, nhắm đến giỏ hàng ở góc trên bên phải
      const cartButton = document.querySelector('.cart-btn');

      if (cartButton) {
        const cartButtonRect = cartButton.getBoundingClientRect();
        console.log('Desktop cart button rect:', cartButtonRect);

        // Nhắm đến giữa nút giỏ hàng
        targetTop = cartButtonRect.top + cartButtonRect.height / 2;
        targetLeft = cartButtonRect.left + cartButtonRect.width / 2;
      } else {
        // Fallback: nhắm đến góc trên bên phải màn hình
        targetTop = 30;
        targetLeft = window.innerWidth - 30;
      }

      console.log('Target position for desktop:', { targetTop, targetLeft });
    }

    // Thiết lập hiệu ứng bay
    setTimeout(() => {
      element.style.top = targetTop + 'px';
      element.style.left = targetLeft + 'px';
      element.style.width = '20px';
      element.style.height = '20px';
      element.style.opacity = '0';
      element.style.backgroundColor = 'rgba(249, 115, 22, 0.6)';
      element.style.transform = 'scale(0.5)';

      // Xóa phần tử sau khi hoàn thành hiệu ứng
      setTimeout(
        () => {
          element.remove();
        },
        isMobile ? 800 : 600
      );
    }, 10);

    // Thêm hiệu ứng nhấp nháy cho badge giỏ hàng - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT
    if (isMobile) {
      // Hiệu ứng cho mobile - ĐÃ TẮT
      const mobileNavBadge = document.querySelector('.mobile-nav-badge');

      if (mobileNavBadge) {
        // Đợi hiệu ứng bay hoàn thành một phần
        setTimeout(() => {
          // mobileNavBadge.classList.add('badge-pulse');
          setTimeout(() => {
            // mobileNavBadge.classList.remove('badge-pulse');
          }, 1000);
        }, 400);
      }

      // Thêm hiệu ứng active cho nút giỏ hàng - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT
      const mobileCartButton = document.querySelector(
        '.mobile-bottom-nav .mobile-nav-item[href*="/cart.php"]'
      );

      if (mobileCartButton) {
        setTimeout(() => {
          // mobileCartButton.classList.add('active');
          setTimeout(() => {
            // mobileCartButton.classList.remove('active');
          }, 800);
        }, 400);
      }
    } else {
      // Hiệu ứng cho desktop
      const cartBadges = document.querySelectorAll('.cart-badge');

      cartBadges.forEach((badge) => {
        badge.classList.add('badge-pulse');
        setTimeout(() => {
          badge.classList.remove('badge-pulse');
        }, 1000);
      });
    }

    // Cập nhật số lượng giỏ hàng
    if (typeof window.updateCartCount === 'function') {
      window.updateCartCount();
    }
  };

  console.log('New cart animation implementation applied');
});
