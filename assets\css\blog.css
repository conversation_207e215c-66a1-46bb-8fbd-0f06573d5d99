/* Blog CSS - <PERSON><PERSON><PERSON>t <PERSON> */

/* Blog Container */
.blog-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Blog Header */
.blog-header {
    margin-bottom: 2rem;
    text-align: center;
}

.blog-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.blog-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
}

/* Blog Grid */
.blog-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

/* Blog Sidebar Layout */
.blog-with-sidebar {
    display: grid;
    grid-template-columns: 3fr 1fr;
    gap: 2rem;
}

/* Blog Card */
.blog-card {
    background-color: #fff;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.blog-card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.blog-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.blog-card:hover .blog-card-image img {
    transform: scale(1.05);
}

.blog-card-content {
    padding: 1.5rem;
}

.blog-card-category {
    display: inline-block;
    background-color: rgba(243, 115, 33, 0.1);
    color: #F37321;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    margin-bottom: 0.75rem;
}

.blog-card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.blog-card-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.2s ease;
}

.blog-card-title a:hover {
    color: #F37321;
}

.blog-card-excerpt {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.blog-card-meta {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    color: #888;
}

.blog-card-author {
    display: flex;
    align-items: center;
    margin-right: 1rem;
}

.blog-card-author-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 0.5rem;
}

.blog-card-author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.blog-card-date {
    display: flex;
    align-items: center;
}

.blog-card-date i {
    margin-right: 0.25rem;
}

/* Featured Post */
.blog-featured {
    grid-column: span 3;
    display: grid;
    grid-template-columns: 1fr 1fr;
    background-color: #fff;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
}

.blog-featured-image {
    height: 100%;
    min-height: 350px;
}

.blog-featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.blog-featured-content {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.blog-featured-label {
    display: inline-block;
    background-color: #F37321;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    margin-bottom: 1rem;
}

.blog-featured-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.blog-featured-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.2s ease;
}

.blog-featured-title a:hover {
    color: #F37321;
}

.blog-featured-excerpt {
    font-size: 1rem;
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.blog-featured-meta {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #888;
    margin-bottom: 1.5rem;
}

.blog-featured-button {
    display: inline-block;
    background-color: #F37321;
    color: white;
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: background-color 0.2s ease;
    align-self: flex-start;
}

.blog-featured-button:hover {
    background-color: #e06518;
}

/* Blog Sidebar */
.blog-sidebar {
    position: sticky;
    top: 2rem;
}

.sidebar-widget {
    background-color: #fff;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.sidebar-widget-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #f0f0f0;
}

.sidebar-categories,
.sidebar-tags {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-categories li,
.sidebar-tags li {
    margin-bottom: 0.5rem;
}

.sidebar-categories a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #555;
    text-decoration: none;
    padding: 0.5rem 0;
    transition: color 0.2s ease;
}

.sidebar-categories a:hover {
    color: #F37321;
}

.sidebar-categories .category-count {
    background-color: #f0f0f0;
    color: #666;
    font-size: 0.75rem;
    padding: 0.15rem 0.5rem;
    border-radius: 1rem;
}

.sidebar-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.sidebar-tags a {
    display: inline-block;
    background-color: #f0f0f0;
    color: #666;
    font-size: 0.8rem;
    padding: 0.35rem 0.75rem;
    border-radius: 1rem;
    text-decoration: none;
    transition: all 0.2s ease;
}

.sidebar-tags a:hover {
    background-color: #F37321;
    color: white;
}

/* Blog Pagination */
.blog-pagination {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
}

.pagination-item {
    margin: 0 0.25rem;
}

.pagination-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #fff;
    color: #666;
    text-decoration: none;
    transition: all 0.2s ease;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pagination-link:hover {
    background-color: #f0f0f0;
}

.pagination-link.active {
    background-color: #F37321;
    color: white;
}

.pagination-prev,
.pagination-next {
    width: auto;
    padding: 0 1rem;
    border-radius: 20px;
}

/* Responsive */
@media (max-width: 1024px) {
    .blog-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .blog-featured {
        grid-column: span 2;
    }
}

@media (max-width: 768px) {
    .blog-with-sidebar {
        grid-template-columns: 1fr;
    }
    
    .blog-featured {
        grid-template-columns: 1fr;
    }
    
    .blog-featured-image {
        min-height: 250px;
    }
}

@media (max-width: 640px) {
    .blog-grid {
        grid-template-columns: 1fr;
    }
    
    .blog-featured {
        grid-column: span 1;
    }
    
    .blog-header h1 {
        font-size: 2rem;
    }
}
