/**
 * JavaScript for Company Introduction Section - Professional & Trustworthy Design
 */
document.addEventListener('DOMContentLoaded', function() {
    initCompanyIntro();
});

/**
 * Initialize company introduction section
 */
function initCompanyIntro() {
    // Add scroll animation for company intro section
    addScrollAnimation();

    // Add hover effects for advantage items
    addAdvantageHoverEffects();

    // Add subtle hover effect for company image
    addImageHoverEffect();

    // Add click effect for call button
    addCallButtonEffect();

    // Initialize counters for company stats
    initCompanyStats();

    // Initialize warranty badge counter
    initWarrantyBadge();

    // Add hover effects for material tags
    addMaterialTagsEffect();
}

/**
 * Add scroll animation for company intro section
 */
function addScrollAnimation() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // Observe all elements with fade-in-up class
    document.querySelectorAll('.company-intro-section .fade-in-up').forEach(element => {
        observer.observe(element);
    });
}

/**
 * Add hover effects for advantage items
 */
function addAdvantageHoverEffects() {
    const advantageItems = document.querySelectorAll('.advantage-item');

    advantageItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.advantage-icon i');
            if (icon) {
                icon.classList.add('fa-bounce');
                setTimeout(() => {
                    icon.classList.remove('fa-bounce');
                }, 1000);
            }

            // Add subtle shadow increase
            this.style.boxShadow = '0 15px 20px -5px rgba(0, 0, 0, 0.15)';
        });

        item.addEventListener('mouseleave', function() {
            // Reset shadow
            this.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)';
        });
    });
}

/**
 * Add subtle hover effect for company image
 */
function addImageHoverEffect() {
    const imageWrapper = document.querySelector('.company-image-wrapper');

    if (!imageWrapper) return;

    imageWrapper.addEventListener('mouseenter', function() {
        const image = this.querySelector('.company-image');
        if (image) {
            image.style.transform = 'scale(1.03)';
        }
    });

    imageWrapper.addEventListener('mouseleave', function() {
        const image = this.querySelector('.company-image');
        if (image) {
            image.style.transform = 'scale(1)';
        }
    });
}

/**
 * Add click effect for call button
 */
function addCallButtonEffect() {
    const callButton = document.querySelector('.call-button');

    if (!callButton) return;

    callButton.addEventListener('click', function() {
        // Add click animation
        this.classList.add('button-clicked');

        // Remove animation class after animation completes
        setTimeout(() => {
            this.classList.remove('button-clicked');
        }, 300);

        // Get phone number from data attribute
        const phoneNumber = this.getAttribute('data-phone');

        // Open phone dialer
        if (phoneNumber) {
            window.location.href = `tel:${phoneNumber}`;
        }
    });
}

/**
 * Initialize counters for company stats
 */
function initCompanyStats() {
    const statValues = document.querySelectorAll('.stat-value');

    if (statValues.length === 0) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const targetValue = parseInt(element.getAttribute('data-value'), 10);

                animateCounter(element, targetValue);
                observer.unobserve(element);
            }
        });
    }, {
        threshold: 0.5
    });

    statValues.forEach(stat => {
        observer.observe(stat);
    });
}

/**
 * Initialize warranty badge counter
 */
function initWarrantyBadge() {
    const yearsElement = document.querySelector('.warranty-badge .years');

    if (!yearsElement) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const targetYears = parseInt(yearsElement.getAttribute('data-years'), 10);
                animateCounter(yearsElement, targetYears);
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.5
    });

    observer.observe(yearsElement);
}

/**
 * Animate counter from 0 to target value
 */
function animateCounter(element, targetValue) {
    const duration = 2000; // 2 seconds
    const frameDuration = 1000 / 60; // 60fps
    const totalFrames = Math.round(duration / frameDuration);
    let frame = 0;

    const counter = setInterval(() => {
        frame++;

        // Calculate current count with easing
        const progress = easeOutQuad(frame / totalFrames);
        const currentCount = Math.round(targetValue * progress);

        // Update count
        element.textContent = currentCount;

        // Check if animation is complete
        if (frame === totalFrames) {
            clearInterval(counter);
            element.textContent = targetValue;
        }
    }, frameDuration);
}

/**
 * Easing function for smoother animation
 */
function easeOutQuad(x) {
    return 1 - (1 - x) * (1 - x);
}

/**
 * Add hover effects for material tags
 */
function addMaterialTagsEffect() {
    const materialTags = document.querySelectorAll('.material-tag');

    materialTags.forEach(tag => {
        tag.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(243, 115, 33, 0.05)';
            this.style.borderColor = 'rgba(243, 115, 33, 0.3)';
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';

            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1.2)';
            }
        });

        tag.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'white';
            this.style.borderColor = 'rgba(0, 0, 0, 0.1)';
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 1px 2px rgba(0, 0, 0, 0.05)';

            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1)';
            }
        });
    });
}
