{"version": 3, "file": "lang/summernote-hu-HU.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,kBAAkB;QACzBC,MAAM,EAAE,QAAQ;QAChBC,IAAI,EAAE,WAAW;QACjBC,aAAa,EAAE,UAAU;QACzBC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,aAAa;QAC1BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,eAAe;QACvBC,UAAU,EAAE,4BAA4B;QACxCC,UAAU,EAAE,oBAAoB;QAChCC,aAAa,EAAE,uBAAuB;QACtCC,SAAS,EAAE,gBAAgB;QAC3BC,UAAU,EAAE,iBAAiB;QAC7BC,SAAS,EAAE,kBAAkB;QAC7BC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,gCAAgC;QAC/CC,SAAS,EAAE,kCAAkC;QAC7CC,eAAe,EAAE,qBAAqB;QACtCC,eAAe,EAAE,mBAAmB;QACpCC,oBAAoB,EAAE,6BAA6B;QACnDC,GAAG,EAAE,cAAc;QACnBC,MAAM,EAAE,aAAa;QACrBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,kBAAkB;QAC7BpB,MAAM,EAAE,iBAAiB;QACzBgB,GAAG,EAAE,gBAAgB;QACrBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,YAAY;QAClBtB,MAAM,EAAE,sBAAsB;QAC9BuB,MAAM,EAAE,0BAA0B;QAClCC,IAAI,EAAE,aAAa;QACnBC,aAAa,EAAE,uBAAuB;QACtCT,GAAG,EAAE,+BAA+B;QACpCU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,UAAU;QACjBC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,eAAe;QAC5BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,CAAC,EAAE,QAAQ;QACXC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,KAAK;QACVC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,kBAAkB;QAC7BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,MAAM;QACZC,UAAU,EAAE,iBAAiB;QAC7BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,qBAAqB;QAC9BC,MAAM,EAAE,kBAAkB;QAC1BC,IAAI,EAAE,gBAAgB;QACtBC,MAAM,EAAE,kBAAkB;QAC1BC,KAAK,EAAE,iBAAiB;QACxBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE,YAAY;QACxBC,UAAU,EAAE,UAAU;QACtBC,WAAW,EAAE,UAAU;QACvBC,cAAc,EAAE,uBAAuB;QACvCC,KAAK,EAAE,eAAe;QACtBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,gBAAgB;QAC3BC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE,kBAAkB;QAClCC,MAAM,EAAE,SAAS;QACjBC,mBAAmB,EAAE,oBAAoB;QACzCC,aAAa,EAAE,kBAAkB;QACjCC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,aAAa;QAChC,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,MAAM;QACd,KAAK,EAAE,kBAAkB;QACzB,OAAO,EAAE,qBAAqB;QAC9B,MAAM,EAAE,oBAAoB;QAC5B,QAAQ,EAAE,gBAAgB;QAC1B,WAAW,EAAE,UAAU;QACvB,eAAe,EAAE,SAAS;QAC1B,cAAc,EAAE,kBAAkB;QAClC,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,WAAW;QAC1B,qBAAqB,EAAE,yBAAyB;QAChD,mBAAmB,EAAE,uBAAuB;QAC5C,SAAS,EAAE,8CAA8C;QACzD,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sCAAsC;QACpD,UAAU,EAAE,gCAAgC;QAC5C,UAAU,EAAE,gCAAgC;QAC5C,UAAU,EAAE,gCAAgC;QAC5C,UAAU,EAAE,gCAAgC;QAC5C,UAAU,EAAE,gCAAgC;QAC5C,UAAU,EAAE,gCAAgC;QAC5C,sBAAsB,EAAE,4BAA4B;QACpD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,aAAa;QACnBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-hu-HU.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'hu-HU': {\n      font: {\n        bold: 'Félkövér',\n        italic: 'Dőlt',\n        underline: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        clear: '<PERSON><PERSON>z<PERSON> törlése',\n        height: '<PERSON><PERSON><PERSON><PERSON>',\n        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        strikethrough: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Betűméret',\n      },\n      image: {\n        image: 'Kép',\n        insert: 'Kép beszúrása',\n        resizeFull: 'Átméretezés teljes méretre',\n        resizeHalf: 'Átméretezés felére',\n        resizeQuarter: 'Átméretezés negyedére',\n        floatLeft: 'Igazítás balra',\n        floatRight: 'Igazítás jobbra',\n        floatNone: 'Igazítás törlése',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Ide húzhat képet vagy szöveget',\n        dropImage: 'Engedje el a képet vagy szöveget',\n        selectFromFiles: 'Fájlok kiválasztása',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'Kép URL címe',\n        remove: 'Kép törlése',\n        original: 'Original',\n      },\n      video: {\n        video: 'Videó',\n        videoLink: 'Videó hivatkozás',\n        insert: 'Videó beszúrása',\n        url: 'Videó URL címe',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion vagy Youku)',\n      },\n      link: {\n        link: 'Hivatkozás',\n        insert: 'Hivatkozás beszúrása',\n        unlink: 'Hivatkozás megszüntetése',\n        edit: 'Szerkesztés',\n        textToDisplay: 'Megjelenítendő szöveg',\n        url: 'Milyen URL címre hivatkozzon?',\n        openInNewWindow: 'Megnyitás új ablakban',\n      },\n      table: {\n        table: 'Táblázat',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Elválasztó vonal beszúrása',\n      },\n      style: {\n        style: 'Stílus',\n        p: 'Normál',\n        blockquote: 'Idézet',\n        pre: 'Kód',\n        h1: 'Fejléc 1',\n        h2: 'Fejléc 2',\n        h3: 'Fejléc 3',\n        h4: 'Fejléc 4',\n        h5: 'Fejléc 5',\n        h6: 'Fejléc 6',\n      },\n      lists: {\n        unordered: 'Listajeles lista',\n        ordered: 'Számozott lista',\n      },\n      options: {\n        help: 'Súgó',\n        fullscreen: 'Teljes képernyő',\n        codeview: 'Kód nézet',\n      },\n      paragraph: {\n        paragraph: 'Bekezdés',\n        outdent: 'Behúzás csökkentése',\n        indent: 'Behúzás növelése',\n        left: 'Igazítás balra',\n        center: 'Igazítás középre',\n        right: 'Igazítás jobbra',\n        justify: 'Sorkizárt',\n      },\n      color: {\n        recent: 'Jelenlegi szín',\n        more: 'További színek',\n        background: 'Háttérszín',\n        foreground: 'Betűszín',\n        transparent: 'Átlátszó',\n        setTransparent: 'Átlászóság beállítása',\n        reset: 'Visszaállítás',\n        resetToDefault: 'Alaphelyzetbe állítás',\n      },\n      shortcut: {\n        shortcuts: 'Gyorsbillentyű',\n        close: 'Bezárás',\n        textFormatting: 'Szöveg formázása',\n        action: 'Művelet',\n        paragraphFormatting: 'Bekezdés formázása',\n        documentStyle: 'Dokumentumstílus',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Új bekezdés',\n        'undo': 'Visszavonás',\n        'redo': 'Újra',\n        'tab': 'Behúzás növelése',\n        'untab': 'Behúzás csökkentése',\n        'bold': 'Félkövérre állítás',\n        'italic': 'Dőltre állítás',\n        'underline': 'Aláhúzás',\n        'strikethrough': 'Áthúzás',\n        'removeFormat': 'Formázás törlése',\n        'justifyLeft': 'Balra igazítás',\n        'justifyCenter': 'Középre igazítás',\n        'justifyRight': 'Jobbra igazítás',\n        'justifyFull': 'Sorkizárt',\n        'insertUnorderedList': 'Számozatlan lista be/ki',\n        'insertOrderedList': 'Számozott lista be/ki',\n        'outdent': 'Jelenlegi bekezdés behúzásának megszüntetése',\n        'indent': 'Jelenlegi bekezdés behúzása',\n        'formatPara': 'Blokk formázása bekezdésként (P tag)',\n        'formatH1': 'Blokk formázása, mint Fejléc 1',\n        'formatH2': 'Blokk formázása, mint Fejléc 2',\n        'formatH3': 'Blokk formázása, mint Fejléc 3',\n        'formatH4': 'Blokk formázása, mint Fejléc 4',\n        'formatH5': 'Blokk formázása, mint Fejléc 5',\n        'formatH6': 'Blokk formázása, mint Fejléc 6',\n        'insertHorizontalRule': 'Vízszintes vonal beszúrása',\n        'linkDialog.show': 'Link párbeszédablak megjelenítése',\n      },\n      history: {\n        undo: 'Visszavonás',\n        redo: 'Újra',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}