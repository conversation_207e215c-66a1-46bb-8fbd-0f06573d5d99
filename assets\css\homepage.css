/* Homepage CSS - <PERSON><PERSON><PERSON>hất Băng <PERSON>ũ */

/* <PERSON><PERSON><PERSON>n màu ch<PERSON>h */
:root {
    --primary: #F37321;
    --primary-dark: #d15a0a;
    --secondary: #2A3B47;
    --accent: #4CAF50;
    --light: #F8F9FA;
    --dark: #212529;
    --gray: #6c757d;
    --success: #28a745;
    --info: #17a2b8;
    --warning: #ffc107;
    --danger: #dc3545;
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fadeIn {
    animation: fadeIn 1s ease-in-out;
}

.animate-fadeInUp {
    animation: fadeInUp 1s ease-in-out 0.3s;
    animation-fill-mode: both;
}

.animate-fadeInLeft {
    animation: fadeInLeft 1s ease-in-out 0.6s;
    animation-fill-mode: both;
}

.animate-fadeInRight {
    animation: fadeInRight 1s ease-in-out 0.9s;
    animation-fill-mode: both;
}

/* Hero Section */
.hero-slider {
    position: relative;
}

/* Category Cards */
.category-card {
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-10px);
}

/* Product Card Grid */
.product-card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* Product Cards */
.product-card {
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-image {
    position: relative;
    padding-top: 100%;
    /* Tỷ lệ 1:1 */
    overflow: hidden;
}

.product-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

/* Premium Sale Badge Hover Effect - Synchronized with product card hover */
.premium-sale-badge:hover,
.modern-product-card:hover .premium-sale-badge,
.product-card:hover .premium-sale-badge,
.group:hover .premium-sale-badge {
    transform: rotate(0deg) scale(1.05) !important;
    box-shadow:
        0 6px 16px rgba(243, 115, 33, 0.5),
        0 3px 6px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    background: linear-gradient(135deg, #FF8A3D 0%, #F37321 50%, #D65A0F 100%) !important;
    animation: none !important; /* Stop pulse animation on hover */
}

/* Product Tabs */
.product-tab-btn.active {
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
}

/* Testimonial Slider */
.testimonial-slider {
    position: relative;
}

/* Blog/News Cards */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Two Column Layout */
.two-column-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .two-column-layout {
        grid-template-columns: 1fr 2fr;
        gap: 2rem;
    }
}

/* Utilities */
.bg-primary {
    background-color: var(--primary) !important;
}

.bg-primary-dark {
    background-color: var(--primary-dark) !important;
}

.text-primary {
    color: var(--primary) !important;
}

.border-primary {
    border-color: var(--primary) !important;
}

.hover\:bg-primary:hover {
    background-color: var(--primary) !important;
}

.hover\:text-primary:hover {
    color: var(--primary) !important;
}

.hover\:border-primary:hover {
    border-color: var(--primary) !important;
}