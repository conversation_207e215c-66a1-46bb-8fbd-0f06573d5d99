<?php
/**
 * File quản lý xác thực người dùng
 * Sử dụng một cơ chế đăng nhập duy nhất cho cả admin và user
 */

/**
 * Đăng ký tài khoản mới
 */
function register_user($username, $email, $password, $full_name, $avatar = null) {
    global $conn;

    try {
        // Kiểm tra tên đăng nhập hợp lệ (không dấu cách, không dấu tiếng Việt, chỉ chứa chữ cái, số và dấu gạch dưới)
        if (strpos($username, ' ') !== false ||
            preg_match('/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i', $username) ||
            !preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            return [
                'success' => false,
                'message' => 'Tên đăng nhập chỉ được phép chứa chữ cái không dấu, số và dấu gạch dưới'
            ];
        }

        // Kiểm tra username đã tồn tại chưa
        $stmt = $conn->prepare("SELECT id FROM users WHERE username = :username");
        $stmt->bindParam(':username', $username);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return [
                'success' => false,
                'message' => 'Tên đăng nhập đã tồn tại'
            ];
        }

        // Kiểm tra email đã tồn tại chưa
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = :email");
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return [
                'success' => false,
                'message' => 'Email đã tồn tại'
            ];
        }

        // Mã hóa mật khẩu
        $hashed_password = password_hash($password, PASSWORD_DEFAULT, ['cost' => HASH_COST]);

        // Xử lý upload ảnh đại diện nếu có
        $avatar_filename = null;
        if ($avatar && $avatar['error'] === UPLOAD_ERR_OK) {
            $upload_dir = ROOT_PATH . 'uploads/avatars/';

            // Tạo thư mục nếu chưa tồn tại
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            $upload_result = upload_file($avatar, $upload_dir, $allowed_types);

            if ($upload_result['success']) {
                $avatar_filename = $upload_result['filename'];
            }
        }

        // Bắt đầu transaction
        $conn->beginTransaction();

        // Thêm người dùng mới
        if ($avatar_filename) {
            $stmt = $conn->prepare("INSERT INTO users (username, email, password, full_name, avatar) VALUES (:username, :email, :password, :full_name, :avatar)");
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':password', $hashed_password);
            $stmt->bindParam(':full_name', $full_name);
            $stmt->bindParam(':avatar', $avatar_filename);
        } else {
            $stmt = $conn->prepare("INSERT INTO users (username, email, password, full_name) VALUES (:username, :email, :password, :full_name)");
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':password', $hashed_password);
            $stmt->bindParam(':full_name', $full_name);
        }
        $stmt->execute();

        // Lấy ID của người dùng vừa tạo
        $user_id = $conn->lastInsertId();

        // Commit transaction
        $conn->commit();

        return [
            'success' => true,
            'message' => 'Đăng ký tài khoản thành công',
            'user_id' => $user_id
        ];
    } catch (PDOException $e) {
        // Rollback transaction nếu có lỗi
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }

        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Đăng nhập
 */
function login_user($username, $password) {
    global $conn;

    try {
        // Tìm người dùng theo username
        $stmt = $conn->prepare("SELECT * FROM users WHERE username = :username");
        $stmt->bindParam(':username', $username);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Tên đăng nhập hoặc mật khẩu không đúng'
            ];
        }

        $user = $stmt->fetch();

        // Kiểm tra mật khẩu
        if (!password_verify($password, $user['password'])) {
            return [
                'success' => false,
                'message' => 'Tên đăng nhập hoặc mật khẩu không đúng'
            ];
        }

        // Kiểm tra trạng thái tài khoản
        if (isset($user['status']) && $user['status'] === 'locked') {
            return [
                'success' => false,
                'message' => 'Tài khoản của bạn đã bị khóa. Vui lòng liên hệ quản trị viên để được hỗ trợ.'
            ];
        }

        // Lưu thông tin người dùng vào session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['full_name'] = $user['full_name'];

        // Đồng bộ hóa giỏ hàng từ session vào database
        if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
            // Xóa giỏ hàng cũ trong database (nếu có)
            clear_cart_in_database($user['id']);

            // Lưu giỏ hàng hiện tại vào database
            foreach ($_SESSION['cart'] as $item) {
                save_cart_to_database(
                    $user['id'],
                    $item['product_id'],
                    $item['name'],
                    $item['price'],
                    $item['image'],
                    $item['quantity']
                );
            }
        } else {
            // Nếu giỏ hàng trống, tải giỏ hàng từ database (nếu có)
            load_cart_from_database($user['id']);
        }

        return [
            'success' => true,
            'message' => 'Đăng nhập thành công',
            'user' => $user
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Đăng xuất
 */
function logout_user() {
    // Xóa tất cả các biến session
    $_SESSION = [];

    // Xóa cookie session
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // Hủy session
    session_destroy();

    return [
        'success' => true,
        'message' => 'Đăng xuất thành công',
        'clear_cart_storage' => true // Thêm flag để JavaScript biết cần xóa localStorage
    ];
}

/**
 * Lấy thông tin người dùng theo ID
 */
function get_user_by_id($user_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = :id");
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return null;
        }

        return $stmt->fetch();
    } catch (PDOException $e) {
        return null;
    }
}

/**
 * Đăng ký tài khoản sau khi đặt hàng và liên kết với đơn hàng
 */
function register_after_order($username, $email, $password, $full_name, $order_id) {
    global $conn;

    try {
        $conn->beginTransaction();

        // Kiểm tra tên đăng nhập hợp lệ (không dấu cách, không dấu tiếng Việt, chỉ chứa chữ cái, số và dấu gạch dưới)
        if (strpos($username, ' ') !== false ||
            preg_match('/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i', $username) ||
            !preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            return [
                'success' => false,
                'message' => 'Tên đăng nhập chỉ được phép chứa chữ cái không dấu, số và dấu gạch dưới'
            ];
        }

        // Kiểm tra username đã tồn tại chưa
        $stmt = $conn->prepare("SELECT id FROM users WHERE username = :username");
        $stmt->bindParam(':username', $username);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return [
                'success' => false,
                'message' => 'Tên đăng nhập đã tồn tại'
            ];
        }

        // Kiểm tra email đã tồn tại chưa
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = :email");
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return [
                'success' => false,
                'message' => 'Email đã tồn tại'
            ];
        }

        // Mã hóa mật khẩu
        $hashed_password = password_hash($password, PASSWORD_DEFAULT, ['cost' => HASH_COST]);

        // Thêm người dùng mới
        $stmt = $conn->prepare("INSERT INTO users (username, email, password, full_name) VALUES (:username, :email, :password, :full_name)");
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':password', $hashed_password);
        $stmt->bindParam(':full_name', $full_name);
        $stmt->execute();

        $user_id = $conn->lastInsertId();

        // Liên kết đơn hàng với tài khoản
        $stmt = $conn->prepare("UPDATE orders SET user_id = :user_id WHERE id = :order_id");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();

        $conn->commit();

        // Đăng nhập người dùng
        $_SESSION['user_id'] = $user_id;
        $_SESSION['username'] = $username;
        $_SESSION['user_role'] = 'customer';
        $_SESSION['full_name'] = $full_name;

        return [
            'success' => true,
            'message' => 'Đăng ký tài khoản thành công',
            'user_id' => $user_id
        ];
    } catch (PDOException $e) {
        $conn->rollBack();
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Cập nhật thông tin người dùng
 */
function update_user_profile($user_id, $full_name, $phone, $address, $province_code = null, $district_code = null, $ward_code = null, $address_detail = null) {
    global $conn;

    try {
        // Nếu có thông tin địa chỉ mới (tỉnh/thành, quận/huyện, phường/xã)
        if ($province_code !== null && $district_code !== null && $ward_code !== null) {
            $stmt = $conn->prepare("UPDATE users SET
                full_name = :full_name,
                phone = :phone,
                address = :address,
                province_code = :province_code,
                district_code = :district_code,
                ward_code = :ward_code,
                address_detail = :address_detail
                WHERE id = :id");
            $stmt->bindParam(':full_name', $full_name);
            $stmt->bindParam(':phone', $phone);
            $stmt->bindParam(':address', $address);
            $stmt->bindParam(':province_code', $province_code);
            $stmt->bindParam(':district_code', $district_code);
            $stmt->bindParam(':ward_code', $ward_code);
            $stmt->bindParam(':address_detail', $address_detail);
            $stmt->bindParam(':id', $user_id);
        } else {
            // Nếu không có thông tin địa chỉ mới, chỉ cập nhật các trường cơ bản
            $stmt = $conn->prepare("UPDATE users SET full_name = :full_name, phone = :phone, address = :address WHERE id = :id");
            $stmt->bindParam(':full_name', $full_name);
            $stmt->bindParam(':phone', $phone);
            $stmt->bindParam(':address', $address);
            $stmt->bindParam(':id', $user_id);
        }

        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Cập nhật thông tin thành công'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Đổi mật khẩu
 */
function change_password($user_id, $current_password, $new_password) {
    global $conn;

    try {
        // Lấy thông tin người dùng
        $stmt = $conn->prepare("SELECT password FROM users WHERE id = :id");
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Người dùng không tồn tại'
            ];
        }

        $user = $stmt->fetch();

        // Kiểm tra mật khẩu hiện tại
        if (!password_verify($current_password, $user['password'])) {
            return [
                'success' => false,
                'message' => 'Mật khẩu hiện tại không đúng'
            ];
        }

        // Mã hóa mật khẩu mới
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT, ['cost' => HASH_COST]);

        // Cập nhật mật khẩu
        $stmt = $conn->prepare("UPDATE users SET password = :password WHERE id = :id");
        $stmt->bindParam(':password', $hashed_password);
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Đổi mật khẩu thành công'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Cập nhật avatar người dùng
 */
function update_user_avatar($user_id, $avatar) {
    global $conn;

    try {
        $stmt = $conn->prepare("UPDATE users SET avatar = :avatar WHERE id = :id");
        $stmt->bindParam(':avatar', $avatar);
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Cập nhật avatar thành công'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Kiểm tra người dùng đã đăng nhập chưa
 */
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

/**
 * Kiểm tra người dùng có phải admin không
 */
function is_admin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Kiểm tra người dùng đã đăng nhập và có quyền admin không
 * (Hàm này được thêm để tương thích với code hiện tại)
 */
function is_admin_logged_in() {
    return is_logged_in() && is_admin();
}

/**
 * Kiểm tra người dùng có quyền admin không
 * (Hàm này được thêm để tương thích với code hiện tại)
 */
function check_admin_role() {
    return is_admin();
}

/**
 * Tạo token CSRF
 */
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Kiểm tra token CSRF
 */
function check_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Lấy danh sách người dùng
 */
function get_users($limit = null, $offset = 0, $search = null, $status = null, $role = null) {
    global $conn;

    try {
        $sql = "SELECT * FROM users WHERE 1=1";
        $params = [];

        // Tìm kiếm theo tên hoặc email
        if ($search !== null) {
            $sql .= " AND (full_name LIKE :search OR email LIKE :search)";
            $params[':search'] = "%$search%";
        }

        // Lọc theo trạng thái
        if ($status !== null) {
            $sql .= " AND status = :status";
            $params[':status'] = $status;
        }

        // Lọc theo vai trò
        if ($role !== null) {
            $sql .= " AND role = :role";
            $params[':role'] = $role;
        }

        // Sắp xếp
        $sql .= " ORDER BY created_at DESC";

        // Giới hạn số lượng
        if ($limit !== null) {
            $sql .= " LIMIT :offset, :limit";
            $params[':offset'] = $offset;
            $params[':limit'] = $limit;
        }

        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Lỗi khi lấy danh sách người dùng: " . $e->getMessage());
        return [];
    }
}

/**
 * Đếm tổng số người dùng
 */
function count_users($search = null, $status = null, $role = null) {
    global $conn;

    try {
        $sql = "SELECT COUNT(*) as total FROM users WHERE 1=1";
        $params = [];

        // Tìm kiếm theo tên hoặc email
        if ($search !== null) {
            $sql .= " AND (full_name LIKE :search OR email LIKE :search)";
            $params[':search'] = "%$search%";
        }

        // Lọc theo trạng thái
        if ($status !== null) {
            $sql .= " AND status = :status";
            $params[':status'] = $status;
        }

        // Lọc theo vai trò
        if ($role !== null) {
            $sql .= " AND role = :role";
            $params[':role'] = $role;
        }

        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch();

        return $result['total'];
    } catch (PDOException $e) {
        error_log("Lỗi khi đếm số người dùng: " . $e->getMessage());
        return 0;
    }
}

/**
 * Đếm số đơn hàng của người dùng
 */
function count_user_orders($user_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM orders WHERE user_id = :user_id");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $result = $stmt->fetch();

        return $result['total'];
    } catch (PDOException $e) {
        error_log("Lỗi khi đếm số đơn hàng của người dùng: " . $e->getMessage());
        return 0;
    }
}

/**
 * Liên kết đơn hàng với tài khoản người dùng
 */
function link_order_to_user($user_id, $order_id) {
    global $conn;

    try {
        // Kiểm tra đơn hàng tồn tại và chưa được liên kết với tài khoản nào
        $stmt = $conn->prepare("SELECT id, user_id FROM orders WHERE id = :order_id");
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Đơn hàng không tồn tại'
            ];
        }

        $order = $stmt->fetch();

        // Nếu đơn hàng đã được liên kết với một tài khoản khác
        if ($order['user_id'] !== null && $order['user_id'] != $user_id) {
            return [
                'success' => false,
                'message' => 'Đơn hàng đã được liên kết với một tài khoản khác'
            ];
        }

        // Liên kết đơn hàng với tài khoản
        $stmt = $conn->prepare("UPDATE orders SET user_id = :user_id WHERE id = :order_id");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Liên kết đơn hàng thành công'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Khóa tài khoản người dùng
 */
function lock_user_account($user_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("UPDATE users SET status = 'locked' WHERE id = :id AND role != 'admin'");
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Không thể khóa tài khoản admin hoặc người dùng không tồn tại'
            ];
        }

        return [
            'success' => true,
            'message' => 'Đã khóa tài khoản người dùng thành công'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Mở khóa tài khoản người dùng
 */
function unlock_user_account($user_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE id = :id");
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Người dùng không tồn tại'
            ];
        }

        return [
            'success' => true,
            'message' => 'Đã mở khóa tài khoản người dùng thành công'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Tạo token reset mật khẩu
 */
function create_password_reset_token($user_id) {
    global $conn;

    try {
        // Tạo token ngẫu nhiên
        $token = bin2hex(random_bytes(32));

        // Thời gian hết hạn (24 giờ)
        $expires_at = date('Y-m-d H:i:s', strtotime('+24 hours'));

        // Lưu token vào cơ sở dữ liệu
        $stmt = $conn->prepare("UPDATE users SET reset_token = :token, reset_token_expires_at = :expires_at WHERE id = :id");
        $stmt->bindParam(':token', $token);
        $stmt->bindParam(':expires_at', $expires_at);
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Người dùng không tồn tại'
            ];
        }

        return [
            'success' => true,
            'message' => 'Đã tạo token reset mật khẩu thành công',
            'token' => $token,
            'expires_at' => $expires_at
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Tạo mã OTP ngẫu nhiên gồm 6 chữ số
 */
function generate_otp_code() {
    return str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * Tạo mã OTP và lưu vào cơ sở dữ liệu
 */
function create_otp_code($user_id) {
    global $conn;

    try {
        // Tạo mã OTP ngẫu nhiên
        $otp_code = generate_otp_code();

        // Thời gian hết hạn (15 phút)
        $expires_at = date('Y-m-d H:i:s', strtotime('+15 minutes'));

        // Lưu mã OTP vào cơ sở dữ liệu
        $stmt = $conn->prepare("UPDATE users SET otp_code = :otp_code, otp_expires_at = :expires_at WHERE id = :id");
        $stmt->bindParam(':otp_code', $otp_code);
        $stmt->bindParam(':expires_at', $expires_at);
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Người dùng không tồn tại'
            ];
        }

        return [
            'success' => true,
            'message' => 'Đã tạo mã OTP thành công',
            'otp_code' => $otp_code,
            'expires_at' => $expires_at
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Xác thực mã OTP
 */
function verify_otp_code($email, $otp_code) {
    global $conn;

    try {
        // Tìm người dùng theo email
        $stmt = $conn->prepare("SELECT * FROM users WHERE email = :email");
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Email không tồn tại trong hệ thống'
            ];
        }

        $user = $stmt->fetch();

        // Kiểm tra mã OTP
        if ($user['otp_code'] !== $otp_code) {
            return [
                'success' => false,
                'message' => 'Mã xác nhận không đúng'
            ];
        }

        // Kiểm tra thời gian hết hạn
        if (strtotime($user['otp_expires_at']) < time()) {
            return [
                'success' => false,
                'message' => 'Mã xác nhận đã hết hạn'
            ];
        }

        return [
            'success' => true,
            'message' => 'Xác thực mã OTP thành công',
            'user_id' => $user['id']
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Đặt lại mật khẩu sau khi xác thực OTP
 */
function reset_password_with_otp($user_id, $new_password) {
    global $conn;

    try {
        // Mã hóa mật khẩu mới
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT, ['cost' => HASH_COST]);

        // Cập nhật mật khẩu và xóa mã OTP
        $stmt = $conn->prepare("UPDATE users SET password = :password, otp_code = NULL, otp_expires_at = NULL WHERE id = :id");
        $stmt->bindParam(':password', $hashed_password);
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Người dùng không tồn tại'
            ];
        }

        return [
            'success' => true,
            'message' => 'Đặt lại mật khẩu thành công'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Tìm người dùng theo email
 */
function get_user_by_email($email) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT * FROM users WHERE email = :email");
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return null;
        }

        return $stmt->fetch();
    } catch (PDOException $e) {
        return null;
    }
}

/**
 * Gửi email chứa mã OTP
 */
function send_otp_email($email) {
    global $conn;

    try {
        // Tìm người dùng theo email
        $user = get_user_by_email($email);

        if (!$user) {
            return [
                'success' => false,
                'message' => 'Email không tồn tại trong hệ thống'
            ];
        }

        // Tạo mã OTP
        $result = create_otp_code($user['id']);

        if (!$result['success']) {
            return $result;
        }

        $otp_code = $result['otp_code'];

        // Nạp thư viện PHPMailer
        require_once ROOT_PATH . 'includes/PHPMailer/src/Exception.php';
        require_once ROOT_PATH . 'includes/PHPMailer/src/PHPMailer.php';
        require_once ROOT_PATH . 'includes/PHPMailer/src/SMTP.php';

        // Tạo đối tượng PHPMailer
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);

        try {
            // Cấu hình SMTP
            $mail->isSMTP();
            $mail->Host = 'smtp.gmail.com';
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>';
            $mail->Password = 'dpld dxeq pwmu taxz'; // Mật khẩu ứng dụng
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS; // Sử dụng SSL thay vì TLS
            $mail->Port = 465; // Port cho SSL
            $mail->CharSet = 'UTF-8';

            // Bật chế độ debug (0: tắt, 1: thông báo client, 2: thông báo client và server)
            $mail->SMTPDebug = 0; // Đặt thành 2 khi cần debug

            // Người gửi và người nhận
            $mail->setFrom('<EMAIL>', SITE_NAME);
            $mail->addAddress($email, $user['full_name']);

            // Nội dung email
            $mail->isHTML(true);
            $mail->Subject = '🔐 Mã xác nhận đặt lại mật khẩu – Nội Thất Chất Lượng Bàng Vũ';

            // Lấy đường dẫn đến logo
            $logo_url = BASE_URL . '/assets/images/logo/logo.png';

            // Tạo nội dung email với thiết kế hiện đại và tinh tế
            $mail_body = '
            <!DOCTYPE html>
            <html lang="vi">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Mã xác nhận đặt lại mật khẩu</title>
                <style>
                    @import url("https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700&display=swap");

                    body, html {
                        margin: 0;
                        padding: 0;
                        font-family: "Be Vietnam Pro", Arial, sans-serif;
                        line-height: 1.6;
                        color: #333333;
                        background-color: #f5f5f5;
                    }

                    .email-wrapper {
                        padding: 20px;
                        background-color: #f5f5f5;
                    }

                    .email-container {
                        max-width: 600px;
                        margin: 0 auto;
                        background-color: #ffffff;
                        border-radius: 12px;
                        overflow: hidden;
                        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
                    }

                    .email-header {
                        background: linear-gradient(135deg,rgb(248, 202, 174) 0%,rgb(252, 238, 227) 100%);
                        padding: 25px 30px;
                        display: flex;
                        align-items: center;
                    }

                    .email-header img {
                        max-width: 320px;
                        height: auto;
                    }

                    .header-title {
                        margin-left: auto;
                        color: white;
                        font-weight: 600;
                        font-size: 16px;
                        text-align: right;
                    }

                    .email-body {
                        padding: 35px;
                        background-color: #ffffff;
                    }

                    .greeting {
                        font-size: 20px;
                        font-weight: 600;
                        margin-bottom: 20px;
                        color: #2A3B47;
                    }

                    .message {
                        margin-bottom: 30px;
                        font-size: 16px;
                        color: #4A5568;
                        line-height: 1.7;
                    }

                    .otp-container {
                        background: linear-gradient(to right, #FFF4EC, #FFF9F5);
                        border: 1px solid #FFE0CC;
                        border-radius: 12px;
                        padding: 25px;
                        margin: 30px 0;
                        text-align: center;
                        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.08);
                    }

                    .otp-label {
                        font-size: 15px;
                        font-weight: 600;
                        color: #2A3B47;
                        margin-bottom: 15px;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }

                    .otp-code {
                        font-size: 36px;
                        font-weight: 700;
                        letter-spacing: 8px;
                        color: #F37321;
                        margin: 20px 0;
                        padding: 15px 20px;
                        background-color: white;
                        border-radius: 10px;
                        display: inline-block;
                        box-shadow: 0 2px 8px rgba(243, 115, 33, 0.15);
                        border: 1px dashed #F37321;
                    }

                    .otp-note {
                        font-size: 14px;
                        color: #718096;
                        font-style: italic;
                        margin-top: 10px;
                    }

                    .security-notes {
                        background-color: #F8FAFC;
                        border-left: 4px solid #3182CE;
                        padding: 20px;
                        margin: 30px 0;
                        border-radius: 8px;
                    }

                    .security-notes h3 {
                        margin-top: 0;
                        font-size: 17px;
                        color: #2A3B47;
                        display: flex;
                        align-items: center;
                    }

                    .security-notes h3:before {
                        content: "🔒";
                        margin-right: 8px;
                    }

                    .security-notes ul {
                        margin: 15px 0 5px;
                        padding-left: 20px;
                    }

                    .security-notes li {
                        margin-bottom: 10px;
                        font-size: 14px;
                        color: #4A5568;
                    }

                    .contact-info {
                        margin-top: 35px;
                        padding: 25px;
                        border-radius: 8px;
                        background-color: #2A3B47;
                        color: white;
                    }

                    .contact-info h3 {
                        font-size: 17px;
                        color: white;
                        margin-top: 0;
                        margin-bottom: 15px;
                        display: flex;
                        align-items: center;
                    }

                    .contact-info h3:before {
                        content: "📞";
                        margin-right: 8px;
                    }

                    .contact-info p {
                        margin: 8px 0;
                        font-size: 14px;
                        color: rgba(255, 255, 255, 0.9);
                    }

                    .contact-info strong {
                        color: white;
                        font-weight: 600;
                    }

                    .contact-info .support-message {
                        margin-top: 15px;
                        font-size: 13px;
                        color: rgba(255, 255, 255, 0.8);
                        font-style: italic;
                    }

                    .email-footer {
                        background: linear-gradient(135deg, #2A3B47 0%, #1A2A36 100%);
                        color: #ffffff;
                        padding: 25px;
                        text-align: center;
                        font-size: 13px;
                    }

                    .email-footer p {
                        margin: 5px 0;
                        color: rgba(255, 255, 255, 0.9);
                    }

                    .signature {
                        font-weight: 600;
                        margin-top: 10px;
                    }

                    .divider {
                        height: 1px;
                        background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
                        margin: 15px 0;
                    }

                    @media only screen and (max-width: 600px) {
                        .email-body {
                            padding: 25px 20px;
                        }

                        .email-header {
                            padding: 20px;
                            flex-direction: column;
                            text-align: center;
                        }

                        .email-header img {
                            max-width: 320px;
                            margin-bottom: 10px;
                        }

                        .header-title {
                            margin-left: 0;
                            text-align: center;
                        }

                        .otp-code {
                            font-size: 28px;
                            letter-spacing: 5px;
                            padding: 12px 15px;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="email-wrapper">
                    <div class="email-container">
                        <div class="email-header">
                            <img src="https://res.cloudinary.com/dpwsaqvl9/image/upload/v1747505891/noithatbangvu/logo-email/logo_f9sars.png" alt="Nội Thất Chất Lượng Bàng Vũ" width="220" height="auto" style="max-width: 250px; height: auto;" />

                        </div>
                        <div class="email-body">
                            <div class="greeting">
                                Xin chào ' . $user['full_name'] . ',
                            </div>
                            <div class="message">
                                Chúng tôi vừa nhận được yêu cầu đặt lại mật khẩu từ tài khoản của bạn. Để tiếp tục quá trình, bạn cần nhập mã xác nhận một lần (OTP) dưới đây trong vòng 15 phút:
                            </div>

                            <div class="otp-container">
                                <div class="otp-label">🔒 MÃ XÁC NHẬN CỦA BẠN</div>
                                <div class="otp-code">' . $otp_code . '</div>
                                <div class="otp-note">Mã này chỉ có hiệu lực trong 15 phút và chỉ dùng một lần duy nhất</div>
                            </div>

                            <div class="security-notes">
                                <h3>Lưu ý quan trọng</h3>
                                <ul>
                                    <li>Không chia sẻ mã này với bất kỳ ai – kể cả nhân viên hỗ trợ.</li>
                                    <li>Nếu bạn không thực hiện yêu cầu này, vui lòng bỏ qua email này – tài khoản của bạn sẽ không bị ảnh hưởng.</li>
                                    <li>Sau nhiều lần nhập sai, tính năng đặt lại mật khẩu sẽ tạm thời bị khóa để đảm bảo an toàn.</li>
                                </ul>
                            </div>

                            <div class="contact-info">
                                <h3>Cần hỗ trợ?</h3>
                                <p><strong>Hotline tư vấn khách hàng:</strong> 097.277.4646</p>
                                <p><strong>Kỹ thuật viên trực tiếp hỗ trợ:</strong> 083.422.0174</p>
                                <p class="support-message">Chúng tôi luôn sẵn sàng đồng hành cùng bạn trong mọi trải nghiệm với sản phẩm và dịch vụ của Nội Thất Chất Lượng Bàng Vũ.</p>
                            </div>
                        </div>
                        <div class="email-footer">
                            <p>Trân trọng,</p>
                            <div class="divider"></div>
                            <p class="signature">Đội ngũ hỗ trợ kỹ thuật</p>
                            <p>Nội Thất Chất Lượng Bàng Vũ</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>';

            $mail->Body = $mail_body;
            $mail->AltBody = "Xin chào {$user['full_name']},\n\nChúng tôi vừa nhận được yêu cầu đặt lại mật khẩu từ tài khoản của bạn. Để tiếp tục quá trình, bạn cần nhập mã xác nhận một lần (OTP) dưới đây trong vòng 15 phút:\n\nMÃ XÁC NHẬN CỦA BẠN: $otp_code\n\nLưu ý quan trọng:\n- Không chia sẻ mã này với bất kỳ ai – kể cả nhân viên hỗ trợ.\n- Nếu bạn không thực hiện yêu cầu này, vui lòng bỏ qua email này – tài khoản của bạn sẽ không bị ảnh hưởng.\n- Sau nhiều lần nhập sai, tính năng đặt lại mật khẩu sẽ tạm thời bị khóa để đảm bảo an toàn.\n\nCần hỗ trợ?\nHotline tư vấn khách hàng: 097.277.4646\nKỹ thuật viên trực tiếp hỗ trợ: 083.422.0174\n\nTrân trọng,\nĐội ngũ hỗ trợ kỹ thuật\nNội Thất Chất Lượng Bàng Vũ";

            // Gửi email
            $mail->send();

            return [
                'success' => true,
                'message' => 'Đã gửi mã xác nhận đến email của bạn'
            ];
        } catch (Exception $e) {
            // Ghi log lỗi để debug
            $error_message = "Lỗi gửi email OTP: " . $e->getMessage();
            if (isset($mail) && is_object($mail)) {
                $error_message .= " - " . $mail->ErrorInfo;
            }
            error_log($error_message);

            return [
                'success' => false,
                'message' => 'Không thể gửi email. Vui lòng thử lại sau hoặc liên hệ quản trị viên.',
                'error_details' => [
                    'message' => $e->getMessage(),
                    'smtp_error' => isset($mail) && is_object($mail) ? $mail->ErrorInfo : null
                ]
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Gửi email reset mật khẩu
 */
function send_password_reset_email($user_id) {
    global $conn;

    try {
        // Lấy thông tin người dùng
        $user = get_user_by_id($user_id);

        if (!$user) {
            return [
                'success' => false,
                'message' => 'Người dùng không tồn tại'
            ];
        }

        // Tạo token reset mật khẩu
        $result = create_password_reset_token($user_id);

        if (!$result['success']) {
            return $result;
        }

        $token = $result['token'];

        // Tạo URL reset mật khẩu
        $reset_url = BASE_URL . '/reset-password.php?token=' . $token;

        // Nội dung email
        $subject = 'Đặt lại mật khẩu tài khoản ' . SITE_NAME;
        $message = "Xin chào {$user['full_name']},\n\n";
        $message .= "Bạn đã yêu cầu đặt lại mật khẩu cho tài khoản của mình tại " . SITE_NAME . ".\n\n";
        $message .= "Vui lòng nhấp vào liên kết sau để đặt lại mật khẩu của bạn:\n";
        $message .= $reset_url . "\n\n";
        $message .= "Liên kết này sẽ hết hạn sau 24 giờ.\n\n";
        $message .= "Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này.\n\n";
        $message .= "Trân trọng,\n";
        $message .= SITE_NAME;

        // Gửi email
        $headers = 'From: ' . SITE_EMAIL . "\r\n" .
                   'Reply-To: ' . SITE_EMAIL . "\r\n" .
                   'X-Mailer: PHP/' . phpversion();

        if (mail($user['email'], $subject, $message, $headers)) {
            return [
                'success' => true,
                'message' => 'Đã gửi email đặt lại mật khẩu thành công'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Không thể gửi email đặt lại mật khẩu'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Reset mật khẩu người dùng (từ admin)
 */
function admin_reset_user_password($user_id) {
    global $conn;

    try {
        // Lấy thông tin người dùng
        $user = get_user_by_id($user_id);

        if (!$user) {
            return [
                'success' => false,
                'message' => 'Người dùng không tồn tại'
            ];
        }

        // Gửi email reset mật khẩu
        return send_password_reset_email($user_id);
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Xóa tài khoản người dùng
 */
function delete_user($user_id) {
    global $conn;

    try {
        // Lấy thông tin người dùng
        $user = get_user_by_id($user_id);

        if (!$user) {
            return [
                'success' => false,
                'message' => 'Người dùng không tồn tại'
            ];
        }

        // Không cho phép xóa tài khoản admin
        if ($user['role'] === 'admin') {
            return [
                'success' => false,
                'message' => 'Không thể xóa tài khoản admin'
            ];
        }

        // Bắt đầu transaction
        $conn->beginTransaction();

        // Xóa avatar người dùng nếu có
        if (!empty($user['avatar'])) {
            $avatar_path = ROOT_PATH . 'uploads/avatars/' . $user['avatar'];
            if (file_exists($avatar_path)) {
                unlink($avatar_path);
            }
        }

        // Xóa người dùng
        $stmt = $conn->prepare("DELETE FROM users WHERE id = :id");
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();

        // Commit transaction
        $conn->commit();

        return [
            'success' => true,
            'message' => 'Đã xóa tài khoản người dùng thành công'
        ];
    } catch (PDOException $e) {
        // Rollback transaction nếu có lỗi
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }

        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}
?>
