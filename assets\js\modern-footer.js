/**
 * Modern Footer JavaScript for Nội Thất Băng Vũ
 * <PERSON><PERSON> lý các tương tác và hiệu ứng cho footer hiện đại
 * Phiên bản nâng cấp với nhiều hiệu ứng và tương tác hơn
 */

document.addEventListener('DOMContentLoaded', function() {
    // Xử lý form đăng ký nhận tin
    initNewsletterForm();

    // Hiệu ứng hover cho các liên kết
    initLinkHoverEffects();

    // Hiệu ứng parallax cho footer wave
    initFooterWaveParallax();

    // Hiệu ứng cho các phần tử khi scroll
    initScrollAnimations();

    // Hiệu ứng shine cho các nút
    initButtonShineEffect();

    // Hiệu ứng cho Facebook container
    initFacebookContainerEffect();
});

/**
 * Xử lý form đăng ký nhận tin
 */
function initNewsletterForm() {
    const newsletterForm = document.querySelector('.footer-newsletter-form');

    if (newsletterForm) {
        // Thêm hiệu ứng focus cho input
        const newsletterInput = newsletterForm.querySelector('.footer-newsletter-input');
        if (newsletterInput) {
            newsletterInput.addEventListener('focus', function() {
                this.parentElement.classList.add('form-focused');
            });

            newsletterInput.addEventListener('blur', function() {
                this.parentElement.classList.remove('form-focused');
            });
        }

        // Xử lý submit form
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const emailInput = this.querySelector('.footer-newsletter-input');
            const email = emailInput.value.trim();

            if (!isValidEmail(email)) {
                showNewsletterMessage('Vui lòng nhập địa chỉ email hợp lệ', 'error');
                return;
            }

            // Hiệu ứng loading
            const submitButton = this.querySelector('.footer-newsletter-button');
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            submitButton.disabled = true;

            // Giả lập gửi yêu cầu đến server
            setTimeout(() => {
                // Hiển thị thông báo thành công
                showNewsletterMessage('Cảm ơn bạn đã đăng ký nhận tin!', 'success');

                // Reset form
                emailInput.value = '';
                submitButton.innerHTML = 'Đăng ký';
                submitButton.disabled = false;

                // Ghi log (sẽ thay bằng AJAX request thực tế)
                console.log('Đăng ký nhận tin với email:', email);
            }, 1000);
        });
    }
}

/**
 * Kiểm tra email hợp lệ
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Hiển thị thông báo cho form đăng ký nhận tin
 */
function showNewsletterMessage(message, type) {
    const newsletterForm = document.querySelector('.footer-newsletter-form');

    if (!newsletterForm) return;

    // Xóa thông báo cũ nếu có
    const oldMessage = document.querySelector('.newsletter-message');
    if (oldMessage) {
        oldMessage.remove();
    }

    // Tạo thông báo mới
    const messageElement = document.createElement('div');
    messageElement.className = `newsletter-message newsletter-message-${type}`;

    // Thêm icon phù hợp
    const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';
    messageElement.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;

    // Thêm vào sau form
    newsletterForm.parentNode.insertBefore(messageElement, newsletterForm.nextSibling);

    // Hiệu ứng hiển thị
    setTimeout(() => {
        messageElement.classList.add('newsletter-message-show');
    }, 10);

    // Tự động ẩn sau 5 giây
    setTimeout(() => {
        messageElement.classList.add('newsletter-message-hide');

        // Xóa khỏi DOM sau khi animation kết thúc
        setTimeout(() => {
            messageElement.remove();
        }, 500);
    }, 5000);
}

/**
 * Hiệu ứng hover cho các liên kết
 */
function initLinkHoverEffects() {
    // Hiệu ứng cho các liên kết trong footer
    const footerLinks = document.querySelectorAll('.footer-links a');

    footerLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)';

            // Thêm hiệu ứng cho icon
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'translateX(3px)';
                icon.style.transition = 'transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            }
        });

        link.addEventListener('mouseleave', function() {
            // Reset hiệu ứng cho icon
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'translateX(0)';
            }
        });
    });

    // Hiệu ứng cho các liên kết mạng xã hội
    const socialLinks = document.querySelectorAll('.footer-social-link');

    socialLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            // Thêm hiệu ứng pulse nhẹ
            this.classList.add('social-pulse');

            // Thêm hiệu ứng cho icon
            const icon = this.querySelector('img');
            if (icon) {
                icon.style.transform = 'scale(1.1)';
                icon.style.transition = 'transform 0.3s ease';
            }
        });

        link.addEventListener('mouseleave', function() {
            // Xóa hiệu ứng pulse
            this.classList.remove('social-pulse');

            // Reset hiệu ứng cho icon
            const icon = this.querySelector('img');
            if (icon) {
                icon.style.transform = 'scale(1)';
            }
        });
    });
}

/**
 * Hiệu ứng parallax cho footer wave
 */
function initFooterWaveParallax() {
    const footerWave = document.querySelector('.footer-wave');
    const wavePath = footerWave ? footerWave.querySelector('path') : null;

    if (footerWave && wavePath) {
        // Thêm hiệu ứng chuyển động nhẹ cho wave
        let waveAnimation = null;

        // Hiệu ứng parallax khi scroll
        window.addEventListener('scroll', function() {
            const scrollPosition = window.scrollY;
            const windowHeight = window.innerHeight;
            const documentHeight = document.body.scrollHeight;

            // Chỉ áp dụng hiệu ứng khi gần đến footer
            if (scrollPosition + windowHeight > documentHeight - 600) {
                const parallaxValue = (scrollPosition + windowHeight - (documentHeight - 600)) * 0.05;
                const rotateValue = parallaxValue * 0.02;

                footerWave.style.transform = `translateY(-99%) translateX(${parallaxValue}px) rotate(${rotateValue}deg)`;

                // Bắt đầu animation nếu chưa có
                if (!waveAnimation) {
                    startWaveAnimation(wavePath);
                    waveAnimation = true;
                }
            }
        });
    }
}

/**
 * Tạo hiệu ứng chuyển động cho wave
 */
function startWaveAnimation(wavePath) {
    if (!wavePath) return;

    // Lưu lại path gốc
    const originalPath = wavePath.getAttribute('d');

    // Tạo các path khác nhau cho hiệu ứng sóng
    const paths = [
        originalPath,
        "M321.39,50.44c58-8.79,114.16-28.13,172-39.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,25,906.67,68,985.66,88.83c70.05,18.48,146.53,26.09,214.34,3V0H0V21.35A600.21,600.21,0,0,0,321.39,50.44Z",
        "M321.39,60.44c58-12.79,114.16-32.13,172-43.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,35,906.67,76,985.66,96.83c70.05,18.48,146.53,26.09,214.34,3V0H0V31.35A600.21,600.21,0,0,0,321.39,60.44Z",
        originalPath
    ];

    let currentPathIndex = 0;

    // Tạo interval để thay đổi path
    setInterval(() => {
        currentPathIndex = (currentPathIndex + 1) % paths.length;
        wavePath.setAttribute('d', paths[currentPathIndex]);

        // Thêm hiệu ứng transition
        wavePath.style.transition = 'all 1.5s ease-in-out';
    }, 2000);
}

/**
 * Hiệu ứng khi scroll đến các phần tử
 */
function initScrollAnimations() {
    // Lấy tất cả các cột footer
    const footerColumns = document.querySelectorAll('.footer-column');

    // Tạo Intersection Observer
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('footer-column-visible');
                observer.unobserve(entry.target);
            }
        });
    }, {
        root: null,
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // Thêm class ban đầu và observe các phần tử
    footerColumns.forEach((column, index) => {
        column.classList.add('footer-column-hidden');
        column.style.transitionDelay = `${index * 0.1}s`;
        observer.observe(column);
    });

    // Thêm CSS động cho hiệu ứng
    const style = document.createElement('style');
    style.textContent = `
        .footer-column-hidden {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }

        .footer-column-visible {
            opacity: 1;
            transform: translateY(0);
        }

        .social-pulse {
            animation: socialPulse 1s infinite alternate;
        }

        @keyframes socialPulse {
            0% {
                box-shadow: 0 0 0 0 rgba(243, 115, 33, 0.4);
            }
            100% {
                box-shadow: 0 0 0 8px rgba(243, 115, 33, 0);
            }
        }

        .form-focused {
            transform: scale(1.01);
        }
    `;
    document.head.appendChild(style);
}

/**
 * Hiệu ứng shine cho các nút
 */
function initButtonShineEffect() {
    const buttons = document.querySelectorAll('.footer-newsletter-button');

    buttons.forEach(button => {
        button.addEventListener('mouseover', function() {
            this.classList.add('button-shine');
        });

        button.addEventListener('mouseout', function() {
            this.classList.remove('button-shine');
        });
    });

    // Thêm CSS động cho hiệu ứng
    const style = document.createElement('style');
    style.textContent = `
        .button-shine::before {
            left: 100%;
            transition: 0.7s;
        }

        .newsletter-message {
            transform: translateY(10px);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .newsletter-message-show {
            transform: translateY(0);
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
}

/**
 * Hiệu ứng cho Facebook container
 */
function initFacebookContainerEffect() {
    const fbContainer = document.querySelector('.footer-facebook-container');

    if (fbContainer) {
        // Thêm hiệu ứng hover
        fbContainer.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = 'var(--footer-hover-shadow)';
            this.style.transition = 'var(--footer-transition)';
        });

        fbContainer.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });

        // Đảm bảo Facebook plugin hiển thị đúng
        if (window.FB) {
            setTimeout(() => {
                FB.XFBML.parse();
            }, 1000);
        }

        // Thêm sự kiện resize để đảm bảo Facebook plugin hiển thị đúng khi thay đổi kích thước màn hình
        window.addEventListener('resize', function() {
            if (window.FB) {
                FB.XFBML.parse();
            }
        });
    }
}
