/**
 * Cart Modal Fix CSS - Nội Thất Băng <PERSON>
 * Sửa lỗi modal xác nhận xóa sản phẩm bị che khuất bởi header và breadcrumb
 */

/* Đặt z-index cực cao cho modal xác nhận xóa */
#delete-confirmation-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 2147483647 !important; /* Z-index tối đa có thể (2^31 - 1) */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: rgba(0, 0, 0, 0.7) !important; /* Tăng độ đậm của nền để bù đắp cho việc loại bỏ blur */
}

/* Đảm bảo modal box hiển thị đúng cách */
#delete-confirmation-modal .bg-white {
    position: relative !important;
    z-index: 2147483647 !important; /* Z-index tối đa */
    max-width: 500px !important;
    width: 90% !important;
    margin: 0 auto !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
}

/* Hiệu ứng mượt mà khi hiển thị và ẩn */
#delete-confirmation-modal {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

#delete-confirmation-modal.active {
    opacity: 1;
    visibility: visible;
}

/* Hiệu ứng cho modal box */
#delete-confirmation-modal .bg-white {
    transform: scale(0.95) translateY(20px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

#delete-confirmation-modal.active .bg-white {
    transform: scale(1) translateY(0);
    opacity: 1;
}

/* Hiệu ứng shake cho modal khi lỗi */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

#delete-confirmation-modal.shake .bg-white {
    animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
}

/* Đảm bảo modal luôn hiển thị trên tất cả các phần tử khác */
body > #delete-confirmation-modal {
    position: fixed !important;
    z-index: 2147483647 !important;
}

/* LOẠI BỎ việc reset z-index của tất cả elements để tránh ảnh hưởng đến header */
/* Thay vào đó, chỉ đảm bảo modal có z-index cao nhất */

/* Đặt z-index cao cho modal và các phần tử con của nó */
body.modal-active #delete-confirmation-modal,
body.modal-active #delete-confirmation-modal * {
    z-index: 2147483647 !important;
}

/* Ngăn chặn các vấn đề bố cục khi modal hiển thị */
/* Sử dụng overflow: hidden nhưng đảm bảo header vẫn hoạt động */
body.modal-active {
    overflow: hidden !important; /* Ngăn cuộn trang khi modal hiển thị */
}

/* Đảm bảo header vẫn hoạt động bình thường khi modal active */
body.modal-active .premium-header,
body.modal-active .luxury-header,
body.modal-active .three-tier-header,
body.modal-active .elegant-header {
    position: fixed !important; /* Chuyển từ sticky sang fixed khi modal active */
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    z-index: 1000 !important; /* Cao hơn content nhưng thấp hơn modal */
}

/* Đảm bảo modal không bị ảnh hưởng bởi các thuộc tính CSS khác */
#delete-confirmation-modal {
    transform: none !important;
    filter: none !important;
    perspective: none !important;
    contain: none !important;
    isolation: isolate !important;
    will-change: auto !important;
}

/* Đảm bảo modal hiển thị trên tất cả các phần tử khác */
#delete-confirmation-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 2147483647 !important;
}

/* Đảm bảo modal box hiển thị đúng cách */
#delete-confirmation-modal .bg-white {
    margin: auto !important;
    position: relative !important;
    z-index: 2147483647 !important;
}



/* Thêm một lớp phủ toàn màn hình để ngăn chặn tương tác với các phần tử khác */
body.modal-active::after {
    content: '' !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: transparent !important;
    z-index: 2147483646 !important; /* Ngay dưới modal */
    pointer-events: auto !important;
}
