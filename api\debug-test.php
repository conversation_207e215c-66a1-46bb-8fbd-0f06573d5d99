<?php
/**
 * Debug test API
 */

// Đặt header JSON
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

$debug_info = [];

try {
    $debug_info['step'] = 'Starting';
    
    // Test file paths
    $init_path = dirname(__DIR__) . '/includes/init.php';
    $debug_info['init_path'] = $init_path;
    $debug_info['init_exists'] = file_exists($init_path);
    
    if (!file_exists($init_path)) {
        throw new Exception("Init file not found: $init_path");
    }
    
    $debug_info['step'] = 'Including init';
    require_once $init_path;
    
    $debug_info['step'] = 'Checking database';
    $debug_info['conn_exists'] = isset($conn);
    
    if (!isset($conn)) {
        throw new Exception('Database connection not found');
    }
    
    $debug_info['step'] = 'Testing database query';
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM products WHERE status = 1");
    $stmt->execute();
    $result = $stmt->fetch();
    $debug_info['total_products'] = $result['total'];
    
    $debug_info['step'] = 'Checking functions';
    $debug_info['functions'] = [
        'get_products_with_filters' => function_exists('get_products_with_filters'),
        'count_products_with_filters' => function_exists('count_products_with_filters'),
        'sanitize' => function_exists('sanitize'),
        'get_category_by_id' => function_exists('get_category_by_id'),
        'get_product_url' => function_exists('get_product_url'),
        'format_currency' => function_exists('format_currency')
    ];
    
    $debug_info['step'] = 'Testing get_products_with_filters';
    if (function_exists('get_products_with_filters')) {
        $test_products = get_products_with_filters([], [], 3, 0, '', 1);
        $debug_info['test_products_count'] = count($test_products);
        $debug_info['test_products_sample'] = array_slice($test_products, 0, 1);
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'All tests passed',
        'debug' => $debug_info
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $debug_info['error'] = $e->getMessage();
    $debug_info['file'] = $e->getFile();
    $debug_info['line'] = $e->getLine();
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug' => $debug_info
    ], JSON_UNESCAPED_UNICODE);
}
?>
