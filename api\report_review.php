<?php
// Include init
require_once '../includes/init.php';

// <PERSON><PERSON>m tra phương thức request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '<PERSON><PERSON>ơng thức không được hỗ trợ']);
    exit;
}

// Kiểm tra CSRF token
if (!check_csrf_token($_POST['csrf_token'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'CSRF token không hợp lệ']);
    exit;
}

// L<PERSON>y dữ liệu từ request
$review_id = isset($_POST['review_id']) ? intval($_POST['review_id']) : 0;
$reason = isset($_POST['reason']) ? sanitize($_POST['reason']) : '';

// <PERSON><PERSON><PERSON> tra dữ liệu
if (empty($review_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID đánh giá không hợp lệ']);
    exit;
}

if (empty($reason)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Vui lòng nhập lý do báo cáo']);
    exit;
}

// Lấy user_id nếu đã đăng nhập
$user_id = is_logged_in() ? $_SESSION['user_id'] : null;

// Lấy IP của người dùng
$ip_address = $_SERVER['REMOTE_ADDR'];

// Báo cáo đánh giá
$result = report_review($review_id, $reason, $user_id, $ip_address);

// Trả về kết quả
header('Content-Type: application/json');
echo json_encode($result);
?>
