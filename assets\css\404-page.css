/**
 * 404 Error Page CSS - Nội Thất Bàng <PERSON>ũ
 * Thiết kế hiện đại với gradient cam và furniture motifs
 */

/* Import Be Vietnam Pro font */
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700;800&display=swap');

/* CSS Variables - Scoped để tránh xung đột */
.error-404-container {
    /* <PERSON><PERSON><PERSON><PERSON> */
    --error-primary: #F37321;
    --error-primary-dark: #D65A0F;
    --error-primary-light: #FF8A3D;
    --error-primary-lighter: #FFA66B;
    --error-primary-lightest: #FFD0AD;
    --error-primary-ultra-light: #FFF4EC;

    /* <PERSON><PERSON>u sắc phụ */
    --error-secondary: #2A3B47;
    --error-secondary-dark: #1E2A32;
    --error-secondary-light: #435868;

    /* <PERSON><PERSON><PERSON>rung <PERSON> */
    --error-white: #FFFFFF;
    --error-light-gray: #F8F9FA;
    --error-medium-gray: #E0E0E0;
    --error-dark-gray: #757575;
    --error-black: #212121;

    /* Hi<PERSON><PERSON>ng */
    --error-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --error-shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --error-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    --error-shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.15);

    /* Transitions */
    --error-transition-fast: all 0.2s ease;
    --error-transition-normal: all 0.3s ease;
    --error-transition-slow: all 0.5s ease;
}

/* Đảm bảo không ảnh hưởng đến header và các phần tử khác */
.error-404-container * {
    box-sizing: border-box;
}

/* Reset cho các phần tử trong container 404 */
.error-404-container input,
.error-404-container button,
.error-404-container a {
    font-family: inherit;
}

/* Keyframes cho animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Container chính - Scope CSS chỉ cho trang 404 */
.error-404-container {
    min-height: calc(100vh - 120px); /* Trừ đi chiều cao header */
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg,
        var(--error-primary-ultra-light) 0%,
        var(--error-white) 25%,
        var(--error-primary-ultra-light) 50%,
        var(--error-white) 75%,
        var(--error-primary-ultra-light) 100%);
    font-family: 'Be Vietnam Pro', sans-serif;
    overflow: hidden;
    padding: 2rem 1rem;
    margin-top: 0; /* Đảm bảo không có margin top */
    isolation: isolate; /* Tạo stacking context riêng */
}

/* Background Pattern */
.error-404-container .error-bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.03;
    background-image:
        radial-gradient(circle at 20% 20%, var(--error-primary) 2px, transparent 2px),
        radial-gradient(circle at 80% 80%, var(--error-primary) 2px, transparent 2px),
        radial-gradient(circle at 40% 60%, var(--error-primary) 1px, transparent 1px);
    background-size: 50px 50px, 80px 80px, 30px 30px;
    animation: float 6s ease-in-out infinite;
}

/* Main Content */
.error-404-container .error-content {
    text-align: center;
    max-width: 800px;
    width: 100%;
    position: relative;
    z-index: 2;
    animation: fadeInUp 0.8s ease-out;
}

/* Error Animation Container */
.error-404-container .error-animation {
    margin-bottom: 3rem;
    position: relative;
}

/* Furniture Icon */
.error-404-container .furniture-icon {
    font-size: 4rem;
    color: var(--error-primary);
    margin-bottom: 2rem;
    animation: bounceIn 1s ease-out 0.2s both;
}

.error-404-container .furniture-icon i {
    filter: drop-shadow(0 4px 8px rgba(243, 115, 33, 0.3));
}

/* 404 Number */
.error-404-container .error-number {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.error-404-container .error-number span {
    font-size: 8rem;
    font-weight: 800;
    line-height: 1;
    background: linear-gradient(135deg, var(--error-primary), var(--error-primary-dark));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 4px 8px rgba(243, 115, 33, 0.3);
    animation: bounceIn 0.6s ease-out both;
}

.error-404-container .number-4 {
    animation-delay: 0.1s;
}

.error-404-container .number-0 {
    animation-delay: 0.2s;
    transform: scale(1.2);
}

.error-404-container .number-4-2 {
    animation-delay: 0.3s;
}

/* Error Message */
.error-message {
    margin-bottom: 3rem;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.error-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--secondary);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.error-description {
    font-size: 1.1rem;
    color: var(--dark-gray);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Action Buttons */
.error-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.error-actions a {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: var(--white);
    color: var(--primary);
    border: 2px solid var(--primary);
    box-shadow: var(--shadow-sm);
}

.btn-outline:hover {
    background: var(--primary);
    color: var(--white);
    transform: translateY(-2px);
}

/* Popular Categories */
.popular-categories {
    animation: fadeInUp 0.8s ease-out 0.8s both;
}

.popular-categories h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--secondary);
    margin-bottom: 1.5rem;
}

.category-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
}

.category-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--white);
    border-radius: 12px;
    text-decoration: none;
    color: var(--secondary);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    min-width: 100px;
}

.category-link:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
    color: var(--primary);
}

.category-link i {
    font-size: 1.5rem;
    color: var(--primary);
}

.category-link span {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.floating-element {
    position: absolute;
    font-size: 2rem;
    color: var(--primary);
    opacity: 0.1;
    animation: float 4s ease-in-out infinite;
}

.element-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.element-2 {
    top: 30%;
    right: 15%;
    animation-delay: 1s;
}

.element-3 {
    bottom: 30%;
    left: 15%;
    animation-delay: 2s;
}

.element-4 {
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
}

/* Responsive Design */
@media (max-width: 768px) {
    .error-404-container {
        padding: 1rem;
    }

    .error-number span {
        font-size: 5rem;
    }

    .error-title {
        font-size: 2rem;
    }

    .error-description {
        font-size: 1rem;
    }

    .error-actions {
        flex-direction: column;
        align-items: center;
    }

    .error-actions a {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .category-links {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.8rem;
    }

    .category-link {
        padding: 0.8rem;
        min-width: auto;
    }

    .furniture-icon {
        font-size: 3rem;
    }

    .floating-element {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .error-number {
        gap: 0.5rem;
    }

    .error-number span {
        font-size: 4rem;
    }

    .error-title {
        font-size: 1.8rem;
    }

    .error-description {
        font-size: 0.95rem;
        padding: 0 1rem;
    }

    .category-links {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.6rem;
    }

    .category-link {
        padding: 0.6rem;
        font-size: 0.85rem;
    }

    .category-link i {
        font-size: 1.2rem;
    }

    .furniture-icon {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
    }
}

/* Hover Effects cho Desktop */
@media (min-width: 769px) {
    .error-actions a::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .error-actions a:hover::before {
        left: 100%;
    }

    .category-link:hover i {
        animation: pulse 0.6s ease-in-out;
    }

    .furniture-icon:hover i {
        animation: rotate 1s ease-in-out;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .error-404-container {
        background: linear-gradient(135deg,
            #1a1a1a 0%,
            #2d2d2d 25%,
            #1a1a1a 50%,
            #2d2d2d 75%,
            #1a1a1a 100%);
    }

    .error-title {
        color: var(--white);
    }

    .error-description {
        color: var(--light-gray);
    }

    .popular-categories h3 {
        color: var(--white);
    }

    .category-link {
        background: #2d2d2d;
        color: var(--white);
        border: 1px solid #404040;
    }

    .category-link:hover {
        background: #3d3d3d;
    }
}

/* Print Styles */
@media print {
    .error-404-container {
        background: white !important;
        color: black !important;
    }

    .floating-elements,
    .error-bg-pattern {
        display: none !important;
    }

    .error-actions a {
        border: 1px solid black !important;
        color: black !important;
        background: white !important;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating-element {
        animation: none;
    }

    .error-bg-pattern {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .error-number span {
        color: var(--black) !important;
        background: none !important;
        -webkit-background-clip: unset !important;
        background-clip: unset !important;
    }

    .btn-primary,
    .btn-secondary {
        background: var(--black) !important;
        color: var(--white) !important;
        border: 2px solid var(--black) !important;
    }

    .btn-outline {
        background: var(--white) !important;
        color: var(--black) !important;
        border: 2px solid var(--black) !important;
    }
}
