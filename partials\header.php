<?php
// Khởi tạo ứng dụng
require_once __DIR__ . '/../includes/init.php';

// Lấy danh sách danh mục theo cấu trúc cây (chỉ lấy danh mục có trạng thái hiển thị)
$categories = get_category_tree(1);

/**
 * Hàm giới hạn tên người dùng tối đa 4 từ
 * Nếu tên dài hơn 4 từ, sẽ hiển thị 4 từ đầu tiên và thêm dấu "..."
 */
function limit_user_name($full_name) {
    // Loại bỏ khoảng trắng thừa ở đầu và cuối
    $full_name = trim($full_name);

    // Chuẩn hóa khoảng trắng (thay thế nhiều khoảng trắng liên tiếp bằng một khoảng trắng)
    $full_name = preg_replace('/\s+/u', ' ', $full_name);

    // Tách thành mảng các từ
    $words = explode(' ', $full_name);

    // Đếm số từ
    $word_count = count($words);

    // Giới hạn tối đa 4 từ
    if ($word_count > 4) {
        return implode(' ', array_slice($words, 0, 4)) . '...';
    }

    // Nếu có 4 từ hoặc ít hơn, trả về tên đầy đủ
    return $full_name;
}
?>
<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : SITE_DESCRIPTION; ?>">
    <meta name="keywords" content="<?php echo isset($page_keywords) ? $page_keywords : META_KEYWORDS; ?>">

    <!-- Favicon - Đầy đủ cho tất cả thiết bị và SEO -->
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo BASE_URL; ?>/assets/images/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="96x96" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon-96x96.png">
    <link rel="icon" type="image/svg+xml" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon.svg">
    <link rel="icon" type="image/x-icon" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon.ico">
    <link rel="manifest" href="<?php echo BASE_URL; ?>/assets/images/favicon/site.webmanifest">
    <meta name="msapplication-TileColor" content="#f37321">
    <meta name="msapplication-TileImage" content="<?php echo BASE_URL; ?>/assets/images/favicon/web-app-manifest-192x192.png">
    <meta name="theme-color" content="#f37321">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:title" content="<?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?>">
    <meta property="og:description" content="<?php echo isset($page_description) ? $page_description : SITE_DESCRIPTION; ?>">
    <meta property="og:image" content="<?php echo BASE_URL; ?>/assets/images/favicon/web-app-manifest-512x512.png">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="twitter:title" content="<?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?>">
    <meta property="twitter:description" content="<?php echo isset($page_description) ? $page_description : SITE_DESCRIPTION; ?>">
    <meta property="twitter:image" content="<?php echo BASE_URL; ?>/assets/images/favicon/web-app-manifest-512x512.png">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/responsive-layout.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/premium-header.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/mobile-header.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/luxury-mobile-menu-new.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/realtime-updates.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/skeleton-loading.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/mega-menu.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/banner-slider.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/fullwidth-banner.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/notifications.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/styles.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/search-variables.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/search-improved.css">

    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- JavaScript Variables -->
    <script>
        const BASE_URL = '<?php echo BASE_URL; ?>';
        const SITE_NAME = '<?php echo SITE_NAME; ?>';
    </script>

    <!-- Custom JavaScript -->
    <script src="<?php echo BASE_URL; ?>/assets/js/search-improved.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/search-z-index-helper.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/typing-effect.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/premium-header.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/mobile-header.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/mobile-menu-toggle-fix.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/mobile-dropdown-fix-v4.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/mega-menu.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/mobile-bottom-nav.js" defer></script>
    <!-- <script src="<?php echo BASE_URL; ?>/assets/js/notifications.js"></script> -->
    <!-- <script src="<?php echo BASE_URL; ?>/assets/js/center-notifications.js"></script> -->

    <!-- Cart Badge Manager - TẠM THỜI VÔ HIỆU HÓA -->
    <!-- <script src="<?php echo BASE_URL; ?>/assets/js/cart-badge-manager.js"></script> -->

    <!-- Debug Cart Issues - TẠM THỜI VÔ HIỆU HÓA -->
    <!-- <script src="<?php echo BASE_URL; ?>/debug-cart-issues.js"></script> -->

    <script src="<?php echo BASE_URL; ?>/assets/js/mobile-cart-animation-fix.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/cart-sync.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/mobile-cart-badge-fix.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/mini-cart-update.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/user-actions.js" defer></script>

    <!-- Touch Dropdown Handler - Xử lý dropdown trên màn hình cảm ứng -->
    <script src="<?php echo BASE_URL; ?>/assets/js/touch-dropdown-handler.js" defer></script>

    <!-- Tablet Navigation Scroll JS -->
    <script src="<?php echo BASE_URL; ?>/assets/js/tablet-navigation-scroll.js" defer></script>

    <!-- Tablet Mega Menu JS -->
    <script src="<?php echo BASE_URL; ?>/assets/js/tablet-mega-menu.js" defer></script>

    <!-- Swiper JS -->
    <script src="https://unpkg.com/swiper/swiper-bundle.min.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/banner-slider.js" defer></script>

    <!-- CSS cho search đã được chuyển sang file riêng: search-improved.css -->

    <!-- Fix Overflow CSS - Đặt ở cuối để ghi đè các quy tắc CSS khác -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/fix-overflow.css">

    <!-- Custom Avatar CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/custom-avatar.css">

    <!-- Z-index Fix CSS - Đặt ở cuối để ghi đè các quy tắc CSS khác -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/z-index-fix.css">

    <!-- Dropdown Consistency CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/dropdown-consistency.css">

    <!-- Dropdown Position Test CSS - Để kiểm tra vị trí dropdown -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/dropdown-position-test.css">

    <!-- Touch Dropdown CSS - Xử lý dropdown trên màn hình cảm ứng -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/touch-dropdown.css">

    <!-- DROPDOWN FINAL FIX - File cuối cùng để ghi đè tất cả positioning -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/dropdown-final-fix.css">

    <!-- Dropdown Invisible Bridge CSS - Tạo cầu nối vô hình cho hover state -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/dropdown-invisible-bridge.css">

    <!-- Header Actions Consistency CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/header-actions-consistency.css">

    <!-- Tablet Dropdown Force CSS - CUỐI CÙNG để override tất cả -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/tablet-dropdown-force.css">

    <!-- Tablet Navigation Scroll CSS - FORCE OVERRIDE cho tablet navigation -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/tablet-navigation-scroll.css">

    <!-- Tablet Navigation Auto-scroll CSS - Visual enhancements for auto-scroll -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/tablet-navigation-autoscroll.css">

    <!-- Tablet Mega Menu CSS - Hybrid approach for tablet mega menu -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/tablet-mega-menu.css">

    <!-- Breadcrumb Fix CSS - Sửa lỗi hover từ breadcrumb ảnh hưởng đến menu header -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/breadcrumb-fix.css">

    <!-- Cart Modal Fix CSS - Sửa lỗi modal xác nhận xóa sản phẩm bị che khuất bởi header và breadcrumb -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/cart-modal-fix.css">

    <!-- Top Bar Color Fix CSS - Khắc phục vấn đề màu sắc top-bar khác nhau giữa các trang -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/top-bar-color-fix.css">

    <!-- Search Z-Index Fix CSS - Khắc phục vấn đề search-suggestions bị che khuất bởi bottom-header-container -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/search-z-index-fix.css">

    <!-- Hover Reset CSS - Tắt hover effect sau khi click vào nav link và cart -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/hover-reset.css">

    <!-- EXTREME MODE: Thêm class vào body ngay lập tức để ẩn hover box -->
    <script>
        (function() {
            const currentPage = window.location.pathname;
            const isProductPage = currentPage.includes('products.php') || currentPage.includes('/products');
            const isCartPage = currentPage.includes('cart.php') || currentPage.includes('/cart');

            if (isProductPage) {
                document.documentElement.classList.add('page-products');
            }

            if (isCartPage) {
                document.documentElement.classList.add('page-cart');
            }

            // Force active state cho nav link sản phẩm
            document.addEventListener('DOMContentLoaded', function() {
                const currentFile = window.location.pathname.split('/').pop();
                const isProductsActive = currentFile === 'products.php' ||
                                       currentFile === 'category.php' ||
                                       currentFile === 'product.php' ||
                                       currentPage.includes('/products.php') ||
                                       currentPage.includes('/category.php') ||
                                       currentPage.includes('/product.php');

                if (isProductsActive) {
                    // Tìm nav item sản phẩm và force active state
                    const productNavItems = document.querySelectorAll('.nav-item');
                    productNavItems.forEach(function(item) {
                        const link = item.querySelector('.nav-link');
                        if (link && link.textContent.trim().includes('Sản phẩm')) {
                            item.classList.add('active');
                            // Force style cho gạch chân
                            const afterElement = link;
                            if (afterElement) {
                                afterElement.style.setProperty('position', 'relative');
                            }
                        }
                    });
                }
            });
        })();
    </script>

    <!-- Footer CSS - Di chuyển từ footer.php để tránh FOUC -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-footer.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/contact-buttons.css">

    <!-- Center Notifications CSS đã bị vô hiệu hóa -->
    <!-- <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/center-notifications.css"> -->

    <!-- Simple Notification Modal CSS - Thiết kế lại #simple-notification thành modal box -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/simple-notification-modal.css">

    <!-- CSS đơn giản cho thông báo và khắc phục FOUC -->
    <style>
        /* Đảm bảo body không bị chặn tương tác */
        body {
            pointer-events: auto;
            overflow: auto;
        }

        /* Ẩn thông báo flash message ban đầu */
        #flash-message-container {
            display: none;
        }

        /* Hiệu ứng cho thông báo đơn giản */
        @keyframes fadeIn {
            from { opacity: 0; transform: translate(-50%, -20px); }
            to { opacity: 1; transform: translate(-50%, 0); }
        }

        #simple-notification {
            animation: fadeIn 0.3s ease-out forwards;
        }

        /* Khắc phục FOUC cho footer - Đảm bảo footer có style cơ bản ngay từ đầu */
        .modern-footer {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            font-family: 'Be Vietnam Pro', 'Montserrat', sans-serif;
            position: relative;
            overflow: hidden;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 1;
            visibility: visible;
        }

        /* Đảm bảo footer content có padding cơ bản */
        .footer-content {
            padding: 3rem 2rem;
            position: relative;
            z-index: 1;
        }

        /* Đảm bảo footer grid có layout cơ bản */
        .footer-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Đảm bảo footer columns có layout cơ bản */
        .footer-column {
            display: flex;
            flex-direction: column;
            padding: 0 1.5rem;
            box-sizing: border-box;
        }

        /* Đảm bảo contact buttons có style cơ bản - Chỉ định nghĩa các thuộc tính cơ bản, không ghi đè positioning */
        .contact-buttons-container {
            position: fixed;
            z-index: 999;
            display: flex;
            flex-direction: column;
            /* Không định nghĩa right, bottom, gap ở đây để tránh ghi đè media queries trong contact-buttons.css */
        }
    </style>

    <!-- Simple Notification Modal JS - Cải thiện UX cho #simple-notification modal -->
    <script src="<?php echo BASE_URL; ?>/assets/js/simple-notification-modal.js"></script>

    <!-- Hover Reset JS - Xử lý tắt hover effect sau khi click vào nav link và cart -->
    <script src="<?php echo BASE_URL; ?>/assets/js/hover-reset.js" defer></script>

    <!-- Force Active State CSS - Đảm bảo nav link active luôn hiển thị gạch chân -->
    <style>
        /* FORCE ACTIVE STATE với specificity cao nhất */
        html body .premium-header .nav-item.active .nav-link::after,
        html body .premium-header .nav-item.active.hover-disabled .nav-link::after,
        html body .premium-header .nav-item.hover-disabled.active .nav-link::after {
            width: 100% !important;
            background-color: #f97316 !important;
            background-image: linear-gradient(to right, #f97316, #fdba74, #f97316) !important;
            opacity: 1 !important;
            visibility: visible !important;
            height: 2px !important;
            position: absolute !important;
            bottom: 0 !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            border-radius: 2px !important;
            box-shadow: 0 1px 3px rgba(249, 115, 22, 0.2) !important;
            z-index: 1 !important;
        }

        html body .premium-header .nav-item.active .nav-link,
        html body .premium-header .nav-item.active.hover-disabled .nav-link,
        html body .premium-header .nav-item.hover-disabled.active .nav-link {
            color: #f97316 !important;
            text-shadow: 0 0 1px rgba(249, 115, 22, 0.1) !important;
        }
    </style>

</head>

<body class="bg-gray-100 min-h-screen flex flex-col">
    <!-- Script để xóa tất cả các lớp phủ khi trang tải xong -->
    <script>
        // Xóa tất cả các lớp phủ ngay khi body được tải
        (function() {
            // Đảm bảo body không bị chặn tương tác
            document.body.style.pointerEvents = 'auto';
            document.body.style.overflow = 'auto';

            // Chạy lại sau 100ms
            setTimeout(function() {
                document.body.style.pointerEvents = 'auto';
                document.body.style.overflow = 'auto';
            }, 100);

            // Kiểm tra xem có cần xóa localStorage sau khi đăng xuất không
            <?php if (isset($_SESSION['clear_cart_storage']) && $_SESSION['clear_cart_storage']): ?>
            // Xóa thông tin giỏ hàng trong localStorage
            localStorage.removeItem('cartCount');
            localStorage.removeItem('cartLastUpdated');
            console.log('Đã xóa thông tin giỏ hàng trong localStorage sau khi đăng xuất');
            <?php
            // Xóa flag sau khi đã xử lý
            unset($_SESSION['clear_cart_storage']);
            endif;
            ?>


        })();
    </script>
    <!-- Mobile Header -->
    <header class="mobile-header" role="banner">
        <div class="mobile-header-content">
            <a href="<?php echo BASE_URL; ?>" class="mobile-header-logo" aria-label="<?php echo SITE_NAME; ?> - Trang chủ">
                <img src="<?php echo BASE_URL; ?>/assets/images/logo/logo.svg" alt="<?php echo SITE_NAME; ?>" id="mobile-logo">
            </a>
            <button class="mobile-header-menu-toggle" aria-label="Mở menu di động" aria-expanded="false" aria-controls="mobile-menu">
                <div class="menu-toggle-icon" aria-hidden="true">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </button>
        </div>
    </header>

    <!-- Debug info - Sẽ bị ẩn -->
    <div style="display: none;">
        REQUEST_URI: <?php echo $_SERVER['REQUEST_URI']; ?><br>
        PHP_SELF: <?php echo $_SERVER['PHP_SELF']; ?><br>
        SCRIPT_NAME: <?php echo $_SERVER['SCRIPT_NAME']; ?><br>
        BASE_URL: <?php echo BASE_URL; ?><br>
        basename(PHP_SELF): <?php echo basename($_SERVER['PHP_SELF']); ?><br>
        strpos(PHP_SELF, '/account/'): <?php echo strpos($_SERVER['PHP_SELF'], '/account/') !== false ? 'true' : 'false'; ?><br>
    </div>

    <!-- Mobile Bottom Navigation -->
    <div class="mobile-bottom-nav" role="navigation" aria-label="Điều hướng di động">
        <div class="mobile-bottom-nav-content">
            <a href="<?php echo BASE_URL; ?>"
               class="mobile-nav-item home-nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php' || $_SERVER['REQUEST_URI'] == '/' || $_SERVER['REQUEST_URI'] == '/noithatbangvu/' || strpos($_SERVER['REQUEST_URI'], '/index.php') !== false) ? 'active' : ''; ?>"
               <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php' || $_SERVER['REQUEST_URI'] == '/' || $_SERVER['REQUEST_URI'] == '/noithatbangvu/' || strpos($_SERVER['REQUEST_URI'], '/index.php') !== false) ? 'aria-current="page"' : ''; ?>>
                <i class="fas fa-home" aria-hidden="true"></i>
                <span>Trang chủ</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/products.php"
               class="mobile-nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'products.php' || basename($_SERVER['PHP_SELF']) == 'category.php' || basename($_SERVER['PHP_SELF']) == 'product.php') ? 'active' : ''; ?>"
               <?php echo (basename($_SERVER['PHP_SELF']) == 'products.php' || basename($_SERVER['PHP_SELF']) == 'category.php' || basename($_SERVER['PHP_SELF']) == 'product.php') ? 'aria-current="page"' : ''; ?>>
                <i class="fas fa-th-large" aria-hidden="true"></i>
                <span>Sản phẩm</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/search.php"
               class="mobile-nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'search.php') ? 'active' : ''; ?>"
               <?php echo (basename($_SERVER['PHP_SELF']) == 'search.php') ? 'aria-current="page"' : ''; ?>>
                <i class="fas fa-search" aria-hidden="true"></i>
                <span>Tìm kiếm</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/cart.php"
               class="mobile-nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'cart.php') ? 'active' : ''; ?>"
               <?php echo (basename($_SERVER['PHP_SELF']) == 'cart.php') ? 'aria-current="page"' : ''; ?>
               aria-label="Giỏ hàng<?php echo (get_cart_count() > 0) ? ', ' . get_cart_count() . ' sản phẩm' : ''; ?>">
                <i class="fas fa-shopping-cart mobile-cart-icon" aria-hidden="true"></i>
                <span>Giỏ hàng</span>
                <?php if (get_cart_count() > 0): ?>
                <span class="mobile-nav-badge mobile-cart-badge" data-count="<?php echo get_cart_count(); ?>" aria-hidden="true"><?php echo get_cart_count() > 99 ? '99+' : get_cart_count(); ?></span>
                <?php endif; ?>
            </a>
            <?php if (is_logged_in()): ?>
            <a href="<?php echo BASE_URL; ?>/account/profile.php"
               class="mobile-nav-item account-nav-item <?php echo (strpos($_SERVER['PHP_SELF'], '/account/') !== false) ? 'active' : ''; ?>"
               <?php echo (strpos($_SERVER['PHP_SELF'], '/account/') !== false) ? 'aria-current="page"' : ''; ?>
               aria-label="Tài khoản của bạn">
                <?php
                // Lấy thông tin người dùng để hiển thị avatar
                $current_user = get_user_by_id($_SESSION['user_id']);
                if (!empty($current_user['avatar'])): ?>
                <div class="mobile-nav-avatar">
                    <div class="avatar-inner">
                        <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $current_user['avatar']; ?>" alt="Avatar của <?php echo $current_user['full_name']; ?>">
                    </div>
                    <div class="avatar-indicators">
                        <span class="avatar-status-dot" aria-hidden="true"></span>
                    </div>
                </div>
                <?php else: ?>
                <div class="mobile-nav-avatar default-avatar">
                    <div class="avatar-inner no-border">
                        <i class="fas fa-user-circle" aria-hidden="true"></i>
                    </div>
                    <div class="avatar-indicators">
                        <span class="avatar-status-dot" aria-hidden="true"></span>
                    </div>
                </div>
                <?php endif; ?>
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/login.php"
               class="mobile-nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'login.php') ? 'active' : ''; ?>"
               <?php echo (basename($_SERVER['PHP_SELF']) == 'login.php') ? 'aria-current="page"' : ''; ?>
               aria-label="Đăng nhập">
                <i class="fas fa-user-circle" aria-hidden="true"></i>
                <span>Đăng nhập</span>
            </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Premium Header -->
    <header class="premium-header" role="banner">
        <!-- Tier 1: Top Bar -->
        <div class="top-bar" aria-label="Thông tin liên hệ">
            <div class="top-bar-content">
                <div class="top-bar-contact">
                    <a href="tel:+84972774646" aria-label="Gọi điện thoại: ************"><i class="fas fa-phone-alt" aria-hidden="true"></i> ************</a>
                    <a href="mailto:<EMAIL>" class="top-bar-email" aria-label="Gửi email: <EMAIL>"><i class="fas fa-envelope" aria-hidden="true"></i> <EMAIL></a>
                </div>
                <div class="top-bar-address">
                    <a href="https://maps.google.com/?q=Số+91,93+Ngõ+85,+đường+Đức+Diễn,+Phúc+Diễn,+Bắc+Từ+Liêm,+Hà+Nội" target="_blank" class="top-bar-address-link" aria-label="Xem địa chỉ trên bản đồ: Số 91,93 Ngõ 85, đường Đức Diễn, Phúc Diễn, Bắc Từ Liêm, Hà Nội"><i class="fas fa-map-marker-alt" aria-hidden="true"></i> Số 91,93 Ngõ 85, đường Đức Diễn, Phúc Diễn, Bắc Từ Liêm, Hà Nội</a>
                </div>
            </div>
        </div>

        <!-- Tier 2: Logo and Search -->
        <div class="mid-header-container">
            <div class="mid-header">
                <!-- Logo -->
                <a href="<?php echo BASE_URL; ?>" class="premium-logo" aria-label="<?php echo SITE_NAME; ?> - Trang chủ">
                    <div class="premium-logo-image">
                        <img src="<?php echo BASE_URL; ?>/assets/images/logo/logo.svg" alt="<?php echo SITE_NAME; ?>">
                    </div>
                </a>

                <!-- Search Container -->
                <div class="search-container" role="search">
                    <form action="<?php echo BASE_URL; ?>/products.php" method="GET" class="search-form">
                        <label for="header-search" class="sr-only">Tìm kiếm sản phẩm</label>
                        <input type="text" id="header-search" name="keyword" placeholder="Tìm kiếm sản phẩm..." class="search-input"
                            autocomplete="off" aria-label="Tìm kiếm sản phẩm">
                        <button type="submit" class="search-button" aria-label="Tìm kiếm">
                            <i class="fas fa-search" aria-hidden="true"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Tier 3: Navigation and User Actions -->
        <div class="bottom-header-container">
            <div class="bottom-header">
                <!-- Navigation Menu -->
                <nav role="navigation" aria-label="Menu chính">
                    <!-- Arrow Indicators -->
                    <button class="nav-scroll-arrow nav-scroll-left" aria-label="Cuộn menu sang trái">
                        <i class="fas fa-chevron-left" aria-hidden="true"></i>
                    </button>

                    <ul class="nav-menu">
                        <li
                            class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php') ? 'active' : ''; ?>">
                            <a href="<?php echo BASE_URL; ?>" class="nav-link" <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php') ? 'aria-current="page"' : ''; ?>>Trang chủ</a>
                        </li>
                        <li
                            class="nav-item <?php
                                $current_page = basename($_SERVER['PHP_SELF']);
                                $request_uri = $_SERVER['REQUEST_URI'];
                                $is_products_active = ($current_page == 'products.php' ||
                                                     $current_page == 'category.php' ||
                                                     $current_page == 'product.php' ||
                                                     strpos($request_uri, '/products.php') !== false ||
                                                     strpos($request_uri, '/category.php') !== false ||
                                                     strpos($request_uri, '/product.php') !== false);
                                echo $is_products_active ? 'active' : '';
                            ?>" role="menuitem">
                            <a href="<?php echo BASE_URL; ?>/products.php" class="nav-link" aria-haspopup="true" aria-expanded="false" <?php echo $is_products_active ? 'aria-current="page"' : ''; ?>>
                                Sản phẩm <i class="fas fa-chevron-down" aria-hidden="true"></i>
                            </a>

                            <!-- Mega Menu -->
                            <?php
                            // Include file mega-menu.php để sử dụng các hàm liên quan
                            require_once __DIR__ . '/../includes/mega-menu.php';

                            // Lấy danh sách icon cho danh mục
                            $category_icons = get_category_icons();
                            ?>

                            <div class="mega-menu" role="menu" aria-label="Danh mục sản phẩm">
                                <!-- Cột trái - Danh sách danh mục -->
                                <div class="mega-menu-categories" role="tablist" aria-label="Danh sách danh mục chính">
                                    <?php foreach ($categories as $index => $category): ?>
                                    <div class="mega-menu-category <?php echo ($index === 0) ? 'active' : ''; ?>"
                                         data-category-id="<?php echo $category['id']; ?>"
                                         role="tab"
                                         id="tab-category-<?php echo $category['id']; ?>"
                                         aria-selected="<?php echo ($index === 0) ? 'true' : 'false'; ?>"
                                         aria-controls="category-content-<?php echo $category['id']; ?>"
                                         tabindex="<?php echo ($index === 0) ? '0' : '-1'; ?>">
                                        <div class="mega-menu-category-icon">
                                            <?php
                                            // Lấy icon phù hợp cho danh mục
                                            $icon_class = get_category_icon($category['name']);
                                            ?>
                                            <i class="fas <?php echo $icon_class; ?>" aria-hidden="true"></i>
                                        </div>
                                        <div class="mega-menu-category-name"><?php echo $category['name']; ?></div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>

                                <!-- Cột phải - Nội dung theo danh mục -->
                                <?php foreach ($categories as $index => $category): ?>
                                <div class="mega-menu-content <?php echo ($index === 0) ? 'active' : ''; ?>"
                                     id="category-content-<?php echo $category['id']; ?>"
                                     role="tabpanel"
                                     aria-labelledby="tab-category-<?php echo $category['id']; ?>"
                                     <?php echo ($index === 0) ? '' : 'hidden'; ?>>
                                    <h3 class="mega-menu-title"><?php echo $category['name']; ?></h3>

                                    <!-- Danh mục con -->
                                    <?php
                                    // Lấy danh sách danh mục con trực tiếp
                                    $subcategories = isset($category['children']) ? $category['children'] : [];

                                    if (!empty($subcategories)):
                                    ?>
                                    <div class="mega-menu-subcategories" role="menu" aria-label="Danh mục con của <?php echo $category['name']; ?>">
                                        <?php foreach ($subcategories as $subcategory): ?>
                                        <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $subcategory['slug']; ?>"
                                           class="mega-menu-subcategory"
                                           role="menuitem">
                                            <?php if (!empty($subcategory['image'])): ?>
                                            <div class="mega-menu-subcategory-image">
                                                <img src="<?php echo BASE_URL; ?>/uploads/categories/<?php echo $subcategory['image']; ?>" alt="<?php echo $subcategory['name']; ?>">
                                            </div>
                                            <?php else: ?>
                                            <div class="mega-menu-subcategory-icon">
                                                <?php
                                                // Lấy icon phù hợp cho danh mục con
                                                $icon_class = get_category_icon($subcategory['name']);
                                                ?>
                                                <i class="fas <?php echo $icon_class; ?>" aria-hidden="true"></i>
                                            </div>
                                            <?php endif; ?>
                                            <div class="mega-menu-subcategory-name"><?php echo $subcategory['name']; ?></div>
                                        </a>
                                        <?php endforeach; ?>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Sản phẩm bán chạy -->
                                    <div class="mega-menu-bestsellers">
                                        <h4 class="mega-menu-bestsellers-title">
                                            <i class="fas fa-fire" aria-hidden="true"></i> Sản phẩm bán chạy
                                        </h4>

                                        <div class="mega-menu-products" role="menu" aria-label="Sản phẩm bán chạy của <?php echo $category['name']; ?>">
                                            <?php
                                            // Lấy sản phẩm bán chạy theo danh mục
                                            $bestsellers = get_bestsellers_by_category($category['id'], 4);

                                            foreach ($bestsellers as $product):
                                            ?>
                                            <a href="<?php echo BASE_URL; ?>/product.php?slug=<?php echo $product['slug']; ?>"
                                               class="mega-menu-product"
                                               role="menuitem"
                                               aria-label="<?php echo $product['name']; ?> - <?php echo !empty($product['sale_price']) ? number_format($product['sale_price']) : number_format($product['price']); ?>₫">
                                                <div class="mega-menu-product-image">
                                                    <?php if (!empty($product['image'])): ?>
                                                    <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>">
                                                    <?php else: ?>
                                                    <div class="w-full h-full bg-gray-300 flex items-center justify-center">
                                                        <i class="fas fa-image text-gray-500" aria-hidden="true"></i>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="mega-menu-product-name"><?php echo $product['name']; ?></div>
                                                <div class="mega-menu-product-price">
                                                    <?php if (!empty($product['sale_price'])): ?>
                                                    <?php echo number_format($product['sale_price'], 0, '.', '.'); ?>₫
                                                    <?php else: ?>
                                                    <?php echo number_format($product['price'], 0, '.', '.'); ?>₫
                                                    <?php endif; ?>
                                                </div>
                                            </a>
                                            <?php endforeach; ?>
                                        </div>

                                        <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $category['slug']; ?>"
                                           class="mega-menu-view-all"
                                           aria-label="Xem tất cả sản phẩm <?php echo $category['name']; ?>">
                                            Xem tất cả <i class="fas fa-arrow-right" aria-hidden="true"></i>
                                        </a>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </li>
                        <li
                            class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'pricing.php') ? 'active' : ''; ?>" role="menuitem">
                            <a href="<?php echo BASE_URL; ?>/pricing.php" class="nav-link" <?php echo (basename($_SERVER['PHP_SELF']) == 'pricing.php') ? 'aria-current="page"' : ''; ?>>Báo giá</a>
                        </li>
                        <li
                            class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'design-process.php') ? 'active' : ''; ?>" role="menuitem">
                            <a href="<?php echo BASE_URL; ?>/design-process.php" class="nav-link" <?php echo (basename($_SERVER['PHP_SELF']) == 'design-process.php') ? 'aria-current="page"' : ''; ?>>Quy trình thiết kế</a>
                        </li>
                        <li
                            class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'blog.php') ? 'active' : ''; ?>" role="menuitem">
                            <a href="<?php echo BASE_URL; ?>/blog.php" class="nav-link" <?php echo (basename($_SERVER['PHP_SELF']) == 'blog.php') ? 'aria-current="page"' : ''; ?>>Blog</a>
                        </li>
                        <li
                            class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'about.php') ? 'active' : ''; ?>" role="menuitem">
                            <a href="<?php echo BASE_URL; ?>/about.php" class="nav-link" <?php echo (basename($_SERVER['PHP_SELF']) == 'about.php') ? 'aria-current="page"' : ''; ?>>Giới thiệu</a>
                        </li>
                        <li
                            class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'contact.php') ? 'active' : ''; ?>" role="menuitem">
                            <a href="<?php echo BASE_URL; ?>/contact.php" class="nav-link" <?php echo (basename($_SERVER['PHP_SELF']) == 'contact.php') ? 'aria-current="page"' : ''; ?>>Liên hệ</a>
                        </li>
                    </ul>

                    <!-- Right Arrow Indicator -->
                    <button class="nav-scroll-arrow nav-scroll-right" aria-label="Cuộn menu sang phải">
                        <i class="fas fa-chevron-right" aria-hidden="true"></i>
                    </button>
                </nav>

                <!-- User Actions -->
                <div class="user-actions" role="navigation" aria-label="Tài khoản và giỏ hàng">
                    <!-- User Account -->
                    <?php if (is_logged_in()): ?>
                    <div class="user-dropdown">
                        <a href="#" class="action-btn user-account-btn" aria-haspopup="true" aria-expanded="false" aria-label="Menu tài khoản">
                            <div class="user-avatar">
                                <?php
                                // Lấy thông tin người dùng để hiển thị avatar
                                $current_user = get_user_by_id($_SESSION['user_id']);
                                if (!empty($current_user['avatar'])): ?>
                                <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $current_user['avatar']; ?>"
                                    alt="Avatar của <?php echo $current_user['full_name']; ?>" class="w-full h-full object-cover rounded-full">
                                <?php else: ?>
                                <i class="fas fa-user-circle default-user-icon" aria-hidden="true"></i>
                                <?php endif; ?>
                                <span class="online-dot" aria-hidden="true"></span>
                            </div>
                            <div class="user-name">
                                <span><?php echo limit_user_name($current_user['full_name']); ?></span>
                                <i class="fas fa-chevron-down" aria-hidden="true"></i>
                            </div>
                        </a>
                        <div class="user-dropdown-menu" role="menu" aria-label="Menu tài khoản người dùng">
                            <div class="user-dropdown-header">
                                <div class="user-dropdown-header-title"><?php echo $current_user['full_name']; ?></div>
                                <div class="user-dropdown-header-email"><?php echo $current_user['email']; ?></div>
                            </div>
                            <a href="<?php echo BASE_URL; ?>/account/profile.php" class="user-dropdown-item" role="menuitem">
                                <i class="fas fa-user" aria-hidden="true"></i> Thông tin tài khoản
                            </a>
                            <div class="user-dropdown-divider" role="separator"></div>
                            <a href="<?php echo BASE_URL; ?>/account/orders.php" class="user-dropdown-item" role="menuitem">
                                <i class="fas fa-shopping-bag" aria-hidden="true"></i> Đơn hàng của tôi
                            </a>
                            <div class="user-dropdown-divider" role="separator"></div>
                            <a href="<?php echo BASE_URL; ?>/account/change-password.php" class="user-dropdown-item" role="menuitem">
                                <i class="fas fa-key" aria-hidden="true"></i> Đổi mật khẩu
                            </a>
                            <?php if (is_admin()): ?>
                            <div class="user-dropdown-divider" role="separator"></div>
                            <a href="<?php echo BASE_URL; ?>/admin" class="user-dropdown-item" role="menuitem">
                                <i class="fas fa-cogs" aria-hidden="true"></i> Quản trị hệ thống
                            </a>
                            <?php endif; ?>
                            <div class="user-dropdown-divider" role="separator"></div>
                            <a href="<?php echo BASE_URL; ?>/logout.php" class="user-dropdown-item" role="menuitem">
                                <i class="fas fa-sign-out-alt" aria-hidden="true"></i> Đăng xuất
                            </a>
                        </div>
                    </div>
                    <?php else: ?>
                    <a href="<?php echo BASE_URL; ?>/login.php" class="action-btn login-btn" aria-label="Đăng nhập">
                        <i class="fas fa-user" aria-hidden="true"></i>
                        <span>Đăng nhập</span>
                    </a>
                    <?php endif; ?>

                    <!-- Cart -->
                    <div class="cart-container">
                        <a href="<?php echo BASE_URL; ?>/cart.php" class="action-btn cart-btn" aria-label="Giỏ hàng<?php echo (get_cart_count() > 0) ? ', ' . get_cart_count() . ' sản phẩm' : ''; ?>">
                            <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                            <span>Giỏ hàng</span>
                        </a>
                        <?php if (get_cart_count() > 0): ?>
                        <span class="cart-badge" aria-hidden="true"><?php echo get_cart_count(); ?></span>
                        <?php endif; ?>

                        <!-- Mini Cart -->
                        <div class="mini-cart">
                            <div class="mini-cart-header">
                                <div class="mini-cart-title">Giỏ hàng của bạn</div>
                                <div class="mini-cart-count"><?php echo get_cart_count(); ?> sản phẩm</div>
                            </div>

                            <div class="mini-cart-items">
                                <?php
                                // Lấy tất cả sản phẩm trong giỏ hàng
                                $all_cart_items = get_cart_items();
                                $total_items = count($all_cart_items);

                                // Lấy 3 sản phẩm mới nhất để hiển thị
                                $cart_items = array_slice(array_reverse($all_cart_items), 0, 3);
                                $cart_items = array_reverse($cart_items);

                                if (!empty($cart_items)):
                                    foreach ($cart_items as $item):
                                ?>
                                <div class="mini-cart-item">
                                    <div class="mini-cart-item-image">
                                        <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $item['image']; ?>" alt="<?php echo $item['name']; ?>">
                                    </div>
                                    <div class="mini-cart-item-info">
                                        <div class="mini-cart-item-name"><?php echo $item['name']; ?></div>
                                        <div class="mini-cart-item-price"><?php echo format_currency($item['price']); ?></div>
                                        <div class="mini-cart-item-quantity">Số lượng: <?php echo $item['quantity']; ?></div>
                                    </div>
                                </div>
                                <?php
                                    endforeach;

                                    // Hiển thị thông báo nếu có nhiều hơn 3 sản phẩm
                                    if ($total_items > 3):
                                ?>
                                <div class="mini-cart-more-items">
                                    <a href="<?php echo BASE_URL; ?>/cart.php" class="mini-cart-view-more">
                                        + <?php echo $total_items - 3; ?> sản phẩm khác
                                    </a>
                                </div>
                                <?php
                                    endif;
                                else:
                                ?>
                                <div class="mini-cart-empty">
                                    <p>Giỏ hàng của bạn đang trống</p>
                                </div>
                                <?php
                                endif;
                                ?>
                            </div>

                            <div class="mini-cart-footer">
                                <div class="mini-cart-total">
                                    <div class="mini-cart-total-label">Tổng cộng:</div>
                                    <div class="mini-cart-total-value"><?php echo format_currency(get_cart_total()); ?></div>
                                </div>

                                <div class="mini-cart-buttons">
                                    <a href="<?php echo BASE_URL; ?>/cart.php" class="mini-cart-button view-cart">Xem giỏ hàng</a>
                                    <a href="<?php echo BASE_URL; ?>/checkout.php" class="mini-cart-button checkout">Thanh toán</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle" aria-label="Mở menu di động" aria-expanded="false" aria-controls="mobile-menu">
                        <i class="fas fa-bars" aria-hidden="true"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Menu - Luxury Design -->
    <div class="mobile-menu" id="mobile-menu" aria-hidden="true">
        <!-- Menu chính -->
        <div class="mobile-menu-main">
            <nav class="mobile-menu-nav" role="navigation" aria-label="Menu di động">
                <ul class="mobile-menu-list" role="menu">
                    <!-- Danh mục chính -->
                    <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php') ? 'active' : ''; ?>">
                        <a href="<?php echo BASE_URL; ?>" class="mobile-menu-link">
                            <i class="fas fa-home"></i> Trang chủ
                        </a>
                    </li>

                    <!-- Danh mục sản phẩm -->
                    <li class="mobile-menu-item <?php
                        $current_page = basename($_SERVER['PHP_SELF']);
                        $request_uri = $_SERVER['REQUEST_URI'];
                        $is_products_active = ($current_page == 'products.php' ||
                                             $current_page == 'category.php' ||
                                             $current_page == 'product.php' ||
                                             strpos($request_uri, '/products.php') !== false ||
                                             strpos($request_uri, '/category.php') !== false ||
                                             strpos($request_uri, '/product.php') !== false);
                        echo $is_products_active ? 'active' : '';
                    ?>">
                        <a href="javascript:void(0);" class="mobile-menu-link mobile-dropdown-toggle">
                            <i class="fas fa-couch"></i> Sản phẩm <i class="fas fa-chevron-down"></i>
                        </a>
                        <ul class="mobile-submenu">
                            <li class="mobile-submenu-header">
                                <button class="mobile-menu-back">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                                <span>Danh mục sản phẩm</span>
                            </li>
                            <li class="mobile-submenu-item">
                                <a href="<?php echo BASE_URL; ?>/products.php" class="mobile-submenu-link">
                                    <i class="fas fa-list"></i> Tất cả sản phẩm
                                </a>
                            </li>
                            <?php foreach ($categories as $category): ?>
                            <li class="mobile-submenu-item">
                                <?php if (isset($category['children']) && !empty($category['children'])): ?>
                                <a href="javascript:void(0);" class="mobile-submenu-link mobile-dropdown-toggle">
                                    <i class="fas <?php echo get_category_icon($category['name']); ?>"></i>
                                    <?php echo $category['name']; ?> <i class="fas fa-chevron-down"></i>
                                </a>
                                <ul class="mobile-submenu mobile-submenu-child">
                                    <li class="mobile-submenu-header">
                                        <button class="mobile-menu-back">
                                            <i class="fas fa-arrow-left"></i>
                                        </button>
                                        <span><?php echo $category['name']; ?></span>
                                    </li>
                                    <li class="mobile-submenu-item">
                                        <a href="<?php echo BASE_URL; ?>/category.php?id=<?php echo $category['id']; ?>" class="mobile-submenu-link">
                                            <i class="fas fa-list"></i> Tất cả <?php echo $category['name']; ?>
                                        </a>
                                    </li>
                                    <?php foreach ($category['children'] as $child): ?>
                                    <li class="mobile-submenu-item">
                                        <a href="<?php echo BASE_URL; ?>/category.php?id=<?php echo $child['id']; ?>" class="mobile-submenu-link">
                                            <i class="fas <?php echo get_category_icon($child['name']); ?>"></i> <?php echo $child['name']; ?>
                                        </a>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                                <?php else: ?>
                                <a href="<?php echo BASE_URL; ?>/category.php?id=<?php echo $category['id']; ?>" class="mobile-submenu-link">
                                    <i class="fas <?php echo get_category_icon($category['name']); ?>"></i> <?php echo $category['name']; ?>
                                </a>
                                <?php endif; ?>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </li>

                    <!-- Dịch vụ & Thông tin -->
                    <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'pricing.php') ? 'active' : ''; ?>">
                        <a href="<?php echo BASE_URL; ?>/pricing.php" class="mobile-menu-link">
                            <i class="fas fa-tag"></i> Báo giá
                        </a>
                    </li>
                    <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'design-process.php') ? 'active' : ''; ?>">
                        <a href="<?php echo BASE_URL; ?>/design-process.php" class="mobile-menu-link">
                            <i class="fas fa-drafting-compass"></i> Quy trình thiết kế
                        </a>
                    </li>
                    <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'blog.php') ? 'active' : ''; ?>">
                        <a href="<?php echo BASE_URL; ?>/blog.php" class="mobile-menu-link">
                            <i class="fas fa-newspaper"></i> Blog
                        </a>
                    </li>
                    <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'about.php') ? 'active' : ''; ?>">
                        <a href="<?php echo BASE_URL; ?>/about.php" class="mobile-menu-link">
                            <i class="fas fa-info-circle"></i> Giới thiệu
                        </a>
                    </li>
                    <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'contact.php') ? 'active' : ''; ?>">
                        <a href="<?php echo BASE_URL; ?>/contact.php" class="mobile-menu-link">
                            <i class="fas fa-phone-alt"></i> Liên hệ
                        </a>
                    </li>

                    <!-- Tài khoản & Đăng nhập -->
                    <?php if (is_logged_in()): ?>
                    <li class="mobile-menu-item">
                        <a href="javascript:void(0);" class="mobile-menu-link mobile-dropdown-toggle">
                            <i class="fas fa-user-circle"></i>
                            <?php
                            // Lấy thông tin người dùng để hiển thị tên
                            $current_user = get_user_by_id($_SESSION['user_id']);
                            echo limit_user_name($current_user['full_name']);
                            ?>
                            <i class="fas fa-chevron-down"></i>
                        </a>
                        <ul class="mobile-submenu">
                            <li class="mobile-submenu-header">
                                <button class="mobile-menu-back">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                                <span>Tài khoản của tôi</span>
                            </li>
                            <li class="mobile-submenu-item">
                                <a href="<?php echo BASE_URL; ?>/account/profile.php" class="mobile-submenu-link">
                                    <i class="fas fa-user"></i> Thông tin tài khoản
                                </a>
                            </li>
                            <li class="mobile-submenu-item">
                                <a href="<?php echo BASE_URL; ?>/account/orders.php" class="mobile-submenu-link">
                                    <i class="fas fa-shopping-bag"></i> Đơn hàng của tôi
                                </a>
                            </li>
                            <li class="mobile-submenu-item">
                                <a href="<?php echo BASE_URL; ?>/account/change-password.php" class="mobile-submenu-link">
                                    <i class="fas fa-key"></i> Đổi mật khẩu
                                </a>
                            </li>
                            <?php if (is_admin()): ?>
                            <li class="mobile-submenu-item">
                                <a href="<?php echo BASE_URL; ?>/admin" class="mobile-submenu-link">
                                    <i class="fas fa-cogs"></i> Quản trị hệ thống
                                </a>
                            </li>
                            <?php endif; ?>
                            <li class="mobile-submenu-item">
                                <a href="<?php echo BASE_URL; ?>/logout.php" class="mobile-submenu-link">
                                    <i class="fas fa-sign-out-alt"></i> Đăng xuất
                                </a>
                            </li>
                        </ul>
                    </li>
                    <?php else: ?>
                    <li class="mobile-menu-item">
                        <a href="<?php echo BASE_URL; ?>/login.php" class="mobile-menu-link">
                            <i class="fas fa-sign-in-alt"></i> Đăng nhập
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- Giỏ hàng -->
                    <li class="mobile-menu-item">
                        <a href="<?php echo BASE_URL; ?>/cart.php" class="mobile-menu-link">
                            <i class="fas fa-shopping-cart"></i> Giỏ hàng
                            <?php if (get_cart_count() > 0): ?>
                            <span class="mobile-badge"><?php echo get_cart_count(); ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="mobile-menu-footer">
                <div class="mobile-contact-info">
                    <h3>Thông tin liên hệ</h3>
                    <div class="mobile-contact-item">
                        <i class="fas fa-phone-alt"></i>
                        <a href="tel:+84972774646">************</a>
                    </div>
                    <div class="mobile-contact-item">
                        <i class="fas fa-envelope"></i>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                    <div class="mobile-contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Số 91,93 Ngõ 85, đường Đức Diễn, Phúc Diễn, Bắc Từ Liêm, Hà Nội</span>
                    </div>
                </div>

                <div class="mobile-social">
                    <a href="#" class="mobile-social-link" aria-label="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="mobile-social-link" aria-label="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="mobile-social-link" aria-label="Youtube">
                        <i class="fab fa-youtube"></i>
                    </a>
                    <a href="#" class="mobile-social-link" aria-label="Pinterest">
                        <i class="fab fa-pinterest-p"></i>
                    </a>
                </div>

                <div class="mobile-cta">
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="mobile-cta-button">
                        <i class="fas fa-headset"></i> Liên hệ tư vấn
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay" aria-hidden="true" role="presentation"></div>

    <!-- Flash Messages (Hidden by default, will be shown as center notification) -->
    <?php $flash_message = get_flash_message(); ?>
    <?php if ($flash_message): ?>


    <!-- Thông báo đơn giản -->
    <?php if ($flash_message): ?>
    <div id="simple-notification"
         role="alert"
         aria-live="assertive"
         style="position: fixed; top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; background-color: <?php echo $flash_message['type'] === 'success' ? '#10B981' : '#EF4444'; ?>; color: white; padding: 12px 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); display: flex; align-items: center; max-width: 90%; width: auto;">
        <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?>" style="margin-right: 10px;" aria-hidden="true"></i>
        <span style="flex: 1;"><?php echo $flash_message['message']; ?></span>
        <button onclick="this.parentElement.remove();"
                style="background: none; border: none; color: white; cursor: pointer; margin-left: 10px;"
                aria-label="Đóng thông báo">
            <i class="fas fa-times" aria-hidden="true"></i>
        </button>
    </div>

    <!-- Script đơn giản để tự động đóng thông báo -->
    <script>
        // Tự động đóng thông báo sau 5 giây
        setTimeout(function() {
            var notification = document.getElementById('simple-notification');
            if (notification) {
                notification.remove();
            }
        }, 5000);
    </script>
    <?php endif; ?>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="flex-grow site-main" role="main" id="main-content">
        <div class="container mx-auto px-4 py-0"><?php // Main content will be here ?>