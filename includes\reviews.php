<?php
/**
 * File chứa các hàm xử lý bình luận và đánh gi<PERSON> sản phẩm
 */

/**
 * Thêm đánh giá sản phẩm
 */
function add_product_review($product_id, $review_data, $media_files = []) {
    global $conn;

    try {
        $conn->beginTransaction();

        // Chuẩn bị dữ liệu
        $user_id = isset($review_data['user_id']) ? $review_data['user_id'] : null;
        $guest_name = isset($review_data['guest_name']) ? $review_data['guest_name'] : null;
        $guest_email = isset($review_data['guest_email']) ? $review_data['guest_email'] : null;
        $rating = isset($review_data['rating']) ? $review_data['rating'] : null;
        $review_title = isset($review_data['review_title']) ? $review_data['review_title'] : null;
        $review_content = isset($review_data['review_content']) ? $review_data['review_content'] : null;
        $is_anonymous = isset($review_data['is_anonymous']) && $review_data['is_anonymous'] ? 1 : 0;

        // Xác định trạng thái: nếu là người dùng đã đăng nhập thì approved, ngược lại pending
        $status = $user_id ? 'approved' : 'pending';

        // Thêm đánh giá vào database
        $stmt = $conn->prepare("INSERT INTO product_reviews (product_id, user_id, guest_name, guest_email, rating, review_title, review_content, is_anonymous, status)
                                VALUES (:product_id, :user_id, :guest_name, :guest_email, :rating, :review_title, :review_content, :is_anonymous, :status)");

        $stmt->bindParam(':product_id', $product_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':guest_name', $guest_name);
        $stmt->bindParam(':guest_email', $guest_email);
        $stmt->bindParam(':rating', $rating);
        $stmt->bindParam(':review_title', $review_title);
        $stmt->bindParam(':review_content', $review_content);
        $stmt->bindParam(':is_anonymous', $is_anonymous);
        $stmt->bindParam(':status', $status);

        $stmt->execute();

        $review_id = $conn->lastInsertId();

        // Xử lý upload media files
        if (!empty($media_files) && is_array($media_files)) {
            // Đảm bảo thư mục tồn tại
            $image_upload_dir = '../uploads/reviews/images/';
            $video_upload_dir = '../uploads/reviews/videos/';

            if (!file_exists($image_upload_dir)) {
                mkdir($image_upload_dir, 0777, true);
            }

            if (!file_exists($video_upload_dir)) {
                mkdir($video_upload_dir, 0777, true);
            }

            foreach ($media_files as $media) {
                if ($media['error'] === UPLOAD_ERR_OK) {
                    $media_type = 'image'; // Mặc định là hình ảnh
                    $upload_dir = $image_upload_dir;
                    $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

                    // Xác định loại file dựa vào extension
                    $file_extension = strtolower(pathinfo($media['name'], PATHINFO_EXTENSION));

                    // Nếu là video
                    if (in_array($file_extension, ['mp4', 'webm', 'ogg'])) {
                        $media_type = 'video';
                        $upload_dir = $video_upload_dir;
                        $allowed_types = ['mp4', 'webm', 'ogg'];
                    }
                    // Nếu là gif
                    else if ($file_extension === 'gif') {
                        $media_type = 'gif';
                    }

                    // Kiểm tra MIME type thực tế của file
                    $finfo = new finfo(FILEINFO_MIME_TYPE);
                    $mime_type = $finfo->file($media['tmp_name']);

                    // Kiểm tra MIME type
                    $valid_mime = false;
                    if ($media_type === 'video' && strpos($mime_type, 'video/') === 0) {
                        $valid_mime = true;
                    } else if (($media_type === 'image' || $media_type === 'gif') && strpos($mime_type, 'image/') === 0) {
                        $valid_mime = true;
                    }

                    if (!$valid_mime) {
                        continue; // Bỏ qua nếu MIME type không hợp lệ
                    }

                    // Upload file
                    $upload_result = upload_file($media, $upload_dir, $allowed_types);

                    if ($upload_result['success']) {
                        // Lưu thông tin media vào database
                        $stmt = $conn->prepare("INSERT INTO review_media (review_id, media_type, file_name)
                                                VALUES (:review_id, :media_type, :file_name)");

                        $stmt->bindParam(':review_id', $review_id);
                        $stmt->bindParam(':media_type', $media_type);
                        $stmt->bindParam(':file_name', $upload_result['filename']);

                        $stmt->execute();
                    } else {
                        // Log lỗi upload
                        error_log("Lỗi upload media cho review $review_id: " . $upload_result['message']);
                    }
                }
            }
        }

        $conn->commit();

        return [
            'success' => true,
            'message' => 'Đánh giá của bạn đã được gửi thành công' . ($status === 'pending' ? ' và đang chờ kiểm duyệt' : ''),
            'review_id' => $review_id
        ];
    } catch (PDOException $e) {
        $conn->rollBack();

        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Thêm phản hồi cho đánh giá
 */
function add_review_reply($review_id, $reply_data, $media_files = []) {
    global $conn;

    try {
        $conn->beginTransaction();

        // Chuẩn bị dữ liệu
        $user_id = isset($reply_data['user_id']) ? $reply_data['user_id'] : null;
        $guest_name = isset($reply_data['guest_name']) ? $reply_data['guest_name'] : null;
        $guest_email = isset($reply_data['guest_email']) ? $reply_data['guest_email'] : null;
        $reply_content = isset($reply_data['reply_content']) ? $reply_data['reply_content'] : null;

        // Xác định trạng thái: nếu là người dùng đã đăng nhập thì approved, ngược lại pending
        $status = $user_id ? 'approved' : 'pending';

        // Thêm phản hồi vào database
        $stmt = $conn->prepare("INSERT INTO review_replies (review_id, user_id, guest_name, guest_email, reply_content, status)
                                VALUES (:review_id, :user_id, :guest_name, :guest_email, :reply_content, :status)");

        $stmt->bindParam(':review_id', $review_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':guest_name', $guest_name);
        $stmt->bindParam(':guest_email', $guest_email);
        $stmt->bindParam(':reply_content', $reply_content);
        $stmt->bindParam(':status', $status);

        $stmt->execute();

        $reply_id = $conn->lastInsertId();

        // Xử lý upload media files
        if (!empty($media_files) && is_array($media_files)) {
            // Đảm bảo thư mục tồn tại
            $image_upload_dir = '../uploads/reviews/images/';
            $video_upload_dir = '../uploads/reviews/videos/';

            if (!file_exists($image_upload_dir)) {
                mkdir($image_upload_dir, 0777, true);
            }

            if (!file_exists($video_upload_dir)) {
                mkdir($video_upload_dir, 0777, true);
            }

            foreach ($media_files as $media) {
                if ($media['error'] === UPLOAD_ERR_OK) {
                    $media_type = 'image'; // Mặc định là hình ảnh
                    $upload_dir = $image_upload_dir;
                    $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

                    // Xác định loại file dựa vào extension
                    $file_extension = strtolower(pathinfo($media['name'], PATHINFO_EXTENSION));

                    // Nếu là video
                    if (in_array($file_extension, ['mp4', 'webm', 'ogg'])) {
                        $media_type = 'video';
                        $upload_dir = $video_upload_dir;
                        $allowed_types = ['mp4', 'webm', 'ogg'];
                    }
                    // Nếu là gif
                    else if ($file_extension === 'gif') {
                        $media_type = 'gif';
                    }

                    // Kiểm tra MIME type thực tế của file
                    $finfo = new finfo(FILEINFO_MIME_TYPE);
                    $mime_type = $finfo->file($media['tmp_name']);

                    // Kiểm tra MIME type
                    $valid_mime = false;
                    if ($media_type === 'video' && strpos($mime_type, 'video/') === 0) {
                        $valid_mime = true;
                    } else if (($media_type === 'image' || $media_type === 'gif') && strpos($mime_type, 'image/') === 0) {
                        $valid_mime = true;
                    }

                    if (!$valid_mime) {
                        continue; // Bỏ qua nếu MIME type không hợp lệ
                    }

                    // Upload file
                    $upload_result = upload_file($media, $upload_dir, $allowed_types);

                    if ($upload_result['success']) {
                        // Lưu thông tin media vào database
                        $stmt = $conn->prepare("INSERT INTO review_media (reply_id, media_type, file_name)
                                                VALUES (:reply_id, :media_type, :file_name)");

                        $stmt->bindParam(':reply_id', $reply_id);
                        $stmt->bindParam(':media_type', $media_type);
                        $stmt->bindParam(':file_name', $upload_result['filename']);

                        $stmt->execute();
                    } else {
                        // Log lỗi upload
                        error_log("Lỗi upload media cho reply $reply_id: " . $upload_result['message']);
                    }
                }
            }
        }

        $conn->commit();

        return [
            'success' => true,
            'message' => 'Phản hồi của bạn đã được gửi thành công' . ($status === 'pending' ? ' và đang chờ kiểm duyệt' : ''),
            'reply_id' => $reply_id
        ];
    } catch (PDOException $e) {
        $conn->rollBack();

        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Lấy danh sách đánh giá của sản phẩm
 */
function get_product_reviews($product_id, $page = 1, $limit = 5, $sort = 'newest', $filter_type = null) {
    global $conn;

    try {
        // Tính offset cho phân trang
        $offset = ($page - 1) * $limit;

        // Xác định sắp xếp
        $order_by = 'r.created_at DESC'; // Mặc định là mới nhất
        if ($sort === 'oldest') {
            $order_by = 'r.created_at ASC';
        } else if ($sort === 'highest') {
            $order_by = 'r.rating DESC, r.created_at DESC';
        } else if ($sort === 'lowest') {
            $order_by = 'r.rating ASC, r.created_at DESC';
        } else if ($sort === 'helpful') {
            $order_by = '(SELECT COUNT(*) FROM review_helpful WHERE review_id = r.id) DESC, r.created_at DESC';
        }

        // Xác định điều kiện lọc
        $filter_condition = "";
        $params = [':product_id' => $product_id];

        // Nếu là tab đánh giá có sao, chỉ lấy những đánh giá có rating
        if ($filter_type === 'ratings') {
            $filter_condition = " AND r.rating IS NOT NULL";
        }
        // Nếu là tab bình luận, lấy tất cả (bao gồm cả có rating và không có rating)

        // Lấy tổng số đánh giá theo điều kiện lọc
        $count_sql = "SELECT COUNT(*) FROM product_reviews r WHERE r.product_id = :product_id AND r.status = 'approved'" . $filter_condition;
        $stmt = $conn->prepare($count_sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        $total_reviews = $stmt->fetchColumn();

        // Tính tổng số trang
        $total_pages = ceil($total_reviews / $limit);

        // Lấy danh sách đánh giá
        $sql = "SELECT r.*, u.full_name, u.avatar,
                (SELECT COUNT(*) FROM review_helpful WHERE review_id = r.id) as helpful_count,
                (SELECT COUNT(*) FROM order_items oi
                 JOIN orders o ON oi.order_id = o.id
                 WHERE o.user_id = r.user_id AND oi.product_id = r.product_id AND o.status = 'completed') as is_verified_buyer
                FROM product_reviews r
                LEFT JOIN users u ON r.user_id = u.id
                WHERE r.product_id = :product_id AND r.status = 'approved'" . $filter_condition . "
                ORDER BY $order_by
                LIMIT :limit OFFSET :offset";

        $stmt = $conn->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        $reviews = $stmt->fetchAll();

        // Lấy media cho mỗi đánh giá
        foreach ($reviews as &$review) {
            $stmt = $conn->prepare("SELECT * FROM review_media WHERE review_id = :review_id");
            $stmt->bindParam(':review_id', $review['id']);
            $stmt->execute();
            $review['media'] = $stmt->fetchAll();

            // Lấy danh sách phản hồi
            $stmt = $conn->prepare("SELECT rp.*, u.full_name, u.avatar, u.role
                                    FROM review_replies rp
                                    LEFT JOIN users u ON rp.user_id = u.id
                                    WHERE rp.review_id = :review_id AND rp.status = 'approved'
                                    ORDER BY rp.created_at ASC");
            $stmt->bindParam(':review_id', $review['id']);
            $stmt->execute();
            $review['replies'] = $stmt->fetchAll();

            // Lấy media cho mỗi phản hồi
            foreach ($review['replies'] as &$reply) {
                $stmt = $conn->prepare("SELECT * FROM review_media WHERE reply_id = :reply_id");
                $stmt->bindParam(':reply_id', $reply['id']);
                $stmt->execute();
                $reply['media'] = $stmt->fetchAll();
            }
        }

        // Lấy thống kê đánh giá (chỉ tính những đánh giá có sao)
        $stmt = $conn->prepare("SELECT rating, COUNT(*) as count
                                FROM product_reviews
                                WHERE product_id = :product_id AND status = 'approved' AND rating IS NOT NULL
                                GROUP BY rating");
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();

        $rating_stats = [];
        while ($row = $stmt->fetch()) {
            $rating_stats[$row['rating']] = $row['count'];
        }

        // Tính điểm đánh giá trung bình
        $stmt = $conn->prepare("SELECT AVG(rating) as average, COUNT(*) as count
                                FROM product_reviews
                                WHERE product_id = :product_id AND status = 'approved' AND rating IS NOT NULL");
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();

        $result = $stmt->fetch();
        $average_rating = $result['average'];
        $rated_reviews_count = $result['count'];

        return [
            'reviews' => $reviews,
            'total_reviews' => $total_reviews,
            'total_pages' => $total_pages,
            'current_page' => $page,
            'rating_stats' => $rating_stats,
            'average_rating' => $average_rating ?: 0,
            'rated_reviews_count' => $rated_reviews_count ?: 0
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}
/**
 * Đánh dấu đánh giá là hữu ích
 */
function mark_review_helpful($review_id, $user_id = null) {
    global $conn;

    try {
        // Lấy IP của người dùng
        $ip_address = $_SERVER['REMOTE_ADDR'];

        // Kiểm tra xem đã đánh dấu chưa
        $stmt = $conn->prepare("SELECT id FROM review_helpful WHERE review_id = :review_id AND (user_id = :user_id OR ip_address = :ip_address)");
        $stmt->bindParam(':review_id', $review_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':ip_address', $ip_address);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Đã đánh dấu rồi, xóa đánh dấu (toggle)
            $stmt = $conn->prepare("DELETE FROM review_helpful WHERE review_id = :review_id AND (user_id = :user_id OR ip_address = :ip_address)");
            $stmt->bindParam(':review_id', $review_id);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->execute();

            return [
                'success' => true,
                'message' => 'Đã bỏ đánh dấu đánh giá là hữu ích',
                'action' => 'removed'
            ];
        } else {
            // Chưa đánh dấu, thêm mới
            $stmt = $conn->prepare("INSERT INTO review_helpful (review_id, user_id, ip_address) VALUES (:review_id, :user_id, :ip_address)");
            $stmt->bindParam(':review_id', $review_id);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->execute();

            return [
                'success' => true,
                'message' => 'Đã đánh dấu đánh giá là hữu ích',
                'action' => 'added'
            ];
        }
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Kiểm tra người dùng đã mua sản phẩm chưa và có thể đánh giá không
 * Cho phép đánh giá lại nếu mua sản phẩm nhiều lần
 */
function has_purchased_product($user_id, $product_id) {
    global $conn;

    try {
        // Đếm số lần mua sản phẩm đã hoàn thành
        $stmt = $conn->prepare("SELECT COUNT(*) FROM order_items oi
                                JOIN orders o ON oi.order_id = o.id
                                WHERE o.user_id = :user_id AND oi.product_id = :product_id AND o.status = 'completed'");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();

        $purchase_count = $stmt->fetchColumn();

        // Nếu chưa mua sản phẩm nào
        if ($purchase_count <= 0) {
            return false;
        }

        // Đếm số lần đã đánh giá sản phẩm
        $stmt = $conn->prepare("SELECT COUNT(*) FROM product_reviews
                                WHERE user_id = :user_id AND product_id = :product_id AND rating IS NOT NULL");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();

        $rating_count = $stmt->fetchColumn();

        // Nếu số lần mua nhiều hơn số lần đánh giá thì có thể đánh giá
        return $purchase_count > $rating_count;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Kiểm tra người dùng đã mua sản phẩm chưa (không quan tâm đã đánh giá hay chưa)
 */
function has_purchased_product_simple($user_id, $product_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM order_items oi
                                JOIN orders o ON oi.order_id = o.id
                                WHERE o.user_id = :user_id AND oi.product_id = :product_id AND o.status = 'completed'");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();

        return $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Báo cáo đánh giá
 */
function report_review($review_id, $reason, $user_id = null, $ip_address = null) {
    global $conn;

    try {
        // Kiểm tra xem đánh giá có tồn tại không
        $stmt = $conn->prepare("SELECT id FROM product_reviews WHERE id = :review_id");
        $stmt->bindParam(':review_id', $review_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Đánh giá không tồn tại'
            ];
        }

        // Kiểm tra xem đã báo cáo chưa
        $stmt = $conn->prepare("SELECT id FROM review_reports WHERE review_id = :review_id AND (user_id = :user_id OR ip_address = :ip_address)");
        $stmt->bindParam(':review_id', $review_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':ip_address', $ip_address);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return [
                'success' => false,
                'message' => 'Bạn đã báo cáo đánh giá này rồi'
            ];
        }

        // Thêm báo cáo mới
        $stmt = $conn->prepare("INSERT INTO review_reports (review_id, user_id, ip_address, reason, status, created_at)
                                VALUES (:review_id, :user_id, :ip_address, :reason, 'pending', NOW())");
        $stmt->bindParam(':review_id', $review_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':ip_address', $ip_address);
        $stmt->bindParam(':reason', $reason);
        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Cảm ơn bạn đã báo cáo. Chúng tôi sẽ xem xét đánh giá này.'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Lấy danh sách đánh giá chờ duyệt (cho admin)
 */
function get_pending_reviews($page = 1, $limit = 10) {
    global $conn;

    try {
        // Tính offset cho phân trang
        $offset = ($page - 1) * $limit;

        // Lấy tổng số đánh giá chờ duyệt
        $stmt = $conn->prepare("SELECT COUNT(*) FROM product_reviews WHERE status = 'pending'");
        $stmt->execute();
        $total_reviews = $stmt->fetchColumn();

        // Tính tổng số trang
        $total_pages = ceil($total_reviews / $limit);

        // Lấy danh sách đánh giá chờ duyệt
        $stmt = $conn->prepare("SELECT r.*, p.name as product_name, p.slug as product_slug, u.full_name, u.email
                                FROM product_reviews r
                                JOIN products p ON r.product_id = p.id
                                LEFT JOIN users u ON r.user_id = u.id
                                WHERE r.status = 'pending'
                                ORDER BY r.created_at ASC
                                LIMIT :limit OFFSET :offset");
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        $reviews = $stmt->fetchAll();

        // Lấy media cho mỗi đánh giá
        foreach ($reviews as &$review) {
            $stmt = $conn->prepare("SELECT * FROM review_media WHERE review_id = :review_id");
            $stmt->bindParam(':review_id', $review['id']);
            $stmt->execute();
            $review['media'] = $stmt->fetchAll();
        }

        return [
            'reviews' => $reviews,
            'total_reviews' => $total_reviews,
            'total_pages' => $total_pages,
            'current_page' => $page
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Lấy danh sách phản hồi chờ duyệt (cho admin)
 */
function get_pending_replies($page = 1, $limit = 10) {
    global $conn;

    try {
        // Tính offset cho phân trang
        $offset = ($page - 1) * $limit;

        // Lấy tổng số phản hồi chờ duyệt
        $stmt = $conn->prepare("SELECT COUNT(*) FROM review_replies WHERE status = 'pending'");
        $stmt->execute();
        $total_replies = $stmt->fetchColumn();

        // Tính tổng số trang
        $total_pages = ceil($total_replies / $limit);

        // Lấy danh sách phản hồi chờ duyệt
        $stmt = $conn->prepare("SELECT rp.*, p.name as product_name, p.slug as product_slug, u.full_name, u.email
                                FROM review_replies rp
                                JOIN product_reviews r ON rp.review_id = r.id
                                JOIN products p ON r.product_id = p.id
                                LEFT JOIN users u ON rp.user_id = u.id
                                WHERE rp.status = 'pending'
                                ORDER BY rp.created_at ASC
                                LIMIT :limit OFFSET :offset");
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        $replies = $stmt->fetchAll();

        // Lấy media cho mỗi phản hồi
        foreach ($replies as &$reply) {
            $stmt = $conn->prepare("SELECT * FROM review_media WHERE reply_id = :reply_id");
            $stmt->bindParam(':reply_id', $reply['id']);
            $stmt->execute();
            $reply['media'] = $stmt->fetchAll();
        }

        return [
            'replies' => $replies,
            'total_replies' => $total_replies,
            'total_pages' => $total_pages,
            'current_page' => $page
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Cập nhật trạng thái đánh giá
 */
function update_review_status($review_id, $status) {
    global $conn;

    try {
        $stmt = $conn->prepare("UPDATE product_reviews SET status = :status WHERE id = :id");
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $review_id);
        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Cập nhật trạng thái đánh giá thành công'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}



/**
 * Cập nhật trạng thái phản hồi
 */
function update_reply_status($reply_id, $status) {
    global $conn;

    try {
        $stmt = $conn->prepare("UPDATE review_replies SET status = :status WHERE id = :id");
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $reply_id);
        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Cập nhật trạng thái phản hồi thành công'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Xóa đánh giá
 */
function delete_review($review_id, $user_id = null) {
    global $conn;

    try {
        // Kiểm tra xem người dùng có quyền xóa đánh giá này không
        $stmt = $conn->prepare("SELECT * FROM product_reviews WHERE id = :id");
        $stmt->bindParam(':id', $review_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Đánh giá không tồn tại'
            ];
        }

        $review = $stmt->fetch();

        // Chỉ cho phép người dùng xóa đánh giá của chính họ hoặc admin
        if ($user_id !== null && $review['user_id'] != $user_id && !is_admin()) {
            return [
                'success' => false,
                'message' => 'Bạn không có quyền xóa đánh giá này'
            ];
        }

        $conn->beginTransaction();

        // Xóa media của đánh giá
        $stmt = $conn->prepare("SELECT * FROM review_media WHERE review_id = :review_id");
        $stmt->bindParam(':review_id', $review_id);
        $stmt->execute();
        $media_files = $stmt->fetchAll();

        foreach ($media_files as $media) {
            $file_path = '../uploads/reviews/' . ($media['media_type'] === 'video' ? 'videos/' : 'images/') . $media['file_name'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
        }

        // Xóa media của các phản hồi
        $stmt = $conn->prepare("SELECT rm.* FROM review_media rm
                                JOIN review_replies rp ON rm.reply_id = rp.id
                                WHERE rp.review_id = :review_id");
        $stmt->bindParam(':review_id', $review_id);
        $stmt->execute();
        $reply_media_files = $stmt->fetchAll();

        foreach ($reply_media_files as $media) {
            $file_path = '../uploads/reviews/' . ($media['media_type'] === 'video' ? 'videos/' : 'images/') . $media['file_name'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
        }

        // Xóa đánh giá (các bảng liên quan sẽ tự động xóa do ràng buộc khóa ngoại)
        $stmt = $conn->prepare("DELETE FROM product_reviews WHERE id = :id");
        $stmt->bindParam(':id', $review_id);
        $stmt->execute();

        $conn->commit();

        return [
            'success' => true,
            'message' => 'Xóa đánh giá thành công'
        ];
    } catch (PDOException $e) {
        $conn->rollBack();

        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Xóa phản hồi
 */
function delete_reply($reply_id, $user_id = null) {
    global $conn;

    try {
        // Kiểm tra xem người dùng có quyền xóa phản hồi này không
        $stmt = $conn->prepare("SELECT * FROM review_replies WHERE id = :id");
        $stmt->bindParam(':id', $reply_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Phản hồi không tồn tại'
            ];
        }

        $reply = $stmt->fetch();

        // Chỉ cho phép người dùng xóa phản hồi của chính họ hoặc admin
        if ($user_id !== null && $reply['user_id'] != $user_id && !is_admin()) {
            return [
                'success' => false,
                'message' => 'Bạn không có quyền xóa phản hồi này'
            ];
        }

        $conn->beginTransaction();

        // Xóa media của phản hồi
        $stmt = $conn->prepare("SELECT * FROM review_media WHERE reply_id = :reply_id");
        $stmt->bindParam(':reply_id', $reply_id);
        $stmt->execute();
        $media_files = $stmt->fetchAll();

        foreach ($media_files as $media) {
            $file_path = '../uploads/reviews/' . ($media['media_type'] === 'video' ? 'videos/' : 'images/') . $media['file_name'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
        }

        // Xóa phản hồi
        $stmt = $conn->prepare("DELETE FROM review_replies WHERE id = :id");
        $stmt->bindParam(':id', $reply_id);
        $stmt->execute();

        $conn->commit();

        return [
            'success' => true,
            'message' => 'Xóa phản hồi thành công'
        ];
    } catch (PDOException $e) {
        $conn->rollBack();

        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}
?>
