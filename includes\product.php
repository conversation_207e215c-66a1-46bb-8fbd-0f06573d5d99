<?php
/**
 * File xử lý các thao tác với sản phẩm
 */

/**
 * Tạo SKU tự động cho sản phẩm
 * Format: NTBV-SP001, NTBV-SP002, ...
 *
 * @return string SKU mới
 */
function generate_next_sku() {
    global $conn;

    try {
        // Kiểm tra xem trường sku có tồn tại không
        $check_column = $conn->prepare("SHOW COLUMNS FROM products LIKE 'sku'");
        $check_column->execute();

        if ($check_column->rowCount() == 0) {
            // Trường sku chưa tồn tại, trả về SKU mặc định
            return 'NTBV-SP001';
        }

        // Lấy SKU cuối cùng có format NTBV-SP###
        $stmt = $conn->prepare("SELECT sku FROM products WHERE sku LIKE 'NTBV-SP%' ORDER BY sku DESC LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch();

        if ($result) {
            // Tách số từ SKU cuối cùng
            $last_sku = $result['sku'];
            $number_part = substr($last_sku, 7); // Bỏ "NTBV-SP"
            $next_number = intval($number_part) + 1;
        } else {
            $next_number = 1;
        }

        // Tạo SKU mới với format 3 chữ số
        return 'NTBV-SP' . str_pad($next_number, 3, '0', STR_PAD_LEFT);

    } catch (PDOException $e) {
        // Nếu có lỗi, trả về SKU mặc định
        return 'NTBV-SP001';
    }
}

/**
 * Kiểm tra SKU có tồn tại không
 *
 * @param string $sku SKU cần kiểm tra
 * @param int|null $exclude_id ID sản phẩm cần loại trừ (dùng khi update)
 * @return bool True nếu SKU đã tồn tại
 */
function check_sku_exists($sku, $exclude_id = null) {
    global $conn;

    try {
        // Kiểm tra xem trường sku có tồn tại không
        $check_column = $conn->prepare("SHOW COLUMNS FROM products LIKE 'sku'");
        $check_column->execute();

        if ($check_column->rowCount() == 0) {
            // Trường sku chưa tồn tại, trả về false
            return false;
        }

        $sql = "SELECT id FROM products WHERE sku = :sku";
        $params = [':sku' => $sku];

        if ($exclude_id !== null) {
            $sql .= " AND id != :exclude_id";
            $params[':exclude_id'] = $exclude_id;
        }

        $stmt = $conn->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();

        return $stmt->rowCount() > 0;

    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Validate format SKU
 *
 * @param string $sku SKU cần validate
 * @return bool True nếu format hợp lệ
 */
function validate_sku_format($sku) {
    // Format: NTBV-SP### (3 chữ số)
    return preg_match('/^NTBV-SP\d{3}$/', $sku);
}

/**
 * Lấy danh sách sản phẩm với multiple filters (categories + promotions)
 *
 * @param array $category_ids Mảng ID các danh mục
 * @param array $promotion_filters Mảng promotion filters
 * @param int|null $limit Số lượng sản phẩm tối đa
 * @param int $offset Vị trí bắt đầu
 * @param string|null $search Từ khóa tìm kiếm
 * @param int|null $status Trạng thái sản phẩm (1: Hiển thị, 0: Ẩn, null: Tất cả)
 * @param float|null $price_min Giá tối thiểu
 * @param float|null $price_max Giá tối đa
 * @param array $sort_options Mảng tùy chọn sắp xếp
 * @return array Danh sách sản phẩm
 */
function get_products_with_filters($category_ids = [], $promotion_filters = [], $limit = null, $offset = 0, $search = null, $status = null, $price_min = null, $price_max = null, $sort_options = []) {
    global $conn;

    $sql = "SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE 1=1";
    $params = [];

    // Lọc theo multiple categories
    if (!empty($category_ids)) {
        $placeholders = [];
        foreach ($category_ids as $index => $cat_id) {
            $placeholder = ":category_id_$index";
            $placeholders[] = $placeholder;
            $params[$placeholder] = (int)$cat_id;
        }
        $sql .= " AND p.category_id IN (" . implode(',', $placeholders) . ")";
    }

    // Lọc theo promotion filters
    if (!empty($promotion_filters)) {
        $promotion_conditions = [];

        foreach ($promotion_filters as $index => $filter) {
            switch ($filter) {
                case 'sale':
                    $promotion_conditions[] = "(p.sale_price IS NOT NULL AND p.sale_price > 0 AND p.sale_price < p.price)";
                    break;
                case 'flash_sale':
                    $promotion_conditions[] = "(p.flash_sale = 1)";
                    break;
                case 'featured':
                    // Sản phẩm nổi bật dựa trên lượt xem (views > 100 hoặc điều kiện khác)
                    $promotion_conditions[] = "(p.views > 100)";
                    break;
            }
        }

        if (!empty($promotion_conditions)) {
            $sql .= " AND (" . implode(' OR ', $promotion_conditions) . ")";
        }
    }

    // Lọc theo trạng thái
    if ($status !== null) {
        $sql .= " AND p.status = :status";
        $params[':status'] = $status;
    }

    // Lọc theo từ khóa tìm kiếm
    if (!empty($search)) {
        $sql .= " AND (p.name LIKE :search OR p.description LIKE :search OR p.sku LIKE :search)";
        $params[':search'] = '%' . $search . '%';
    }

    // Lọc theo giá
    if ($price_min !== null) {
        $sql .= " AND p.price >= :price_min";
        $params[':price_min'] = $price_min;
    }
    if ($price_max !== null) {
        $sql .= " AND p.price <= :price_max";
        $params[':price_max'] = $price_max;
    }

    // Sắp xếp
    if (!empty($sort_options['by'])) {
        $order_by = $sort_options['by'];
        $order_direction = isset($sort_options['order']) ? $sort_options['order'] : 'ASC';

        switch ($order_by) {
            case 'price':
                // Sắp xếp theo giá khả dụng (sale_price nếu có và > 0, nếu không thì price)
                $sql .= " ORDER BY CASE
                    WHEN p.sale_price IS NOT NULL AND p.sale_price > 0 THEN p.sale_price
                    ELSE p.price
                END $order_direction";
                break;
            case 'name':
                $sql .= " ORDER BY p.name $order_direction";
                break;
            case 'created_at':
            default:
                $sql .= " ORDER BY p.created_at $order_direction";
                break;
        }
    } else {
        $sql .= " ORDER BY p.created_at DESC";
    }

    // Phân trang
    if ($limit !== null) {
        $sql .= " LIMIT :offset, :limit";
        $params[':offset'] = $offset;
        $params[':limit'] = $limit;
    }

    try {
        $stmt = $conn->prepare($sql);

        // Bind parameters
        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset' || $key === ':status' || strpos($key, ':category_id_') === 0) {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error in get_products_with_filters: " . $e->getMessage());
        return [];
    }
}

/**
 * Đếm số sản phẩm với multiple filters
 *
 * @param array $category_ids Mảng ID các danh mục
 * @param array $promotion_filters Mảng promotion filters
 * @param string|null $search Từ khóa tìm kiếm
 * @param int|null $status Trạng thái sản phẩm
 * @param float|null $price_min Giá tối thiểu
 * @param float|null $price_max Giá tối đa
 * @return int Số lượng sản phẩm
 */
function count_products_with_filters($category_ids = [], $promotion_filters = [], $search = null, $status = null, $price_min = null, $price_max = null) {
    global $conn;

    $sql = "SELECT COUNT(DISTINCT p.id) as total FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE 1=1";
    $params = [];

    // Lọc theo multiple categories
    if (!empty($category_ids)) {
        $placeholders = [];
        foreach ($category_ids as $index => $cat_id) {
            $placeholder = ":category_id_$index";
            $placeholders[] = $placeholder;
            $params[$placeholder] = (int)$cat_id;
        }
        $sql .= " AND p.category_id IN (" . implode(',', $placeholders) . ")";
    }

    // Lọc theo promotion filters
    if (!empty($promotion_filters)) {
        $promotion_conditions = [];

        foreach ($promotion_filters as $index => $filter) {
            switch ($filter) {
                case 'sale':
                    $promotion_conditions[] = "(p.sale_price IS NOT NULL AND p.sale_price > 0 AND p.sale_price < p.price)";
                    break;
                case 'flash_sale':
                    $promotion_conditions[] = "(p.flash_sale = 1)";
                    break;
                case 'featured':
                    $promotion_conditions[] = "(p.views > 100)";
                    break;
            }
        }

        if (!empty($promotion_conditions)) {
            $sql .= " AND (" . implode(' OR ', $promotion_conditions) . ")";
        }
    }

    // Lọc theo trạng thái
    if ($status !== null) {
        $sql .= " AND p.status = :status";
        $params[':status'] = $status;
    }

    // Lọc theo từ khóa tìm kiếm
    if (!empty($search)) {
        $sql .= " AND (p.name LIKE :search OR p.description LIKE :search OR p.sku LIKE :search)";
        $params[':search'] = '%' . $search . '%';
    }

    // Lọc theo giá
    if ($price_min !== null) {
        $sql .= " AND p.price >= :price_min";
        $params[':price_min'] = $price_min;
    }
    if ($price_max !== null) {
        $sql .= " AND p.price <= :price_max";
        $params[':price_max'] = $price_max;
    }

    try {
        $stmt = $conn->prepare($sql);

        // Bind parameters
        foreach ($params as $key => $value) {
            if ($key === ':status' || strpos($key, ':category_id_') === 0) {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();
        $result = $stmt->fetch();
        return (int)$result['total'];
    } catch (PDOException $e) {
        error_log("Error in count_products_with_filters: " . $e->getMessage());
        return 0;
    }
}

/**
 * Lấy danh sách sản phẩm với multiple categories
 *
 * @param array $category_ids Mảng ID các danh mục
 * @param int|null $limit Số lượng sản phẩm tối đa
 * @param int $offset Vị trí bắt đầu
 * @param string|null $search Từ khóa tìm kiếm
 * @param int|null $status Trạng thái sản phẩm (1: Hiển thị, 0: Ẩn, null: Tất cả)
 * @param float|null $price_min Giá tối thiểu
 * @param float|null $price_max Giá tối đa
 * @param array $sort_options Mảng tùy chọn sắp xếp, ví dụ: ['by' => 'price', 'order' => 'ASC']
 * @return array Danh sách sản phẩm
 */
function get_products_multiple_categories($category_ids = [], $limit = null, $offset = 0, $search = null, $status = null, $price_min = null, $price_max = null, $sort_options = []) {
    global $conn;

    // Debug log (có thể comment out khi production)
    // error_log("DEBUG get_products_multiple_categories: category_ids = " . print_r($category_ids, true));

    $sql = "SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE 1=1";
    $params = [];

    // Lọc theo multiple categories
    if (!empty($category_ids)) {
        $placeholders = [];
        foreach ($category_ids as $index => $cat_id) {
            $placeholder = ":category_id_$index";
            $placeholders[] = $placeholder;
            $params[$placeholder] = (int)$cat_id;
        }
        $sql .= " AND p.category_id IN (" . implode(',', $placeholders) . ")";
    }

    // Lọc theo trạng thái
    if ($status !== null) {
        $sql .= " AND p.status = :status";
        $params[':status'] = $status;
    }

    // Lọc theo từ khóa tìm kiếm
    if (!empty($search)) {
        $sql .= " AND (p.name LIKE :search OR p.description LIKE :search OR p.sku LIKE :search)";
        $params[':search'] = '%' . $search . '%';
    }

    // Lọc theo giá
    if ($price_min !== null) {
        $sql .= " AND p.price >= :price_min";
        $params[':price_min'] = $price_min;
    }
    if ($price_max !== null) {
        $sql .= " AND p.price <= :price_max";
        $params[':price_max'] = $price_max;
    }

    // Sắp xếp
    if (!empty($sort_options['by'])) {
        $order_by = $sort_options['by'];
        $order_direction = isset($sort_options['order']) ? $sort_options['order'] : 'ASC';

        switch ($order_by) {
            case 'price':
                // Sắp xếp theo giá khả dụng (sale_price nếu có và > 0, nếu không thì price)
                $sql .= " ORDER BY CASE
                    WHEN p.sale_price IS NOT NULL AND p.sale_price > 0 THEN p.sale_price
                    ELSE p.price
                END $order_direction";
                break;
            case 'name':
                $sql .= " ORDER BY p.name $order_direction";
                break;
            case 'created_at':
            default:
                $sql .= " ORDER BY p.created_at $order_direction";
                break;
        }
    } else {
        $sql .= " ORDER BY p.created_at DESC";
    }

    // Phân trang
    if ($limit !== null) {
        $sql .= " LIMIT :offset, :limit";
        $params[':offset'] = $offset;
        $params[':limit'] = $limit;
    }

    try {
        // Debug: Log SQL và parameters (có thể comment out khi production)
        // error_log("DEBUG SQL: " . $sql);
        // error_log("DEBUG params: " . print_r($params, true));

        $stmt = $conn->prepare($sql);

        // Bind parameters
        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset' || $key === ':status' || strpos($key, ':category_id_') === 0) {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();
        $results = $stmt->fetchAll();

        // Debug: Log số kết quả (có thể comment out khi production)
        // error_log("DEBUG results count: " . count($results));

        return $results;
    } catch (PDOException $e) {
        error_log("Error in get_products_multiple_categories: " . $e->getMessage());
        return [];
    }
}

/**
 * Đếm số sản phẩm với multiple categories
 *
 * @param array $category_ids Mảng ID các danh mục
 * @param string|null $search Từ khóa tìm kiếm
 * @param int|null $status Trạng thái sản phẩm
 * @param float|null $price_min Giá tối thiểu
 * @param float|null $price_max Giá tối đa
 * @return int Số lượng sản phẩm
 */
function count_products_multiple_categories($category_ids = [], $search = null, $status = null, $price_min = null, $price_max = null) {
    global $conn;

    $sql = "SELECT COUNT(DISTINCT p.id) as total FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE 1=1";
    $params = [];

    // Lọc theo multiple categories
    if (!empty($category_ids)) {
        $placeholders = [];
        foreach ($category_ids as $index => $cat_id) {
            $placeholder = ":category_id_$index";
            $placeholders[] = $placeholder;
            $params[$placeholder] = (int)$cat_id;
        }
        $sql .= " AND p.category_id IN (" . implode(',', $placeholders) . ")";
    }

    // Lọc theo trạng thái
    if ($status !== null) {
        $sql .= " AND p.status = :status";
        $params[':status'] = $status;
    }

    // Lọc theo từ khóa tìm kiếm
    if (!empty($search)) {
        $sql .= " AND (p.name LIKE :search OR p.description LIKE :search OR p.sku LIKE :search)";
        $params[':search'] = '%' . $search . '%';
    }

    // Lọc theo giá
    if ($price_min !== null) {
        $sql .= " AND p.price >= :price_min";
        $params[':price_min'] = $price_min;
    }
    if ($price_max !== null) {
        $sql .= " AND p.price <= :price_max";
        $params[':price_max'] = $price_max;
    }

    try {
        $stmt = $conn->prepare($sql);

        // Bind parameters
        foreach ($params as $key => $value) {
            if ($key === ':status' || strpos($key, ':category_id_') === 0) {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();
        $result = $stmt->fetch();
        return (int)$result['total'];
    } catch (PDOException $e) {
        error_log("Error in count_products_multiple_categories: " . $e->getMessage());
        return 0;
    }
}

/**
 * Lấy danh sách sản phẩm
 *
 * @param int|null $limit Số lượng sản phẩm tối đa
 * @param int $offset Vị trí bắt đầu
 * @param int|null $category_id ID của danh mục
 * @param int|null $featured Sản phẩm nổi bật (1: Có, 0: Không)
 * @param string|null $search Từ khóa tìm kiếm
 * @param int|null $status Trạng thái sản phẩm (1: Hiển thị, 0: Ẩn, null: Tất cả)
 * @param float|null $price_min Giá tối thiểu
 * @param float|null $price_max Giá tối đa
 * @param int|null $quantity_min Số lượng tồn kho tối thiểu
 * @param int|null $quantity_max Số lượng tồn kho tối đa
 * @param array $sort_options Mảng tùy chọn sắp xếp, ví dụ: ['by' => 'price', 'order' => 'ASC']
 * @param string|null $homepage_filter_mode Chế độ lọc sản phẩm trang chủ ('all_homepage', 'only_featured', hoặc 'cat_hp_X' với X là ID danh mục)
 * @return array Danh sách sản phẩm
 */
function get_products($limit = null, $offset = 0, $category_id = null, $featured = null, $search = null, $status = null, $price_min = null, $price_max = null, $quantity_min = null, $quantity_max = null, $sort_options = [], $homepage_filter_mode = null, $price_type = null) {
    global $conn;

    $base_sql_select_fields = "p.*, c.name as category_name";
    $sql_from_join_where = " FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE 1=1";

    $final_params = [];

    // Xử lý bộ lọc trang chủ (homepage_filter_mode)
    // Bộ lọc này sẽ được ưu tiên và có thể ghi đè $category_id hoặc $featured nếu cần.
    $homepage_conditions = [];

    if ($homepage_filter_mode !== null) {
        if (strpos($homepage_filter_mode, 'cat_hp_') === 0) {
            $hp_category_id = (int)substr($homepage_filter_mode, 7);
            if ($hp_category_id > 0) {
                // Lọc theo một danh mục cụ thể được đánh dấu là show_on_homepage
                // Chúng ta giả định danh mục này đã được kiểm tra là show_on_homepage ở admin/products.php
                $sql_from_join_where .= " AND p.category_id = :hp_category_id";
                $final_params[':hp_category_id'] = $hp_category_id;
                $category_id = null; // Ghi đè bộ lọc category_id chung nếu có
                $featured = null;    // Ghi đè bộ lọc featured chung nếu có
            }
        } elseif ($homepage_filter_mode === 'only_featured') {
            $sql_from_join_where .= " AND p.featured = 1";
            $featured = null; // Ghi đè bộ lọc featured chung vì đã xử lý ở đây
            $category_id = null; // Có thể cũng nên clear category_id để chỉ tập trung vào featured trên toàn bộ sản phẩm
        } elseif ($homepage_filter_mode === 'all_homepage') {
            // Lấy ID của các danh mục được hiển thị trên trang chủ VÀ ĐANG HOẠT ĐỘNG
            $homepage_categories = get_homepage_categories(1); // CHỈ LẤY DANH MỤC STATUS = 1
            $current_homepage_category_ids = [];
            if (!empty($homepage_categories)) {
                foreach ($homepage_categories as $hp_cat) {
                    $current_homepage_category_ids[] = $hp_cat['id'];
                }
            }

            $all_homepage_conditions_sql = [];
            // Điều kiện 1: Sản phẩm là nổi bật
            $all_homepage_conditions_sql[] = "p.featured = 1";
            // Điều kiện 2: Sản phẩm được đánh dấu show_on_homepage ở cấp độ sản phẩm
            $all_homepage_conditions_sql[] = "p.show_on_homepage = 1";

            // Điều kiện 3: Sản phẩm thuộc danh mục hiển thị trên trang chủ (đang hoạt động)
            if (!empty($current_homepage_category_ids)) {
                $cat_id_placeholders = [];
                foreach ($current_homepage_category_ids as $idx => $cat_id) {
                    $placeholder_name = ':hp_cat_id_' . $idx . '_filt';
                    $cat_id_placeholders[] = $placeholder_name;
                    $final_params[$placeholder_name] = $cat_id;
                }
                $all_homepage_conditions_sql[] = "p.category_id IN (" . implode(',', $cat_id_placeholders) . ")";
            }

            if (!empty($all_homepage_conditions_sql)) {
                 $sql_from_join_where .= " AND (" . implode(' OR ', $all_homepage_conditions_sql) . ")";
            }

            $featured = null;
            $category_id = null;
        }
    }

    // Lọc theo danh mục (nếu không bị ghi đè bởi homepage_filter_mode)
    if ($category_id !== null && $category_id > 0) {
        $sql_from_join_where .= " AND p.category_id = :category_id";
        $final_params[':category_id'] = $category_id;
    }

    // Lọc sản phẩm nổi bật
    if ($featured !== null) {
        $sql_from_join_where .= " AND p.featured = :featured";
        $final_params[':featured'] = $featured;
    }

    // Lọc theo trạng thái (đưa lên trước phần search để áp dụng cho cả count_products)
    if ($status !== null) {
        $sql_from_join_where .= " AND p.status = :status";
        $final_params[':status'] = $status;
    }

    // Hiển thị tất cả sản phẩm (bao gồm cả "liên hệ báo giá")
    // $sql_from_join_where .= " AND p.price_type = 'fixed'"; // Đã loại bỏ để hiển thị cả sản phẩm liên hệ báo giá

    // Lọc theo khoảng giá
    if ($price_min !== null && is_numeric($price_min)) {
        $sql_from_join_where .= " AND p.price >= :price_min";
        $final_params[':price_min'] = (float)$price_min;
    }
    if ($price_max !== null && is_numeric($price_max) && $price_max >= ($price_min ?? 0) ) {
        $sql_from_join_where .= " AND p.price <= :price_max";
        $final_params[':price_max'] = (float)$price_max;
    }

    // Lọc theo loại giá
    if ($price_type !== null && in_array($price_type, ['fixed', 'contact'])) {
        $sql_from_join_where .= " AND p.price_type = :price_type";
        $final_params[':price_type'] = $price_type;
    }

    // Lọc theo khoảng số lượng tồn kho
    if ($quantity_min !== null && is_numeric($quantity_min)) {
        $sql_from_join_where .= " AND p.quantity >= :quantity_min";
        $final_params[':quantity_min'] = (int)$quantity_min;
    }
    if ($quantity_max !== null && is_numeric($quantity_max) && $quantity_max >= ($quantity_min ?? 0) ) {
        $sql_from_join_where .= " AND p.quantity <= :quantity_max";
        $final_params[':quantity_max'] = (int)$quantity_max;
    }

    $is_sku_search_type = false; // Cờ để xác định loại tìm kiếm

    if ($search !== null && trim($search) !== '') {
        $search_query = trim($search);
        $search_conditions_sql_parts = [];
        $search_specific_params = [];

        // Ưu tiên 1: Kiểm tra tìm kiếm dạng SKU (ví dụ: "NTBV-SP006", "SP006", "006")
        // Regex: Optional "ntbv-sp" hoặc "sp", theo sau là số.
        if (preg_match('/^(?:ntbv-sp|sp)?(\d+)$/i', $search_query, $sku_matches)) {
            $is_sku_search_type = true;
            $sku_number_part = str_pad($sku_matches[1], 3, '0', STR_PAD_LEFT);
            $formatted_sku_to_find = 'NTBV-SP' . $sku_number_part;

            $sku_exact_condition = "p.sku = :search_formatted_sku";
            $search_specific_params[':search_formatted_sku'] = $formatted_sku_to_find;

            // Nếu người dùng chỉ nhập số (ví dụ "006" hoặc "6"), cũng có thể là ID sản phẩm
            // Điều này xảy ra khi $sku_matches[0] (toàn bộ match) giống $sku_matches[1] (phần số)
            // và $search_query là một số.
            if (is_numeric($search_query) && $sku_matches[0] === $sku_matches[1]) {
                 $id_condition = "p.id = :search_as_id_for_num_sku";
                 $search_specific_params[':search_as_id_for_num_sku'] = (int)$search_query;
                 $search_conditions_sql_parts[] = "($sku_exact_condition OR $id_condition)";
            } else {
                $search_conditions_sql_parts[] = $sku_exact_condition;
            }

        } else {
            // Ưu tiên 2: Nếu không phải SKU pattern, tìm kiếm theo từ khóa trong tên và SKU (có dấu và không dấu)
            $is_sku_search_type = false;
            $term_like_param = "%$search_query%";
            $search_specific_params[':search_keyword_term_like'] = $term_like_param;

            $keyword_search_sub_conditions = ["p.name LIKE :search_keyword_term_like", "p.sku LIKE :search_keyword_term_like"];

            $search_query_no_accent = remove_accents($search_query);
            if ($search_query_no_accent !== $search_query) {
                $term_no_accent_like_param = "%$search_query_no_accent%";
                $search_specific_params[':search_keyword_term_no_accent_like'] = $term_no_accent_like_param;
                $keyword_search_sub_conditions[] = "p.name LIKE :search_keyword_term_no_accent_like";
                $keyword_search_sub_conditions[] = "p.sku LIKE :search_keyword_term_no_accent_like";
            }
            $search_conditions_sql_parts[] = "(" . implode(" OR ", $keyword_search_sub_conditions) . ")";
        }

        if (!empty($search_conditions_sql_parts)) {
            $sql_from_join_where .= " AND (" . implode(" OR ", $search_conditions_sql_parts) . ")";
            $final_params = array_merge($final_params, $search_specific_params);
        }
    }

    // Xử lý SELECT fields và ORDER BY cho get_products
    $current_select_fields = $base_sql_select_fields;

    // -- Xử lý sắp xếp --
    $allowed_sort_columns = ['name', 'price', 'quantity', 'created_at', 'sku', 'relevance_score']; // Thêm 'relevance_score' vào đây
    $default_sort_by = 'created_at';
    $default_sort_order = 'DESC';

    $sort_by = $default_sort_by;
    $sort_order = $default_sort_order;

    if (!empty($sort_options['by']) && in_array($sort_options['by'], $allowed_sort_columns)) {
        $sort_by = $sort_options['by'];
    }
    if (!empty($sort_options['order']) && in_array(strtoupper($sort_options['order']), ['ASC', 'DESC'])) {
        $sort_order = strtoupper($sort_options['order']);
    }

    // Đặc biệt xử lý cho relevance_score, nếu có search và không phải SKU search
    if ($search !== null && trim($search) !== '' && !$is_sku_search_type) {
        $relevance_calculation_parts = [];
        if (isset($final_params[':search_keyword_term_like'])) {
            $relevance_calculation_parts[] = "(CASE WHEN p.sku LIKE :search_keyword_term_like THEN 150 ELSE 0 END)";
            $relevance_calculation_parts[] = "(CASE WHEN p.name LIKE :search_keyword_term_like THEN 100 ELSE 0 END)";
        }
        if (isset($final_params[':search_keyword_term_no_accent_like'])) {
            $relevance_calculation_parts[] = "(CASE WHEN p.sku LIKE :search_keyword_term_no_accent_like THEN 140 ELSE 0 END)";
            $relevance_calculation_parts[] = "(CASE WHEN p.name LIKE :search_keyword_term_no_accent_like THEN 90 ELSE 0 END)";
        }

        if (!empty($relevance_calculation_parts)) {
            $current_select_fields .= ", (" . implode(" + ", $relevance_calculation_parts) . ") as relevance_score";
            // Nếu có search từ khóa, và người dùng không chỉ định sort_by khác, thì ưu tiên relevance_score
            if ($sort_by === $default_sort_by && (!isset($sort_options['by']) || $sort_options['by'] !== 'relevance_score')) { // Chỉ ghi đè nếu sort_by là mặc định và không cố ý sort theo relevance
                 $sort_by = 'relevance_score'; // Sẽ được sử dụng bên dưới
                 // $sort_order giữ nguyên DESC mặc định cho relevance
            }
        }
    }

    $order_by_clause = "ORDER BY ";
    if ($sort_by === 'relevance_score') {
        // Đảm bảo relevance_score chỉ được dùng khi nó thực sự được tính toán
        if (strpos($current_select_fields, 'relevance_score') !== false) {
            $order_by_clause .= "relevance_score " . $sort_order . ", p.created_at DESC"; // Thêm sắp xếp phụ
        } else {
             // Fallback nếu relevance_score không được tính (ví dụ khi search rỗng hoặc là SKU search)
            $order_by_clause .= "p." . $default_sort_by . " " . $default_sort_order;
        }
    } elseif ($sort_by === 'price') {
        // Xử lý đặc biệt cho sort theo giá: sản phẩm "contact" luôn ở cuối
        $order_by_clause .= "p.price_type " . ($sort_order === 'ASC' ? 'ASC' : 'ASC') . ", ";
        $order_by_clause .= "CASE WHEN p.price_type = 'contact' THEN 1 ELSE 0 END ASC, ";
        // Sắp xếp theo giá khả dụng (sale_price nếu có và > 0, nếu không thì price)
        $order_by_clause .= "CASE
            WHEN p.sale_price IS NOT NULL AND p.sale_price > 0 THEN p.sale_price
            ELSE p.price
        END " . $sort_order;
        // Thêm sắp xếp phụ
        $order_by_clause .= ", p.created_at DESC";
    } else {
        // Đối với các cột khác, đảm bảo chúng thuộc bảng 'p' (products) để tránh lỗi ambiguity nếu join thêm bảng sau này
        $order_by_clause .= "p." . $sort_by . " " . $sort_order;
        // Thêm sắp xếp phụ để đảm bảo thứ tự nhất quán nếu giá trị cột chính bằng nhau
        if ($sort_by !== 'created_at') { // Tránh lặp lại nếu đã sắp xếp theo created_at
            $order_by_clause .= ", p.created_at DESC";
        }
    }
    // -- Kết thúc xử lý sắp xếp --

    $sql_get_query = "SELECT " . $current_select_fields . $sql_from_join_where . " " . $order_by_clause;

    // Giới hạn số lượng
    if ($limit !== null) {
        $sql_get_query .= " LIMIT :offset, :limit";
        $final_params[':offset'] = (int)$offset; // Thêm vào $final_params
        $final_params[':limit'] = (int)$limit;   // Thêm vào $final_params
    }

    // Debug logging for items per page
    $debug_items_per_page = true; // Set to false in production
    if ($debug_items_per_page) {
        error_log("get_products debug: limit = " . ($limit ?? 'null'));
        error_log("get_products debug: offset = " . $offset);
        error_log("get_products debug: SQL = " . $sql_get_query);
        error_log("get_products debug: final_params = " . json_encode($final_params));
    }

    try {
        $stmt = $conn->prepare($sql_get_query);
        foreach ($final_params as $param_key => $param_value) {
            if ($param_key === ':limit' || $param_key === ':offset' || $param_key === ':category_id' || $param_key === ':featured' || $param_key === ':status' || $param_key === ':search_as_id_for_num_sku' || $param_key === ':quantity_min' || $param_key === ':quantity_max') {
                 $stmt->bindValue($param_key, $param_value, PDO::PARAM_INT);
            } else if ($param_key === ':price_min' || $param_key === ':price_max') {
                 $stmt->bindValue($param_key, $param_value, PDO::PARAM_STR);
            } else {
                 $stmt->bindValue($param_key, $param_value);
            }
        }
        $stmt->execute();
        $results = $stmt->fetchAll();

        if ($debug_items_per_page) {
            error_log("get_products debug: results count = " . count($results));
        }

        return $results;
    } catch (PDOException $e) {
        if ($debug_items_per_page) {
            error_log("get_products debug: SQL Error = " . $e->getMessage());
        }
        return [];
    }
}

/**
 * Đếm tổng số sản phẩm
 *
 * @param int|null $category_id ID của danh mục
 * @param string|null $search Từ khóa tìm kiếm
 * @param int|null $status Trạng thái sản phẩm (1: Hiển thị, 0: Ẩn, null: Tất cả)
 * @param float|null $price_min Giá tối thiểu
 * @param float|null $price_max Giá tối đa
 * @param int|null $quantity_min Số lượng tồn kho tối thiểu
 * @param int|null $quantity_max Số lượng tồn kho tối đa
 * @param int|null $featured Sản phẩm nổi bật (1: Có, 0: Không)
 * @param string|null $homepage_filter_mode Chế độ lọc sản phẩm trang chủ ('all_homepage', 'only_featured', hoặc 'cat_hp_X' với X là ID danh mục)
 * @return int Tổng số sản phẩm
 */
function count_products($category_id = null, $search = null, $status = null, $price_min = null, $price_max = null, $quantity_min = null, $quantity_max = null, $featured = null, $homepage_filter_mode = null, $price_type = null) {
    global $conn;
    $sql = "SELECT COUNT(DISTINCT p.id) as total FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE 1=1";
    $final_params = [];

    // Xử lý bộ lọc trang chủ (homepage_filter_mode) - Tương tự như get_products
    if ($homepage_filter_mode !== null) {
        if (strpos($homepage_filter_mode, 'cat_hp_') === 0) {
            $hp_category_id = (int)substr($homepage_filter_mode, 7);
            if ($hp_category_id > 0) {
                $sql .= " AND p.category_id = :hp_category_id";
                $final_params[':hp_category_id'] = $hp_category_id;
                $category_id = null;
                $featured = null;
            }
        } elseif ($homepage_filter_mode === 'only_featured') {
            $sql .= " AND p.featured = 1";
            $featured = null;
            $category_id = null;
        } elseif ($homepage_filter_mode === 'all_homepage') {
            // Lấy ID của các danh mục được hiển thị trên trang chủ VÀ ĐANG HOẠT ĐỘNG
            $homepage_categories_for_count = get_homepage_categories(1); // CHỈ LẤY DANH MỤC STATUS = 1
            $current_homepage_category_ids_for_count = [];
            if (!empty($homepage_categories_for_count)) {
                foreach ($homepage_categories_for_count as $hp_cat_count) {
                    $current_homepage_category_ids_for_count[] = $hp_cat_count['id'];
                }
            }
            $all_homepage_conditions_count_sql = [];
            // Điều kiện 1: Sản phẩm là nổi bật
            $all_homepage_conditions_count_sql[] = "p.featured = 1";
            // Điều kiện 2: Sản phẩm được đánh dấu show_on_homepage ở cấp độ sản phẩm
            $all_homepage_conditions_count_sql[] = "p.show_on_homepage = 1";

            // Điều kiện 3: Sản phẩm thuộc danh mục hiển thị trên trang chủ (đang hoạt động)
            if (!empty($current_homepage_category_ids_for_count)) {
                $cat_id_placeholders_count = [];
                foreach ($current_homepage_category_ids_for_count as $idx_count => $cat_id_count) {
                    $placeholder_name_count = ':hp_cat_id_count_' . $idx_count . '_filt';
                    $cat_id_placeholders_count[] = $placeholder_name_count;
                    $final_params[$placeholder_name_count] = $cat_id_count;
                }
                $all_homepage_conditions_count_sql[] = "p.category_id IN (" . implode(',', $cat_id_placeholders_count) . ")";
            }

            if (!empty($all_homepage_conditions_count_sql)) {
                $sql .= " AND (" . implode(' OR ', $all_homepage_conditions_count_sql) . ")";
            }
            $featured = null;
            $category_id = null;
        }
    }

    // Lọc theo danh mục (nếu không bị ghi đè)
    if ($category_id !== null && $category_id > 0) {
        $sql .= " AND p.category_id = :category_id";
        $final_params[':category_id'] = $category_id;
    }

    // Lọc theo trạng thái
    if ($status !== null) {
        $sql .= " AND p.status = :status";
        $final_params[':status'] = $status;
    }

    // Hiển thị tất cả sản phẩm (bao gồm cả "liên hệ báo giá")
    // $sql .= " AND p.price_type = 'fixed'"; // Đã loại bỏ để hiển thị cả sản phẩm liên hệ báo giá

    // Lọc theo khoảng giá
    if ($price_min !== null && is_numeric($price_min)) {
        $sql .= " AND p.price >= :price_min";
        $final_params[':price_min'] = (float)$price_min;
    }
    if ($price_max !== null && is_numeric($price_max) && $price_max >= ($price_min ?? 0) ) {
        $sql .= " AND p.price <= :price_max";
        $final_params[':price_max'] = (float)$price_max;
    }

    // Lọc theo loại giá
    if ($price_type !== null && in_array($price_type, ['fixed', 'contact'])) {
        $sql .= " AND p.price_type = :price_type";
        $final_params[':price_type'] = $price_type;
    }

    // Lọc theo khoảng số lượng tồn kho
    if ($quantity_min !== null && is_numeric($quantity_min)) {
        $sql .= " AND p.quantity >= :quantity_min";
        $final_params[':quantity_min'] = (int)$quantity_min;
    }
    if ($quantity_max !== null && is_numeric($quantity_max) && $quantity_max >= ($quantity_min ?? 0) ) {
        $sql .= " AND p.quantity <= :quantity_max";
        $final_params[':quantity_max'] = (int)$quantity_max;
    }

    if ($search !== null && trim($search) !== '') {
        $search_query = trim($search);
        $search_conditions_sql_parts = [];
        $search_specific_params = [];

        if (preg_match('/^(?:ntbv-sp|sp)?(\d+)$/i', $search_query, $sku_matches)) {
            $sku_number_part = str_pad($sku_matches[1], 3, '0', STR_PAD_LEFT);
            $formatted_sku_to_find = 'NTBV-SP' . $sku_number_part;

            $sku_exact_condition = "p.sku = :search_formatted_sku";
            $search_specific_params[':search_formatted_sku'] = $formatted_sku_to_find;

            if (is_numeric($search_query) && $sku_matches[0] === $sku_matches[1]) {
                 $id_condition = "p.id = :search_as_id_for_num_sku";
                 $search_specific_params[':search_as_id_for_num_sku'] = (int)$search_query;
                 $search_conditions_sql_parts[] = "($sku_exact_condition OR $id_condition)";
            } else {
                $search_conditions_sql_parts[] = $sku_exact_condition;
            }
        } else {
            $term_like_param = "%$search_query%";
            $search_specific_params[':search_keyword_term_like'] = $term_like_param;

            $keyword_search_sub_conditions = ["p.name LIKE :search_keyword_term_like", "p.sku LIKE :search_keyword_term_like"];

            $search_query_no_accent = remove_accents($search_query);
            if ($search_query_no_accent !== $search_query) {
                $term_no_accent_like_param = "%$search_query_no_accent%";
                $search_specific_params[':search_keyword_term_no_accent_like'] = $term_no_accent_like_param;
                $keyword_search_sub_conditions[] = "p.name LIKE :search_keyword_term_no_accent_like";
                $keyword_search_sub_conditions[] = "p.sku LIKE :search_keyword_term_no_accent_like";
            }
            $search_conditions_sql_parts[] = "(" . implode(" OR ", $keyword_search_sub_conditions) . ")";
        }

        if (!empty($search_conditions_sql_parts)) {
            $sql .= " AND (" . implode(" OR ", $search_conditions_sql_parts) . ")";
            $final_params = array_merge($final_params, $search_specific_params);
        }
    }

    $stmt = $conn->prepare($sql);

    // Bind các tham số chính
    foreach ($final_params as $key => $value) {
        $stmt->bindValue($key, $value);
    }

    $stmt->execute();
    $result = $stmt->fetch();
    return $result ? (int)$result['total'] : 0;
}

/**
 * Lấy thông tin sản phẩm theo ID
 */
function get_product_by_id($product_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT p.*, c.name as category_name
                                FROM products p
                                LEFT JOIN categories c ON p.category_id = c.id
                                WHERE p.id = :id");
        $stmt->bindParam(':id', $product_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return null;
        }

        return $stmt->fetch();
    } catch (PDOException $e) {
        return null;
    }
}

/**
 * Lấy thông tin sản phẩm theo slug
 */
function get_product_by_slug($slug) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT p.*, c.name as category_name
                                FROM products p
                                LEFT JOIN categories c ON p.category_id = c.id
                                WHERE p.slug = :slug");
        $stmt->bindParam(':slug', $slug);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return null;
        }

        return $stmt->fetch();
    } catch (PDOException $e) {
        return null;
    }
}

/**
 * Thêm sản phẩm mới
 */
function add_product($data) {
    global $conn;

    try {
        // Tạo slug từ tên sản phẩm
        $slug = slugify($data['name']);

        // Kiểm tra slug đã tồn tại chưa
        $stmt = $conn->prepare("SELECT id FROM products WHERE slug = :slug");
        $stmt->bindParam(':slug', $slug);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $slug = $slug . '-' . time();
        }

        // Xử lý SKU
        $sku = '';
        if (isset($data['sku']) && !empty(trim($data['sku']))) {
            // Người dùng nhập SKU thủ công
            $sku = trim($data['sku']);

            // Validate format SKU
            if (!validate_sku_format($sku)) {
                return [
                    'success' => false,
                    'message' => 'Format SKU không hợp lệ. Vui lòng sử dụng format: NTBV-SP001'
                ];
            }

            // Kiểm tra SKU đã tồn tại chưa
            if (check_sku_exists($sku)) {
                return [
                    'success' => false,
                    'message' => 'SKU đã tồn tại. Vui lòng sử dụng SKU khác.'
                ];
            }
        } else {
            // Tự động tạo SKU
            $sku = generate_next_sku();
        }

        // Thêm sản phẩm mới
        $stmt = $conn->prepare("INSERT INTO products (
                                category_id, name, slug, sku, description, content,
                                specifications, usage_guide, price, sale_price, image,
                                gallery, quantity, status, featured, rating,
                                sold, views, flash_sale, show_on_homepage, material, dimensions,
                                color, price_type, size_options, meta_title, meta_description,
                                overview, features, specifications_json, usage_guide_json, warranty_info
                                )
                                VALUES (
                                :category_id, :name, :slug, :sku, :description, :content,
                                :specifications, :usage_guide, :price, :sale_price, :image,
                                :gallery, :quantity, :status, :featured, :rating,
                                :sold, :views, :flash_sale, :show_on_homepage, :material, :dimensions,
                                :color, :price_type, :size_options, :meta_title, :meta_description,
                                :overview, :features, :specifications_json, :usage_guide_json, :warranty_info
                                )");

        // Chuẩn bị các biến tạm thời cho các trường mới
        $rating = isset($data['rating']) ? $data['rating'] : 5;
        $sold = isset($data['sold']) ? $data['sold'] : 0;
        $views = isset($data['views']) ? $data['views'] : 0;
        $flash_sale = isset($data['flash_sale']) ? $data['flash_sale'] : 0;
        $show_on_homepage = isset($data['show_on_homepage']) ? $data['show_on_homepage'] : 0;
        $material = isset($data['material']) ? $data['material'] : null;
        $dimensions = isset($data['dimensions']) ? $data['dimensions'] : null;
        $color = isset($data['color']) ? $data['color'] : null;
        $price_type = isset($data['price_type']) ? $data['price_type'] : 'fixed';
        $size_options = isset($data['size_options']) ? $data['size_options'] : null;
        $specifications = isset($data['specifications']) ? $data['specifications'] : '';
        $usage_guide = isset($data['usage_guide']) ? $data['usage_guide'] : '';

        $stmt->bindParam(':category_id', $data['category_id']);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':slug', $slug);
        $stmt->bindParam(':sku', $sku);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':content', $data['content']);
        $stmt->bindParam(':specifications', $specifications);
        $stmt->bindParam(':usage_guide', $usage_guide);
        $stmt->bindParam(':price', $data['price']);
        $stmt->bindParam(':sale_price', $data['sale_price']);
        $stmt->bindParam(':image', $data['image']);
        $stmt->bindParam(':gallery', $data['gallery']);
        $stmt->bindParam(':quantity', $data['quantity']);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':featured', $data['featured']);
        $stmt->bindParam(':rating', $rating);
        $stmt->bindParam(':sold', $sold);
        $stmt->bindParam(':views', $views);
        $stmt->bindParam(':flash_sale', $flash_sale);
        $stmt->bindParam(':show_on_homepage', $show_on_homepage);
        $stmt->bindParam(':material', $material);
        $stmt->bindParam(':dimensions', $dimensions);
        $stmt->bindParam(':color', $color);
        $stmt->bindParam(':price_type', $price_type);
        $stmt->bindParam(':size_options', $size_options);
        $stmt->bindParam(':meta_title', $data['meta_title']);
        $stmt->bindParam(':meta_description', $data['meta_description']);

        // Bind các tham số mới
        $overview = isset($data['overview']) ? $data['overview'] : null;
        $features = isset($data['features']) ? $data['features'] : null;
        $specifications_json = isset($data['specifications_json']) ? $data['specifications_json'] : null;
        $usage_guide_json = isset($data['usage_guide_json']) ? $data['usage_guide_json'] : null;
        $warranty_info = isset($data['warranty_info']) ? $data['warranty_info'] : null;

        $stmt->bindParam(':overview', $overview);
        $stmt->bindParam(':features', $features);
        $stmt->bindParam(':specifications_json', $specifications_json);
        $stmt->bindParam(':usage_guide_json', $usage_guide_json);
        $stmt->bindParam(':warranty_info', $warranty_info);

        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Thêm sản phẩm thành công',
            'product_id' => $conn->lastInsertId()
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Cập nhật sản phẩm
 */
function update_product($product_id, $data) {
    global $conn;

    try {
        // Kiểm tra sản phẩm tồn tại
        $stmt = $conn->prepare("SELECT id, slug, name FROM products WHERE id = :id");
        $stmt->bindParam(':id', $product_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Sản phẩm không tồn tại'
            ];
        }

        $product = $stmt->fetch();

        // Tạo slug mới nếu tên sản phẩm thay đổi
        $slug = $product['slug'];
        if ($data['name'] !== $product['name']) {
            $slug = slugify($data['name']);

            // Kiểm tra slug đã tồn tại chưa
            $stmt = $conn->prepare("SELECT id FROM products WHERE slug = :slug AND id != :id");
            $stmt->bindParam(':slug', $slug);
            $stmt->bindParam(':id', $product_id);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $slug = $slug . '-' . time();
            }
        }

        // Xử lý SKU
        $sku = '';
        if (isset($data['sku']) && !empty(trim($data['sku']))) {
            // Người dùng nhập SKU thủ công
            $sku = trim($data['sku']);

            // Validate format SKU
            if (!validate_sku_format($sku)) {
                return [
                    'success' => false,
                    'message' => 'Format SKU không hợp lệ. Vui lòng sử dụng format: NTBV-SP001'
                ];
            }

            // Kiểm tra SKU đã tồn tại chưa (loại trừ sản phẩm hiện tại)
            if (check_sku_exists($sku, $product_id)) {
                return [
                    'success' => false,
                    'message' => 'SKU đã tồn tại. Vui lòng sử dụng SKU khác.'
                ];
            }
        } else {
            // Nếu không có SKU, tự động tạo SKU mới
            $sku = generate_next_sku();
        }

        // Cập nhật sản phẩm
        $stmt = $conn->prepare("UPDATE products SET
                                category_id = :category_id,
                                name = :name,
                                slug = :slug,
                                sku = :sku,
                                description = :description,
                                content = :content,
                                specifications = :specifications,
                                usage_guide = :usage_guide,
                                price = :price,
                                sale_price = :sale_price,
                                image = :image,
                                gallery = :gallery,
                                quantity = :quantity,
                                status = :status,
                                featured = :featured,
                                rating = :rating,
                                sold = :sold,
                                views = :views,
                                flash_sale = :flash_sale,
                                show_on_homepage = :show_on_homepage,
                                material = :material,
                                dimensions = :dimensions,
                                color = :color,
                                price_type = :price_type,
                                size_options = :size_options,
                                meta_title = :meta_title,
                                meta_description = :meta_description,
                                overview = :overview,
                                features = :features,
                                specifications_json = :specifications_json,
                                usage_guide_json = :usage_guide_json,
                                warranty_info = :warranty_info
                                WHERE id = :id");

        // Chuẩn bị các biến tạm thời cho các trường mới
        $rating = isset($data['rating']) ? $data['rating'] : 5;
        $sold = isset($data['sold']) ? $data['sold'] : 0;
        $views = isset($data['views']) ? $data['views'] : 0;
        $flash_sale = isset($data['flash_sale']) ? $data['flash_sale'] : 0;
        $show_on_homepage = isset($data['show_on_homepage']) ? $data['show_on_homepage'] : 0;
        $material = isset($data['material']) ? $data['material'] : null;
        $dimensions = isset($data['dimensions']) ? $data['dimensions'] : null;
        $color = isset($data['color']) ? $data['color'] : null;
        $price_type = isset($data['price_type']) ? $data['price_type'] : 'fixed';
        $size_options = isset($data['size_options']) ? $data['size_options'] : null;

        // Chuẩn bị các biến tạm thời cho các trường có thể null
        $specifications = isset($data['specifications']) ? $data['specifications'] : '';
        $usage_guide = isset($data['usage_guide']) ? $data['usage_guide'] : '';

        $stmt->bindParam(':category_id', $data['category_id']);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':slug', $slug);
        $stmt->bindParam(':sku', $sku);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':content', $data['content']);
        $stmt->bindParam(':specifications', $specifications);
        $stmt->bindParam(':usage_guide', $usage_guide);
        $stmt->bindParam(':price', $data['price']);
        $stmt->bindParam(':sale_price', $data['sale_price']);
        $stmt->bindParam(':image', $data['image']);
        $stmt->bindParam(':gallery', $data['gallery']);
        $stmt->bindParam(':quantity', $data['quantity']);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':featured', $data['featured']);
        $stmt->bindParam(':rating', $rating);
        $stmt->bindParam(':sold', $sold);
        $stmt->bindParam(':views', $views);
        $stmt->bindParam(':flash_sale', $flash_sale);
        $stmt->bindParam(':show_on_homepage', $show_on_homepage);
        $stmt->bindParam(':material', $material);
        $stmt->bindParam(':dimensions', $dimensions);
        $stmt->bindParam(':color', $color);
        $stmt->bindParam(':price_type', $price_type);
        $stmt->bindParam(':size_options', $size_options);
        $stmt->bindParam(':meta_title', $data['meta_title']);
        $stmt->bindParam(':meta_description', $data['meta_description']);

        // Bind các tham số mới
        $overview = isset($data['overview']) ? $data['overview'] : null;
        $features = isset($data['features']) ? $data['features'] : null;
        $specifications_json = isset($data['specifications_json']) ? $data['specifications_json'] : null;
        $usage_guide_json = isset($data['usage_guide_json']) ? $data['usage_guide_json'] : null;
        $warranty_info = isset($data['warranty_info']) ? $data['warranty_info'] : null;

        $stmt->bindParam(':overview', $overview);
        $stmt->bindParam(':features', $features);
        $stmt->bindParam(':specifications_json', $specifications_json);
        $stmt->bindParam(':usage_guide_json', $usage_guide_json);
        $stmt->bindParam(':warranty_info', $warranty_info);

        $stmt->bindParam(':id', $product_id);

        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Cập nhật sản phẩm thành công'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Xóa sản phẩm
 */
function delete_product($product_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("DELETE FROM products WHERE id = :id");
        $stmt->bindParam(':id', $product_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Sản phẩm không tồn tại'
            ];
        }

        return [
            'success' => true,
            'message' => 'Xóa sản phẩm thành công'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Lấy danh sách sản phẩm hiển thị trên trang chủ
 *
 * @param int|null $limit Số lượng sản phẩm tối đa
 * @param int $offset Vị trí bắt đầu
 * @param int|null $status Trạng thái sản phẩm (1: Hiển thị, 0: Ẩn, null: Tất cả)
 * @return array Danh sách sản phẩm
 */
function get_homepage_products($limit = null, $offset = 0, $status = 1) {
    global $conn;

    try {
        $sql = "SELECT p.*, c.name as category_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.show_on_homepage = 1";
        $params = [];

        // Lọc theo trạng thái
        if ($status !== null) {
            $sql .= " AND p.status = :status";
            $params[':status'] = $status;
        }

        // Sắp xếp
        $sql .= " ORDER BY p.created_at DESC";

        // Giới hạn số lượng
        if ($limit !== null) {
            $sql .= " LIMIT :offset, :limit";
            $params[':offset'] = $offset;
            $params[':limit'] = $limit;
        }

        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Cập nhật lượt xem sản phẩm
 * Chỉ tăng lượt xem khi người dùng mới xem sản phẩm
 * Đảm bảo số lượt xem luôn tăng khi có người dùng mới, kể cả khi admin đã thay đổi số lượt xem
 *
 * @param int $product_id ID của sản phẩm
 * @return bool Kết quả cập nhật
 */
function update_product_views($product_id) {
    global $conn;

    try {
        // Kiểm tra xem bảng product_views đã tồn tại chưa
        $table_exists = false;
        try {
            $check_table = $conn->query("SHOW TABLES LIKE 'product_views'");
            $table_exists = ($check_table->rowCount() > 0);
        } catch (PDOException $e) {
            // Bảng chưa tồn tại
            $table_exists = false;
        }

        // Nếu bảng chưa tồn tại, tạo bảng
        if (!$table_exists) {
            $sql = "
            CREATE TABLE IF NOT EXISTS product_views (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_id INT NOT NULL,
                visitor_identifier VARCHAR(255) NOT NULL,
                view_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                UNIQUE KEY unique_view (product_id, visitor_identifier)
            )";

            $conn->exec($sql);

            // Tạo chỉ mục riêng biệt
            try {
                $conn->exec("CREATE INDEX idx_product_views_product_id ON product_views(product_id)");
            } catch (PDOException $e) {
                // Bỏ qua lỗi nếu chỉ mục đã tồn tại
            }

            try {
                $conn->exec("CREATE INDEX idx_product_views_visitor ON product_views(visitor_identifier)");
            } catch (PDOException $e) {
                // Bỏ qua lỗi nếu chỉ mục đã tồn tại
            }
        }

        // Kiểm tra xem người dùng hiện tại có phải là admin không
        $is_admin = isset($_SESSION['user']) && $_SESSION['user']['role'] === 'admin';

        // Nếu là admin đang xem sản phẩm, không tăng lượt xem
        if ($is_admin) {
            return true;
        }

        // Lấy định danh của người dùng (IP hoặc session ID)
        $visitor_identifier = get_visitor_identifier();

        // Kiểm tra xem người dùng đã xem sản phẩm này chưa
        $stmt = $conn->prepare("SELECT id FROM product_views
                               WHERE product_id = :product_id
                               AND visitor_identifier = :visitor_identifier");
        $stmt->bindParam(':product_id', $product_id);
        $stmt->bindParam(':visitor_identifier', $visitor_identifier);
        $stmt->execute();

        // Nếu người dùng chưa xem sản phẩm này
        if ($stmt->rowCount() === 0) {
            // Thêm bản ghi vào bảng product_views
            $stmt = $conn->prepare("INSERT INTO product_views (product_id, visitor_identifier)
                                   VALUES (:product_id, :visitor_identifier)");
            $stmt->bindParam(':product_id', $product_id);
            $stmt->bindParam(':visitor_identifier', $visitor_identifier);
            $stmt->execute();

            // Tăng lượt xem trong bảng products
            // Đảm bảo lượt xem luôn tăng, kể cả khi admin đã thay đổi số lượt xem
            $stmt = $conn->prepare("UPDATE products SET views = views + 1 WHERE id = :id");
            $stmt->bindParam(':id', $product_id);
            $stmt->execute();

            // Ghi log để debug
            error_log("Đã thêm lượt xem mới cho sản phẩm ID: $product_id");
        } else {
            // Nếu người dùng đã xem sản phẩm này rồi, cập nhật thời gian xem để đưa lên đầu danh sách
            $stmt = $conn->prepare("UPDATE product_views
                                   SET view_date = CURRENT_TIMESTAMP
                                   WHERE product_id = :product_id
                                   AND visitor_identifier = :visitor_identifier");
            $stmt->bindParam(':product_id', $product_id);
            $stmt->bindParam(':visitor_identifier', $visitor_identifier);
            $stmt->execute();

            // Ghi log để debug
            error_log("Đã cập nhật thời gian xem cho sản phẩm ID: $product_id");
        }

        return true;
    } catch (PDOException $e) {
        // Ghi log lỗi
        error_log("Lỗi cập nhật lượt xem: " . $e->getMessage());
        return false;
    }
}

/**
 * Lấy định danh của người dùng
 * Sử dụng kết hợp IP và session ID để xác định người dùng
 *
 * @return string Định danh của người dùng
 */
function get_visitor_identifier() {
    // Lấy IP của người dùng
    $ip = $_SERVER['REMOTE_ADDR'];

    // Đảm bảo session đã được bắt đầu
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Lấy session ID
    $session_id = session_id();

    // Nếu không có session ID, tạo một cookie để theo dõi người dùng
    if (empty($session_id)) {
        $cookie_name = 'visitor_id';
        if (!isset($_COOKIE[$cookie_name])) {
            $visitor_id = uniqid('visitor_', true);
            setcookie($cookie_name, $visitor_id, time() + (86400 * 30), "/"); // Cookie tồn tại 30 ngày
            $_COOKIE[$cookie_name] = $visitor_id; // Đặt giá trị cookie ngay lập tức
        }
        $session_id = $_COOKIE[$cookie_name];
    }

    // Kết hợp IP và session ID để tạo định danh duy nhất
    $identifier = md5($ip . $session_id);

    return $identifier;
}

/**
 * Cập nhật số lượng đã bán của sản phẩm
 *
 * @param int $product_id ID của sản phẩm
 * @param int $quantity Số lượng đã bán thêm
 * @return bool Kết quả cập nhật
 */
function update_product_sold($product_id, $quantity) {
    global $conn;

    try {
        $stmt = $conn->prepare("UPDATE products SET sold = sold + :quantity WHERE id = :id");
        $stmt->bindParam(':id', $product_id);
        $stmt->bindParam(':quantity', $quantity, PDO::PARAM_INT);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Cập nhật trạng thái cho nhiều sản phẩm
 */
function update_products_status(array $product_ids, $status) {
    global $conn;

    if (empty($product_ids)) {
        return ['success' => false, 'message' => 'Không có sản phẩm nào được chọn.'];
    }

    // Chuyển status về dạng integer (0 hoặc 1)
    $status_value = intval($status);
    if ($status_value !== 0 && $status_value !== 1) {
        return ['success' => false, 'message' => 'Trạng thái không hợp lệ.'];
    }

    try {
        // Tạo chuỗi placeholder cho IN clause
        $placeholders = implode(',', array_fill(0, count($product_ids), '?'));
        $sql = "UPDATE products SET status = ? WHERE id IN ($placeholders)";

        $stmt = $conn->prepare($sql);

        // Bind status value
        $stmt->bindValue(1, $status_value, PDO::PARAM_INT);

        // Bind product IDs
        foreach ($product_ids as $key => $product_id) {
            $stmt->bindValue($key + 2, (int)$product_id, PDO::PARAM_INT);
        }

        $stmt->execute();
        $affected_rows = $stmt->rowCount();

        if ($affected_rows > 0) {
            return ['success' => true, 'message' => "Đã cập nhật trạng thái cho {$affected_rows} sản phẩm."];
        } else {
            return ['success' => false, 'message' => 'Không có sản phẩm nào được cập nhật (có thể ID không tồn tại hoặc trạng thái đã đúng).'
            ];
        }
    } catch (PDOException $e) {
        error_log("Lỗi khi cập nhật trạng thái nhiều sản phẩm: " . $e->getMessage());
        return ['success' => false, 'message' => 'Lỗi cơ sở dữ liệu: ' . $e->getMessage()];
    }
}

/**
 * Nhân bản sản phẩm
 *
 * @param int $product_id ID của sản phẩm cần nhân bản
 * @return array Kết quả nhân bản
 */
function duplicate_product($product_id) {
    global $conn;
    $original_product = get_product_by_id($product_id);

    if (!$original_product) {
        return ['success' => false, 'message' => 'Sản phẩm gốc không tồn tại.'];
    }

    $new_product_data = $original_product;

    // Xử lý tên sản phẩm mới
    $new_product_data['name'] = $original_product['name'] . ' (Bản sao)';
    // Slug sẽ được tạo tự động trong hàm add_product
    unset($new_product_data['id']);
    unset($new_product_data['slug']);
    // Reset một số trường
    $new_product_data['sold'] = 0;
    $new_product_data['views'] = 0;
    $new_product_data['created_at'] = date('Y-m-d H:i:s');
    $new_product_data['updated_at'] = date('Y-m-d H:i:s');
    // Mặc định sản phẩm nhân bản sẽ bị ẩn
    $new_product_data['status'] = 0;

    // Để trống SKU để hàm add_product tự động tạo SKU mới
    unset($new_product_data['sku']);

    $upload_dir = ROOT_PATH . 'uploads/products/';

    // Sao chép ảnh đại diện
    if (!empty($original_product['image'])) {
        $source_image_path = $upload_dir . $original_product['image'];
        $duplicate_image_result = duplicate_file($source_image_path, $upload_dir);
        if ($duplicate_image_result['success']) {
            $new_product_data['image'] = $duplicate_image_result['filename'];
        } else {
            error_log("Lỗi sao chép ảnh đại diện cho sản phẩm nhân bản: " . $duplicate_image_result['message']);
            $new_product_data['image'] = null; // Hoặc giữ ảnh cũ nếu không muốn thay đổi
        }
    }

    // Sao chép gallery
    if (!empty($original_product['gallery'])) {
        $original_gallery_images = explode(',', $original_product['gallery']);
        $new_gallery_images = [];
        foreach ($original_gallery_images as $img_name) {
            $source_gallery_image_path = $upload_dir . trim($img_name);
            $duplicate_gallery_result = duplicate_file($source_gallery_image_path, $upload_dir);
            if ($duplicate_gallery_result['success']) {
                $new_gallery_images[] = $duplicate_gallery_result['filename'];
            }
        }
        $new_product_data['gallery'] = !empty($new_gallery_images) ? implode(',', $new_gallery_images) : null;
    }

    // Loại bỏ các trường không cần thiết hoặc sẽ được tạo tự động
    unset($new_product_data['category_name']); // Sẽ được join lại khi get_product

    // Gọi hàm add_product (giả sử đã tồn tại và xử lý các trường cần thiết)
    $result = add_product($new_product_data);

    return $result;
}

/**
 * Đếm số sản phẩm hết hàng
 *
 * @return int Số sản phẩm hết hàng
 */
function count_products_out_of_stock() {
    global $conn;

    try {
        $sql = "SELECT COUNT(*) as total FROM products WHERE quantity <= 0 AND status = 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        return (int)$result['total'];
    } catch (PDOException $e) {
        return 0;
    }
}

/**
 * Đếm số sản phẩm nổi bật
 *
 * @return int Số sản phẩm nổi bật
 */
function count_featured_products() {
    global $conn;

    try {
        $sql = "SELECT COUNT(*) as total FROM products WHERE featured = 1 AND status = 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        return (int)$result['total'];
    } catch (PDOException $e) {
        return 0;
    }
}

/**
 * Lấy sản phẩm xem gần đây của người dùng
 *
 * @param int $limit Số lượng sản phẩm tối đa
 * @param string $visitor_identifier Định danh người dùng (nếu null sẽ tự động lấy)
 * @return array Danh sách sản phẩm xem gần đây
 */
function get_recently_viewed_products($limit = 5, $visitor_identifier = null) {
    global $conn;

    if ($visitor_identifier === null) {
        $visitor_identifier = get_visitor_identifier();
    }

    try {
        $sql = "SELECT p.*, c.name as category_name, pv.view_date
                FROM product_views pv
                JOIN products p ON pv.product_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE pv.visitor_identifier = :visitor_identifier
                AND p.status = 1
                ORDER BY pv.view_date DESC
                LIMIT :limit";

        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':visitor_identifier', $visitor_identifier);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Lỗi khi lấy sản phẩm xem gần đây: " . $e->getMessage());
        return [];
    }
}

/**
 * Lấy sản phẩm được quan tâm nhiều (trending)
 * Dựa trên tổng số lượt xem, ưu tiên sản phẩm có lượt xem cao nhất
 *
 * @param int $limit Số lượng sản phẩm tối đa
 * @param int $days Số ngày gần đây để tính trending (mặc định 7 ngày, chỉ để tham khảo)
 * @return array Danh sách sản phẩm trending
 */
function get_trending_products($limit = 5, $days = 7) {
    global $conn;

    try {
        // Sắp xếp chính xác theo tổng lượt xem (views) từ cao xuống thấp
        $sql = "SELECT p.*, c.name as category_name,
                       COUNT(pv.id) as recent_views,
                       p.views as total_views
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN product_views pv ON p.id = pv.product_id
                    AND pv.view_date >= DATE_SUB(NOW(), INTERVAL :days DAY)
                WHERE p.status = 1
                GROUP BY p.id
                ORDER BY p.views DESC, recent_views DESC, p.created_at DESC
                LIMIT :limit";

        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':days', $days, PDO::PARAM_INT);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Lỗi khi lấy sản phẩm trending: " . $e->getMessage());
        return [];
    }
}

?>
