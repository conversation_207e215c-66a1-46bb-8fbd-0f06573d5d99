/**
 * Dropdown Position Test CSS
 * Để kiểm tra và điều chỉnh vị trí của các dropdown box
 */

/* Chỉ áp dụng cho desktop */
@media (min-width: 769px) {
    
    /* Reset tất cả positioning về cơ bản */
    .mega-menu,
    .user-dropdown-menu,
    .mini-cart {
        /* Loại bỏ tất cả margin-top và transform translateY */
        margin-top: 0 !important;
        transform: scale(0.98) !important; /* Chỉ giữ scale, loại bỏ translateY */
        
        /* Đặt tất cả ở cùng một vị trí cố định */
        top: calc(100% + 15px) !important;
        
        /* Đảm bảo opacity và visibility đồng nhất */
        opacity: 0 !important;
        visibility: hidden !important;
        
        /* Đảm bảo transition đồng nhất */
        transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1) !important;
    }
    
    /* Hiệu ứng khi hover - Mega Menu */
    .nav-item:hover .mega-menu,
    .mega-menu:hover {
        transform: scale(1) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* Hiệu ứng khi hover - User Dropdown */
    .user-dropdown:hover .user-dropdown-menu,
    .user-dropdown-menu:hover {
        transform: scale(1) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* Hiệu ứng khi hover - Mini Cart */
    .cart-container:hover .mini-cart,
    .mini-cart:hover {
        transform: scale(1) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* Đảm bảo arrow positioning đồng nhất */
    .mega-menu::before,
    .user-dropdown-menu::before,
    .mini-cart::before {
        top: -5px !important;
    }
    
    /* Arrow cho mega-menu (bên trái) */
    .mega-menu::before {
        left: 20px !important;
    }
    
    /* Arrow cho user-dropdown và mini-cart (bên phải) */
    .user-dropdown-menu::before,
    .mini-cart::before {
        right: 20px !important;
    }
}
