{"version": 3, "file": "lang/summernote-gl-ES.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,wBAAwB;QAC/BC,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,OAAO;QACbC,aAAa,EAAE,SAAS;QACxBC,WAAW,EAAE,aAAa;QAC1BC,SAAS,EAAE,WAAW;QACtBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,eAAe;QACvBC,UAAU,EAAE,iCAAiC;QAC7CC,UAAU,EAAE,wBAAwB;QACpCC,aAAa,EAAE,2BAA2B;QAC1CC,SAAS,EAAE,mBAAmB;QAC9BC,UAAU,EAAE,kBAAkB;QAC9BC,SAAS,EAAE,YAAY;QACvBC,YAAY,EAAE,mBAAmB;QACjCC,WAAW,EAAE,gBAAgB;QAC7BC,cAAc,EAAE,cAAc;QAC9BC,SAAS,EAAE,iBAAiB;QAC5BC,aAAa,EAAE,oCAAoC;QACnDC,SAAS,EAAE,wBAAwB;QACnCC,eAAe,EAAE,+BAA+B;QAChDC,eAAe,EAAE,0BAA0B;QAC3CC,oBAAoB,EAAE,wCAAwC;QAC9DC,GAAG,EAAE,cAAc;QACnBC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,kBAAkB;QAC7BpB,MAAM,EAAE,gBAAgB;QACxBgB,GAAG,EAAE,eAAe;QACpBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,SAAS;QACftB,MAAM,EAAE,iBAAiB;QACzBuB,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE,mBAAmB;QAClCT,GAAG,EAAE,gCAAgC;QACrCU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,eAAe;QAC5BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,CAAC,EAAE,QAAQ;QACXC,UAAU,EAAE,MAAM;QAClBC,GAAG,EAAE,QAAQ;QACbC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,mBAAmB;QAC9BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,mBAAmB;QAC/BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,WAAW;QACtBC,OAAO,EAAE,kBAAkB;QAC3BC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,mBAAmB;QACzBC,MAAM,EAAE,kBAAkB;QAC1BC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,YAAY;QACpBC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE,cAAc;QAC1BC,UAAU,EAAE,eAAe;QAC3BC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,yBAAyB;QACzCC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,oBAAoB;QAC/BC,KAAK,EAAE,QAAQ;QACfC,cAAc,EAAE,kBAAkB;QAClCC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,sBAAsB;QAC3CC,aAAa,EAAE,qBAAqB;QACpCC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,mBAAmB;QACtC,MAAM,EAAE,wBAAwB;QAChC,MAAM,EAAE,uBAAuB;QAC/B,KAAK,EAAE,SAAS;QAChB,OAAO,EAAE,qBAAqB;QAC9B,MAAM,EAAE,2BAA2B;QACnC,QAAQ,EAAE,2BAA2B;QACrC,WAAW,EAAE,6BAA6B;QAC1C,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,mBAAmB;QAClC,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,kBAAkB;QAClC,aAAa,EAAE,YAAY;QAC3B,qBAAqB,EAAE,2BAA2B;QAClD,mBAAmB,EAAE,wBAAwB;QAC7C,SAAS,EAAE,iCAAiC;QAC5C,QAAQ,EAAE,kCAAkC;QAC5C,YAAY,EAAE,iDAAiD;QAC/D,UAAU,EAAE,6BAA6B;QACzC,UAAU,EAAE,6BAA6B;QACzC,UAAU,EAAE,6BAA6B;QACzC,UAAU,EAAE,6BAA6B;QACzC,UAAU,EAAE,6BAA6B;QACzC,UAAU,EAAE,6BAA6B;QACzC,sBAAsB,EAAE,yBAAyB;QACjD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,sBAAsB;QACnCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-gl-ES.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'gl-ES': {\n      font: {\n        bold: 'Negrita',\n        italic: 'Curs<PERSON>',\n        underline: 'Subliñado',\n        clear: 'Quitar estilo de fonte',\n        height: 'Altura de liña',\n        name: '<PERSON><PERSON>',\n        strikethrough: 'Risca<PERSON>',\n        superscript: 'Superíndice',\n        subscript: 'Subíndice',\n        size: 'Tamaño da fonte',\n      },\n      image: {\n        image: 'Imaxe',\n        insert: 'Inserir imaxe',\n        resizeFull: 'Redimensionar a tamaño completo',\n        resizeHalf: 'Redimensionar á metade',\n        resizeQuarter: 'Redimensionar a un cuarto',\n        floatLeft: 'Flotar á esquerda',\n        floatRight: 'Flotar á dereita',\n        floatNone: 'Non flotar',\n        shapeRounded: 'Forma: Redondeado',\n        shapeCircle: 'Forma: Círculo',\n        shapeThumbnail: 'Forma: Marco',\n        shapeNone: 'Forma: Ningunha',\n        dragImageHere: 'Arrastrar unha imaxe ou texto aquí',\n        dropImage: 'Solta a imaxe ou texto',\n        selectFromFiles: 'Seleccionar desde os arquivos',\n        maximumFileSize: 'Tamaño máximo do arquivo',\n        maximumFileSizeError: 'Superaches o tamaño máximo do arquivo.',\n        url: 'URL da imaxe',\n        remove: 'Eliminar imaxe',\n        original: 'Original',\n      },\n      video: {\n        video: 'Vídeo',\n        videoLink: 'Ligazón do vídeo',\n        insert: 'Insertar vídeo',\n        url: 'URL do vídeo?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion, o Youku)',\n      },\n      link: {\n        link: 'Ligazón',\n        insert: 'Inserir Ligazón',\n        unlink: 'Quitar Ligazón',\n        edit: 'Editar',\n        textToDisplay: 'Texto para amosar',\n        url: 'Cara a que URL leva a ligazón?',\n        openInNewWindow: 'Abrir nunha nova xanela',\n      },\n      table: {\n        table: 'Táboa',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Inserir liña horizontal',\n      },\n      style: {\n        style: 'Estilo',\n        p: 'Normal',\n        blockquote: 'Cita',\n        pre: 'Código',\n        h1: 'Título 1',\n        h2: 'Título 2',\n        h3: 'Título 3',\n        h4: 'Título 4',\n        h5: 'Título 5',\n        h6: 'Título 6',\n      },\n      lists: {\n        unordered: 'Lista desordenada',\n        ordered: 'Lista ordenada',\n      },\n      options: {\n        help: 'Axuda',\n        fullscreen: 'Pantalla completa',\n        codeview: 'Ver código fonte',\n      },\n      paragraph: {\n        paragraph: 'Parágrafo',\n        outdent: 'Menos tabulación',\n        indent: 'Máis tabulación',\n        left: 'Aliñar á esquerda',\n        center: 'Aliñar ao centro',\n        right: 'Aliñar á dereita',\n        justify: 'Xustificar',\n      },\n      color: {\n        recent: 'Última cor',\n        more: 'Máis cores',\n        background: 'Cor de fondo',\n        foreground: 'Cor de fuente',\n        transparent: 'Transparente',\n        setTransparent: 'Establecer transparente',\n        reset: 'Restaurar',\n        resetToDefault: 'Restaurar por defecto',\n      },\n      shortcut: {\n        shortcuts: 'Atallos de teclado',\n        close: 'Pechar',\n        textFormatting: 'Formato de texto',\n        action: 'Acción',\n        paragraphFormatting: 'Formato de parágrafo',\n        documentStyle: 'Estilo de documento',\n        extraKeys: 'Teclas adicionais',\n      },\n      help: {\n        'insertParagraph': 'Inserir parágrafo',\n        'undo': 'Desfacer última acción',\n        'redo': 'Refacer última acción',\n        'tab': 'Tabular',\n        'untab': 'Eliminar tabulación',\n        'bold': 'Establecer estilo negrita',\n        'italic': 'Establecer estilo cursiva',\n        'underline': 'Establecer estilo subliñado',\n        'strikethrough': 'Establecer estilo riscado',\n        'removeFormat': 'Limpar estilo',\n        'justifyLeft': 'Aliñar á esquerda',\n        'justifyCenter': 'Aliñar ao centro',\n        'justifyRight': 'Aliñar á dereita',\n        'justifyFull': 'Xustificar',\n        'insertUnorderedList': 'Inserir lista desordenada',\n        'insertOrderedList': 'Inserir lista ordenada',\n        'outdent': 'Reducir tabulación do parágrafo',\n        'indent': 'Aumentar tabulación do parágrafo',\n        'formatPara': 'Mudar estilo do bloque a parágrafo (etiqueta P)',\n        'formatH1': 'Mudar estilo do bloque a H1',\n        'formatH2': 'Mudar estilo do bloque a H2',\n        'formatH3': 'Mudar estilo do bloque a H3',\n        'formatH4': 'Mudar estilo do bloque a H4',\n        'formatH5': 'Mudar estilo do bloque a H5',\n        'formatH6': 'Mudar estilo do bloque a H6',\n        'insertHorizontalRule': 'Inserir liña horizontal',\n        'linkDialog.show': 'Amosar panel ligazóns',\n      },\n      history: {\n        undo: 'Desfacer',\n        redo: 'Refacer',\n      },\n      specialChar: {\n        specialChar: 'CARACTERES ESPECIAIS',\n        select: 'Selecciona Caracteres especiais',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "superscript", "subscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}