<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => '<PERSON><PERSON>ơng thức không được hỗ trợ'
    ]);
    exit;
}

// Lấy dữ liệu
$product_id = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;
$quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;

// Kiểm tra dữ liệu
if ($product_id <= 0 || $quantity <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Dữ liệu không hợp lệ'
    ]);
    exit;
}

// Thêm vào giỏ hàng
$result = add_to_cart($product_id, $quantity);

// Thêm thông tin bổ sung cho phản hồi
if ($result['success']) {
    // Cập nhật thời gian cập nhật giỏ hàng
    $_SESSION['cart_updated'] = time() * 1000; // Chuyển đổi sang milliseconds để phù hợp với JavaScript
    $result['timestamp'] = $_SESSION['cart_updated'];

    // Lấy dữ liệu giỏ hàng mới nhất
    $cart_count = get_cart_count(); // Tổng số lượng sản phẩm
    $cart_items_count = get_cart_items_count(); // Số lượng items khác nhau
    $cart_total = get_cart_total();

    $result['count'] = $cart_count; // Tổng số lượng sản phẩm (cho badge)
    $result['items_count'] = $cart_items_count; // Số lượng items khác nhau (cho hiển thị "X sản phẩm")
    $result['total'] = format_currency($cart_total);
    $result['raw_total'] = $cart_total;

    // Lấy danh sách sản phẩm trong giỏ hàng
    $result['items'] = get_cart_items();

    // Log để debug
    error_log("Product added to cart - Product ID: $product_id, Quantity: $quantity, Total Count: $cart_count");
}

// Trả về kết quả
header('Content-Type: application/json');
echo json_encode($result);
?>
