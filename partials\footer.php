        </div>
    </main>

    <?php
    // Include footer helper
    if (!function_exists('get_setting')) {
        include_once __DIR__ . '/../includes/footer-helper.php';
    }

    // L<PERSON>y danh mục sản phẩm cho footer
    $footer_categories = function_exists('get_footer_categories') ? get_footer_categories() : [];

    // Lấy bài viết mới nhất cho footer
    $footer_latest_posts = function_exists('get_footer_latest_posts') ? get_footer_latest_posts() : [];

    // L<PERSON>y liên kết nhanh cho footer
    $footer_quick_links = function_exists('get_footer_quick_links') ? get_footer_quick_links() : [];

    // L<PERSON>y liên kết hỗ trợ khách hàng cho footer
    $footer_support_links = function_exists('get_footer_support_links') ? get_footer_support_links() : [];
    ?>

    <!-- Modern Footer -->
    <footer class="modern-footer">
        <!-- Footer Wave Divider -->
        <div class="footer-wave">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
            </svg>
        </div>

        <!-- Footer Content -->
        <div class="footer-content">
            <div class="footer-grid">
                <!-- Column 1: About -->
                <div class="footer-column col-about">
                    <a href="<?php echo BASE_URL; ?>" class="footer-logo">
                        <img src="<?php echo BASE_URL; ?>/assets/images/logo/logo-chu-trang.svg" alt="<?php echo SITE_NAME; ?>">
                    </a>

                    <p class="footer-about-text">
                        <?php echo get_setting('footer_col1_content', SITE_NAME . ' cung cấp các sản phẩm nội thất cao cấp, chất lượng với giá thành hợp lý. Chúng tôi tự hào mang đến những sản phẩm đẹp, bền và phù hợp với mọi không gian sống.'); ?>
                    </p>

                    <?php echo function_exists('get_contact_info_html') ? get_contact_info_html() : ''; ?>
                </div>

                <!-- Column 2: Quick Links -->
                <div class="footer-column col-links">
                    <h3 class="footer-column-title">
                        <?php echo get_setting('footer_col2_title', 'Liên kết nhanh'); ?>
                    </h3>

                    <ul class="footer-links">
                        <?php if (!empty($footer_quick_links)): ?>
                            <?php foreach ($footer_quick_links as $link): ?>
                                <li>
                                    <a href="<?php echo BASE_URL . $link['url']; ?>">
                                        <i class="fas fa-chevron-right"></i>
                                        <?php echo $link['text']; ?>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <li>
                                <a href="<?php echo BASE_URL; ?>">
                                    <i class="fas fa-chevron-right"></i>
                                    Trang chủ
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/products.php">
                                    <i class="fas fa-chevron-right"></i>
                                    Sản phẩm
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/about.php">
                                    <i class="fas fa-chevron-right"></i>
                                    Giới thiệu
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/blog.php">
                                    <i class="fas fa-chevron-right"></i>
                                    Blog
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/contact.php">
                                    <i class="fas fa-chevron-right"></i>
                                    Liên hệ
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>

                <!-- Column 3: Hỗ trợ khách hàng -->
                <div class="footer-column col-categories">
                    <h3 class="footer-column-title">
                        <?php echo get_setting('footer_col3_title', 'Hỗ trợ khách hàng'); ?>
                    </h3>

                    <ul class="footer-links">
                        <?php if (!empty($footer_support_links)): ?>
                            <?php foreach ($footer_support_links as $link): ?>
                                <li>
                                    <a href="<?php echo BASE_URL . $link['url']; ?>">
                                        <i class="fas fa-chevron-right"></i>
                                        <?php echo $link['text']; ?>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/warranty.php">
                                    <i class="fas fa-chevron-right"></i>
                                    Đổi trả bảo hành
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/payment.php">
                                    <i class="fas fa-chevron-right"></i>
                                    Hình thức thanh toán
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/shipping.php">
                                    <i class="fas fa-chevron-right"></i>
                                    Vận chuyển giao hàng
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/privacy-policy.php">
                                    <i class="fas fa-chevron-right"></i>
                                    Chính sách bảo mật
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/terms-of-service.php">
                                    <i class="fas fa-chevron-right"></i>
                                    Điều khoản sử dụng
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>

                <!-- Column 4: Kết nối với chúng tôi -->
                <div class="footer-column col-connect">
                    <h3 class="footer-column-title">
                        <?php echo get_setting('footer_col4_title', 'Kết nối với chúng tôi'); ?>
                    </h3>

                    <!-- Facebook Page Plugin -->
                    <div class="footer-facebook-container">
                        <?php echo function_exists('get_facebook_page_plugin') ? get_facebook_page_plugin() : ''; ?>
                    </div>

                    <!-- Mạng xã hội -->
                    <?php echo function_exists('get_social_links_html') ? get_social_links_html() : ''; ?>

                    <!-- Chứng nhận Bộ Công Thương -->
                    <?php echo function_exists('get_government_certification_html') ? get_government_certification_html() : ''; ?>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="footer-bottom">
            <div class="footer-bottom-content">
                <div class="footer-copyright">
                    <?php echo get_setting('footer_copyright_text', '© ' . date('Y') . ' ' . SITE_NAME . '. Tất cả quyền được bảo lưu.'); ?>
                </div>
            </div>
        </div>
    </footer>

    <!-- Include Contact Buttons -->
    <?php include_once __DIR__ . '/contact-buttons.php'; ?>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Facebook SDK -->
    <div id="fb-root"></div>
    <script async defer crossorigin="anonymous" src="https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v18.0" nonce="random123"></script>

    <!-- Script để đảm bảo Facebook Plugin tự điều chỉnh kích thước -->
    <script>
        window.fbAsyncInit = function() {
            FB.init({
                xfbml: true,
                version: 'v18.0'
            });

            // Đảm bảo Facebook Plugin được render lại khi cửa sổ thay đổi kích thước
            FB.Event.subscribe('xfbml.render', function() {
                adjustFacebookPlugin();
            });

            // Điều chỉnh kích thước Facebook Plugin
            function adjustFacebookPlugin() {
                var container = document.querySelector('.footer-facebook-container');
                if (container) {
                    var width = container.offsetWidth;
                    var fbPage = container.querySelector('.fb-page');
                    if (fbPage) {
                        fbPage.setAttribute('data-width', width);
                        // Đảm bảo chiều cao đủ để hiển thị đầy đủ
                        fbPage.setAttribute('data-height', '130');
                    }
                }
            }

            // Gọi hàm điều chỉnh khi cửa sổ thay đổi kích thước
            window.addEventListener('resize', function() {
                if (FB && FB.XFBML) {
                    FB.XFBML.parse();
                }
            });
        };
    </script>

    <!-- Custom JS -->
    <script src="<?php echo BASE_URL; ?>/assets/js/main.js"></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/modern-footer.js"></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/custom-scrollbar.js"></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/contact-buttons.js"></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/center-notifications.js"></script>

    <!-- Hiển thị thông báo -->
    <?php echo process_and_display_notifications(); ?>

    <!-- Script để đảm bảo không có lớp phủ nào xuất hiện sau khi trang tải xong -->
    <script>
        // Hàm để khắc phục vấn đề lớp phủ vô hình
        function fixInvisibleOverlay() {
            // Đảm bảo body không bị chặn tương tác
            document.body.style.pointerEvents = 'auto';
            document.body.style.overflow = 'auto';

            // Xóa tất cả các lớp phủ có thể tồn tại (NHƯNG KHÔNG XÓA PHOTO-OVERLAY)
            var overlays = document.querySelectorAll('.notification-container, .notification-overlay, [class*="overlay"], [style*="position: fixed"]');
            overlays.forEach(function(overlay) {
                // Kiểm tra xem đây có phải là thông báo đơn giản không
                if (overlay.id !== 'simple-notification') {
                    // Kiểm tra xem đây có phải là phần tử cần thiết không
                    var isEssential = false;

                    // Danh sách các id và class cần thiết
                    var essentialIds = ['mobile-menu', 'cart-sidebar', 'search-overlay'];
                    var essentialClasses = ['mobile-menu', 'cart-sidebar', 'search-overlay', 'photo-overlay', 'overlay-text', 'filter-modal-overlay', 'filter-modal'];

                    // QUAN TRỌNG: Không xóa photo-overlay, overlay-text và filter modal
                    if (overlay.classList.contains('photo-overlay') ||
                        overlay.classList.contains('overlay-text') ||
                        overlay.classList.contains('filter-modal-overlay') ||
                        overlay.classList.contains('filter-modal') ||
                        overlay.closest('.photo-overlay') ||
                        overlay.closest('.testimonial-photo-box') ||
                        overlay.closest('.testimonial-photos-grid')) {
                        isEssential = true;
                    }

                    // Kiểm tra id
                    if (overlay.id && essentialIds.includes(overlay.id)) {
                        isEssential = true;
                    }

                    // Kiểm tra class
                    if (!isEssential) {
                        for (var i = 0; i < essentialClasses.length; i++) {
                            if (overlay.classList.contains(essentialClasses[i])) {
                                isEssential = true;
                                break;
                            }
                        }
                    }

                    // Nếu không phải là phần tử cần thiết, xóa nó
                    if (!isEssential) {
                        if (overlay && overlay.parentNode) {
                            overlay.parentNode.removeChild(overlay);
                            console.log('Đã xóa lớp phủ:', overlay);
                        }
                    }
                }
            });

            // Đặt lại z-index cho tất cả các phần tử
            var allElements = document.querySelectorAll('*');
            allElements.forEach(function(element) {
                // Kiểm tra xem đây có phải là phần tử cần thiết không
                var isEssential = false;

                // Danh sách các id và class cần thiết
                var essentialIds = ['simple-notification', 'mobile-menu', 'cart-sidebar', 'search-overlay'];
                var essentialClasses = ['mobile-menu', 'cart-sidebar', 'search-overlay'];

                // Kiểm tra id
                if (element.id && essentialIds.includes(element.id)) {
                    isEssential = true;
                }

                // Kiểm tra class
                if (!isEssential) {
                    for (var i = 0; i < essentialClasses.length; i++) {
                        if (element.classList.contains(essentialClasses[i])) {
                            isEssential = true;
                            break;
                        }
                    }
                }

                // Nếu không phải là phần tử cần thiết, đặt lại z-index
                if (!isEssential) {
                    var zIndex = window.getComputedStyle(element).zIndex;
                    if (zIndex !== 'auto' && parseInt(zIndex) > 9000) {
                        element.style.zIndex = 'auto';
                    }
                }
            });
        }

        // Chạy ngay khi trang tải xong
        document.addEventListener('DOMContentLoaded', function() {
            fixInvisibleOverlay();

            // Chạy lại sau 1 giây
            setTimeout(fixInvisibleOverlay, 1000);

            // Chạy lại sau 2 giây
            setTimeout(fixInvisibleOverlay, 2000);

            // Chạy lại sau 3 giây
            setTimeout(fixInvisibleOverlay, 3000);

            // Chạy lại sau 5 giây
            setTimeout(fixInvisibleOverlay, 5000);
        });

        // Chạy khi người dùng tương tác với trang
        document.addEventListener('click', function() {
            setTimeout(fixInvisibleOverlay, 100);
        });

        // Chạy khi người dùng cuộn trang
        window.addEventListener('scroll', function() {
            setTimeout(fixInvisibleOverlay, 100);
        });

        // Chạy khi người dùng nhấn phím
        document.addEventListener('keydown', function() {
            setTimeout(fixInvisibleOverlay, 100);
        });

        // Chạy khi người dùng di chuyển chuột
        document.addEventListener('mousemove', function() {
            setTimeout(fixInvisibleOverlay, 500);
        });
    </script>

    <!-- CSS đã được di chuyển lên header.php để tránh FOUC -->
</body>
</html>
