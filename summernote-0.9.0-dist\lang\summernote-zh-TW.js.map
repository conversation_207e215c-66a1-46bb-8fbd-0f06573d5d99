{"version": 3, "file": "lang/summernote-zh-TW.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE,IAAI;QACVC,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,IAAI;QACjBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,SAAS;QACrBC,UAAU,EAAE,SAAS;QACrBC,aAAa,EAAE,SAAS;QACxBC,SAAS,EAAE,MAAM;QACjBC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,MAAM;QACjBC,YAAY,EAAE,QAAQ;QACtBC,WAAW,EAAE,OAAO;QACpBC,cAAc,EAAE,SAAS;QACzBC,SAAS,EAAE,OAAO;QAClBC,aAAa,EAAE,UAAU;QACzBC,SAAS,EAAE,oBAAoB;QAC/BC,eAAe,EAAE,OAAO;QACxBC,eAAe,EAAE,SAAS;QAC1BC,oBAAoB,EAAE,YAAY;QAClCC,GAAG,EAAE,MAAM;QACXC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,MAAM;QACjBpB,MAAM,EAAE,MAAM;QACdgB,GAAG,EAAE,MAAM;QACXK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,IAAI;QACVtB,MAAM,EAAE,MAAM;QACduB,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,MAAM;QACZC,aAAa,EAAE,MAAM;QACrBT,GAAG,EAAE,MAAM;QACXU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,IAAI;QACXC,WAAW,EAAE,OAAO;QACpBC,WAAW,EAAE,OAAO;QACpBC,UAAU,EAAE,OAAO;QACnBC,WAAW,EAAE,OAAO;QACpBC,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,IAAI;QACXC,CAAC,EAAE,IAAI;QACPC,UAAU,EAAE,MAAM;QAClBC,GAAG,EAAE,OAAO;QACZC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,IAAI;QACVC,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,IAAI;QACVC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,IAAI;QAChBC,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE,IAAI;QACpBC,KAAK,EAAE,IAAI;QACXC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE,IAAI;QACXC,cAAc,EAAE,MAAM;QACtBC,MAAM,EAAE,IAAI;QACZC,mBAAmB,EAAE,MAAM;QAC3BC,aAAa,EAAE,MAAM;QACrBC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,kBAAkB;QACrC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,yBAAyB;QACjC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,kBAAkB;QAC1B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-zh-TW.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'zh-TW': {\n      font: {\n        bold: '粗體',\n        italic: '斜體',\n        underline: '底線',\n        clear: '清除格式',\n        height: '行高',\n        name: '字體',\n        strikethrough: '刪除線',\n        subscript: '下標',\n        superscript: '上標',\n        size: '字號',\n      },\n      image: {\n        image: '圖片',\n        insert: '插入圖片',\n        resizeFull: '縮放至100%',\n        resizeHalf: '縮放至 50%',\n        resizeQuarter: '縮放至 25%',\n        floatLeft: '靠左浮動',\n        floatRight: '靠右浮動',\n        floatNone: '取消浮動',\n        shapeRounded: '形狀: 圓角',\n        shapeCircle: '形狀: 圓',\n        shapeThumbnail: '形狀: 縮略圖',\n        shapeNone: '形狀: 無',\n        dragImageHere: '將圖片拖曳至此處',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: '從本機上傳',\n        maximumFileSize: '文件大小最大值',\n        maximumFileSizeError: '文件大小超出最大值。',\n        url: '圖片網址',\n        remove: '移除圖片',\n        original: 'Original',\n      },\n      video: {\n        video: '影片',\n        videoLink: '影片連結',\n        insert: '插入影片',\n        url: '影片網址',\n        providers: '(優酷, Instagram, DailyMotion, Youtube等)',\n      },\n      link: {\n        link: '連結',\n        insert: '插入連結',\n        unlink: '取消連結',\n        edit: '編輯連結',\n        textToDisplay: '顯示文字',\n        url: '連結網址',\n        openInNewWindow: '在新視窗開啟',\n      },\n      table: {\n        table: '表格',\n        addRowAbove: '上方插入列',\n        addRowBelow: '下方插入列',\n        addColLeft: '左方插入欄',\n        addColRight: '右方插入欄',\n        delRow: '刪除列',\n        delCol: '刪除欄',\n        delTable: '刪除表格',\n      },\n      hr: {\n        insert: '水平線',\n      },\n      style: {\n        style: '樣式',\n        p: '一般',\n        blockquote: '引用區塊',\n        pre: '程式碼區塊',\n        h1: '標題 1',\n        h2: '標題 2',\n        h3: '標題 3',\n        h4: '標題 4',\n        h5: '標題 5',\n        h6: '標題 6',\n      },\n      lists: {\n        unordered: '項目清單',\n        ordered: '編號清單',\n      },\n      options: {\n        help: '幫助',\n        fullscreen: '全螢幕',\n        codeview: '原始碼',\n      },\n      paragraph: {\n        paragraph: '段落',\n        outdent: '取消縮排',\n        indent: '增加縮排',\n        left: '靠左對齊',\n        center: '靠中對齊',\n        right: '靠右對齊',\n        justify: '左右對齊',\n      },\n      color: {\n        recent: '字型顏色',\n        more: '更多',\n        background: '背景',\n        foreground: '字體',\n        transparent: '透明',\n        setTransparent: '透明',\n        reset: '重設',\n        resetToDefault: '預設',\n      },\n      shortcut: {\n        shortcuts: '快捷鍵',\n        close: '關閉',\n        textFormatting: '文字格式',\n        action: '動作',\n        paragraphFormatting: '段落格式',\n        documentStyle: '文件格式',\n        extraKeys: '額外按鍵',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: '復原',\n        redo: '取消復原',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}