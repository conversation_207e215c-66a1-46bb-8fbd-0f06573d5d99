<?php
/**
 * File xử lý các thao tác với danh mục
 */

/**
 * L<PERSON>y danh sách danh mục
 *
 * @param int|null $parent_id ID của danh mục cha (null để lấy tất cả)
 * @param int|null $status Trạng thái danh mục (1: Hiển thị, 0: Ẩn, null: Tất cả)
 * @return array Danh sách danh mục
 */
function get_categories($parent_id = null, $status = null) {
    global $conn;

    try {
        $sql = "SELECT * FROM categories WHERE 1=1";
        $params = [];

        // Lọc theo danh mục cha
        if ($parent_id !== null) {
            $sql .= " AND parent_id = :parent_id";
            $params[':parent_id'] = $parent_id;
        }

        // Lọc theo trạng thái
        if ($status !== null) {
            $sql .= " AND status = :status";
            $params[':status'] = $status;
        }

        // Sắp xếp
        $sql .= " ORDER BY name ASC";

        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Lấy thông tin danh mục theo ID
 */
function get_category_by_id($category_id) {
    global $db;

    try {
        if (!$db) {
            return null;
        }

        $stmt = $db->prepare("SELECT * FROM categories WHERE id = :id");
        $stmt->bindParam(':id', $category_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return null;
        }

        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Error in get_category_by_id: " . $e->getMessage());
        return null;
    }
}

/**
 * Lấy thông tin danh mục theo slug
 */
function get_category_by_slug($slug) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT * FROM categories WHERE slug = :slug");
        $stmt->bindParam(':slug', $slug);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return null;
        }

        return $stmt->fetch();
    } catch (PDOException $e) {
        return null;
    }
}

/**
 * Thêm danh mục mới
 */
function add_category($data) {
    global $conn;

    try {
        // Tạo slug từ tên danh mục
        $slug = slugify($data['name']);

        // Kiểm tra slug đã tồn tại chưa
        $stmt = $conn->prepare("SELECT id FROM categories WHERE slug = :slug");
        $stmt->bindParam(':slug', $slug);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $slug = $slug . '-' . time();
        }

        // Thêm danh mục mới
        $stmt = $conn->prepare("INSERT INTO categories (name, slug, description, image, parent_id, status, show_on_homepage, show_in_popular_search, popular_search_order)
                                VALUES (:name, :slug, :description, :image, :parent_id, :status, :show_on_homepage, :show_in_popular_search, :popular_search_order)");

        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':slug', $slug);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':image', $data['image']);
        $stmt->bindParam(':parent_id', $data['parent_id']);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':show_on_homepage', $data['show_on_homepage']);
        $show_in_popular_search = $data['show_in_popular_search'] ?? 0;
        $popular_search_order = $data['popular_search_order'] ?? 0;
        $stmt->bindParam(':show_in_popular_search', $show_in_popular_search);
        $stmt->bindParam(':popular_search_order', $popular_search_order);

        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Thêm danh mục thành công',
            'category_id' => $conn->lastInsertId()
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Cập nhật danh mục
 */
function update_category($category_id, $data) {
    global $conn;

    try {
        // Kiểm tra danh mục tồn tại
        $stmt = $conn->prepare("SELECT * FROM categories WHERE id = :id");
        $stmt->bindParam(':id', $category_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Danh mục không tồn tại'
            ];
        }

        $category = $stmt->fetch();

        // Tạo slug mới nếu tên danh mục thay đổi
        $slug = $category['slug'];
        if ($data['name'] !== $category['name']) {
            $slug = slugify($data['name']);

            // Kiểm tra slug đã tồn tại chưa
            $stmt = $conn->prepare("SELECT id FROM categories WHERE slug = :slug AND id != :id");
            $stmt->bindParam(':slug', $slug);
            $stmt->bindParam(':id', $category_id);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $slug = $slug . '-' . time();
            }
        }

        // Giữ nguyên hình ảnh cũ nếu không có hình ảnh mới
        $image = empty($data['image']) ? $category['image'] : $data['image'];

        // Cập nhật danh mục
        $stmt = $conn->prepare("UPDATE categories SET
                                name = :name,
                                slug = :slug,
                                description = :description,
                                image = :image,
                                parent_id = :parent_id,
                                status = :status,
                                show_on_homepage = :show_on_homepage,
                                show_in_popular_search = :show_in_popular_search,
                                popular_search_order = :popular_search_order
                                WHERE id = :id");

        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':slug', $slug);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':image', $image);
        $stmt->bindParam(':parent_id', $data['parent_id']);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':show_on_homepage', $data['show_on_homepage']);
        $show_in_popular_search = $data['show_in_popular_search'] ?? 0;
        $popular_search_order = $data['popular_search_order'] ?? 0;
        $stmt->bindParam(':show_in_popular_search', $show_in_popular_search);
        $stmt->bindParam(':popular_search_order', $popular_search_order);
        $stmt->bindParam(':id', $category_id);

        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Cập nhật danh mục thành công'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Xóa danh mục
 *
 * @param int $category_id ID của danh mục cần xóa
 * @param bool $force_delete Xóa cả danh mục con và sản phẩm thuộc danh mục
 * @return array Kết quả xóa danh mục
 */
function delete_category($category_id, $force_delete = false) {
    global $conn;

    try {
        // Kiểm tra danh mục tồn tại
        $stmt = $conn->prepare("SELECT * FROM categories WHERE id = :id");
        $stmt->bindParam(':id', $category_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Danh mục không tồn tại'
            ];
        }

        $category = $stmt->fetch();

        // Nếu không phải xóa cưỡng chế, kiểm tra các ràng buộc
        if (!$force_delete) {
            // Kiểm tra xem có sản phẩm nào thuộc danh mục này không
            $stmt = $conn->prepare("SELECT COUNT(*) as total FROM products WHERE category_id = :category_id");
            $stmt->bindParam(':category_id', $category_id);
            $stmt->execute();
            $result = $stmt->fetch();

            if ($result['total'] > 0) {
                return [
                    'success' => false,
                    'message' => 'Không thể xóa danh mục này vì có ' . $result['total'] . ' sản phẩm thuộc danh mục',
                    'has_products' => true,
                    'product_count' => $result['total']
                ];
            }

            // Kiểm tra xem có danh mục con nào không
            $stmt = $conn->prepare("SELECT COUNT(*) as total FROM categories WHERE parent_id = :category_id");
            $stmt->bindParam(':category_id', $category_id);
            $stmt->execute();
            $result = $stmt->fetch();

            if ($result['total'] > 0) {
                return [
                    'success' => false,
                    'message' => 'Không thể xóa danh mục này vì có ' . $result['total'] . ' danh mục con',
                    'has_subcategories' => true,
                    'subcategory_count' => $result['total']
                ];
            }
        } else {
            // Xóa cưỡng chế - xóa tất cả danh mục con và sản phẩm

            // Lấy tất cả danh mục con (trực tiếp và gián tiếp)
            $subcategories = get_all_subcategories($category_id);
            $all_category_ids = array_merge([$category_id], array_column($subcategories, 'id'));

            // Bắt đầu transaction
            $conn->beginTransaction();

            // Xóa tất cả sản phẩm thuộc các danh mục này
            if (!empty($all_category_ids)) {
                $placeholders = implode(',', array_fill(0, count($all_category_ids), '?'));
                $stmt = $conn->prepare("DELETE FROM products WHERE category_id IN ($placeholders)");

                foreach ($all_category_ids as $index => $id) {
                    $stmt->bindValue($index + 1, $id);
                }

                $stmt->execute();
                $deleted_products = $stmt->rowCount();
            } else {
                $deleted_products = 0;
            }

            // Xóa tất cả danh mục con (từ dưới lên trên để tránh lỗi khóa ngoại)
            $deleted_subcategories = 0;
            if (!empty($subcategories)) {
                // Sắp xếp danh mục con theo cấp độ (từ sâu nhất lên)
                usort($subcategories, function($a, $b) {
                    return $b['level'] - $a['level'];
                });

                foreach ($subcategories as $subcat) {
                    $stmt = $conn->prepare("DELETE FROM categories WHERE id = :id");
                    $stmt->bindParam(':id', $subcat['id']);
                    $stmt->execute();
                    $deleted_subcategories += $stmt->rowCount();
                }
            }

            // Xóa danh mục chính
            $stmt = $conn->prepare("DELETE FROM categories WHERE id = :id");
            $stmt->bindParam(':id', $category_id);
            $stmt->execute();

            // Commit transaction
            $conn->commit();

            return [
                'success' => true,
                'message' => 'Đã xóa danh mục thành công' .
                            ($deleted_subcategories > 0 ? ', bao gồm ' . $deleted_subcategories . ' danh mục con' : '') .
                            ($deleted_products > 0 ? ' và ' . $deleted_products . ' sản phẩm' : ''),
                'deleted_subcategories' => $deleted_subcategories,
                'deleted_products' => $deleted_products
            ];
        }

        // Xóa danh mục
        $stmt = $conn->prepare("DELETE FROM categories WHERE id = :id");
        $stmt->bindParam(':id', $category_id);
        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Xóa danh mục thành công'
        ];
    } catch (PDOException $e) {
        // Rollback nếu đang trong transaction
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }

        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Lấy tất cả danh mục con (trực tiếp và gián tiếp) của một danh mục
 *
 * @param int $category_id ID của danh mục cha
 * @param int $level Cấp độ của danh mục con (dùng cho đệ quy)
 * @return array Mảng chứa tất cả danh mục con
 */
function get_all_subcategories($category_id, $level = 1) {
    global $conn;

    $subcategories = [];

    try {
        $stmt = $conn->prepare("SELECT * FROM categories WHERE parent_id = :parent_id");
        $stmt->bindParam(':parent_id', $category_id);
        $stmt->execute();

        $direct_subcategories = $stmt->fetchAll();

        foreach ($direct_subcategories as $subcat) {
            $subcat['level'] = $level;
            $subcategories[] = $subcat;

            // Đệ quy để lấy các danh mục con của danh mục con này
            $child_subcategories = get_all_subcategories($subcat['id'], $level + 1);
            $subcategories = array_merge($subcategories, $child_subcategories);
        }

        return $subcategories;
    } catch (PDOException $e) {
        return [];
    }
}
/**
 * Lấy danh sách danh mục theo cấu trúc cây
 * Hàm này sẽ trả về một mảng danh mục được sắp xếp theo cấu trúc cây
 * với các danh mục con nằm trong thuộc tính 'children' của danh mục cha
 *
 * @param int|null $status Trạng thái danh mục (1: Hiển thị, 0: Ẩn, null: Tất cả)
 * @return array Danh sách danh mục theo cấu trúc cây
 */
function get_category_tree($status = null) {
    // Lấy tất cả danh mục
    $all_categories = get_categories(null, $status);

    // Tạo mảng danh mục cha (parent_id = NULL)
    $category_tree = [];

    // Tạo mảng danh mục con theo parent_id
    $children_categories = [];

    // Phân loại danh mục
    foreach ($all_categories as $category) {
        if (empty($category['parent_id'])) {
            // Đây là danh mục cha
            $category_tree[$category['id']] = $category;
            $category_tree[$category['id']]['children'] = [];
        } else {
            // Đây là danh mục con
            if (!isset($children_categories[$category['parent_id']])) {
                $children_categories[$category['parent_id']] = [];
            }
            $children_categories[$category['parent_id']][] = $category;
        }
    }

    // Đệ quy để xây dựng cây danh mục
    _build_category_tree($category_tree, $children_categories);

    return array_values($category_tree); // Chuyển từ mảng kết hợp sang mảng tuần tự
}

/**
 * Hàm đệ quy để xây dựng cây danh mục
 * @param array &$categories Mảng danh mục cần xây dựng cây
 * @param array $children_categories Mảng danh mục con theo parent_id
 */
function _build_category_tree(&$categories, $children_categories) {
    foreach ($categories as $cat_id => &$category) {
        if (isset($children_categories[$cat_id])) {
            foreach ($children_categories[$cat_id] as $child) {
                $child['children'] = [];
                $category['children'][$child['id']] = $child;
            }

            if (!empty($category['children'])) {
                _build_category_tree($category['children'], $children_categories);
                $category['children'] = array_values($category['children']); // Chuyển từ mảng kết hợp sang mảng tuần tự
            }
        }
    }
}

/**
 * Lấy danh sách danh mục hiển thị ở trang chủ
 *
 * @param int|null $status Trạng thái danh mục (1: Hiển thị, 0: Ẩn, null: Tất cả)
 * @return array Danh sách danh mục hiển thị ở trang chủ
 */
function get_homepage_categories($status = 1) {
    global $conn;

    try {
        $sql = "SELECT * FROM categories WHERE show_on_homepage = 1";
        $params = [];

        // Lọc theo trạng thái
        if ($status !== null) {
            $sql .= " AND status = :status";
            $params[':status'] = $status;
        }

        // Sắp xếp
        $sql .= " ORDER BY name ASC";

        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}
?>