<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra quyền admin
require_once 'partials/check_admin.php';

// Kiểm tra ID
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('error', 'ID tác giả không hợp lệ.');
    redirect(BASE_URL . '/admin/blog-authors.php');
}

$author_id = (int)$_GET['id'];

try {
    // Kiểm tra tác giả tồn tại
    $stmt = $conn->prepare("SELECT * FROM blog_authors WHERE id = :id");
    $stmt->bindParam(':id', $author_id, PDO::PARAM_INT);
    $stmt->execute();

    if ($stmt->rowCount() === 0) {
        set_flash_message('error', 'Không tìm thấy tác giả.');
        redirect(BASE_URL . '/admin/blog-authors.php');
    }

    // Lấy thông tin tác giả để xóa avatar nếu có
    $author = $stmt->fetch(PDO::FETCH_ASSOC);

    // Xóa tác giả
    $stmt = $conn->prepare("DELETE FROM blog_authors WHERE id = :id");
    $stmt->bindParam(':id', $author_id, PDO::PARAM_INT);
    $stmt->execute();

    // Xóa avatar nếu có
    if (!empty($author['avatar'])) {
        $avatar_path = UPLOADS_PATH . 'blog/authors/' . $author['avatar'];
        if (file_exists($avatar_path)) {
            unlink($avatar_path);
        }
    }

    set_flash_message('success', 'Xóa tác giả thành công.');
} catch (PDOException $e) {
    set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
}

redirect(BASE_URL . '/admin/blog-authors.php');
