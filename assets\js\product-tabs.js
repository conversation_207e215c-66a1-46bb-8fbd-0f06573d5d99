/**
 * Product Tabs JS
 * Xử lý tabs trong trang chi tiết sản phẩm
 */

document.addEventListener('DOMContentLoaded', function() {
    // Khởi tạo tabs
    initProductTabs();
});

/**
 * Khởi tạo tabs cho trang chi tiết sản phẩm
 */
function initProductTabs() {
    const tabs = document.querySelectorAll('#product-tabs button');
    const tabContents = document.querySelectorAll('#product-tab-content > div');
    
    if (tabs.length === 0 || tabContents.length === 0) return;
    
    // Xử lý sự kiện click cho các tab
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Lấy target từ thuộc tính data-tabs-target
            const target = this.getAttribute('data-tabs-target');
            const tabContent = document.querySelector(target);
            
            if (!tabContent) return;
            
            // Xóa class active khỏi tất cả các tab và tab content
            tabs.forEach(t => {
                t.classList.remove('border-blue-500', 'text-blue-600');
                t.classList.add('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');
                t.setAttribute('aria-selected', 'false');
            });
            
            tabContents.forEach(content => {
                content.classList.add('hidden');
                content.classList.remove('block');
            });
            
            // Thêm class active cho tab được chọn
            this.classList.remove('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');
            this.classList.add('border-blue-500', 'text-blue-600');
            this.setAttribute('aria-selected', 'true');
            
            // Hiển thị tab content tương ứng
            tabContent.classList.remove('hidden');
            tabContent.classList.add('block');
            
            // Lưu tab đang active vào localStorage
            localStorage.setItem('activeProductTab', target);
        });
    });
    
    // Khôi phục tab đã chọn từ localStorage (nếu có)
    const activeTab = localStorage.getItem('activeProductTab');
    if (activeTab) {
        const tabToActivate = document.querySelector(`button[data-tabs-target="${activeTab}"]`);
        if (tabToActivate) {
            tabToActivate.click();
        }
    }
}
