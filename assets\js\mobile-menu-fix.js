/**
 * Mobile Menu Fix JavaScript for Nội Thất Bàng Vũ
 * <PERSON><PERSON> lý các tính năng tương tác của menu mobile
 */

document.addEventListener('DOMContentLoaded', function () {
  console.log('Mobile Menu Fix loaded');

  // <PERSON><PERSON><PERSON> phần tử DOM
  const mobileHeader = document.querySelector('.mobile-header');
  const mobileMenuToggle = document.querySelector(
    '.mobile-header .mobile-header-menu-toggle'
  );
  const mobileMenu = document.querySelector('.mobile-menu');
  const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');

  // Debug
  console.log('Mobile header:', mobileHeader);
  console.log(
    'Mobile menu toggle selector:',
    '.mobile-header .mobile-header-menu-toggle'
  );
  console.log('Mobile menu toggle element:', mobileMenuToggle);
  console.log('Mobile menu:', mobileMenu);
  console.log('Mobile menu overlay:', mobileMenuOverlay);

  // Xử lý mở/đóng menu bằng nút toggle
  if (mobileMenuToggle && mobileMenu) {
    mobileMenuToggle.addEventListener('click', function () {
      console.log('Mobile menu toggle clicked');

      // Thêm hiệu ứng ripple khi nhấn
      const ripple = document.createElement('span');
      ripple.classList.add('menu-toggle-ripple');
      this.appendChild(ripple);

      // Xóa hiệu ứng ripple sau khi hoàn thành
      setTimeout(() => {
        ripple.remove();
      }, 600);

      // Toggle trạng thái active của nút
      this.classList.toggle('active');

      // Mở/đóng menu
      if (mobileMenu.classList.contains('active')) {
        // Đóng menu
        mobileMenu.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
        if (mobileMenuOverlay) {
          mobileMenuOverlay.classList.remove('active');
        }
      } else {
        // Mở menu
        // Sử dụng requestAnimationFrame để đảm bảo hiệu ứng mượt mà
        requestAnimationFrame(() => {
          // Thêm một khoảng thời gian nhỏ để đảm bảo CSS được áp dụng đúng
          setTimeout(() => {
            mobileMenu.classList.add('active');
            document.body.classList.add('overflow-hidden');
            if (mobileMenuOverlay) {
              mobileMenuOverlay.classList.add('active');
            }
          }, 10);
        });
      }
    });
  }

  // Xử lý đóng menu khi nhấp vào overlay
  if (mobileMenuOverlay) {
    mobileMenuOverlay.addEventListener('click', function () {
      console.log('Mobile menu overlay clicked');

      // Xóa class active khỏi nút toggle
      if (mobileMenuToggle) {
        mobileMenuToggle.classList.remove('active');
      }

      // Đóng menu
      mobileMenu.classList.remove('active');
      document.body.classList.remove('overflow-hidden');
      this.classList.remove('active');
    });
  }

  // Xử lý dropdown menu
  function initMobileMenu() {
    // Lấy tất cả các dropdown toggle
    const mobileDropdownToggles = document.querySelectorAll(
      '.mobile-dropdown-toggle'
    );
    console.log('Mobile dropdown toggles:', mobileDropdownToggles.length);

    // Xử lý sự kiện click cho các dropdown toggle
    mobileDropdownToggles.forEach(function (toggle) {
      toggle.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();

        const parent = this.parentElement;
        console.log('Toggle clicked:', parent);

        // Toggle class active cho parent
        parent.classList.toggle('active');
      });
    });

    // Xử lý nút quay lại
    const mobileBackButtons = document.querySelectorAll('.mobile-menu-back');
    console.log('Mobile back buttons:', mobileBackButtons.length);

    mobileBackButtons.forEach(function (button) {
      button.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();

        // Tìm submenu cha
        const submenu = this.closest('.mobile-submenu');
        const parentItem = submenu.closest(
          '.mobile-menu-item, .mobile-submenu-item'
        );

        if (parentItem) {
          parentItem.classList.remove('active');
          console.log('Back button clicked:', parentItem);
        }
      });
    });
  }

  // Khởi tạo menu mobile
  initMobileMenu();
});
