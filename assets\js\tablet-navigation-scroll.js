/**
 * Tablet Navigation Scroll JavaScript for Nội Thất Bàng Vũ
 * Handles touch scroll for navigation menu on tablet devices
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run on tablet devices
    function isTablet() {
        return window.innerWidth >= 768 && window.innerWidth <= 1200;
    }
    
    if (!isTablet()) return;
    
    const navMenu = document.querySelector('.nav-menu');
    const navContainer = document.querySelector('nav[role="navigation"]');
    const leftArrow = document.querySelector('.nav-scroll-left');
    const rightArrow = document.querySelector('.nav-scroll-right');

    if (!navMenu || !navContainer || !leftArrow || !rightArrow) return;

    console.log('Tablet navigation scroll initialized');

    // Function to scroll to a specific item and center it
    function scrollToItem(item) {
        if (!isTablet() || !item) return;

        // Set auto-scrolling flag and add visual feedback class
        isAutoScrolling = true;
        navMenu.classList.add('auto-scrolling');

        // Wait for any ongoing animations to complete
        setTimeout(() => {
            const menuRect = navMenu.getBoundingClientRect();
            const itemRect = item.getBoundingClientRect();

            // Calculate the position to center the item
            const menuCenter = menuRect.width / 2;
            const itemCenter = itemRect.left - menuRect.left + (itemRect.width / 2);
            const scrollOffset = itemCenter - menuCenter;

            // Only scroll if the offset is significant (avoid unnecessary micro-scrolls)
            if (Math.abs(scrollOffset) > 10) {
                // Add pulse effect to relevant arrows
                if (scrollOffset > 0) {
                    rightArrow.classList.add('auto-scroll-active');
                } else {
                    leftArrow.classList.add('auto-scroll-active');
                }

                // Smooth scroll to center the item
                navMenu.scrollBy({
                    left: scrollOffset,
                    behavior: 'smooth'
                });

                console.log('Auto-scrolled to item:', item.textContent.trim(), 'Offset:', scrollOffset);

                // Update arrow indicators and remove visual feedback after scroll
                setTimeout(() => {
                    updateArrowIndicators();
                    navMenu.classList.remove('auto-scrolling');
                    leftArrow.classList.remove('auto-scroll-active');
                    rightArrow.classList.remove('auto-scroll-active');
                    isAutoScrolling = false;
                }, 600);
            } else {
                // Remove visual feedback immediately if no scroll needed
                navMenu.classList.remove('auto-scrolling');
                isAutoScrolling = false;
            }
        }, 50);
    }

    // Function to scroll to active item and center it
    function scrollToActiveItem() {
        if (!isTablet()) return;

        // Don't auto-scroll if user is currently scrolling manually
        if (userIsScrolling) {
            console.log('User is scrolling, skipping auto-scroll to active item');
            return;
        }

        const activeItem = navMenu.querySelector('.nav-item.active');
        if (!activeItem) {
            console.log('No active item found for auto-scroll');
            return;
        }

        scrollToItem(activeItem);
    }

    // Function to update arrow indicators based on scroll position
    function updateArrowIndicators() {
        if (!isTablet()) return;

        const scrollLeft = navMenu.scrollLeft;
        const scrollWidth = navMenu.scrollWidth;
        const clientWidth = navMenu.clientWidth;
        const maxScrollLeft = scrollWidth - clientWidth;

        // Left arrow: show when scrolled right (scrollLeft > 0)
        if (scrollLeft > 5) {
            leftArrow.classList.add('show');
        } else {
            leftArrow.classList.remove('show');
        }

        // Right arrow: show when can scroll more to right
        if (scrollLeft < maxScrollLeft - 5) {
            rightArrow.classList.add('show');
        } else {
            rightArrow.classList.remove('show');
        }
    }

    // Arrow click handlers
    leftArrow.addEventListener('click', (e) => {
        e.preventDefault();
        if (!isTablet()) return;

        const scrollAmount = navMenu.clientWidth / 2;
        navMenu.scrollBy({
            left: -scrollAmount,
            behavior: 'smooth'
        });
    });

    rightArrow.addEventListener('click', (e) => {
        e.preventDefault();
        if (!isTablet()) return;

        const scrollAmount = navMenu.clientWidth / 2;
        navMenu.scrollBy({
            left: scrollAmount,
            behavior: 'smooth'
        });
    });

    // Initial setup with multiple attempts to ensure proper positioning
    function initializeAutoScroll() {
        updateArrowIndicators();
        scrollToActiveItem();

        // Additional attempts to ensure proper positioning after all elements are rendered
        setTimeout(() => {
            scrollToActiveItem();
        }, 300);

        setTimeout(() => {
            scrollToActiveItem();
        }, 600);
    }

    // Initial setup
    setTimeout(initializeAutoScroll, 100);

    // Also run after window load to ensure all resources are loaded
    window.addEventListener('load', () => {
        setTimeout(initializeAutoScroll, 200);
    });

    // Update indicators on scroll and detect user scrolling
    navMenu.addEventListener('scroll', function() {
        updateArrowIndicators();

        // Detect user manual scrolling
        if (!isAutoScrolling) {
            userIsScrolling = true;
            clearTimeout(userScrollTimeout);

            // Reset user scrolling flag after user stops scrolling
            userScrollTimeout = setTimeout(() => {
                userIsScrolling = false;
                console.log('User stopped scrolling, auto-scroll enabled again');
            }, 2000); // 2 seconds after user stops scrolling
        }
    });

    // Update indicators and re-center active item on resize
    window.addEventListener('resize', () => {
        setTimeout(() => {
            updateArrowIndicators();
            if (isTablet()) {
                scrollToActiveItem(); // Re-center active item after resize
            }
        }, 100);
    });

    // Handle orientation change on tablets
    window.addEventListener('orientationchange', () => {
        setTimeout(() => {
            if (isTablet()) {
                updateArrowIndicators();
                scrollToActiveItem(); // Re-center active item after orientation change
            }
        }, 300); // Longer delay for orientation change
    });
    
    let isDown = false;
    let startX;
    let scrollLeft;
    let isDragging = false;
    let isAutoScrolling = false;
    let userIsScrolling = false;
    let userScrollTimeout;
    
    // Mouse events for desktop testing
    navMenu.addEventListener('mousedown', (e) => {
        if (!isTablet()) return;

        isDown = true;
        isDragging = false;
        startX = e.pageX - navMenu.offsetLeft;
        scrollLeft = navMenu.scrollLeft;
        navMenu.style.cursor = 'grabbing';
        navMenu.classList.add('user-scrolling');
        e.preventDefault();
    });
    
    navMenu.addEventListener('mouseleave', () => {
        isDown = false;
        navMenu.style.cursor = 'grab';
    });
    
    navMenu.addEventListener('mouseup', (e) => {
        isDown = false;
        navMenu.style.cursor = 'grab';

        // If we were dragging, prevent click events
        if (isDragging) {
            e.preventDefault();
            e.stopPropagation();
        }

        setTimeout(() => {
            isDragging = false;
            navMenu.classList.remove('user-scrolling');
        }, 100);
    });
    
    navMenu.addEventListener('mousemove', (e) => {
        if (!isDown || !isTablet()) return;

        e.preventDefault();
        const x = e.pageX - navMenu.offsetLeft;
        const walk = (x - startX) * 2;

        if (Math.abs(walk) > 5) {
            isDragging = true;
        }

        navMenu.scrollLeft = scrollLeft - walk;
        updateArrowIndicators();
    });
    
    // Touch events for tablet
    navMenu.addEventListener('touchstart', (e) => {
        if (!isTablet()) return;
        
        startX = e.touches[0].pageX - navMenu.offsetLeft;
        scrollLeft = navMenu.scrollLeft;
        isDragging = false;
    }, { passive: true });
    
    navMenu.addEventListener('touchmove', (e) => {
        if (!isTablet()) return;

        const x = e.touches[0].pageX - navMenu.offsetLeft;
        const walk = (x - startX) * 1.5;

        if (Math.abs(walk) > 5) {
            isDragging = true;
        }

        navMenu.scrollLeft = scrollLeft - walk;
        updateArrowIndicators();
    }, { passive: true });
    
    navMenu.addEventListener('touchend', () => {
        if (!isTablet()) return;
        
        // Prevent click events if we were dragging
        if (isDragging) {
            setTimeout(() => {
                isDragging = false;
            }, 100);
        }
    }, { passive: true });
    
    // Enhanced nav link click handling
    const navLinks = navMenu.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            if (isDragging) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }

            // Optional: Auto-scroll to clicked item after a short delay
            // This helps with visual feedback when navigating
            setTimeout(() => {
                if (isTablet()) {
                    const clickedItem = link.closest('.nav-item');
                    if (clickedItem && clickedItem.classList.contains('active')) {
                        scrollToActiveItem();
                    }
                }
            }, 50);
        });

        // Handle keyboard navigation
        link.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                // Will trigger click event which handles the scroll
            } else if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                // Handle arrow key navigation
                e.preventDefault();
                const currentItem = link.closest('.nav-item');
                const allItems = Array.from(navMenu.querySelectorAll('.nav-item'));
                const currentIndex = allItems.indexOf(currentItem);

                let targetIndex;
                if (e.key === 'ArrowLeft') {
                    targetIndex = currentIndex > 0 ? currentIndex - 1 : allItems.length - 1;
                } else {
                    targetIndex = currentIndex < allItems.length - 1 ? currentIndex + 1 : 0;
                }

                const targetLink = allItems[targetIndex].querySelector('.nav-link');
                if (targetLink) {
                    targetLink.focus();
                    // Auto-scroll to focused item
                    setTimeout(() => {
                        if (isTablet()) {
                            const targetItem = targetLink.closest('.nav-item');
                            scrollToItem(targetItem);
                        }
                    }, 50);
                }
            }
        });
    });
    
    // Handle window resize
    window.addEventListener('resize', () => {
        if (!isTablet()) {
            // Reset styles if not tablet anymore
            navMenu.style.cursor = '';
            isDown = false;
            isDragging = false;
        }
    });
    
    // Force scroll behavior
    navMenu.style.scrollBehavior = 'smooth';

    // Observer to watch for active item changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                const target = mutation.target;
                if (target.classList.contains('nav-item') && target.classList.contains('active')) {
                    // Active item changed, auto-scroll to it
                    setTimeout(() => {
                        if (isTablet()) {
                            scrollToActiveItem();
                        }
                    }, 100);
                }
            }
        });
    });

    // Start observing nav items for class changes
    const navItems = navMenu.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        observer.observe(item, { attributes: true, attributeFilter: ['class'] });
    });

    // Expose functions globally for external use
    window.tabletNavScrollToActive = scrollToActiveItem;
    window.tabletNavScrollToItem = scrollToItem;

    // Utility function to scroll to item by text content
    window.tabletNavScrollToItemByText = function(text) {
        if (!isTablet()) return;

        const items = navMenu.querySelectorAll('.nav-item');
        for (let item of items) {
            if (item.textContent.trim().toLowerCase().includes(text.toLowerCase())) {
                scrollToItem(item);
                break;
            }
        }
    };

    console.log('Tablet navigation scroll setup complete with auto-scroll features');
    console.log('Available functions: tabletNavScrollToActive(), tabletNavScrollToItem(element), tabletNavScrollToItemByText(text)');
});
