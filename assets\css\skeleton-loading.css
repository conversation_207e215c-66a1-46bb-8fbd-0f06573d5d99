/**
 * Skeleton Loading Effects
 * Hi<PERSON><PERSON> ứng loading hiện đại cho Nội Thất Bàng <PERSON>
 */

/* Base Skeleton */
.skeleton-loading {
    position: relative;
    overflow: hidden;
    background-color: #f0f0f0;
    border-radius: 4px;
}

/* Hiệu ứng shimmer (lấp lánh) */
.skeleton-loading::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transform: translateX(-100%);
    background-image: linear-gradient(90deg,
            rgba(255, 255, 255, 0) 0,
            rgba(255, 255, 255, 0.2) 20%,
            rgba(255, 255, 255, 0.5) 60%,
            rgba(255, 255, 255, 0));
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

/* Skeleton cho ảnh sản phẩm */
.skeleton-image {
    width: 100%;
    height: 100%;
    background-color: #f0f0f0;
    border-radius: 4px;
}

/* Skeleton cho ảnh sản phẩm trong kết quả tì<PERSON> kiếm */
.skeleton-search-image {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    position: absolute;
    top: 0;
    left: 0;
}

/* Skeleton cho text */
.skeleton-text {
    height: 1em;
    margin-bottom: 0.5em;
    border-radius: 4px;
}

/* Skeleton cho tiêu đề */
.skeleton-title {
    width: 80%;
    height: 1.2em;
    margin-bottom: 0.7em;
    border-radius: 4px;
}

/* Skeleton cho giá */
.skeleton-price {
    width: 40%;
    height: 1em;
    border-radius: 4px;
}

/* Skeleton cho danh mục */
.skeleton-category {
    width: 60%;
    height: 0.8em;
    border-radius: 4px;
}

/* Skeleton cho kết quả tìm kiếm */
.skeleton-search-item {
    display: flex;
    padding: 10px;
    border-bottom: 1px solid #eee;
    align-items: center;
}

.skeleton-search-info {
    flex-grow: 1;
    margin-left: 10px;
}

/* Skeleton cho nút */
.skeleton-button {
    width: 100px;
    height: 36px;
    border-radius: 18px;
}

/* Skeleton cho card sản phẩm */
.skeleton-product-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
}

.skeleton-product-image {
    width: 100%;
    padding-bottom: 100%;
    /* Tỉ lệ 1:1 */
    position: relative;
}

.skeleton-product-content {
    padding: 1rem;
}

/* Hiệu ứng pulse (nhịp đập) */
.skeleton-pulse {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.6;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0.6;
    }
}

/* Hiệu ứng wave (sóng) */
.skeleton-wave {
    position: relative;
    overflow: hidden;
}

.skeleton-wave::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: wave 1.6s linear 0.5s infinite;
}

@keyframes wave {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

/* Hiệu ứng gradient (màu chuyển) */
.skeleton-gradient {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: gradient 1.5s ease-in-out infinite;
}

@keyframes gradient {
    0% {
        background-position: 200% 0;
    }

    100% {
        background-position: -200% 0;
    }
}

/* Màu sắc theo theme */
.skeleton-primary {
    background-color: rgba(243, 115, 33, 0.1);
}

.skeleton-secondary {
    background-color: rgba(44, 62, 80, 0.1);
}

.skeleton-light {
    background-color: rgba(240, 240, 240, 0.8);
}

.skeleton-dark {
    background-color: rgba(51, 51, 51, 0.1);
}

/* Đặc biệt cho nút checkout - đảm bảo không bị ghi đè bởi skeleton loading */
#checkout-btn.skeleton-loading {
    /* Giữ nguyên background gradient của premium-checkout-btn */
    background: linear-gradient(135deg, #f37321 0%, #e67e22 50%, #d35400 100%) !important;
    /* Giữ nguyên màu text */
    color: white !important;
    /* Giữ nguyên border radius */
    border-radius: 12px !important;
    /* Giữ nguyên box shadow */
    box-shadow:
        0 6px 20px rgba(243, 115, 33, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset !important;
}

/* Đảm bảo hiệu ứng shimmer vẫn hoạt động nhưng không ghi đè background */
#checkout-btn.skeleton-loading::after {
    background-image: linear-gradient(90deg,
            rgba(255, 255, 255, 0) 0,
            rgba(255, 255, 255, 0.1) 20%,
            rgba(255, 255, 255, 0.2) 60%,
            rgba(255, 255, 255, 0)) !important;
}