{"version": 3, "file": "lang/summernote-pt-BR.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,YAAY;QACvBC,KAAK,EAAE,yBAAyB;QAChCC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,OAAO;QACbC,aAAa,EAAE,SAAS;QACxBC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,aAAa;QAC1BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE,gBAAgB;QACxBC,UAAU,EAAE,6BAA6B;QACzCC,UAAU,EAAE,2BAA2B;QACvCC,aAAa,EAAE,2BAA2B;QAC1CC,SAAS,EAAE,uBAAuB;QAClCC,UAAU,EAAE,sBAAsB;QAClCC,SAAS,EAAE,aAAa;QACxBC,YAAY,EAAE,oBAAoB;QAClCC,WAAW,EAAE,gBAAgB;QAC7BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,eAAe;QAC1BC,aAAa,EAAE,iCAAiC;QAChDC,SAAS,EAAE,uBAAuB;QAClCC,eAAe,EAAE,iCAAiC;QAClDC,eAAe,EAAE,2BAA2B;QAC5CC,oBAAoB,EAAE,qCAAqC;QAC3DC,GAAG,EAAE,eAAe;QACpBC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,iBAAiB;QAC5BpB,MAAM,EAAE,eAAe;QACvBgB,GAAG,EAAE,eAAe;QACpBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,cAAc;QACtBuB,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE,mBAAmB;QAClCT,GAAG,EAAE,+BAA+B;QACpCU,eAAe,EAAE,0BAA0B;QAC3CC,WAAW,EAAE;MACf,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,uBAAuB;QACpCC,WAAW,EAAE,wBAAwB;QACrCC,UAAU,EAAE,6BAA6B;QACzCC,WAAW,EAAE,4BAA4B;QACzCC,MAAM,EAAE,eAAe;QACvBC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFpC,MAAM,EAAE;MACV,CAAC;MACDqC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,CAAC,EAAE,QAAQ;QACXC,UAAU,EAAE,SAAS;QACrBC,GAAG,EAAE,QAAQ;QACbC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,sBAAsB;QACjCC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,YAAY;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,WAAW;QACtBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,oBAAoB;QAC1BC,MAAM,EAAE,mBAAmB;QAC3BC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE,OAAO;QACnBC,UAAU,EAAE,OAAO;QACnBC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,oBAAoB;QACpCC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE,kBAAkB;QAClCC,QAAQ,EAAE;MACZ,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,oBAAoB;QAC/BC,KAAK,EAAE,QAAQ;QACfC,cAAc,EAAE,qBAAqB;QACrCC,MAAM,EAAE,MAAM;QACdC,mBAAmB,EAAE,yBAAyB;QAC9CC,aAAa,EAAE,qBAAqB;QACpCC,SAAS,EAAE;MACb,CAAC;MACD3B,IAAI,EAAE;QACJ,iBAAiB,EAAE,mBAAmB;QACtC,MAAM,EAAE,2BAA2B;QACnC,MAAM,EAAE,0BAA0B;QAClC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,cAAc;QACvB,MAAM,EAAE,oBAAoB;QAC5B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,YAAY;QACzB,eAAe,EAAE,SAAS;QAC1B,cAAc,EAAE,gBAAgB;QAChC,aAAa,EAAE,oBAAoB;QACnC,eAAe,EAAE,aAAa;QAC9B,cAAc,EAAE,oBAAoB;QACpC,aAAa,EAAE,YAAY;QAC3B,qBAAqB,EAAE,oBAAoB;QAC3C,mBAAmB,EAAE,gBAAgB;QACrC,SAAS,EAAE,wBAAwB;QACnC,QAAQ,EAAE,yBAAyB;QACnC,YAAY,EAAE,gDAAgD;QAC9D,UAAU,EAAE,kCAAkC;QAC9C,UAAU,EAAE,kCAAkC;QAC9C,UAAU,EAAE,kCAAkC;QAC9C,UAAU,EAAE,kCAAkC;QAC9C,UAAU,EAAE,kCAAkC;QAC9C,UAAU,EAAE,kCAAkC;QAC9C,sBAAsB,EAAE,0BAA0B;QAClD,iBAAiB,EAAE;MACrB,CAAC;MACD4B,OAAO,EAAE;QACPC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,sBAAsB;QACnCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-pt-BR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'pt-BR': {\n      font: {\n        bold: 'Negrito',\n        italic: 'Itálico',\n        underline: 'Sublinhado',\n        clear: 'Remover estilo da fonte',\n        height: '<PERSON><PERSON> da linha',\n        name: '<PERSON><PERSON>',\n        strikethrough: 'Risca<PERSON>',\n        subscript: 'Subscrito',\n        superscript: 'Sobrescrito',\n        size: 'Tamanho da fonte',\n      },\n      image: {\n        image: 'Imagem',\n        insert: 'Inserir imagem',\n        resizeFull: 'Redimensionar Completamente',\n        resizeHalf: 'Redimensionar pela Metade',\n        resizeQuarter: 'Redimensionar a um Quarto',\n        floatLeft: 'Flutuar para Esquerda',\n        floatRight: 'Flutuar para Direita',\n        floatNone: 'Não Flutuar',\n        shapeRounded: 'Forma: Arredondado',\n        shapeCircle: 'Forma: Círculo',\n        shapeThumbnail: 'Forma: Miniatura',\n        shapeNone: 'Forma: Nenhum',\n        dragImageHere: 'Arraste Imagem ou Texto para cá',\n        dropImage: 'Solte Imagem ou Texto',\n        selectFromFiles: 'Selecione a partir dos arquivos',\n        maximumFileSize: 'Tamanho máximo do arquivo',\n        maximumFileSizeError: 'Tamanho máximo do arquivo excedido.',\n        url: 'URL da imagem',\n        remove: 'Remover Imagem',\n        original: 'Original',\n      },\n      video: {\n        video: 'Vídeo',\n        videoLink: 'Link para vídeo',\n        insert: 'Inserir vídeo',\n        url: 'URL do vídeo?',\n        providers: '(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Inserir link',\n        unlink: 'Remover link',\n        edit: 'Editar',\n        textToDisplay: 'Texto para exibir',\n        url: 'Para qual URL este link leva?',\n        openInNewWindow: 'Abrir em uma nova janela',\n        useProtocol: 'Usar protocolo padrão',\n      },\n      table: {\n        table: 'Tabela',\n        addRowAbove: 'Adicionar linha acima',\n        addRowBelow: 'Adicionar linha abaixo',\n        addColLeft: 'Adicionar coluna à esquerda',\n        addColRight: 'Adicionar coluna à direita',\n        delRow: 'Excluir linha',\n        delCol: 'Excluir coluna',\n        delTable: 'Excluir tabela',\n      },\n      hr: {\n        insert: 'Linha horizontal',\n      },\n      style: {\n        style: 'Estilo',\n        p: 'Normal',\n        blockquote: 'Citação',\n        pre: 'Código',\n        h1: 'Título 1',\n        h2: 'Título 2',\n        h3: 'Título 3',\n        h4: 'Título 4',\n        h5: 'Título 5',\n        h6: 'Título 6',\n      },\n      lists: {\n        unordered: 'Lista com marcadores',\n        ordered: 'Lista numerada',\n      },\n      options: {\n        help: 'Ajuda',\n        fullscreen: 'Tela cheia',\n        codeview: 'Ver código-fonte',\n      },\n      paragraph: {\n        paragraph: 'Parágrafo',\n        outdent: 'Menor tabulação',\n        indent: 'Maior tabulação',\n        left: 'Alinhar à esquerda',\n        center: 'Alinhar ao centro',\n        right: 'Alinha à direita',\n        justify: 'Justificado',\n      },\n      color: {\n        recent: 'Cor recente',\n        more: 'Mais cores',\n        background: 'Fundo',\n        foreground: 'Fonte',\n        transparent: 'Transparente',\n        setTransparent: 'Fundo transparente',\n        reset: 'Restaurar',\n        resetToDefault: 'Restaurar padrão',\n        cpSelect: 'Selecionar',\n      },\n      shortcut: {\n        shortcuts: 'Atalhos do teclado',\n        close: 'Fechar',\n        textFormatting: 'Formatação de texto',\n        action: 'Ação',\n        paragraphFormatting: 'Formatação de parágrafo',\n        documentStyle: 'Estilo de documento',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Inserir Parágrafo',\n        'undo': 'Desfazer o último comando',\n        'redo': 'Refazer o último comando',\n        'tab': 'Tab',\n        'untab': 'Desfazer tab',\n        'bold': 'Colocar em negrito',\n        'italic': 'Colocar em itálico',\n        'underline': 'Sublinhado',\n        'strikethrough': 'Tachado',\n        'removeFormat': 'Remover estilo',\n        'justifyLeft': 'Alinhar à esquerda',\n        'justifyCenter': 'Centralizar',\n        'justifyRight': 'Alinhar à esquerda',\n        'justifyFull': 'Justificar',\n        'insertUnorderedList': 'Lista não ordenada',\n        'insertOrderedList': 'Lista ordenada',\n        'outdent': 'Recuar parágrafo atual',\n        'indent': 'Avançar parágrafo atual',\n        'formatPara': 'Alterar formato do bloco para parágrafo(tag P)',\n        'formatH1': 'Alterar formato do bloco para H1',\n        'formatH2': 'Alterar formato do bloco para H2',\n        'formatH3': 'Alterar formato do bloco para H3',\n        'formatH4': 'Alterar formato do bloco para H4',\n        'formatH5': 'Alterar formato do bloco para H5',\n        'formatH6': 'Alterar formato do bloco para H6',\n        'insertHorizontalRule': 'Inserir Régua horizontal',\n        'linkDialog.show': 'Inserir um Hiperlink',\n      },\n      history: {\n        undo: 'Desfazer',\n        redo: 'Refazer',\n      },\n      specialChar: {\n        specialChar: 'CARACTERES ESPECIAIS',\n        select: 'Selecionar Caracteres Especiais',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}