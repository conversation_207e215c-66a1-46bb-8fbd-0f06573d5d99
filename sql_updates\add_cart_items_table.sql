-- <PERSON><PERSON><PERSON> tra xem bảng cart_items đã tồn tại chưa
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'cart_items';

-- <PERSON><PERSON><PERSON> bảng cart_items chưa tồn tại, tạo bảng
SET @query = IF(@table_exists = 0, 
    'CREATE TABLE cart_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        product_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        price DECIMAL(15, 2) NOT NULL,
        image VARCHAR(255),
        quantity INT NOT NULL DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        <PERSON>OR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIG<PERSON> KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_product (user_id, product_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci', 
    'SELECT "Bảng cart_items đã tồn tại" AS message');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Tạo chỉ mục để tối ưu truy vấn
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'cart_items' 
AND INDEX_NAME = 'idx_cart_items_user_id';

SET @query = IF(@index_exists = 0, 
    'CREATE INDEX idx_cart_items_user_id ON cart_items(user_id)', 
    'SELECT "Chỉ mục idx_cart_items_user_id đã tồn tại" AS message');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
