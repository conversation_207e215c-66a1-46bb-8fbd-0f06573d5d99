<?php
/**
 * <PERSON><PERSON><PERSON> hàm xử lý liên quan đến Blog
 */

// Include file product.php để sử dụng hàm get_visitor_identifier
require_once __DIR__ . '/product.php';

/**
 * Tạo slug từ chuỗi
 *
 * @param string $string Chuỗi cần chuyển đổi
 * @return string Slug
 */
function create_slug($string) {
    return slugify($string);
}

/**
 * Upload ảnh cho blog
 *
 * @param array $file File từ $_FILES
 * @param string $subfolder Thư mục con trong uploads
 * @return array Kết quả upload
 */
function upload_image($file, $subfolder = '') {
    $target_dir = UPLOADS_PATH . ($subfolder ? $subfolder . '/' : '');
    return upload_file($file, $target_dir, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
}

// Hàm get_user_by_id đã được định nghĩa trong file auth.php
// Hàm get_visitor_identifier đã được định nghĩa trong file product.php

/**
 * L<PERSON>y thông tin tác giả blog
 *
 * @param int $user_id ID của người dùng
 * @param int|null $blog_author_id ID của tác giả trong bảng blog_authors (nếu có)
 * @return array|null Thông tin tác giả hoặc null nếu không tìm thấy
 */
function get_blog_author($user_id, $blog_author_id = null) {
    global $conn;

    try {
        // Kiểm tra xem bảng blog_authors đã tồn tại chưa
        $table_exists = false;
        try {
            $check_table = $conn->query("SHOW TABLES LIKE 'blog_authors'");
            $table_exists = ($check_table->rowCount() > 0);
        } catch (PDOException $e) {
            // Bảng chưa tồn tại
            $table_exists = false;
        }

        if (!$table_exists) {
            // Nếu bảng chưa tồn tại, trả về thông tin người dùng cơ bản
            $user = get_user_by_id($user_id);
            if ($user) {
                return [
                    'id' => null,
                    'user_id' => $user['id'],
                    'name' => $user['full_name'],
                    'email' => $user['email'],
                    'bio' => '',
                    'position' => '',
                    'experience' => '',
                    'education' => '',
                    'avatar' => '',
                    'facebook' => '',
                    'zalo' => '',
                    'twitter' => '',
                    'linkedin' => '',
                    'instagram' => '',
                    'website' => ''
                ];
            }
            return null;
        }

        // Nếu có blog_author_id, ưu tiên lấy theo blog_author_id
        if ($blog_author_id) {
            $stmt = $conn->prepare("
                SELECT *
                FROM blog_authors
                WHERE id = :blog_author_id
            ");
            $stmt->bindParam(':blog_author_id', $blog_author_id, PDO::PARAM_INT);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                return $stmt->fetch(PDO::FETCH_ASSOC);
            }
        }

        // Nếu không có blog_author_id hoặc không tìm thấy, lấy theo user_id (cách cũ)
        if ($user_id) {
            $user = get_user_by_id($user_id);
            if ($user) {
                return [
                    'id' => null,
                    'user_id' => $user['id'],
                    'name' => $user['full_name'],
                    'email' => $user['email'],
                    'bio' => '',
                    'position' => '',
                    'experience' => '',
                    'education' => '',
                    'avatar' => '',
                    'facebook' => '',
                    'zalo' => '',
                    'twitter' => '',
                    'linkedin' => '',
                    'instagram' => '',
                    'website' => ''
                ];
            }
        }

        return null;
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return null;
    }
}

/**
 * Lấy danh sách bài viết blog
 *
 * @param int $limit Số lượng bài viết cần lấy
 * @param int $offset Vị trí bắt đầu
 * @param int|null $category_id ID danh mục (nếu có)
 * @param int|null $tag_id ID tag (nếu có)
 * @param int|null $is_featured Chỉ lấy bài viết nổi bật (1) hoặc không nổi bật (0)
 * @param int|null $status Trạng thái bài viết (1: Published, 0: Draft)
 * @param int|null $show_on_homepage Chỉ lấy bài viết hiển thị trên trang chủ (1) hoặc không hiển thị trên trang chủ (0)
 * @return array Mảng chứa danh sách bài viết
 */
function get_blog_posts($limit = 10, $offset = 0, $category_id = null, $tag_id = null, $is_featured = null, $status = 1, $show_on_homepage = null) {
    global $conn;

    try {
        $params = [];
        $sql = "SELECT p.* FROM blog_posts p";

        // Nếu có lọc theo danh mục
        if ($category_id !== null) {
            $sql .= " JOIN blog_post_categories pc ON p.id = pc.post_id";
            $sql .= " WHERE pc.category_id = :category_id";
            $params[':category_id'] = $category_id;
        } else {
            $sql .= " WHERE 1=1";
        }

        // Nếu có lọc theo tag
        if ($tag_id !== null) {
            $sql .= " AND p.id IN (SELECT post_id FROM blog_post_tags WHERE tag_id = :tag_id)";
            $params[':tag_id'] = $tag_id;
        }

        // Lọc theo trạng thái
        if ($status !== null) {
            $sql .= " AND p.status = :status";
            $params[':status'] = $status;
        }

        // Lọc theo bài viết nổi bật
        if ($is_featured !== null) {
            $sql .= " AND p.is_featured = :is_featured";
            $params[':is_featured'] = $is_featured;
        }

        // Lọc theo bài viết hiển thị trên trang chủ
        if ($show_on_homepage !== null) {
            $sql .= " AND p.show_on_homepage = :show_on_homepage";
            $params[':show_on_homepage'] = $show_on_homepage;
        }

        // Sắp xếp theo thời gian đăng (mới nhất trước)
        $sql .= " ORDER BY p.published_at DESC, p.created_at DESC";

        // Giới hạn số lượng
        if ($limit > 0) {
            $sql .= " LIMIT :offset, :limit";
            $params[':offset'] = $offset;
            $params[':limit'] = $limit;
        }

        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            if ($key == ':limit' || $key == ':offset') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();
        $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Lấy thông tin tác giả và danh mục cho mỗi bài viết
        foreach ($posts as &$post) {
            $post['author'] = get_blog_author($post['author_id'], $post['blog_author_id'] ?? null);
            $post['categories'] = get_post_categories($post['id']);
            $post['tags'] = get_post_tags($post['id']);

            // Format lại ngày tháng
            if (!empty($post['published_at'])) {
                $post['formatted_date'] = date('d/m/Y', strtotime($post['published_at']));
            } else {
                $post['formatted_date'] = date('d/m/Y', strtotime($post['created_at']));
            }
        }

        return $posts;
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Đếm tổng số bài viết theo điều kiện
 *
 * @param int|null $category_id ID danh mục (nếu có)
 * @param int|null $tag_id ID tag (nếu có)
 * @param int|null $status Trạng thái bài viết (1: Published, 0: Draft)
 * @return int Tổng số bài viết
 */
function count_blog_posts($category_id = null, $tag_id = null, $status = 1) {
    global $conn;

    try {
        $params = [];
        $sql = "SELECT COUNT(DISTINCT p.id) as total FROM blog_posts p";

        // Nếu có lọc theo danh mục
        if ($category_id !== null) {
            $sql .= " JOIN blog_post_categories pc ON p.id = pc.post_id";
            $sql .= " WHERE pc.category_id = :category_id";
            $params[':category_id'] = $category_id;
        } else {
            $sql .= " WHERE 1=1";
        }

        // Nếu có lọc theo tag
        if ($tag_id !== null) {
            $sql .= " AND p.id IN (SELECT post_id FROM blog_post_tags WHERE tag_id = :tag_id)";
            $params[':tag_id'] = $tag_id;
        }

        // Lọc theo trạng thái
        if ($status !== null) {
            $sql .= " AND p.status = :status";
            $params[':status'] = $status;
        }

        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return 0;
    }
}

/**
 * Lấy thông tin bài viết theo slug
 *
 * @param string $slug Slug của bài viết
 * @return array|null Thông tin bài viết hoặc null nếu không tìm thấy
 */
function get_blog_post_by_slug($slug) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT * FROM blog_posts WHERE slug = :slug");
        $stmt->bindParam(':slug', $slug);
        $stmt->execute();

        $post = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($post) {
            // Ghi log nội dung để debug
            error_log("Blog post content from database (first 500 chars): " . substr($post['content'], 0, 500));
            error_log("Blog post content length: " . strlen($post['content']));

            // Ghi nội dung vào file để debug
            $debug_file = __DIR__ . '/../logs/blog_content_from_db.txt';
            file_put_contents($debug_file, "Content from database: " . $post['content']);

            // Cập nhật lượt xem
            update_post_view_count($post['id']);

            // Lấy thông tin tác giả và danh mục
            $post['author'] = get_blog_author($post['author_id'], $post['blog_author_id'] ?? null);
            $post['categories'] = get_post_categories($post['id']);
            $post['tags'] = get_post_tags($post['id']);

            // Format lại ngày tháng
            if (!empty($post['published_at'])) {
                $post['formatted_date'] = date('d/m/Y', strtotime($post['published_at']));
            } else {
                $post['formatted_date'] = date('d/m/Y', strtotime($post['created_at']));
            }
        }

        return $post;
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return null;
    }
}

/**
 * Cập nhật lượt xem cho bài viết
 * Chỉ tăng lượt xem khi người dùng mới xem bài viết
 * Đảm bảo số lượt xem luôn tăng khi có người dùng mới, kể cả khi admin đã thay đổi số lượt xem
 *
 * @param int $post_id ID của bài viết
 * @return bool Kết quả cập nhật
 */
function update_post_view_count($post_id) {
    global $conn;

    try {
        // Kiểm tra xem bảng blog_views đã tồn tại chưa
        $table_exists = false;
        try {
            $check_table = $conn->query("SHOW TABLES LIKE 'blog_views'");
            $table_exists = ($check_table->rowCount() > 0);
        } catch (PDOException $e) {
            // Bảng chưa tồn tại
            $table_exists = false;
        }

        // Nếu bảng chưa tồn tại, tạo bảng
        if (!$table_exists) {
            $sql = "
            CREATE TABLE IF NOT EXISTS `blog_views` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `post_id` INT NOT NULL,
                `visitor_identifier` VARCHAR(255) NOT NULL,
                `view_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (`post_id`) REFERENCES `blog_posts`(`id`) ON DELETE CASCADE,
                UNIQUE KEY `unique_view` (`post_id`, `visitor_identifier`)
            )";

            $conn->exec($sql);

            // Tạo chỉ mục riêng biệt
            try {
                $conn->exec("CREATE INDEX idx_blog_views_post_id ON blog_views(post_id)");
            } catch (PDOException $e) {
                // Bỏ qua lỗi nếu chỉ mục đã tồn tại
            }

            try {
                $conn->exec("CREATE INDEX idx_blog_views_visitor ON blog_views(visitor_identifier)");
            } catch (PDOException $e) {
                // Bỏ qua lỗi nếu chỉ mục đã tồn tại
            }
        }

        // Kiểm tra xem người dùng hiện tại có phải là admin không
        $is_admin = isset($_SESSION['user']) && $_SESSION['user']['role'] === 'admin';

        // Nếu là admin đang xem bài viết, không tăng lượt xem
        if ($is_admin) {
            return true;
        }

        // Lấy định danh của người dùng (IP hoặc session ID)
        $visitor_identifier = get_visitor_identifier();

        // Kiểm tra xem người dùng đã xem bài viết này chưa
        $stmt = $conn->prepare("SELECT id FROM blog_views
                               WHERE post_id = :post_id
                               AND visitor_identifier = :visitor_identifier");
        $stmt->bindParam(':post_id', $post_id);
        $stmt->bindParam(':visitor_identifier', $visitor_identifier);
        $stmt->execute();

        // Nếu người dùng chưa xem bài viết này
        if ($stmt->rowCount() === 0) {
            // Thêm bản ghi vào bảng blog_views
            $stmt = $conn->prepare("INSERT INTO blog_views (post_id, visitor_identifier)
                                   VALUES (:post_id, :visitor_identifier)");
            $stmt->bindParam(':post_id', $post_id);
            $stmt->bindParam(':visitor_identifier', $visitor_identifier);
            $stmt->execute();

            // Tăng lượt xem trong bảng blog_posts
            // Đảm bảo lượt xem luôn tăng, kể cả khi admin đã thay đổi số lượt xem
            $stmt = $conn->prepare("UPDATE blog_posts SET view_count = view_count + 1 WHERE id = :id");
            $stmt->bindParam(':id', $post_id);
            $stmt->execute();

            // Lấy số lượt xem hiện tại để kiểm tra
            $stmt = $conn->prepare("SELECT view_count FROM blog_posts WHERE id = :id");
            $stmt->bindParam(':id', $post_id);
            $stmt->execute();
            $current_views = $stmt->fetch(PDO::FETCH_ASSOC)['view_count'];

            // Ghi log để debug
            error_log("Đã tăng lượt xem cho bài viết ID: $post_id. Lượt xem hiện tại: $current_views");
        }

        return true;
    } catch (PDOException $e) {
        // Ghi log lỗi
        error_log("Lỗi cập nhật lượt xem: " . $e->getMessage());
        return false;
    }
}

/**
 * Lấy danh sách danh mục của bài viết
 *
 * @param int $post_id ID bài viết
 * @return array Danh sách danh mục
 */
function get_post_categories($post_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("
            SELECT c.*
            FROM blog_categories c
            JOIN blog_post_categories pc ON c.id = pc.category_id
            WHERE pc.post_id = :post_id
        ");
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Lấy danh sách tag của bài viết
 *
 * @param int $post_id ID bài viết
 * @return array Danh sách tag
 */
function get_post_tags($post_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("
            SELECT t.*
            FROM blog_tags t
            JOIN blog_post_tags pt ON t.id = pt.tag_id
            WHERE pt.post_id = :post_id
        ");
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Lấy danh sách tất cả danh mục blog
 *
 * @param int|null $status Trạng thái danh mục (1: Hiển thị, 0: Ẩn)
 * @return array Danh sách danh mục
 */
function get_blog_categories($status = 1) {
    global $conn;

    try {
        $sql = "SELECT * FROM blog_categories";
        $params = [];

        if ($status !== null) {
            $sql .= " WHERE status = :status";
            $params[':status'] = $status;
        }

        $sql .= " ORDER BY name ASC";

        $stmt = $conn->prepare($sql);

        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Lấy thông tin danh mục theo slug
 *
 * @param string $slug Slug của danh mục
 * @return array|null Thông tin danh mục hoặc null nếu không tìm thấy
 */
function get_blog_category_by_slug($slug) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT * FROM blog_categories WHERE slug = :slug");
        $stmt->bindParam(':slug', $slug);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return null;
    }
}

/**
 * Lấy danh sách tất cả tag
 *
 * @return array Danh sách tag
 */
function get_blog_tags() {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT * FROM blog_tags ORDER BY name ASC");
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Lấy thông tin tag theo slug
 *
 * @param string $slug Slug của tag
 * @return array|null Thông tin tag hoặc null nếu không tìm thấy
 */
function get_blog_tag_by_slug($slug) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT * FROM blog_tags WHERE slug = :slug");
        $stmt->bindParam(':slug', $slug);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return null;
    }
}

/**
 * Lấy danh sách bài viết liên quan
 *
 * @param int $post_id ID bài viết hiện tại
 * @param int $limit Số lượng bài viết cần lấy
 * @return array Danh sách bài viết liên quan
 */
function get_related_posts($post_id, $limit = 4) {
    global $conn;

    try {
        // Lấy danh mục của bài viết hiện tại
        $categories = get_post_categories($post_id);
        $category_ids = array_column($categories, 'id');

        if (empty($category_ids)) {
            return [];
        }

        // Tạo placeholders cho IN clause
        $placeholders = implode(',', array_fill(0, count($category_ids), '?'));

        $sql = "
            SELECT DISTINCT p.*
            FROM blog_posts p
            JOIN blog_post_categories pc ON p.id = pc.post_id
            WHERE p.id != ?
            AND pc.category_id IN ($placeholders)
            AND p.status = 1
            ORDER BY p.published_at DESC
            LIMIT ?
        ";

        $stmt = $conn->prepare($sql);

        // Bind tham số
        $stmt->bindValue(1, $post_id, PDO::PARAM_INT);

        $i = 2;
        foreach ($category_ids as $cat_id) {
            $stmt->bindValue($i++, $cat_id, PDO::PARAM_INT);
        }

        $stmt->bindValue($i, $limit, PDO::PARAM_INT);

        $stmt->execute();
        $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Lấy thông tin tác giả và danh mục cho mỗi bài viết
        foreach ($posts as &$post) {
            $post['author'] = get_blog_author($post['author_id'], $post['blog_author_id'] ?? null);
            $post['categories'] = get_post_categories($post['id']);

            // Format lại ngày tháng
            if (!empty($post['published_at'])) {
                $post['formatted_date'] = date('d/m/Y', strtotime($post['published_at']));
            } else {
                $post['formatted_date'] = date('d/m/Y', strtotime($post['created_at']));
            }
        }

        return $posts;
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Lấy danh sách bài viết hiển thị trên trang chủ
 *
 * @param int $limit Số lượng bài viết cần lấy
 * @param int $offset Vị trí bắt đầu
 * @param int|null $status Trạng thái bài viết (1: Published, 0: Draft)
 * @return array Mảng chứa danh sách bài viết
 */
function get_homepage_blog_posts($limit = 3, $offset = 0, $status = 1) {
    return get_blog_posts($limit, $offset, null, null, null, $status, 1);
}

/**
 * Lấy danh sách bình luận của bài viết
 *
 * @param int $post_id ID bài viết
 * @param int $status Trạng thái bình luận (1: Đã duyệt, 0: Chờ duyệt)
 * @return array Danh sách bình luận
 */
function get_post_comments($post_id, $status = 1) {
    global $conn;

    try {
        $stmt = $conn->prepare("
            SELECT c.*, u.full_name, u.avatar
            FROM blog_comments c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE c.post_id = :post_id AND c.status = :status AND c.parent_id IS NULL
            ORDER BY c.created_at DESC
        ");
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->bindParam(':status', $status, PDO::PARAM_INT);
        $stmt->execute();

        $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Lấy các bình luận phản hồi
        foreach ($comments as &$comment) {
            $comment['replies'] = get_comment_replies($comment['id'], $status);
        }

        return $comments;
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Lấy danh sách phản hồi của một bình luận
 *
 * @param int $parent_id ID bình luận cha
 * @param int $status Trạng thái bình luận (1: Đã duyệt, 0: Chờ duyệt)
 * @return array Danh sách phản hồi
 */
function get_comment_replies($parent_id, $status = 1) {
    global $conn;

    try {
        $stmt = $conn->prepare("
            SELECT c.*, u.full_name, u.avatar
            FROM blog_comments c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE c.parent_id = :parent_id AND c.status = :status
            ORDER BY c.created_at ASC
        ");
        $stmt->bindParam(':parent_id', $parent_id, PDO::PARAM_INT);
        $stmt->bindParam(':status', $status, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Thêm bình luận mới
 *
 * @param array $data Dữ liệu bình luận
 * @return bool|int ID bình luận mới hoặc false nếu thất bại
 */
function add_comment($data) {
    global $conn;

    try {
        $sql = "INSERT INTO blog_comments (post_id, user_id, parent_id, author_name, author_email, content, status)
                VALUES (:post_id, :user_id, :parent_id, :author_name, :author_email, :content, :status)";

        $stmt = $conn->prepare($sql);

        $stmt->bindParam(':post_id', $data['post_id'], PDO::PARAM_INT);
        $stmt->bindParam(':user_id', $data['user_id'], PDO::PARAM_INT);
        $stmt->bindParam(':parent_id', $data['parent_id'], PDO::PARAM_INT);
        $stmt->bindParam(':author_name', $data['author_name']);
        $stmt->bindParam(':author_email', $data['author_email']);
        $stmt->bindParam(':content', $data['content']);
        $stmt->bindParam(':status', $data['status'], PDO::PARAM_INT);

        if ($stmt->execute()) {
            return $conn->lastInsertId();
        }

        return false;
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return false;
    }
}