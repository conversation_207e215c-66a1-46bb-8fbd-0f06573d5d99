/*
 * Luxury Mobile Menu CSS for Nội Thất B<PERSON>ng Vũ
 * Modern, elegant mobile menu for furniture industry
 */

/* Mobile Menu Container */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    max-width: 400px;
    height: 100vh;
    background-color: var(--white);
    z-index: var(--z-modal);
    overflow: hidden;
    transition: right 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    box-shadow: var(--shadow-xl);
    display: flex;
    flex-direction: column;
}

.mobile-menu.active {
    right: 0;
}

/* Menu chính */
.mobile-menu-main {
    position: relative;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

/* Prevent body scroll when menu is open */
body.menu-open {
    overflow: hidden;
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: var(--z-fixed);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    -webkit-backdrop-filter: blur(3px);
    backdrop-filter: blur(3px);
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Menu Close Button */
.mobile-menu-close {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background-color: var(--ultra-light-gray);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--dark-gray);
    font-size: var(--text-xl);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    z-index: 1;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.mobile-menu-close:hover {
    color: var(--primary);
    background-color: var(--primary-ultra-light);
    transform: rotate(90deg);
}

/* Mobile Search */
.mobile-search {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--light-gray);
}

.mobile-search-form {
    position: relative;
    width: 100%;
}

.mobile-search-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-xl);
    border: 2px solid transparent;
    border-radius: var(--radius-full);
    background-color: var(--ultra-light-gray);
    color: var(--dark-gray);
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: var(--text-sm);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.mobile-search-input::placeholder {
    color: var(--medium-gray);
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.mobile-search-input:focus::placeholder {
    opacity: 0.5;
}

.mobile-search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 4px 10px rgba(243, 115, 33, 0.1);
    background-color: var(--white);
}

.mobile-search-button {
    position: absolute;
    right: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--primary);
    border: none;
    color: var(--white);
    width: 36px;
    height: 36px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.mobile-search-button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-50%) scale(1.05);
}

/* Mobile Menu Header */
.mobile-menu-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--light-gray);
    display: flex;
    align-items: center;
}

.mobile-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.mobile-logo-image {
    width: 40px;
    height: 40px;
    margin-right: var(--spacing-md);
}

.mobile-logo-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.mobile-logo-text {
    display: flex;
    flex-direction: column;
}

.mobile-logo-title {
    font-size: var(--text-md);
    font-weight: 800;
    color: var(--primary);
    margin: 0;
    line-height: 1.2;
    text-transform: uppercase;
}

.mobile-logo-tagline {
    font-size: var(--text-xs);
    font-weight: 500;
    color: var(--medium-gray);
    margin: var(--spacing-xs) 0 0;
    line-height: 1;
}

/* Mobile Menu Navigation */
.mobile-menu-nav {
    padding: var(--spacing-lg);
    flex: 1;
}

.mobile-menu-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.mobile-menu-item {
    margin-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--ultra-light-gray);
}

.mobile-menu-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--dark-gray);
    text-decoration: none;
    font-size: var(--text-lg);
    font-weight: 500;
    padding: var(--spacing-md) 0;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    border-radius: var(--radius-sm);
}

.mobile-menu-link::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    height: 0;
    width: 3px;
    background-color: var(--primary);
    transform: translateY(-50%);
    transition: height 0.3s ease;
    border-radius: 3px;
}

.mobile-menu-link:hover,
.mobile-menu-item.active>.mobile-menu-link {
    color: var(--primary);
    padding-left: var(--spacing-sm);
}

.mobile-menu-link:hover::before,
.mobile-menu-item.active>.mobile-menu-link::before {
    height: 70%;
}

.mobile-menu-link i {
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.mobile-menu-item.active>.mobile-menu-link i.fa-chevron-down {
    transform: rotate(180deg);
}

.mobile-badge {
    position: absolute;
    top: var(--spacing-md);
    right: 0;
    background-color: var(--primary);
    color: var(--white);
    font-size: var(--text-xs);
    font-weight: 600;
    min-width: 18px;
    height: 18px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--spacing-xs);
}

/* Mobile Submenu */
.mobile-submenu {
    list-style: none;
    margin: 0;
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background-color: var(--white);
}

.mobile-menu-item.active>.mobile-submenu {
    max-height: 1000px;
    margin-bottom: var(--spacing-md);
}

/* Submenu Header with Back Button */
.mobile-submenu-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--mobile-submenu-header-bg);
    border-bottom: var(--mobile-submenu-header-border);
    position: sticky;
    top: 0;
    z-index: 2;
}

.mobile-menu-back {
    background: none;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-gray);
    margin-right: var(--spacing-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.mobile-menu-back:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--primary);
}

.mobile-submenu-header span {
    font-weight: 600;
    font-size: var(--text-md);
    color: var(--dark-gray);
    flex: 1;
}

/* Submenu Items */
.mobile-submenu-item {
    margin-bottom: 0;
    border-bottom: 1px solid var(--ultra-light-gray);
}

.mobile-submenu-item:last-child {
    border-bottom: none;
}

.mobile-submenu-link {
    display: flex;
    align-items: center;
    color: var(--medium-gray);
    text-decoration: none;
    font-size: var(--text-md);
    font-weight: 400;
    padding: var(--spacing-md) var(--spacing-lg);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
}

.mobile-submenu-link i {
    margin-right: var(--spacing-sm);
    width: 20px;
    text-align: center;
    color: var(--primary);
    font-size: 0.9em;
}

.mobile-submenu-link i.fa-chevron-down,
.mobile-submenu-link i.fa-chevron-up {
    margin-left: auto;
    margin-right: 0;
    color: var(--medium-gray);
    transition: transform 0.3s ease;
}

.mobile-submenu-link:hover,
.mobile-submenu-item.active>.mobile-submenu-link {
    color: var(--primary);
    background-color: rgba(243, 115, 33, 0.05);
}

/* Nested Submenu (Child Categories) */
.mobile-submenu-child {
    background-color: var(--mobile-submenu-child-bg);
    padding-left: 0;
}

.mobile-submenu-child .mobile-submenu-link {
    padding-left: var(--spacing-xl);
}

/* Mobile Menu Footer */
.mobile-menu-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--light-gray);
    background-color: var(--ultra-light-gray);
    position: relative;
    overflow: hidden;
}



.mobile-contact-info {
    margin-bottom: var(--spacing-lg);
}

.mobile-contact-info h3 {
    font-size: var(--text-md);
    font-weight: 600;
    color: var(--dark-gray);
    margin: 0 0 var(--spacing-md);
    position: relative;
    display: inline-block;
}

.mobile-contact-info h3::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--primary);
}

.mobile-contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
    color: var(--medium-gray);
    font-size: var(--text-sm);
}

.mobile-contact-item i {
    color: var(--primary);
    margin-right: var(--spacing-sm);
    width: 20px;
    text-align: center;
    flex-shrink: 0;
    margin-top: 3px;
}

.mobile-contact-item a {
    color: var(--medium-gray);
    text-decoration: none;
    transition: var(--transition-fast);
}

.mobile-contact-item a:hover {
    color: var(--primary);
}

.mobile-contact-item span {
    line-height: 1.5;
    display: inline-block;
    word-break: break-word;
}

.mobile-social {
    display: flex;
    gap: var(--spacing-md);
}

.mobile-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: var(--radius-full);
    background-color: var(--white);
    color: var(--medium-gray);
    text-decoration: none;
    transition: var(--transition-normal);
}

.mobile-social-link:hover {
    background-color: var(--primary);
    color: var(--white);
    transform: translateY(-2px);
}

/* Mobile Menu CTA Button */
.mobile-cta {
    margin-top: var(--spacing-lg);
}

.mobile-cta-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: var(--spacing-md);
    background-color: var(--primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius-md);
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: var(--text-md);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(243, 115, 33, 0.2);
}

.mobile-cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0) 100%);
    transition: left 0.7s ease;
}

.mobile-cta-button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(243, 115, 33, 0.3);
}

.mobile-cta-button:hover::before {
    left: 100%;
}

.mobile-cta-button i {
    margin-right: var(--spacing-sm);
    transition: transform 0.3s ease;
}

.mobile-cta-button:hover i {
    transform: scale(1.2);
}

/* Animation for mobile menu items */
@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.mobile-menu.active .mobile-menu-item {
    animation: fadeInRight 0.3s ease forwards;
    opacity: 0;
}

.mobile-menu.active .mobile-menu-item:nth-child(1) {
    animation-delay: 0.1s;
}

.mobile-menu.active .mobile-menu-item:nth-child(2) {
    animation-delay: 0.15s;
}

.mobile-menu.active .mobile-menu-item:nth-child(3) {
    animation-delay: 0.2s;
}

.mobile-menu.active .mobile-menu-item:nth-child(4) {
    animation-delay: 0.25s;
}

.mobile-menu.active .mobile-menu-item:nth-child(5) {
    animation-delay: 0.3s;
}

.mobile-menu.active .mobile-menu-item:nth-child(6) {
    animation-delay: 0.35s;
}

.mobile-menu.active .mobile-menu-item:nth-child(7) {
    animation-delay: 0.4s;
}

.mobile-menu.active .mobile-menu-item:nth-child(8) {
    animation-delay: 0.45s;
}

.mobile-menu.active .mobile-contact-info {
    animation: fadeInRight 0.3s ease forwards;
    animation-delay: 0.5s;
    opacity: 0;
}

.mobile-menu.active .mobile-social {
    animation: fadeInRight 0.3s ease forwards;
    animation-delay: 0.55s;
    opacity: 0;
}

.mobile-menu.active .mobile-cta {
    animation: fadeInRight 0.3s ease forwards;
    animation-delay: 0.6s;
    opacity: 0;
}