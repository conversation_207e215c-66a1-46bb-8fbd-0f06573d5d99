<?php
// Include header
include_once '../admin/partials/header.php';

// Kiểm tra quyền admin
if (!is_admin()) {
    redirect(BASE_URL);
}

// Include blog functions
include_once '../includes/blog-functions.php';

// Khởi tạo biến
$errors = [];
$success = '';
$post_id = isset($_GET['post_id']) ? (int)$_GET['post_id'] : 0;

// Kiểm tra bài viết tồn tại
if (empty($post_id)) {
    set_flash_message('error', 'ID bài viết không hợp lệ.');
    redirect(BASE_URL . '/admin/blog-posts.php');
}

try {
    $stmt = $conn->prepare("SELECT id, title, slug FROM blog_posts WHERE id = :id");
    $stmt->bindParam(':id', $post_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $post = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$post) {
        set_flash_message('error', 'Không tìm thấy bài viết.');
        redirect(BASE_URL . '/admin/blog-posts.php');
    }
} catch (PDOException $e) {
    set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
    redirect(BASE_URL . '/admin/blog-posts.php');
}

// Lấy danh sách sản phẩm liên quan
try {
    $stmt = $conn->prepare("
        SELECT p.* 
        FROM products p
        JOIN blog_post_products bp ON p.id = bp.product_id
        WHERE bp.post_id = :post_id
        ORDER BY p.name ASC
    ");
    $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $related_products = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $errors[] = 'Có lỗi xảy ra khi lấy danh sách sản phẩm liên quan: ' . $e->getMessage();
    $related_products = [];
}

// Xử lý thêm sản phẩm liên quan
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_product'])) {
    $product_id = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;
    
    if (empty($product_id)) {
        $errors[] = 'Vui lòng chọn sản phẩm.';
    } else {
        try {
            // Kiểm tra sản phẩm đã được liên kết chưa
            $stmt = $conn->prepare("SELECT * FROM blog_post_products WHERE post_id = :post_id AND product_id = :product_id");
            $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
            $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $errors[] = 'Sản phẩm này đã được liên kết với bài viết.';
            } else {
                // Thêm liên kết mới
                $stmt = $conn->prepare("INSERT INTO blog_post_products (post_id, product_id) VALUES (:post_id, :product_id)");
                $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
                $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
                $stmt->execute();
                
                $success = 'Thêm sản phẩm liên quan thành công.';
                
                // Làm mới trang để hiển thị sản phẩm mới
                redirect(BASE_URL . '/admin/blog-post-products.php?post_id=' . $post_id);
            }
        } catch (PDOException $e) {
            $errors[] = 'Có lỗi xảy ra: ' . $e->getMessage();
        }
    }
}

// Xử lý xóa sản phẩm liên quan
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['product_id'])) {
    $product_id = (int)$_GET['product_id'];
    
    try {
        $stmt = $conn->prepare("DELETE FROM blog_post_products WHERE post_id = :post_id AND product_id = :product_id");
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
        $stmt->execute();
        
        set_flash_message('success', 'Xóa sản phẩm liên quan thành công.');
        redirect(BASE_URL . '/admin/blog-post-products.php?post_id=' . $post_id);
    } catch (PDOException $e) {
        set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        redirect(BASE_URL . '/admin/blog-post-products.php?post_id=' . $post_id);
    }
}

// Lấy danh sách tất cả sản phẩm
try {
    $stmt = $conn->prepare("
        SELECT p.id, p.name, p.price, p.price_type, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 1
        ORDER BY p.name ASC
    ");
    $stmt->execute();
    
    $all_products = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $errors[] = 'Có lỗi xảy ra khi lấy danh sách sản phẩm: ' . $e->getMessage();
    $all_products = [];
}
?>

<!-- Begin Page Content -->
<div class="container-fluid">

    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Quản lý sản phẩm liên quan</h1>
        <a href="<?php echo BASE_URL; ?>/admin/blog-post-edit.php?id=<?php echo $post_id; ?>" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại chỉnh sửa bài viết
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin bài viết</h6>
                </div>
                <div class="card-body">
                    <h5><?php echo htmlspecialchars($post['title']); ?></h5>
                    <p>
                        <a href="<?php echo BASE_URL; ?>/blog/<?php echo $post['slug']; ?>" target="_blank">
                            <i class="fas fa-external-link-alt"></i> Xem bài viết
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success">
            <?php echo $success; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?php echo $_SESSION['flash_type']; ?>">
            <?php echo $_SESSION['flash_message']; ?>
        </div>
        <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thêm sản phẩm liên quan</h6>
                </div>
                <div class="card-body">
                    <form action="" method="POST">
                        <div class="form-group">
                            <label for="product_id">Chọn sản phẩm</label>
                            <select class="form-control" id="product_id" name="product_id" required>
                                <option value="">-- Chọn sản phẩm --</option>
                                <?php foreach ($all_products as $product): ?>
                                    <option value="<?php echo $product['id']; ?>">
                                        <?php echo htmlspecialchars($product['name']); ?> 
                                        (<?php echo $product['price_type'] === 'contact' ? 'Liên hệ' : number_format($product['price']) . '₫'; ?>)
                                        - <?php echo htmlspecialchars($product['category_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <button type="submit" name="add_product" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Thêm sản phẩm
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Danh sách sản phẩm liên quan</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($related_products)): ?>
                        <p class="text-center">Chưa có sản phẩm liên quan nào.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Hình ảnh</th>
                                        <th>Tên sản phẩm</th>
                                        <th>Giá</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($related_products as $product): ?>
                                        <tr>
                                            <td><?php echo $product['id']; ?></td>
                                            <td>
                                                <?php if (!empty($product['image'])): ?>
                                                    <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>" class="img-thumbnail" style="max-width: 50px;">
                                                <?php else: ?>
                                                    <img src="<?php echo BASE_URL; ?>/assets/images/product-placeholder.jpg" alt="<?php echo $product['name']; ?>" class="img-thumbnail" style="max-width: 50px;">
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($product['name']); ?></td>
                                            <td>
                                                <?php if ($product['price_type'] === 'contact'): ?>
                                                    <span class="badge badge-info">Liên hệ</span>
                                                <?php else: ?>
                                                    <?php if (!empty($product['sale_price'])): ?>
                                                        <span class="text-danger"><?php echo number_format($product['sale_price']); ?>₫</span>
                                                        <small class="text-muted"><del><?php echo number_format($product['price']); ?>₫</del></small>
                                                    <?php else: ?>
                                                        <span><?php echo number_format($product['price']); ?>₫</span>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="<?php echo BASE_URL; ?>/admin/blog-post-products.php?post_id=<?php echo $post_id; ?>&action=delete&product_id=<?php echo $product['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Bạn có chắc chắn muốn xóa sản phẩm này khỏi bài viết?');">
                                                    <i class="fas fa-trash"></i> Xóa
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /.container-fluid -->

<?php
// Include footer
include_once '../admin/partials/footer.php';
?>
