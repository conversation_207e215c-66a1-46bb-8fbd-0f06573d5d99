<?php
// Bắt đầu session và include các file cần thiết
require_once '../includes/init.php';

// Xử lý cập nhật thông tin trước khi xuất bất kỳ HTML nào
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Kiểm tra CSRF token
    if (!check_csrf_token($_POST['csrf_token'])) {
        set_flash_message('error', 'Phiên làm việc đã hết hạn. Vui lòng thử lại.');
        redirect(BASE_URL . '/account/profile.php');
    }

    // Xác định loại form đang được gửi
    $form_type = isset($_POST['form_type']) ? $_POST['form_type'] : '';

    if ($form_type === 'avatar') {
        // Xử lý upload avatar
        if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/avatars/';
            $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            $upload_result = upload_file($_FILES['avatar'], $upload_dir, $allowed_types);

            if ($upload_result['success']) {
                // Cập nhật avatar trong database
                $avatar_result = update_user_avatar($_SESSION['user_id'], $upload_result['filename']);

                if ($avatar_result['success']) {
                    set_flash_message('success', 'Cập nhật avatar thành công');
                } else {
                    set_flash_message('error', $avatar_result['message']);
                }
            } else {
                set_flash_message('error', $upload_result['message']);
            }
        } else {
            set_flash_message('error', 'Vui lòng chọn ảnh để tải lên');
        }
    } else {
        // Xử lý cập nhật thông tin cá nhân
        // Lấy dữ liệu từ form
        $full_name = sanitize($_POST['full_name']);
        $phone = sanitize($_POST['phone']);
        $address = sanitize($_POST['address']);

        // Lấy dữ liệu địa chỉ mới
        $province_code = isset($_POST['province']) ? (int)$_POST['province'] : null;
        $district_code = isset($_POST['district']) ? (int)$_POST['district'] : null;
        $ward_code = isset($_POST['ward']) ? (int)$_POST['ward'] : null;
        $address_detail = isset($_POST['address_detail']) ? sanitize($_POST['address_detail']) : null;

        // Kiểm tra dữ liệu
        if (empty($full_name)) {
            set_flash_message('error', 'Vui lòng nhập họ và tên.');
            redirect(BASE_URL . '/account/profile.php');
        }

        // Cập nhật thông tin
        $result = update_user_profile(
            $_SESSION['user_id'],
            $full_name,
            $phone,
            $address,
            $province_code,
            $district_code,
            $ward_code,
            $address_detail
        );

        if ($result['success']) {
            // Cập nhật session
            $_SESSION['full_name'] = $full_name;

            set_flash_message('success', $result['message']);
        } else {
            set_flash_message('error', $result['message']);
        }
    }

    redirect(BASE_URL . '/account/profile.php');
}

// Kiểm tra đăng nhập
if (!is_logged_in()) {
    // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
    $_SESSION['redirect_url'] = current_url();

    set_flash_message('error', 'Vui lòng đăng nhập để truy cập trang này.');
    redirect(BASE_URL . '/login.php');
}

// Lấy thông tin người dùng
$user = get_user_by_id($_SESSION['user_id']);

// Thiết lập tiêu đề trang
$page_title = 'Tài khoản của tôi';
$page_description = 'Quản lý thông tin tài khoản của bạn tại Nội Thất Băng Vũ';

// Include header
include_once '../partials/header.php';
?>

<!-- Breadcrumb - Thiết kế hiện đại -->
<div class="modern-breadcrumb">
    <div class="container mx-auto px-4">
        <div class="breadcrumb-wrapper">
            <div class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>" class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-home"></i>
                    </span>
                    <span>Trang chủ</span>
                </a>
            </div>
            <div class="breadcrumb-divider">
                <i class="fas fa-chevron-right"></i>
            </div>
            <div class="breadcrumb-item active">
                <span class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-user-circle"></i>
                    </span>
                    <span>Tài khoản của tôi</span>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Link CSS cho breadcrumb hiện đại -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-breadcrumb.css">

<!-- Account - Thiết kế hiện đại -->
<div class="py-10 bg-gradient-to-b from-white to-gray-50">
    <div class="container mx-auto px-4">
        <!-- Tiêu đề trang với badge giống trang chủ -->
        <div class="mb-8 text-center md:text-left">
            <div class="section-title-badge inline-flex items-center bg-primary/10 text-primary text-sm font-medium px-4 py-2 rounded-full mb-4">
                <span class="flex w-2 h-2 bg-primary rounded-full mr-2 animate-pulse"></span>
                Thông tin cá nhân
            </div>
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 relative inline-block">
                <span class="relative z-10">Tài khoản của tôi</span>
                <span class="absolute bottom-0 left-0 w-full h-1 bg-primary/30 rounded-full"></span>
            </h1>
            <p class="text-gray-600 mt-2 max-w-2xl mx-auto md:mx-0">Quản lý thông tin cá nhân và cập nhật tài khoản của bạn tại đây</p>
        </div>

        <div class="flex flex-col md:flex-row md:space-x-6">
            <!-- Sidebar - Thiết kế hiện đại -->
            <div class="md:w-1/4 mb-6 md:mb-0">
                <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-primary/20 transition-all duration-300">
                    <!-- Phần header với avatar và thông tin - Thiết kế mới -->
                    <div class="relative">
                        <!-- Background pattern -->
                        <div class="absolute inset-0 bg-gradient-to-br from-primary to-primary-dark opacity-90"></div>

                        <!-- Pattern overlay -->
                        <div class="absolute inset-0 opacity-10 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xIj48cGF0aCBkPSJNMzYgMzRjMC0yLjIgMS44LTQgNC00czQgMS44IDQgNC0xLjggNC00IDQtNC0xLjgtNC00eiIvPjxwYXRoIGQ9Ik0xNiAxNmMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00em0xNiAwYzIuMiAwIDQgMS44IDQgNHMtMS44IDQtNCA0LTQtMS44LTQtNCAxLjgtNCA0LTR6bTE2IDBjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHpNMTYgMzJjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHptMTYgMGMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00eiIvPjxwYXRoIGQ9Ik0xNiA0OGMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00em0xNiAwYzIuMiAwIDQgMS44IDQgNHMtMS44IDQtNCA0LTQtMS44LTQtNCAxLjgtNCA0LTR6bTE2IDBjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHpNMTYgMTZjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHptMTYgMGMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00em0xNiAwYzIuMiAwIDQgMS44IDQgNHMtMS44IDQtNCA0LTQtMS44LTQtNCAxLjgtNCA0LTR6TTAgMGg2MHY2MEgweiIvPjwvZz48L2c+PC9zdmc+')]"></div>
                    </div>

                    <!-- Avatar lớn căn giữa -->
                    <div class="flex flex-col items-center px-6 pt-8 pb-6 relative">
                        <div class="w-40 h-40 rounded-xl bg-white p-1.5 shadow-lg mb-5 transform transition-transform hover:scale-105 duration-300 overflow-hidden relative z-10">
                            <div class="w-full h-full rounded-xl overflow-hidden bg-gradient-to-r from-primary/20 to-primary/10 flex items-center justify-center text-4xl font-bold text-primary">
                                <?php if (!empty($user['avatar'])): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $user['avatar']; ?>" alt="Avatar" class="w-full h-full object-cover">
                                <?php else: ?>
                                    <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Thông tin người dùng -->
                        <div class="text-center w-full">
                            <h2 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $user['full_name']; ?></h2>
                            <p class="text-gray-500 text-sm flex items-center justify-center mb-3">
                                <i class="fas fa-envelope mr-2 text-primary/70"></i>
                                <?php echo $user['email']; ?>
                            </p>

                            <!-- Badge thành viên -->
                            <div class="inline-flex items-center bg-primary/10 text-primary text-xs font-medium px-3 py-1.5 rounded-full">
                                <i class="fas fa-user-check mr-1.5"></i> Thành viên
                            </div>
                        </div>
                    </div>

                    <!-- Đường phân cách -->
                    <div class="w-full h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>

                    <!-- Menu tài khoản -->
                    <div class="p-4">
                        <ul class="space-y-1">
                            <li>
                                <a href="<?php echo BASE_URL; ?>/account/profile.php"
                                   class="flex items-center px-4 py-3 rounded-lg bg-primary/10 text-primary font-medium transition-all duration-300">
                                    <i class="fas fa-user-circle mr-3 w-5 text-center"></i>
                                    <span>Thông tin tài khoản</span>
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/account/orders.php"
                                   class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-primary transition-all duration-300">
                                    <i class="fas fa-shopping-bag mr-3 w-5 text-center"></i>
                                    <span>Đơn hàng của tôi</span>
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/account/change-password.php"
                                   class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-primary transition-all duration-300">
                                    <i class="fas fa-key mr-3 w-5 text-center"></i>
                                    <span>Đổi mật khẩu</span>
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/logout.php"
                                   class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-red-50 hover:text-red-500 transition-all duration-300">
                                    <i class="fas fa-sign-out-alt mr-3 w-5 text-center"></i>
                                    <span>Đăng xuất</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="md:w-3/4">
                <!-- Phần Avatar - Thiết kế hiện đại -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 hover:border-primary/20 transition-all duration-300 overflow-hidden mb-6">
                    <!-- Header với gradient -->
                    <div class="bg-gradient-to-r from-primary/5 to-primary/10 px-6 py-5 border-b border-gray-100">
                        <div class="flex items-center">
                            <span class="w-12 h-12 rounded-lg bg-white flex items-center justify-center text-primary shadow-sm mr-4">
                                <i class="fas fa-user-circle text-xl"></i>
                            </span>
                            <div>
                                <h2 class="text-xl font-bold text-gray-800">Ảnh đại diện</h2>
                                <p class="text-gray-500 text-sm">Cập nhật ảnh đại diện của bạn</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <div class="flex flex-col md:flex-row items-center gap-6">
                            <!-- Avatar hiển thị -->
                            <div class="relative group">
                                <div class="w-32 h-32 rounded-xl bg-gradient-to-r from-primary to-primary-dark p-1 shadow-lg overflow-hidden">
                                    <div class="w-full h-full rounded-xl overflow-hidden bg-white flex items-center justify-center text-4xl font-bold text-primary">
                                        <?php if (!empty($user['avatar'])): ?>
                                            <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $user['avatar']; ?>" alt="Avatar" class="w-full h-full object-cover">
                                        <?php else: ?>
                                            <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Hiệu ứng hover -->
                                <div class="absolute inset-0 rounded-xl bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <span class="text-white text-sm font-medium">
                                        <i class="fas fa-camera"></i> Thay đổi
                                    </span>
                                </div>
                            </div>

                            <!-- Form upload -->
                            <div class="flex-1 w-full md:w-auto avatar-upload-form">
                                <form action="<?php echo BASE_URL; ?>/account/profile.php" method="POST" enctype="multipart/form-data" class="validate-form">
                                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                    <input type="hidden" name="form_type" value="avatar">

                                    <div class="mb-4">
                                        <label for="avatar" class="block text-gray-700 font-medium mb-2">Chọn ảnh đại diện mới</label>

                                        <!-- Custom file input -->
                                        <div class="relative">
                                            <input type="file" name="avatar" id="avatar"
                                                   class="absolute inset-0 opacity-0 w-full h-full cursor-pointer z-10"
                                                   accept="image/jpeg,image/png,image/gif,image/webp">

                                            <div class="bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 flex items-center text-gray-700">
                                                <span class="file-name truncate max-w-[calc(100%-120px)]">Chưa có file nào được chọn</span>
                                                <span class="bg-gray-200 hover:bg-gray-300 rounded-lg px-3 py-1.5 text-sm font-medium transition-colors duration-200 ml-auto mr-8 flex-shrink-0">
                                                    Chọn file
                                                </span>
                                            </div>
                                        </div>

                                        <div class="mt-2 flex flex-wrap gap-2">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <i class="fas fa-check-circle mr-1"></i> JPG
                                            </span>
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <i class="fas fa-check-circle mr-1"></i> PNG
                                            </span>
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <i class="fas fa-check-circle mr-1"></i> GIF
                                            </span>
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <i class="fas fa-check-circle mr-1"></i> WEBP
                                            </span>
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                <i class="fas fa-exclamation-circle mr-1"></i> Tối đa 5MB
                                            </span>
                                        </div>
                                    </div>

                                    <button type="submit" class="bg-primary hover:bg-primary-dark text-white font-medium py-2.5 px-5 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 transition duration-300 inline-flex items-center">
                                        <i class="fas fa-cloud-upload-alt mr-2"></i> Cập nhật avatar
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Script để hiển thị tên file và xem trước ảnh khi chọn -->
                <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const fileInput = document.getElementById('avatar');
                    const fileName = document.querySelector('.file-name');

                    // Lấy container cho preview
                    const mainAvatarContainer = document.querySelector('.relative.group .w-full.h-full');
                    const sidebarAvatarContainer = document.querySelector('.flex.flex-col.items-center .w-full.h-full');

                    // Lưu nội dung mặc định
                    let defaultMainContent = '';
                    let defaultSidebarContent = '';

                    if (mainAvatarContainer) {
                        defaultMainContent = mainAvatarContainer.innerHTML;
                    }
                    if (sidebarAvatarContainer) {
                        defaultSidebarContent = sidebarAvatarContainer.innerHTML;
                    }

                    // Thêm nút hủy chọn ảnh
                    const fileInputContainer = document.querySelector('.avatar-upload-form .relative');
                    if (fileInputContainer) {
                        const cancelButton = document.createElement('button');
                        cancelButton.type = 'button';
                        cancelButton.className = 'absolute right-12 top-0 h-full px-2 flex items-center justify-center text-gray-500 hover:text-red-500 transition-colors duration-200 z-20';
                        cancelButton.innerHTML = '<i class="fas fa-times"></i>';
                        cancelButton.style.display = 'none';
                        cancelButton.title = 'Hủy chọn ảnh';

                        fileInputContainer.appendChild(cancelButton);

                        // Xử lý sự kiện khi nhấn nút hủy
                        cancelButton.addEventListener('click', function() {
                            resetFileInput();
                        });

                        // Hiển thị/ẩn nút hủy
                        function toggleCancelButton(show) {
                            cancelButton.style.display = show ? 'flex' : 'none';
                        }

                        // Reset file input
                        function resetFileInput() {
                            fileInput.value = '';
                            fileName.textContent = 'Chưa có file nào được chọn';
                            fileName.title = '';
                            toggleCancelButton(false);

                            // Khôi phục nội dung mặc định
                            if (mainAvatarContainer && defaultMainContent) {
                                mainAvatarContainer.innerHTML = defaultMainContent;
                            }
                            if (sidebarAvatarContainer && defaultSidebarContent) {
                                sidebarAvatarContainer.innerHTML = defaultSidebarContent;
                            }

                            // Xóa badge preview
                            removePreviewBadge();

                            // Ẩn preview section nếu có
                            hidePreviewSection();
                        }

                        // Thêm badge preview
                        function addPreviewBadge() {
                            removePreviewBadge(); // Xóa badge cũ trước

                            const mainAvatarParent = document.querySelector('.relative.group');
                            if (mainAvatarParent) {
                                const previewBadge = document.createElement('div');
                                previewBadge.className = 'preview-badge absolute -top-2 -right-2 bg-primary text-white text-xs font-medium px-2 py-1 rounded-full z-20 shadow-lg animate-pulse';
                                previewBadge.innerHTML = '<i class="fas fa-eye mr-1"></i> Xem trước';
                                mainAvatarParent.appendChild(previewBadge);
                            }
                        }

                        // Xóa badge preview
                        function removePreviewBadge() {
                            const previewBadge = document.querySelector('.preview-badge');
                            if (previewBadge) {
                                previewBadge.remove();
                            }
                        }

                        // Hiển thị preview section
                        function showPreviewSection(imageSrc, fileName, fileSize) {
                            hidePreviewSection(); // Ẩn section cũ trước

                            const avatarCard = document.querySelector('.avatar-upload-form').closest('.bg-white.rounded-xl');
                            if (avatarCard) {
                                const previewSection = document.createElement('div');
                                previewSection.className = 'preview-section mt-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg';
                                previewSection.innerHTML = `
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="text-sm font-semibold text-green-800 flex items-center">
                                            <i class="fas fa-check-circle mr-2"></i>
                                            Ảnh đã chọn - Sẵn sàng tải lên
                                        </h4>
                                        <button type="button" class="preview-close-btn text-green-600 hover:text-red-500 transition-colors duration-200" title="Đóng xem trước">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="flex items-start gap-4">
                                        <div class="flex-shrink-0">
                                            <div class="w-16 h-16 rounded-lg overflow-hidden border-2 border-green-200 shadow-sm">
                                                <img src="${imageSrc}" alt="Preview" class="w-full h-full object-cover">
                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <div class="text-sm font-medium text-gray-800 truncate" title="${fileName}">${fileName}</div>
                                            <div class="text-xs text-gray-500 mt-1">Kích thước: ${fileSize}</div>
                                            <div class="flex items-center mt-2 text-xs text-green-600">
                                                <i class="fas fa-info-circle mr-1"></i>
                                                Nhấn "Cập nhật avatar" để lưu thay đổi
                                            </div>
                                        </div>
                                    </div>
                                `;

                                avatarCard.querySelector('.p-6').appendChild(previewSection);

                                // Thêm sự kiện đóng preview
                                const closeBtn = previewSection.querySelector('.preview-close-btn');
                                closeBtn.addEventListener('click', function() {
                                    resetFileInput();
                                });
                            }
                        }

                        // Ẩn preview section
                        function hidePreviewSection() {
                            const previewSection = document.querySelector('.preview-section');
                            if (previewSection) {
                                previewSection.remove();
                            }
                        }

                        // Format file size
                        function formatFileSize(bytes) {
                            if (bytes === 0) return '0 Bytes';
                            const k = 1024;
                            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                            const i = Math.floor(Math.log(bytes) / Math.log(k));
                            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                        }

                        // Xử lý khi chọn file
                        fileInput.addEventListener('change', function() {
                            if (this.files.length > 0) {
                                const file = this.files[0];

                                // Kiểm tra kích thước file (5MB)
                                if (file.size > 5 * 1024 * 1024) {
                                    alert('Kích thước file không được vượt quá 5MB!');
                                    resetFileInput();
                                    return;
                                }

                                // Kiểm tra định dạng file
                                if (!file.type.match('image.*')) {
                                    alert('Vui lòng chọn file ảnh hợp lệ!');
                                    resetFileInput();
                                    return;
                                }

                                // Xử lý tên file
                                const maxLength = 30;
                                let displayName = file.name;
                                if (file.name.length > maxLength) {
                                    const extension = file.name.split('.').pop();
                                    const nameWithoutExt = file.name.substring(0, file.name.length - extension.length - 1);
                                    displayName = nameWithoutExt.substring(0, maxLength - extension.length - 3) + '...' + '.' + extension;
                                }

                                fileName.textContent = displayName;
                                fileName.title = file.name;
                                toggleCancelButton(true);

                                // Đọc và hiển thị ảnh
                                const reader = new FileReader();
                                reader.onload = function(e) {
                                    const imageSrc = e.target.result;

                                    // Cập nhật avatar chính
                                    if (mainAvatarContainer) {
                                        mainAvatarContainer.innerHTML = `<img src="${imageSrc}" alt="Avatar Preview" class="w-full h-full object-cover">`;
                                    }

                                    // Cập nhật avatar sidebar
                                    if (sidebarAvatarContainer) {
                                        sidebarAvatarContainer.innerHTML = `<img src="${imageSrc}" alt="Avatar Preview" class="w-full h-full object-cover">`;
                                    }

                                    // Thêm badge và preview section
                                    addPreviewBadge();
                                    showPreviewSection(imageSrc, file.name, formatFileSize(file.size));
                                };

                                reader.readAsDataURL(file);
                            } else {
                                resetFileInput();
                            }
                        });
                    }
                });
                </script>

                <!-- Phần Thông tin tài khoản - Thiết kế hiện đại -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 hover:border-primary/20 transition-all duration-300 overflow-hidden">
                    <!-- Header với gradient -->
                    <div class="bg-gradient-to-r from-primary/5 to-primary/10 px-6 py-5 border-b border-gray-100">
                        <div class="flex items-center">
                            <span class="w-12 h-12 rounded-lg bg-white flex items-center justify-center text-primary shadow-sm mr-4">
                                <i class="fas fa-id-card text-xl"></i>
                            </span>
                            <div>
                                <h2 class="text-xl font-bold text-gray-800">Thông tin tài khoản</h2>
                                <p class="text-gray-500 text-sm">Cập nhật thông tin cá nhân của bạn</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <form action="<?php echo BASE_URL; ?>/account/profile.php" method="POST" class="validate-form profile-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="form_type" value="profile">

                            <!-- Phần thông tin không thể thay đổi -->
                            <div class="bg-gray-50 rounded-lg p-5 mb-6 border border-gray-200">
                                <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 flex items-center">
                                    <i class="fas fa-lock text-primary/70 mr-2"></i> Thông tin không thể thay đổi
                                </h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <!-- Tên đăng nhập -->
                                    <div class="bg-white rounded-lg p-4 border border-gray-200 flex items-center">
                                        <div class="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                                            <i class="fas fa-user text-blue-500"></i>
                                        </div>
                                        <div>
                                            <label class="block text-xs font-medium text-gray-500 mb-1">Tên đăng nhập</label>
                                            <div class="font-medium text-gray-800"><?php echo $user['username']; ?></div>
                                        </div>
                                    </div>

                                    <!-- Email -->
                                    <div class="bg-white rounded-lg p-4 border border-gray-200 flex items-center">
                                        <div class="w-10 h-10 rounded-full bg-indigo-50 flex items-center justify-center mr-3">
                                            <i class="fas fa-envelope text-indigo-500"></i>
                                        </div>
                                        <div>
                                            <label class="block text-xs font-medium text-gray-500 mb-1">Email</label>
                                            <div class="font-medium text-gray-800"><?php echo $user['email']; ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Phần thông tin có thể chỉnh sửa -->
                            <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 flex items-center">
                                <i class="fas fa-edit text-primary/70 mr-2"></i> Thông tin có thể chỉnh sửa
                            </h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Cột trái -->
                                <div class="space-y-5">
                                    <!-- Họ và tên -->
                                    <div>
                                        <label for="full_name" class="flex items-center text-gray-700 font-medium mb-2">
                                            Họ và tên
                                            <span class="text-red-500 ml-1">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-user-edit text-gray-400"></i>
                                            </div>
                                            <input type="text" name="full_name" id="full_name"
                                                   class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:border-primary focus:ring focus:ring-primary/30 transition-all duration-300"
                                                   value="<?php echo $user['full_name']; ?>" required>
                                        </div>
                                    </div>

                                    <!-- Số điện thoại -->
                                    <div>
                                        <label for="phone" class="flex items-center text-gray-700 font-medium mb-2">
                                            Số điện thoại
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-phone-alt text-gray-400"></i>
                                            </div>
                                            <input type="text" name="phone" id="phone"
                                                   class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:border-primary focus:ring focus:ring-primary/30 transition-all duration-300"
                                                   value="<?php echo $user['phone']; ?>" placeholder="Nhập số điện thoại của bạn">
                                        </div>
                                    </div>
                                </div>

                                <!-- Cột phải -->
                                <div>
                                    <!-- Địa chỉ -->
                                    <div>
                                        <label class="flex items-center text-gray-700 font-medium mb-2">
                                            Địa chỉ
                                        </label>

                                        <!-- Chọn Tỉnh/Thành phố -->
                                        <div class="mb-3">
                                            <label for="province" class="block text-sm text-gray-600 mb-1">Tỉnh/Thành phố</label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <i class="fas fa-city text-gray-400"></i>
                                                </div>
                                                <select id="province" name="province"
                                                        class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:border-primary focus:ring focus:ring-primary/30 transition-all duration-300"
                                                        data-selected="<?php echo $user['province_code']; ?>">
                                                    <option value="">-- Chọn Tỉnh/Thành phố --</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Chọn Quận/Huyện -->
                                        <div class="mb-3">
                                            <label for="district" class="block text-sm text-gray-600 mb-1">Quận/Huyện</label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <i class="fas fa-building text-gray-400"></i>
                                                </div>
                                                <select id="district" name="district"
                                                        class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:border-primary focus:ring focus:ring-primary/30 transition-all duration-300"
                                                        data-selected="<?php echo $user['district_code']; ?>" disabled>
                                                    <option value="">-- Chọn Quận/Huyện --</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Chọn Phường/Xã -->
                                        <div class="mb-3">
                                            <label for="ward" class="block text-sm text-gray-600 mb-1">Phường/Xã</label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <i class="fas fa-home text-gray-400"></i>
                                                </div>
                                                <select id="ward" name="ward"
                                                        class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:border-primary focus:ring focus:ring-primary/30 transition-all duration-300"
                                                        data-selected="<?php echo $user['ward_code']; ?>" disabled>
                                                    <option value="">-- Chọn Phường/Xã --</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Địa chỉ chi tiết -->
                                        <div class="mb-3">
                                            <label for="address_detail" class="block text-sm text-gray-600 mb-1">Địa chỉ chi tiết</label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <i class="fas fa-map-marker-alt text-gray-400"></i>
                                                </div>
                                                <input type="text" id="address_detail" name="address_detail"
                                                       class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:border-primary focus:ring focus:ring-primary/30 transition-all duration-300"
                                                       placeholder="Nhập số nhà, tên đường..."
                                                       value="<?php echo $user['address_detail']; ?>">
                                            </div>
                                        </div>

                                        <!-- Xem trước địa chỉ đầy đủ -->
                                        <?php if (!empty($user['address'])): ?>
                                        <div class="mt-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                                            <div class="flex items-start">
                                                <i class="fas fa-check-circle text-primary mt-1 mr-2"></i>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-700 mb-1">Địa chỉ đầy đủ:</div>
                                                    <div id="address_preview" class="text-gray-600"><?php echo $user['address']; ?></div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                        <!-- Input ẩn để lưu địa chỉ đầy đủ -->
                                        <input type="hidden" name="address" id="address" value="<?php echo $user['address']; ?>">
                                    </div>
                                </div>
                            </div>

                            <!-- Nút cập nhật -->
                            <div class="mt-8 flex justify-end">
                                <button type="submit"
                                        class="bg-primary hover:bg-primary-dark text-white font-medium py-3 px-8 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-300 inline-flex items-center shadow-md hover:shadow-lg">
                                    <i class="fas fa-save mr-2"></i>
                                    Cập nhật thông tin
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Script cho chọn địa chỉ -->
<script src="<?php echo BASE_URL; ?>/assets/js/address-selector.js"></script>



<!-- CSS tùy chỉnh cho trang profile -->
<style>
    /* Biến CSS cho màu sắc chính */
    :root {
        --primary: #F37321;
        --primary-dark: #E05E00;
        --primary-light: #FF9D5C;
    }

    /* Hiệu ứng hover cho các card */
    .bg-white.rounded-xl {
        transition: all 0.3s ease;
    }

    .bg-white.rounded-xl:hover {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Hiệu ứng cho nút */
    .inline-flex.items-center.px-6.py-3,
    .inline-flex.items-center.px-8.py-3 {
        transition: all 0.3s ease;
    }

    .inline-flex.items-center.px-6.py-3:hover,
    .inline-flex.items-center.px-8.py-3:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px rgba(243, 115, 33, 0.2);
    }

    .inline-flex.items-center.px-6.py-3:active,
    .inline-flex.items-center.px-8.py-3:active {
        transform: translateY(0);
    }

    /* Responsive cho màn hình nhỏ */
    @media (max-width: 768px) {
        .section-title-badge {
            justify-content: center;
            margin-left: auto;
            margin-right: auto;
        }

        .flex.flex-col.md\:flex-row.items-center.gap-6 {
            text-align: center;
        }

        .relative.group {
            margin-left: auto;
            margin-right: auto;
        }

        .mt-8.flex.justify-end {
            justify-content: center;
        }

        /* Cải thiện khoảng cách cho mobile */
        .p-6 {
            padding: 1rem;
        }

        /* Cải thiện kích thước input cho mobile */
        input, select, textarea {
            font-size: 16px !important; /* Ngăn iOS zoom khi focus */
        }
    }

    /* Responsive cho màn hình siêu nhỏ */
    @media (max-width: 360px) {
        .p-4 {
            padding: 0.75rem;
        }

        .rounded-xl {
            border-radius: 0.5rem;
        }

        /* Tối ưu kích thước nút cho màn hình nhỏ */
        button[type="submit"] {
            width: 100%;
            padding-left: 1rem;
            padding-right: 1rem;
        }
    }

    /* Hiệu ứng cho select */
    select:disabled {
        background-color: #f3f4f6;
        cursor: not-allowed;
    }

    /* Hiệu ứng cho bảng */
    .table tr:hover {
        background-color: rgba(243, 115, 33, 0.03);
    }

    /* CSS cho preview ảnh đại diện */
    .preview-badge {
        animation: fadeInScale 0.3s ease-out;
    }

    .preview-section {
        animation: slideInUp 0.4s ease-out;
    }

    @keyframes fadeInScale {
        from {
            opacity: 0;
            transform: scale(0.8);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Hiệu ứng hover cho avatar preview */
    .relative.group:hover .preview-badge {
        animation: none;
        transform: scale(1.05);
        transition: transform 0.2s ease;
    }

    /* Loading animation cho file input */
    .file-loading {
        position: relative;
        overflow: hidden;
    }

    .file-loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(243, 115, 33, 0.2), transparent);
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% {
            left: -100%;
        }
        100% {
            left: 100%;
        }
    }

    /* Cải thiện hiệu ứng cho nút hủy */
    .avatar-upload-form .relative button[title="Hủy chọn ảnh"] {
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(4px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .avatar-upload-form .relative button[title="Hủy chọn ảnh"]:hover {
        background: rgba(255, 255, 255, 1);
        transform: scale(1.1);
    }
</style>

<?php
// Include footer
include_once '../partials/footer.php';
?>
