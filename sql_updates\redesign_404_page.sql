-- =====================================================
-- Redesign 404 Error Page - Nội Thất Bàng Vũ
-- =====================================================
-- Mô tả: Thiết kế lại trang 404 với giao diện hiện đại,
--        gradient cam, furniture motifs và responsive design
-- Ngày tạo: 2024-12-19
-- Tác giả: Augment Agent
-- =====================================================

-- Ki<PERSON>m tra và tạo bảng system_updates nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS system_updates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    update_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    version VARCHAR(50),
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
    details JSON,
    created_by VARCHAR(100) DEFAULT 'system'
);

-- Thêm bản ghi cập nhật cho việc thiết kế lại trang 404
INSERT INTO system_updates (
    update_name,
    description,
    version,
    details,
    created_by
) VALUES (
    'redesign_404_page',
    'Thiết kế lại trang 404 với giao diện hiện đại, gradient cam, furniture motifs và responsive design',
    '1.0.0',
    JSON_OBJECT(
        'files_modified', JSON_ARRAY(
            '404.php',
            'assets/css/404-page.css',
            'assets/css/404-page-fixed.css'
        ),
        'features', JSON_ARRAY(
            'Modern gradient orange background',
            'Furniture-themed icons and motifs',
            'Animated 404 number display',
            'Real categories from database',
            'Dynamic category icons mapping',
            'Floating furniture elements',
            'Responsive design for all devices',
            'Scoped CSS to avoid header conflicts',
            'Accessibility improvements',
            'Print-friendly styles'
        ),
        'fixes_applied', JSON_ARRAY(
            'Fixed CSS conflicts affecting header elements',
            'Scoped all CSS selectors to .error-404-container',
            'Used real categories from database instead of hardcoded',
            'Added dynamic icon mapping for categories',
            'Improved responsive design for mobile devices',
            'Fixed z-index and stacking context issues'
        ),
        'design_elements', JSON_OBJECT(
            'color_scheme', JSON_OBJECT(
                'primary', '#F37321',
                'primary_dark', '#D65A0F',
                'secondary', '#2A3B47',
                'background', 'Linear gradient with orange tones'
            ),
            'typography', 'Be Vietnam Pro font family',
            'animations', JSON_ARRAY(
                'fadeInUp',
                'bounceIn',
                'float',
                'pulse',
                'rotate'
            ),
            'responsive_breakpoints', JSON_ARRAY(
                '768px (tablet)',
                '480px (mobile)'
            )
        ),
        'user_experience', JSON_OBJECT(
            'navigation_options', JSON_ARRAY(
                'Về trang chủ',
                'Xem sản phẩm',
                'Liên hệ'
            ),
            'quick_categories', JSON_ARRAY(
                'Sofa',
                'Bàn ghế',
                'Tủ kệ',
                'Giường ngủ'
            ),
            'visual_elements', JSON_ARRAY(
                'Animated furniture icon',
                'Gradient 404 numbers',
                'Floating furniture elements',
                'Modern button designs'
            )
        )
    ),
    'Augment Agent'
) ON DUPLICATE KEY UPDATE
    description = VALUES(description),
    version = VALUES(version),
    details = VALUES(details),
    executed_at = CURRENT_TIMESTAMP;

-- Kiểm tra và tạo bảng page_analytics nếu chưa tồn tại (để theo dõi lượt truy cập trang 404)
CREATE TABLE IF NOT EXISTS page_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_type VARCHAR(100) NOT NULL,
    page_url VARCHAR(500),
    user_ip VARCHAR(45),
    user_agent TEXT,
    referer_url VARCHAR(500),
    visit_date DATE NOT NULL,
    visit_time TIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_page_type (page_type),
    INDEX idx_visit_date (visit_date),
    INDEX idx_page_url (page_url(255))
);

-- Thêm trigger để tự động log khi có lỗi 404 (tùy chọn)
-- Lưu ý: Trigger này sẽ được kích hoạt từ PHP code khi trang 404 được load

-- Tạo view để xem thống kê trang 404
CREATE OR REPLACE VIEW v_404_statistics AS
SELECT
    DATE(created_at) as date,
    COUNT(*) as total_404_visits,
    COUNT(DISTINCT user_ip) as unique_visitors,
    COUNT(DISTINCT page_url) as unique_missing_pages
FROM page_analytics
WHERE page_type = '404_error'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Thêm comment cho các bảng
ALTER TABLE system_updates
COMMENT = 'Bảng lưu trữ thông tin các cập nhật hệ thống';

ALTER TABLE page_analytics
COMMENT = 'Bảng theo dõi analytics cho các trang, bao gồm trang 404';

-- Thông báo hoàn thành
SELECT
    'Redesign 404 Page Update Completed' as status,
    NOW() as completed_at,
    'Trang 404 đã được thiết kế lại với giao diện hiện đại và responsive' as message;
