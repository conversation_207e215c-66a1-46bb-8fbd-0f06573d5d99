/*
 * Tablet Navigation Scroll CSS for Nội Thất Bàng <PERSON>
 * Force horizontal scroll for navigation menu on tablet devices
 */

/* TABLET NAVIGATION SCROLL - FORCE OVERRIDE */
@media (min-width: 769px) and (max-width: 1200px) {
    
    /* Container adjustments */
    .bottom-header-container {
        overflow: visible !important;
        position: relative !important;
    }

    .bottom-header {
        padding: 0 24px 0 0 !important;
        overflow: visible !important;
        justify-content: space-between !important;
        gap: 8px !important;
        max-width: 1400px !important;
        margin: 0 auto !important;
    }

    /* Navigation container - Allow overflow for scroll but visible for dropdowns */
    nav[role="navigation"] {
        flex: 1 !important;
        min-width: 0 !important;
        overflow: hidden !important;
        position: relative !important;
    }

    /* User Actions container - Allow overflow for dropdowns */
    .user-actions {
        overflow: visible !important;
        position: relative !important;
    }

    /* Arrow Indicators */
    .nav-scroll-arrow {
        position: absolute !important;
        top: 50% !important;
        width: 32px !important;
        height: 32px !important;
        border: none !important;
        border-radius: 50% !important;
        cursor: pointer !important;
        z-index: 15 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 14px !important;
        transition: all 0.3s ease !important;
        pointer-events: auto !important;

        /* Default hidden state */
        opacity: 0 !important;
        visibility: hidden !important;
        transform: translateY(-50%) scale(0.8) !important;

        /* Normal state (light background) */
        background: rgba(255, 255, 255, 0.95) !important;
        color: #374151 !important;
        border: 1px solid rgba(0, 0, 0, 0.15) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    }

    /* Arrow positioning */
    .nav-scroll-left {
        left: 8px !important;
    }

    .nav-scroll-right {
        right: 8px !important;
    }

    /* FORCE both arrows to have same base styling */
    .nav-scroll-left,
    .nav-scroll-right {
        background: rgba(255, 255, 255, 0.95) !important;
        color: #374151 !important;
        border: 1px solid rgba(0, 0, 0, 0.15) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        opacity: 0 !important;
        visibility: hidden !important;
        transform: translateY(-50%) scale(0.8) !important;
        pointer-events: auto !important;
        z-index: 500 !important;
    }

    /* SUPER FORCE for left arrow specifically */
    .nav-scroll-left {
        background: rgba(255, 255, 255, 0.95) !important;
        color: #374151 !important;
        border: 1px solid rgba(0, 0, 0, 0.15) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        z-index: 500 !important;
        pointer-events: auto !important;
        display: flex !important;
    }

    /* Show arrows - FORCE VISIBLE */
    .nav-scroll-arrow.show,
    .nav-scroll-left.show,
    .nav-scroll-right.show {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(-50%) scale(1) !important;
        pointer-events: auto !important;
        display: flex !important;
        background: rgba(255, 255, 255, 0.95) !important;
        color: #374151 !important;
        z-index: 500 !important;
    }

    /* EXTRA FORCE for left arrow when shown */
    .nav-scroll-left.show {
        background: rgba(255, 255, 255, 0.95) !important;
        color: #374151 !important;
        border: 1px solid rgba(0, 0, 0, 0.15) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        opacity: 1 !important;
        visibility: visible !important;
        z-index: 500 !important;
        pointer-events: auto !important;
        display: flex !important;
    }

    /* Hover effects - ENHANCED */
    .nav-scroll-arrow:hover,
    .nav-scroll-left:hover,
    .nav-scroll-right:hover {
        background: rgba(255, 255, 255, 1) !important;
        transform: translateY(-50%) scale(1.05) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
        color: #1f2937 !important;
    }

    /* Active/Click effect */
    .nav-scroll-arrow:active,
    .nav-scroll-left:active,
    .nav-scroll-right:active {
        transform: translateY(-50%) scale(0.95) !important;
        background: rgba(249, 115, 22, 0.1) !important;
    }

    /* Ensure icons are visible */
    .nav-scroll-arrow i {
        pointer-events: none !important;
        font-size: 14px !important;
        line-height: 1 !important;
    }

    /* SCROLLED STATE - Dark background arrows */
    .premium-header.scrolled .nav-scroll-arrow {
        background: rgba(32, 40, 52, 0.95) !important;
        color: #f3f4f6 !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4) !important;
    }

    .premium-header.scrolled .nav-scroll-arrow:hover {
        background: rgba(32, 40, 52, 1) !important;
        color: #ffffff !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
        transform: translateY(-50%) scale(1.05) !important;
    }

    .premium-header.scrolled .nav-scroll-arrow:active {
        background: rgba(249, 115, 22, 0.2) !important;
    }

    /* Show/hide indicators based on scroll state */
    nav[role="navigation"].can-scroll-left::before {
        opacity: 1 !important;
    }

    nav[role="navigation"].can-scroll-right::after {
        opacity: 1 !important;
    }

    nav[role="navigation"]:not(.can-scroll-right)::after {
        opacity: 0 !important;
    }

    /* Navigation menu - FORCE horizontal scroll */
    .nav-menu {
        display: flex !important;
        flex-wrap: nowrap !important;
        overflow-x: auto !important;
        overflow-y: hidden !important;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        width: 100% !important;
        min-width: 0 !important;
        justify-content: flex-start !important;
        align-items: center !important;
        margin: 0 !important;
        padding: 0 !important;
        list-style: none !important;

        /* Touch scroll */
        -webkit-overflow-scrolling: touch !important;
        scroll-behavior: smooth !important;

        /* Cursor */
        cursor: grab !important;
        user-select: none !important;
    }
    
    .nav-menu::-webkit-scrollbar {
        display: none !important;
    }
    
    .nav-menu:active {
        cursor: grabbing !important;
    }
    
    /* Navigation items - PREVENT wrapping */
    .nav-item {
        flex-shrink: 0 !important;
        flex-grow: 0 !important;
        white-space: nowrap !important;
        position: relative !important;
    }
    
    /* Navigation links - IMPROVED size for better UX */
    .nav-link {
        display: flex !important;
        align-items: center !important;
        padding: 18px 14px !important;
        white-space: nowrap !important;
        text-decoration: none !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        pointer-events: auto !important;
        color: var(--dark, #111827) !important;
        font-size: 15px !important;
        min-width: fit-content !important;
        position: relative !important;
        overflow: visible !important;
        min-height: 44px !important; /* Touch-friendly minimum */
    }

    /* Navigation link icons - PROPORTIONAL size */
    .nav-link i {
        margin-left: 5px !important;
        font-size: 0.8em !important;
        transition: all 0.3s ease !important;
    }

    /* Hover and active states - SYNC with desktop */
    .nav-link:hover,
    .nav-item.active .nav-link {
        color: #f97316 !important;
        text-shadow: 0 0 1px rgba(249, 115, 22, 0.1) !important;
    }

    .nav-link:hover i {
        transform: translateY(2px) !important;
    }

    /* Bottom border effect - SYNC with desktop - TABLET ONLY */
    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background-color: #f97316;
        background-image: linear-gradient(to right, #f97316, #fdba74, #f97316);
        transition: width 0.3s ease, transform 0.3s ease;
        transform: translateX(-50%);
        z-index: 1;
        border-radius: 2px;
        box-shadow: 0 1px 3px rgba(249, 115, 22, 0.2);
    }

    .nav-link:hover::after,
    .nav-item.active .nav-link::after {
        width: 100%;
    }

    /* First navigation link - Adjust left padding to align with 24px margin */
    .nav-item:first-child .nav-link {
        padding-left: 24px !important;
    }

    /* User Actions - IMPROVED size for better UX */
    .user-actions .action-btn {
        font-size: 15px !important;
        padding: 12px 16px !important;
        min-height: 44px !important; /* Touch-friendly minimum */
    }

    .user-actions .user-name span {
        font-size: 15px !important;
    }

    .user-actions .cart-count {
        font-size: 13px !important; /* Slightly smaller for badge */
    }

    /* Cart badge - Proportional sizing */
    .cart-badge {
        font-size: 12px !important;
        min-width: 20px !important;
        height: 20px !important;
        line-height: 20px !important;
    }

    /* SCROLLED STATE - Sync with desktop when header background changes */
    .premium-header.scrolled .nav-link {
        color: var(--ultra-light-gray, #f3f4f6) !important;
    }

    .premium-header.scrolled .nav-link:hover,
    .premium-header.scrolled .nav-item.active .nav-link {
        color: #f97316 !important;
    }

    /* Background cho hover nav-link khi ở nền tối */
    .premium-header.scrolled .nav-link::before {
        background-color: rgba(255, 255, 255, 0.1) !important;
    }

    /* Đổi màu nav link active indicator khi nền tối - TABLET ONLY */
    .premium-header.scrolled .nav-link::after {
        background-color: #f97316;
        bottom: 0;
        height: 2px;
    }
    
    /* User actions - COMPACT and FIXED WIDTH */
    .user-actions {
        flex-shrink: 0 !important;
        flex-grow: 0 !important;
        margin-left: 8px !important;
        width: auto !important;
        min-width: fit-content !important;
        max-width: 200px !important;
    }

    /* User action buttons - COMPACT */
    .action-btn {
        padding: 8px !important;
        font-size: 12px !important;
    }

    .user-account-btn {
        padding: 6px 8px !important;
    }

    .user-name span {
        font-size: 12px !important;
        max-width: 80px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
    }
    
    /* Hide desktop mega menu on tablet - will use tablet-specific mega menu instead */
    .mega-menu {
        display: none !important;
    }

    /* Prevent hover effects on products nav item for tablet */
    .nav-item:has(.mega-menu):hover .mega-menu {
        opacity: 0 !important;
        visibility: hidden !important;
        transform: translateY(10px) !important;
    }
    
    /* Ensure navigation is visible */
    .bottom-header {
        display: flex !important;
    }
    
    /* Force show navigation container */
    .bottom-header-container {
        display: block !important;
    }
}

/* EMERGENCY FIX - Force left arrow to always have proper styling */
@media (min-width: 768px) and (max-width: 1024px) {
    button.nav-scroll-left {
        background: rgba(255, 255, 255, 0.95) !important;
        color: #374151 !important;
        border: 1px solid rgba(0, 0, 0, 0.15) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        opacity: 0 !important;
        visibility: hidden !important;
        z-index: 500 !important;
        pointer-events: auto !important;
        display: flex !important;
        position: absolute !important;
        top: 50% !important;
        left: 8px !important;
        width: 32px !important;
        height: 32px !important;
        border-radius: 50% !important;
        align-items: center !important;
        justify-content: center !important;
        transform: translateY(-50%) scale(0.8) !important;
        transition: all 0.3s ease !important;
    }

    button.nav-scroll-left.show {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(-50%) scale(1) !important;
    }
}



/* DESKTOP MEDIUM - Navigation scroll for medium desktop screens */
@media (min-width: 1025px) and (max-width: 1300px) {

    /* Container adjustments */
    .bottom-header-container {
        overflow: visible !important;
        position: relative !important;
    }

    .bottom-header {
        padding: 0 24px 0 0 !important;
        overflow: visible !important;
        justify-content: space-between !important;
        gap: 8px !important;
        max-width: 1400px !important;
        margin: 0 auto !important;
    }

    /* Navigation container - Allow overflow for scroll but visible for dropdowns */
    nav[role="navigation"] {
        flex: 1 !important;
        min-width: 0 !important;
        overflow: visible !important;
        position: relative !important;
    }

    /* Navigation menu container - Handle scroll internally */
    nav[role="navigation"]::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
        z-index: -1;
        pointer-events: none;
    }

    /* User Actions container - Allow overflow for dropdowns */
    .user-actions {
        overflow: visible !important;
        position: relative !important;
    }

    /* Arrow Indicators - Desktop style */
    .nav-scroll-arrow {
        position: absolute !important;
        top: 50% !important;
        width: 36px !important;
        height: 36px !important;
        border: none !important;
        border-radius: 50% !important;
        cursor: pointer !important;
        z-index: 15 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 16px !important;
        transition: all 0.3s ease !important;
        pointer-events: auto !important;

        /* Default hidden state */
        opacity: 0 !important;
        visibility: hidden !important;
        transform: translateY(-50%) scale(0.8) !important;

        /* Desktop style (light background) */
        background: rgba(255, 255, 255, 0.95) !important;
        color: #374151 !important;
        border: 1px solid rgba(0, 0, 0, 0.15) !important;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15) !important;
    }

    /* Arrow positioning */
    .nav-scroll-left {
        left: 12px !important;
    }

    .nav-scroll-right {
        right: 12px !important;
    }

    /* Show arrows */
    .nav-scroll-arrow.show,
    .nav-scroll-left.show,
    .nav-scroll-right.show {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(-50%) scale(1) !important;
        pointer-events: auto !important;
        display: flex !important;
        background: rgba(255, 255, 255, 0.95) !important;
        color: #374151 !important;
        z-index: 500 !important;
    }

    /* Hover effects */
    .nav-scroll-arrow:hover,
    .nav-scroll-left:hover,
    .nav-scroll-right:hover {
        background: rgba(255, 255, 255, 1) !important;
        transform: translateY(-50%) scale(1.1) !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
        color: #1f2937 !important;
    }

    /* Active/Click effect */
    .nav-scroll-arrow:active,
    .nav-scroll-left:active,
    .nav-scroll-right:active {
        transform: translateY(-50%) scale(0.95) !important;
        background: rgba(249, 115, 22, 0.1) !important;
    }

    /* Navigation menu - FORCE horizontal scroll */
    .nav-menu {
        display: flex !important;
        flex-wrap: nowrap !important;
        overflow-x: auto !important;
        overflow-y: hidden !important;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        width: 100% !important;
        min-width: 0 !important;
        justify-content: flex-start !important;
        align-items: center !important;
        margin: 0 !important;
        padding: 0 !important;
        list-style: none !important;

        /* Touch scroll */
        -webkit-overflow-scrolling: touch !important;
        scroll-behavior: smooth !important;

        /* Cursor */
        cursor: grab !important;
        user-select: none !important;
    }

    .nav-menu::-webkit-scrollbar {
        display: none !important;
    }

    .nav-menu:active {
        cursor: grabbing !important;
    }

    /* Navigation items - PREVENT wrapping */
    .nav-item {
        flex-shrink: 0 !important;
        flex-grow: 0 !important;
        white-space: nowrap !important;
        position: relative !important;
    }

    /* Navigation links - Desktop optimized */
    .nav-link {
        display: flex !important;
        align-items: center !important;
        padding: 20px 16px !important;
        white-space: nowrap !important;
        text-decoration: none !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        pointer-events: auto !important;
        color: var(--dark, #111827) !important;
        font-size: 16px !important;
        min-width: fit-content !important;
        position: relative !important;
        overflow: visible !important;
        min-height: 48px !important;
    }

    /* Navigation link icons */
    .nav-link i {
        margin-left: 6px !important;
        font-size: 0.8em !important;
        transition: all 0.3s ease !important;
    }

    /* Hover and active states */
    .nav-link:hover,
    .nav-item.active .nav-link {
        color: #f97316 !important;
        text-shadow: 0 0 1px rgba(249, 115, 22, 0.1) !important;
    }

    .nav-link:hover i {
        transform: translateY(2px) !important;
    }

    /* Bottom border effect */
    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background-color: #f97316;
        background-image: linear-gradient(to right, #f97316, #fdba74, #f97316);
        transition: width 0.3s ease, transform 0.3s ease;
        transform: translateX(-50%);
        z-index: 1;
        border-radius: 2px;
        box-shadow: 0 1px 3px rgba(249, 115, 22, 0.2);
    }

    .nav-link:hover::after,
    .nav-item.active .nav-link::after {
        width: 100%;
    }

    /* First navigation link */
    .nav-item:first-child .nav-link {
        padding-left: 24px !important;
    }

    /* SCROLLED STATE - Dark background arrows */
    .premium-header.scrolled .nav-scroll-arrow {
        background: rgba(32, 40, 52, 0.95) !important;
        color: #f3f4f6 !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.4) !important;
    }

    .premium-header.scrolled .nav-scroll-arrow:hover {
        background: rgba(32, 40, 52, 1) !important;
        color: #ffffff !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5) !important;
        transform: translateY(-50%) scale(1.1) !important;
    }

    .premium-header.scrolled .nav-scroll-arrow:active {
        background: rgba(249, 115, 22, 0.2) !important;
    }

    /* SCROLLED STATE - Navigation links */
    .premium-header.scrolled .nav-link {
        color: var(--ultra-light-gray, #f3f4f6) !important;
    }

    .premium-header.scrolled .nav-link:hover,
    .premium-header.scrolled .nav-item.active .nav-link {
        color: #f97316 !important;
    }

    .premium-header.scrolled .nav-link::after {
        background-color: #f97316;
        bottom: 0;
        height: 2px;
    }

    /* Keep mega menu visible on desktop medium - only hide on tablet */
    .mega-menu {
        display: flex !important; /* Restore original flex display */
        z-index: 1000 !important; /* Ensure mega menu appears above navigation arrows */
        position: absolute !important; /* Ensure proper positioning */
        flex-direction: row !important; /* Ensure horizontal layout */
    }

    /* Ensure mega menu content layout is preserved */
    .mega-menu-categories {
        width: 30% !important;
        flex-shrink: 0 !important;
    }

    .mega-menu-content {
        width: 70% !important;
        flex-shrink: 0 !important;
    }
}

/* DESKTOP LARGE - Navigation scroll for large desktop screens */
@media (min-width: 1301px) and (max-width: 1600px) {

    /* Similar to medium desktop but with larger spacing */
    .bottom-header-container {
        overflow: visible !important;
        position: relative !important;
    }

    .bottom-header {
        padding: 0 32px 0 0 !important;
        overflow: visible !important;
        justify-content: space-between !important;
        gap: 12px !important;
        max-width: 1600px !important;
        margin: 0 auto !important;
    }

    nav[role="navigation"] {
        flex: 1 !important;
        min-width: 0 !important;
        overflow: visible !important;
        position: relative !important;
    }

    /* Navigation menu container - Handle scroll internally */
    nav[role="navigation"]::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
        z-index: -1;
        pointer-events: none;
    }

    .user-actions {
        overflow: visible !important;
        position: relative !important;
    }

    /* Arrow Indicators - Larger for big screens */
    .nav-scroll-arrow {
        position: absolute !important;
        top: 50% !important;
        width: 40px !important;
        height: 40px !important;
        border: none !important;
        border-radius: 50% !important;
        cursor: pointer !important;
        z-index: 15 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 18px !important;
        transition: all 0.3s ease !important;
        pointer-events: auto !important;

        opacity: 0 !important;
        visibility: hidden !important;
        transform: translateY(-50%) scale(0.8) !important;

        background: rgba(255, 255, 255, 0.95) !important;
        color: #374151 !important;
        border: 1px solid rgba(0, 0, 0, 0.15) !important;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.15) !important;
    }

    .nav-scroll-left {
        left: 16px !important;
    }

    .nav-scroll-right {
        right: 16px !important;
    }

    .nav-scroll-arrow.show,
    .nav-scroll-left.show,
    .nav-scroll-right.show {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(-50%) scale(1) !important;
        pointer-events: auto !important;
        display: flex !important;
        z-index: 500 !important;
    }

    .nav-scroll-arrow:hover {
        background: rgba(255, 255, 255, 1) !important;
        transform: translateY(-50%) scale(1.15) !important;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2) !important;
        color: #1f2937 !important;
    }

    /* Navigation menu */
    .nav-menu {
        display: flex !important;
        flex-wrap: nowrap !important;
        overflow-x: auto !important;
        overflow-y: hidden !important;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        width: 100% !important;
        min-width: 0 !important;
        justify-content: flex-start !important;
        align-items: center !important;
        margin: 0 !important;
        padding: 0 !important;
        list-style: none !important;
        -webkit-overflow-scrolling: touch !important;
        scroll-behavior: smooth !important;
        cursor: grab !important;
        user-select: none !important;
    }

    .nav-menu::-webkit-scrollbar {
        display: none !important;
    }

    .nav-menu:active {
        cursor: grabbing !important;
    }

    .nav-item {
        flex-shrink: 0 !important;
        flex-grow: 0 !important;
        white-space: nowrap !important;
        position: relative !important;
    }

    /* Navigation links - Large desktop optimized */
    .nav-link {
        display: flex !important;
        align-items: center !important;
        padding: 22px 20px !important;
        white-space: nowrap !important;
        text-decoration: none !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        pointer-events: auto !important;
        color: var(--dark, #111827) !important;
        font-size: 17px !important;
        min-width: fit-content !important;
        position: relative !important;
        overflow: visible !important;
        min-height: 52px !important;
    }

    .nav-link i {
        margin-left: 8px !important;
        font-size: 0.8em !important;
        transition: all 0.3s ease !important;
    }

    .nav-link:hover,
    .nav-item.active .nav-link {
        color: #f97316 !important;
        text-shadow: 0 0 1px rgba(249, 115, 22, 0.1) !important;
    }

    .nav-link:hover i {
        transform: translateY(2px) !important;
    }

    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 3px;
        background-color: #f97316;
        background-image: linear-gradient(to right, #f97316, #fdba74, #f97316);
        transition: width 0.3s ease, transform 0.3s ease;
        transform: translateX(-50%);
        z-index: 1;
        border-radius: 3px;
        box-shadow: 0 2px 4px rgba(249, 115, 22, 0.2);
    }

    .nav-link:hover::after,
    .nav-item.active .nav-link::after {
        width: 100%;
    }

    .nav-item:first-child .nav-link {
        padding-left: 32px !important;
    }

    /* SCROLLED STATE */
    .premium-header.scrolled .nav-scroll-arrow {
        background: rgba(32, 40, 52, 0.95) !important;
        color: #f3f4f6 !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.4) !important;
    }

    .premium-header.scrolled .nav-scroll-arrow:hover {
        background: rgba(32, 40, 52, 1) !important;
        color: #ffffff !important;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5) !important;
        transform: translateY(-50%) scale(1.15) !important;
    }

    .premium-header.scrolled .nav-link {
        color: var(--ultra-light-gray, #f3f4f6) !important;
    }

    .premium-header.scrolled .nav-link:hover,
    .premium-header.scrolled .nav-item.active .nav-link {
        color: #f97316 !important;
    }

    .premium-header.scrolled .nav-link::after {
        background-color: #f97316;
        bottom: 0;
        height: 3px;
    }

    /* Keep mega menu visible on desktop large - only hide on tablet */
    .mega-menu {
        display: flex !important; /* Restore original flex display */
        z-index: 1000 !important; /* Ensure mega menu appears above navigation arrows */
        position: absolute !important; /* Ensure proper positioning */
        flex-direction: row !important; /* Ensure horizontal layout */
    }

    /* Ensure mega menu content layout is preserved */
    .mega-menu-categories {
        width: 30% !important;
        flex-shrink: 0 !important;
    }

    .mega-menu-content {
        width: 70% !important;
        flex-shrink: 0 !important;
    }
}

/* DESKTOP XL - Normal navigation for very large screens */
@media (min-width: 1601px) {
    /* Hide scroll arrows on very large desktop */
    .nav-scroll-arrow,
    .nav-scroll-left,
    .nav-scroll-right {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }

    /* Restore original nav-link styling */
    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background-color: #f97316;
        background-image: linear-gradient(to right, #f97316, #fdba74, #f97316);
        transition: width 0.3s ease, transform 0.3s ease;
        transform: translateX(-50%);
        z-index: 1;
        border-radius: 2px;
        box-shadow: 0 1px 3px rgba(249, 115, 22, 0.2);
    }

    .nav-link:hover::after,
    .nav-item.active .nav-link::after {
        width: 100%;
    }

    /* Restore mega menu */
    .mega-menu {
        display: flex !important; /* Restore original flex display */
        z-index: 1000 !important; /* Ensure mega menu appears above navigation arrows */
        position: absolute !important; /* Ensure proper positioning */
        flex-direction: row !important; /* Ensure horizontal layout */
    }

    /* Ensure mega menu content layout is preserved */
    .mega-menu-categories {
        width: 30% !important;
        flex-shrink: 0 !important;
    }

    .mega-menu-content {
        width: 70% !important;
        flex-shrink: 0 !important;
    }
}

/* MOBILE - Hide navigation completely and scroll arrows */
@media (max-width: 768px) {
    .bottom-header {
        display: none !important;
    }

    .bottom-header-container {
        display: none !important;
    }

    /* Hide scroll arrows on mobile */
    .nav-scroll-arrow,
    .nav-scroll-left,
    .nav-scroll-right {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }
}
