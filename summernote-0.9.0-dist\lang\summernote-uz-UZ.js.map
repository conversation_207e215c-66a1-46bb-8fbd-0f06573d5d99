{"version": 3, "file": "lang/summernote-uz-UZ.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,4BAA4B;QACnCC,MAAM,EAAE,kBAAkB;QAC1BC,IAAI,EAAE,MAAM;QACZC,aAAa,EAAE,WAAW;QAC1BC,SAAS,EAAE,eAAe;QAC1BC,WAAW,EAAE,cAAc;QAC3BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,cAAc;QACtBC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,sBAAsB;QAClCC,aAAa,EAAE,sBAAsB;QACrCC,SAAS,EAAE,mBAAmB;QAC9BC,UAAU,EAAE,mBAAmB;QAC/BC,SAAS,EAAE,0BAA0B;QACrCC,YAAY,EAAE,eAAe;QAC7BC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,YAAY;QACvBC,aAAa,EAAE,uBAAuB;QACtCC,SAAS,EAAE,uBAAuB;QAClCC,eAAe,EAAE,0BAA0B;QAC3CC,GAAG,EAAE,gBAAgB;QACrBC,MAAM,EAAE;MACV,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,gBAAgB;QAC3BjB,MAAM,EAAE,OAAO;QACfc,GAAG,EAAE,WAAW;QAChBI,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,QAAQ;QACdnB,MAAM,EAAE,gBAAgB;QACxBoB,MAAM,EAAE,sBAAsB;QAC9BC,IAAI,EAAE,cAAc;QACpBC,aAAa,EAAE,kBAAkB;QACjCR,GAAG,EAAE,eAAe;QACpBS,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE;MACT,CAAC;MACDC,EAAE,EAAE;QACFzB,MAAM,EAAE;MACV,CAAC;MACD0B,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,MAAM;QACTC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,KAAK;QACVC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,oBAAoB;QAC/BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,oBAAoB;QAChCC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,4BAA4B;QACrCC,MAAM,EAAE,4BAA4B;QACpCC,IAAI,EAAE,uBAAuB;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BC,KAAK,EAAE,uBAAuB;QAC9BC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,aAAa;QACnBC,UAAU,EAAE,YAAY;QACxBC,UAAU,EAAE,YAAY;QACxBC,WAAW,EAAE,QAAQ;QACrBC,cAAc,EAAE,iBAAiB;QACjCC,KAAK,EAAE,aAAa;QACpBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,6BAA6B;QACxCC,KAAK,EAAE,MAAM;QACbC,cAAc,EAAE,SAAS;QACzBC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,sBAAsB;QAC3CC,aAAa,EAAE,iBAAiB;QAChCC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,aAAa;QACnBC,IAAI,EAAE;MACR;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-uz-UZ.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'uz-UZ': {\n      font: {\n        bold: 'қалин',\n        italic: 'Кур<PERSON>ив',\n        underline: 'Белгиланган',\n        clear: 'Ҳарф турларини олиб ташлаш',\n        height: 'Чизиқ баландлиги',\n        name: 'Ҳарф',\n        strikethrough: 'Ўчирилган',\n        subscript: 'Пастки индекс',\n        superscript: 'Юқори индекс',\n        size: 'ҳарф ҳажми',\n      },\n      image: {\n        image: 'Расм',\n        insert: 'расмни қўйиш',\n        resizeFull: 'Ҳажмни тиклаш',\n        resizeHalf: '50% гача кичрайтириш',\n        resizeQuarter: '25% гача кичрайтириш',\n        floatLeft: 'Чапда жойлаштириш',\n        floatRight: 'Ўнгда жойлаштириш',\n        floatNone: 'Стандарт бўйича жойлашув',\n        shapeRounded: 'Шакли: Юмалоқ',\n        shapeCircle: 'Шакли: Доира',\n        shapeThumbnail: 'Шакли: Миниатюра',\n        shapeNone: 'Шакли: Йўқ',\n        dragImageHere: 'Суратни кўчириб ўтинг',\n        dropImage: 'Суратни кўчириб ўтинг',\n        selectFromFiles: 'Файллардан бирини танлаш',\n        url: 'суратлар URL и',\n        remove: 'Суратни ўчириш',\n      },\n      video: {\n        video: 'Видео',\n        videoLink: 'Видеога ҳавола',\n        insert: 'Видео',\n        url: 'URL видео',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion или Youku)',\n      },\n      link: {\n        link: 'Ҳавола',\n        insert: 'Ҳаволани қўйиш',\n        unlink: 'Ҳаволани олиб ташлаш',\n        edit: 'Таҳрир қилиш',\n        textToDisplay: 'Кўринадиган матн',\n        url: 'URL ўтиш учун',\n        openInNewWindow: 'Янги дарчада очиш',\n      },\n      table: {\n        table: 'Жадвал',\n      },\n      hr: {\n        insert: 'Горизонтал чизиқни қўйиш',\n      },\n      style: {\n        style: 'Услуб',\n        p: 'Яхши',\n        blockquote: 'Жумла',\n        pre: 'Код',\n        h1: 'Сарлавҳа 1',\n        h2: 'Сарлавҳа  2',\n        h3: 'Сарлавҳа  3',\n        h4: 'Сарлавҳа  4',\n        h5: 'Сарлавҳа  5',\n        h6: 'Сарлавҳа  6',\n      },\n      lists: {\n        unordered: 'Белгиланган рўйҳат',\n        ordered: 'Рақамланган рўйҳат',\n      },\n      options: {\n        help: 'Ёрдам',\n        fullscreen: 'Бутун экран бўйича',\n        codeview: 'Бошланғич код',\n      },\n      paragraph: {\n        paragraph: 'Параграф',\n        outdent: 'Орқага қайтишни камайтириш',\n        indent: 'Орқага қайтишни кўпайтириш',\n        left: 'Чап қирғоққа тўғрилаш',\n        center: 'Марказга тўғрилаш',\n        right: 'Ўнг қирғоққа тўғрилаш',\n        justify: 'Эни бўйлаб чўзиш',\n      },\n      color: {\n        recent: 'Охирги ранг',\n        more: 'Яна ранглар',\n        background: 'Фон  ранги',\n        foreground: 'Ҳарф ранги',\n        transparent: 'Шаффоф',\n        setTransparent: 'Шаффофдай қилиш',\n        reset: 'Бекор қилиш',\n        resetToDefault: 'Стандартга оид тиклаш',\n      },\n      shortcut: {\n        shortcuts: 'Клавишларнинг ҳамохҳанглиги',\n        close: 'Ёпиқ',\n        textFormatting: 'Матнни ',\n        action: 'Ҳаркат',\n        paragraphFormatting: 'Параграфни форматлаш',\n        documentStyle: 'Ҳужжатнинг тури',\n        extraKeys: 'Қўшимча имкониятлар',\n      },\n      history: {\n        undo: 'Бекор қилиш',\n        redo: 'Қайтариш',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "url", "remove", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}