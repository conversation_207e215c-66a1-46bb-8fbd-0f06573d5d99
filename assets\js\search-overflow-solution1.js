/**
 * <PERSON><PERSON><PERSON><PERSON> pháp 1: Overflow Visible có điều kiện
 * JavaScript để quản lý việc thay đổi overflow khi search suggestions hiển thị
 */

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        headerSelector: '.header-section-with-search',
        searchInputSelector: '#main-search-input',
        suggestionsSelector: '#search-suggestions',
        activeClass: 'search-suggestions-active',
        showClass: 'show',
        hiddenClass: 'hidden',
        debounceDelay: 150
    };

    // State management
    let isInitialized = false;
    let headerElement = null;
    let searchInput = null;
    let suggestionsElement = null;
    let debounceTimer = null;

    /**
     * Initialize the solution
     */
    function init() {
        if (isInitialized) return;

        // Find elements
        headerElement = document.querySelector(CONFIG.headerSelector);
        searchInput = document.querySelector(CONFIG.searchInputSelector);
        suggestionsElement = document.querySelector(CONFIG.suggestionsSelector);

        if (!headerElement || !searchInput || !suggestionsElement) {
            console.warn('Search Overflow Solution 1: Required elements not found');
            return;
        }

        // Setup event listeners
        setupEventListeners();

        // Setup mutation observer to watch for suggestions changes
        setupMutationObserver();

        isInitialized = true;
        console.log('Search Overflow Solution 1: Initialized successfully');
    }

    /**
     * Setup event listeners
     */
    function setupEventListeners() {
        // Search input events
        searchInput.addEventListener('focus', handleSearchFocus);
        searchInput.addEventListener('blur', handleSearchBlur);
        searchInput.addEventListener('input', handleSearchInput);

        // Suggestions events
        suggestionsElement.addEventListener('mouseenter', handleSuggestionsMouseEnter);
        suggestionsElement.addEventListener('mouseleave', handleSuggestionsMouseLeave);

        // Global click handler
        document.addEventListener('click', handleGlobalClick);

        // Keyboard events
        document.addEventListener('keydown', handleKeyDown);
    }

    /**
     * Setup mutation observer to watch for suggestions visibility changes
     */
    function setupMutationObserver() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    updateHeaderOverflow();
                }
            });
        });

        observer.observe(suggestionsElement, {
            attributes: true,
            attributeFilter: ['class']
        });
    }

    /**
     * Handle search input focus
     */
    function handleSearchFocus() {
        debounce(function() {
            updateHeaderOverflow();
        }, CONFIG.debounceDelay);
    }

    /**
     * Handle search input blur
     */
    function handleSearchBlur() {
        // Delay to allow for suggestions interaction
        debounce(function() {
            if (!isMouseOverSuggestions()) {
                hideSuggestions();
            }
        }, 200);
    }

    /**
     * Handle search input
     */
    function handleSearchInput() {
        debounce(function() {
            updateHeaderOverflow();
        }, CONFIG.debounceDelay);
    }

    /**
     * Handle suggestions mouse enter
     */
    function handleSuggestionsMouseEnter() {
        // Keep suggestions visible when mouse is over
        clearTimeout(debounceTimer);
    }

    /**
     * Handle suggestions mouse leave
     */
    function handleSuggestionsMouseLeave() {
        if (!searchInput.matches(':focus')) {
            debounce(function() {
                hideSuggestions();
            }, 200);
        }
    }

    /**
     * Handle global click
     */
    function handleGlobalClick(event) {
        if (!searchInput.contains(event.target) &&
            !suggestionsElement.contains(event.target)) {
            hideSuggestions();
        }
    }

    /**
     * Handle keyboard events
     */
    function handleKeyDown(event) {
        if (event.key === 'Escape') {
            hideSuggestions();
            searchInput.blur();
        }
    }

    /**
     * Update header overflow based on suggestions visibility
     */
    function updateHeaderOverflow() {
        if (!headerElement || !suggestionsElement) return;

        const isSuggestionsVisible = suggestionsElement.classList.contains(CONFIG.showClass) ||
                                   !suggestionsElement.classList.contains(CONFIG.hiddenClass);

        if (isSuggestionsVisible) {
            headerElement.classList.add(CONFIG.activeClass);
        } else {
            headerElement.classList.remove(CONFIG.activeClass);
        }
    }

    /**
     * Hide suggestions
     */
    function hideSuggestions() {
        if (suggestionsElement) {
            suggestionsElement.classList.remove(CONFIG.showClass);
            suggestionsElement.classList.add(CONFIG.hiddenClass);
            updateHeaderOverflow();
        }
    }

    /**
     * Show suggestions
     */
    function showSuggestions() {
        if (suggestionsElement) {
            suggestionsElement.classList.add(CONFIG.showClass);
            suggestionsElement.classList.remove(CONFIG.hiddenClass);
            updateHeaderOverflow();
        }
    }

    /**
     * Check if mouse is over suggestions
     */
    function isMouseOverSuggestions() {
        if (!suggestionsElement) return false;
        return suggestionsElement.matches(':hover');
    }

    /**
     * Debounce function
     */
    function debounce(func, delay) {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(func, delay);
    }

    /**
     * Public API
     */
    window.SearchOverflowSolution1 = {
        init: init,
        updateHeaderOverflow: updateHeaderOverflow,
        hideSuggestions: hideSuggestions,
        showSuggestions: showSuggestions
    };

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();