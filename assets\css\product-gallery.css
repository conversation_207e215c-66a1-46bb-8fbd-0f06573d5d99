/**
 * CSS cho tính năng gallery sản phẩm
 */

/* Container chính */
.product-gallery-container {
  position: relative;
}

/* Hình ảnh chính với slider */
.product-main-image-container {
  position: relative;
  overflow: hidden;
  border-radius: 0.375rem;
}

.product-main-slider {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  /* Thêm con trỏ chuột để chỉ ra rằng có thể click */
}

/* Hiệu <PERSON>ng khi hover và active cho hình ảnh chính */
.product-main-image-container {
  transition: transform 0.2s ease;
}

.product-main-image-container:hover {
  transform: scale(1.01);
}

.product-main-image-container:active {
  transform: scale(0.99);
}

.main-slider-track {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  will-change: transform;
}

.main-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
}

/* <PERSON><PERSON><PERSON> đ<PERSON> h<PERSON> gallery */
.gallery-nav-btn {
  opacity: 1;
  transform: translateY(-50%) scale(1);
  transition: all 0.2s ease;
  z-index: 10;
}

.gallery-nav-btn:hover {
  background-color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.gallery-nav-btn:active {
  transform: translateY(-50%) scale(0.85);
  background-color: #f8f8f8;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

/* Thumbnails */
.product-thumbnails-container {
  position: relative;
  margin-top: 0.75rem;
}

.product-thumbnail {
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  position: relative;
}

.product-thumbnail:hover {
  transform: translateY(-2px);
}

.product-thumbnail:active {
  transform: scale(0.95);
}

.product-thumbnail img {
  transition: all 0.2s ease;
}

.product-thumbnail.active img {
  border-color: #3b82f6;
}

/* Hiệu ứng gợn sóng khi click */
.product-thumbnail::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: width 0.4s ease-out, height 0.4s ease-out, opacity 0.4s ease-out;
  pointer-events: none;
}

.product-thumbnail:active::after {
  width: 120%;
  height: 120%;
  opacity: 1;
}

/* View more thumbnail */
.view-more-thumbnail {
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  position: relative;
}

.view-more-thumbnail:hover {
  transform: translateY(-2px);
}

.view-more-thumbnail:active {
  transform: scale(0.95);
}

/* Hiệu ứng gợn sóng khi click */
.view-more-thumbnail::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: width 0.5s ease-out, height 0.5s ease-out, opacity 0.5s ease-out;
}

.view-more-thumbnail:active::after {
  width: 150%;
  height: 150%;
  opacity: 1;
}

/* Fullscreen Gallery */
.fullscreen-gallery {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.95);
  z-index: 99999;
  /* Tăng z-index để đảm bảo nằm trên tất cả các phần tử */
  opacity: 0;
  transition: opacity 0.3s ease;
  overflow: hidden;
  /* Ngăn cuộn bên trong hộp ảnh */
}

.fullscreen-gallery.active {
  display: block;
  opacity: 1;
}

.gallery-slider {
  height: 100%;
  width: 100%;
  position: relative;
}

.gallery-slider-track {
  display: flex;
  height: 100%;
  transition: transform 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  will-change: transform;
}

.gallery-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.gallery-slide img {
  max-height: 90vh;
  max-width: 90vw;
  object-fit: contain;
}

.gallery-fullscreen-prev,
.gallery-fullscreen-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(255, 255, 255, 0.75);
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
}

.gallery-fullscreen-prev {
  left: 1rem;
}

.gallery-fullscreen-next {
  right: 1rem;
}

.gallery-fullscreen-prev:hover,
.gallery-fullscreen-next:hover {
  background-color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.gallery-counter {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
}

/* Tabs cho nội dung sản phẩm */
.product-content-tabs {
  position: relative;
}

.product-tab-btn {
  position: relative;
  transition: all 0.3s ease;
}

.product-tab-btn:hover {
  color: #3b82f6;
}

.product-tab-btn.active {
  font-weight: 500;
}

.product-tab-content {
  transition: opacity 0.3s ease;
}

/* Hiệu ứng hover cho các nút tab */
.product-tab-btn::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: #3b82f6;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.product-tab-btn:hover::after {
  width: 80%;
}

.product-tab-btn.active::after {
  width: 100%;
}

/* Cải thiện hiển thị bảng thông số kỹ thuật */
#tab-specifications table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
}

#tab-specifications th {
  background-color: #f9fafb;
  font-weight: 500;
}

#tab-specifications tr:hover {
  background-color: #f9fafb;
}

#tab-specifications tr:last-child td,
#tab-specifications tr:last-child th {
  border-bottom: none;
}

/* Cải thiện hiển thị hướng dẫn sử dụng */
#tab-guide h4 {
  position: relative;
  padding-left: 1.5rem;
}

#tab-guide h4::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0.75rem;
  height: 0.75rem;
  background-color: #3b82f6;
  border-radius: 50%;
}

/* Responsive */
@media (max-width: 640px) {
  .product-content-tabs .flex {
    flex-wrap: wrap;
  }

  .product-tab-btn {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }

  .gallery-fullscreen-prev,
  .gallery-fullscreen-next {
    width: 2.5rem;
    height: 2.5rem;
  }

  .gallery-counter {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }
}