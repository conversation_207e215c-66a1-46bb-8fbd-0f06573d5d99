/**
 * Modern Header JavaScript for Nội Thất Bàng Vũ
 * Handles interactive features of the modern header
 */

document.addEventListener('DOMContentLoaded', function () {
  // Mobile menu toggle
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const mobileMenu = document.querySelector('.mobile-menu');
  const mobileMenuClose = document.querySelector('.mobile-menu-close');
  const mobileContactBtn = document.querySelector('.mobile-contact-btn');

  if (mobileMenuToggle && mobileMenu) {
    mobileMenuToggle.addEventListener('click', function () {
      mobileMenu.classList.toggle('hidden');
      document.body.classList.toggle('overflow-hidden');

      // Show contact button with delay
      if (!mobileMenu.classList.contains('hidden') && mobileContactBtn) {
        setTimeout(function () {
          mobileContactBtn.classList.add('visible');
        }, 500);
      } else if (mobileContactBtn) {
        mobileContactBtn.classList.remove('visible');
      }

      // Change icon based on menu state
      const icon = this.querySelector('i');
      if (icon) {
        if (!mobileMenu.classList.contains('hidden')) {
          icon.classList.remove('fa-bars');
          icon.classList.add('fa-times');
        } else {
          icon.classList.remove('fa-times');
          icon.classList.add('fa-bars');
        }
      }
    });
  }

  // Mobile menu close button
  if (mobileMenuClose && mobileMenu) {
    mobileMenuClose.addEventListener('click', function () {
      mobileMenu.classList.add('hidden');
      document.body.classList.remove('overflow-hidden');

      // Reset mobile menu toggle icon
      const icon = mobileMenuToggle.querySelector('i');
      if (icon) {
        icon.classList.remove('fa-times');
        icon.classList.add('fa-bars');
      }

      // Hide contact button
      if (mobileContactBtn) {
        mobileContactBtn.classList.remove('visible');
      }
    });
  }

  // Mobile dropdown toggles
  const navItems = document.querySelectorAll('.nav-item.has-dropdown');

  navItems.forEach(function (item) {
    const link = item.querySelector('.nav-link');

    if (link && window.innerWidth < 768) {
      link.addEventListener('click', function (e) {
        // Only prevent default if we're in mobile view
        if (window.innerWidth < 768) {
          e.preventDefault();
          item.classList.toggle('active');

          // Change dropdown icon
          const icon = this.querySelector('i');
          if (icon) {
            icon.classList.toggle('fa-chevron-down');
            icon.classList.toggle('fa-chevron-up');
          }
        }
      });
    }
  });

  // Sticky header behavior
  const header = document.querySelector('.modern-header');
  const topBar = document.querySelector('.top-bar');
  let lastScrollTop = 0;
  let ticking = false;
  let lastScrollUpdate = 0; // Thời điểm cập nhật cuối cùng
  const scrollThreshold = 10; // Ngưỡng để thêm/xóa class scrolled
  const throttleDelay = 10; // Thời gian tối thiểu giữa các lần cập nhật (ms)

  if (header && topBar) {
    const topBarHeight = topBar.offsetHeight;

    // Thêm class để kích hoạt hiệu ứng transition mượt mà
    header.classList.add('smooth-transition');

    window.addEventListener('scroll', function () {
      const now = Date.now();

      // Throttle: chỉ xử lý nếu đã qua đủ thời gian từ lần cập nhật trước
      if (now - lastScrollUpdate > throttleDelay) {
        lastScrollUpdate = now;

        if (!ticking) {
          window.requestAnimationFrame(function () {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollProgress = Math.min(1, scrollTop / (scrollThreshold * 2));

            // Thay vì thêm/xóa class ngay lập tức, áp dụng style trực tiếp để có hiệu ứng mượt mà
            if (scrollTop > 0) {
              // Áp dụng hiệu ứng mờ dần khi cuộn xuống
              const opacity = Math.min(scrollProgress * 2, 1);
              const scale = 0.98 + (0.02 * opacity);
              const translateY = Math.min(scrollProgress * 2, 1) * -2;

              // Thêm class scrolled khi vượt qua ngưỡng
              if (scrollTop > scrollThreshold) {
                if (!header.classList.contains('scrolled')) {
                  header.classList.add('scrolled');
                }
              } else {
                if (header.classList.contains('scrolled')) {
                  header.classList.remove('scrolled');
                }
              }

              // Áp dụng hiệu ứng transform mượt mà
              header.style.setProperty('--header-opacity', opacity);
              header.style.setProperty('--header-scale', scale);
              header.style.setProperty('--header-translate-y', `${translateY}px`);
            } else {
              // Reset về trạng thái ban đầu khi ở đầu trang
              header.classList.remove('scrolled');
              header.style.setProperty('--header-opacity', 0);
              header.style.setProperty('--header-scale', 1);
              header.style.setProperty('--header-translate-y', '0px');
            }

            // Hide/show top bar based on scroll direction
            if (scrollTop > lastScrollTop && scrollTop > topBarHeight) {
              // Scrolling down
              topBar.style.transform = 'translateY(-100%)';
            } else {
              // Scrolling up
              topBar.style.transform = 'translateY(0)';
            }

            lastScrollTop = scrollTop;
            ticking = false;
          });

          ticking = true;
        }
      }
    }, { passive: true }); // Thêm passive: true để cải thiện hiệu suất

    // Kiểm tra trạng thái ban đầu khi trang tải
    const initialScrollTop = window.pageYOffset || document.documentElement.scrollTop;
    if (initialScrollTop > scrollThreshold) {
      header.classList.add('scrolled');

      // Thiết lập các biến CSS cho hiệu ứng mượt mà
      const scrollProgress = Math.min(1, initialScrollTop / (scrollThreshold * 2));
      const opacity = Math.min(scrollProgress * 2, 1);
      const scale = 0.98 + (0.02 * opacity);
      const translateY = Math.min(scrollProgress * 2, 1) * -2;

      header.style.setProperty('--header-opacity', opacity);
      header.style.setProperty('--header-scale', scale);
      header.style.setProperty('--header-translate-y', `${translateY}px`);
    }
  }

  // Search suggestions
  const searchInput = document.querySelector('.search-input');
  const searchSuggestions = document.querySelector('.search-suggestions');

  if (searchInput && searchSuggestions) {
    searchInput.addEventListener('focus', function () {
      searchSuggestions.classList.add('active');
    });

    searchInput.addEventListener('blur', function () {
      // Small delay to allow clicking on suggestions
      setTimeout(function () {
        searchSuggestions.classList.remove('active');
      }, 200);
    });

    // Live search functionality would be implemented here
    searchInput.addEventListener('input', function () {
      // This would typically make an AJAX call to get search suggestions
      // For now, we'll just show/hide the suggestions container
      if (this.value.length > 2) {
        searchSuggestions.classList.add('active');
      } else {
        searchSuggestions.classList.remove('active');
      }
    });
  }

  // User dropdown menu
  const userDropdownToggle = document.querySelector('.user-dropdown-toggle');
  const userDropdownMenu = document.querySelector('.user-dropdown-menu');

  if (userDropdownToggle && userDropdownMenu) {
    userDropdownToggle.addEventListener('click', function (e) {
      e.preventDefault();
      userDropdownMenu.classList.toggle('active');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function (e) {
      if (
        !userDropdownToggle.contains(e.target) &&
        !userDropdownMenu.contains(e.target)
      ) {
        userDropdownMenu.classList.remove('active');
      }
    });
  }
});
