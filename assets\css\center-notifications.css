/* 
 * Center Notifications CSS - Nội Thất Băng <PERSON>ũ
 * <PERSON><PERSON> thống thông báo hiển thị giữa màn hình
 */

/* Container chính */
.notification-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    pointer-events: none; /* <PERSON> phép click xuyên qua container nhưng không qua overlay */
}

.notification-container.hidden {
    opacity: 0;
    visibility: hidden;
}

/* Overlay nền mờ */
.notification-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
    pointer-events: auto; /* <PERSON> phép click vào overlay */
}

/* Thông báo */
.notification-box {
    position: relative;
    width: 450px;
    max-width: 90%;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    display: flex;
    pointer-events: auto; /* Cho phép click vào thông báo */
    animation: notification-appear 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: translateY(-50px); /* Đặt cao hơn một chút so với chính giữa */
}

@keyframes notification-appear {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(-50px) scale(1);
    }
}

@keyframes notification-disappear {
    from {
        opacity: 1;
        transform: translateY(-50px) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-70px) scale(0.95);
    }
}

/* Icon */
.notification-icon {
    width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
}

/* Màu sắc theo loại thông báo */
.notification-success {
    border-left: 4px solid #10B981;
}

.notification-success .notification-icon {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10B981;
}

.notification-error {
    border-left: 4px solid #EF4444;
}

.notification-error .notification-icon {
    background-color: rgba(239, 68, 68, 0.1);
    color: #EF4444;
}

.notification-warning {
    border-left: 4px solid #F59E0B;
}

.notification-warning .notification-icon {
    background-color: rgba(245, 158, 11, 0.1);
    color: #F59E0B;
}

.notification-info {
    border-left: 4px solid #3B82F6;
}

.notification-info .notification-icon {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3B82F6;
}

/* Nội dung */
.notification-content {
    flex: 1;
    padding: 20px;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.notification-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    font-family: 'Be Vietnam Pro', sans-serif;
}

.notification-close {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: #6B7280;
    transition: color 0.2s;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.notification-close:hover {
    color: #111827;
    background-color: rgba(0, 0, 0, 0.05);
}

.notification-body {
    margin-bottom: 15px;
    color: #4B5563;
    font-family: 'Be Vietnam Pro', sans-serif;
}

.notification-footer {
    display: flex;
    justify-content: flex-end;
}

.notification-action {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    font-family: 'Be Vietnam Pro', sans-serif;
}

/* Màu nút theo loại thông báo */
.notification-success .notification-action {
    background-color: #10B981;
    color: white;
}

.notification-success .notification-action:hover {
    background-color: #059669;
}

.notification-error .notification-action {
    background-color: #EF4444;
    color: white;
}

.notification-error .notification-action:hover {
    background-color: #DC2626;
}

.notification-warning .notification-action {
    background-color: #F59E0B;
    color: white;
}

.notification-warning .notification-action:hover {
    background-color: #D97706;
}

.notification-info .notification-action {
    background-color: #3B82F6;
    color: white;
}

.notification-info .notification-action:hover {
    background-color: #2563EB;
}

/* Thanh tiến trình */
.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    width: 100%;
    transform-origin: left;
}

.notification-success .notification-progress {
    background-color: #10B981;
}

.notification-error .notification-progress {
    background-color: #EF4444;
}

.notification-warning .notification-progress {
    background-color: #F59E0B;
}

.notification-info .notification-progress {
    background-color: #3B82F6;
}

/* Hiệu ứng animation cho thanh tiến trình */
@keyframes progress {
    from { transform: scaleX(1); }
    to { transform: scaleX(0); }
}

/* Responsive */
@media (max-width: 576px) {
    .notification-box {
        width: 90%;
        flex-direction: column;
    }
    
    .notification-icon {
        width: 100%;
        padding: 15px 0;
    }
    
    .notification-box {
        border-left: none;
    }
    
    .notification-success {
        border-top: 4px solid #10B981;
    }
    
    .notification-error {
        border-top: 4px solid #EF4444;
    }
    
    .notification-warning {
        border-top: 4px solid #F59E0B;
    }
    
    .notification-info {
        border-top: 4px solid #3B82F6;
    }
}
