/* Design Services Section CSS - Compact & Refined */
.design-services-section {
    position: relative;
    padding: 3rem 0;
    background-color: #1a2234; /* <PERSON><PERSON><PERSON> nền đồng nhất */
    overflow: hidden;
    color: white;
    border-radius: 1rem;
    box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.25);
    will-change: transform; /* Tối <PERSON>u hóa cho GPU */
    border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Background decorations - Simplified */
.design-services-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 0;
}

.design-services-bg .bg-shape {
    position: absolute;
    border-radius: 50%;
    filter: blur(50px);
    opacity: 0.1;
    /* Loại bỏ animation để tăng hiệu suất */
}

.design-services-bg .shape-1 {
    top: -5%;
    left: -5%;
    width: 30%;
    height: 30%;
    background: linear-gradient(135deg, #3B82F6, #60A5FA);
}

.design-services-bg .shape-2 {
    bottom: -5%;
    right: -5%;
    width: 25%;
    height: 25%;
    background: linear-gradient(135deg, #F37321, #F59E0B);
}

.design-services-bg .shape-3 {
    top: 30%;
    right: 15%;
    width: 20%;
    height: 20%;
    background: linear-gradient(135deg, #10B981, #34D399);
}

/* Đơn giản hóa grid pattern */
.design-services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
    background-size: 50px 50px; /* Tăng kích thước grid để giảm số lượng đường */
    z-index: 1;
    opacity: 0.3;
}

/* Section title styling - Compact & Refined */
.design-services-title-container {
    position: relative;
    margin-bottom: 2rem;
    text-align: center;
    z-index: 10;
}

.design-services-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.08);
    color: #F37321;
    font-weight: 600;
    border-radius: 9999px;
    margin-bottom: 1rem;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.08);
    letter-spacing: 0.02em;
    transform: translateY(0);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    font-size: 0.875rem;
}

.design-services-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.2);
}

.design-services-badge .pulse-dot {
    width: 0.4rem;
    height: 0.4rem;
    background-color: #F37321;
    border-radius: 50%;
    margin-right: 0.625rem;
    position: relative;
}

.design-services-badge .pulse-dot::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    border: 1.5px solid rgba(243, 115, 33, 0.4);
    animation: pulse-simple 2s infinite;
}

@keyframes pulse-simple {
    0% {
        transform: scale(0.8);
        opacity: 0.8;
    }
    70% {
        transform: scale(1.5);
        opacity: 0;
    }
    100% {
        transform: scale(0.8);
        opacity: 0;
    }
}

.design-services-heading {
    position: relative;
    display: inline-block;
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    letter-spacing: -0.01em;
}

@media (min-width: 768px) {
    .design-services-heading {
        font-size: 2.25rem;
    }
}

@media (min-width: 1024px) {
    .design-services-heading {
        font-size: 2.5rem;
    }
}

.design-services-heading-underline {
    position: absolute;
    bottom: -0.375rem;
    left: 50%;
    width: 60px;
    height: 3px;
    background: #F37321;
    border-radius: 9999px;
    opacity: 0.9;
    transform: translateX(-50%);
}

.design-services-description {
    max-width: 36rem;
    margin: 0 auto;
    color: rgba(255, 255, 255, 0.85);
    font-size: 1rem;
    line-height: 1.5;
    font-weight: 300;
    letter-spacing: 0.01em;
}

/* Service cards styling - Compact & Refined */
.design-services-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    position: relative;
    z-index: 10;
    margin-bottom: 2rem;
}

.design-service-card {
    flex: 1 1 calc(50% - 0.75rem);
    min-width: 280px;
    position: relative;
    overflow: hidden;
    border-radius: 0.875rem;
    box-shadow: 0 10px 20px -6px rgba(0, 0, 0, 0.2);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 1px solid rgba(255, 255, 255, 0.08);
    background-color: #1a2234;
    display: flex;
    flex-direction: column;
}

.design-service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.03) 0%,
        rgba(255, 255, 255, 0) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
    z-index: 1;
}

.design-service-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.15);
}

.design-service-card:hover::before {
    opacity: 1;
}

.design-service-image {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 45%; /* Tỷ lệ thấp hơn để giảm chiều cao */
    overflow: hidden;
    flex-shrink: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.design-service-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
    filter: brightness(0.95);
}

.design-service-card:hover .design-service-image img {
    transform: scale(1.05);
    filter: brightness(1.05);
}

.design-service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top,
        rgba(0, 0, 0, 0.8) 0%,
        rgba(0, 0, 0, 0.5) 40%,
        rgba(0, 0, 0, 0.2) 80%,
        rgba(0, 0, 0, 0.1) 100%);
    opacity: 0.8;
    transition: opacity 0.4s ease;
    z-index: 1;
}

.design-service-card:hover .design-service-overlay {
    opacity: 0.7;
}

.design-service-content {
    position: relative;
    padding: 1.25rem;
    background-color: #1a2234;
    color: white;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.design-service-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.75rem;
    position: relative;
    display: inline-block;
    letter-spacing: -0.01em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.design-service-title::after {
    content: '';
    position: absolute;
    bottom: -0.375rem;
    left: 0;
    width: 2.5rem;
    height: 0.2rem;
    background: #F37321;
    border-radius: 9999px;
    transition: width 0.3s ease;
}

.design-service-card:hover .design-service-title::after {
    width: 3.5rem;
}

.design-service-description {
    color: rgba(255, 255, 255, 0.85);
    margin-bottom: 1.25rem;
    line-height: 1.5;
    font-size: 0.9375rem;
    font-weight: 300;
    flex-grow: 1;
    letter-spacing: 0.01em;
    /* Giới hạn số dòng hiển thị */
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-height: 4.5em; /* Fallback cho trình duyệt không hỗ trợ line-clamp */
}

/* Nút cơ bản */
.design-service-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.625rem 1.25rem;
    color: white;
    font-weight: 600;
    border-radius: 0.5rem;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: none;
    cursor: pointer;
    letter-spacing: 0.02em;
    position: relative;
    overflow: hidden;
    z-index: 1;
    font-size: 0.875rem;
}

/* Nút đặt lịch ngay - Nổi bật */
.design-service-button.primary {
    background: linear-gradient(90deg, #F37321, #F59E0B);
    box-shadow: 0 6px 12px -4px rgba(243, 115, 33, 0.4);
}

.design-service-button.primary:hover {
    background: linear-gradient(90deg, #E65A00, #F37321);
    transform: translateY(-3px);
    box-shadow: 0 8px 15px -4px rgba(243, 115, 33, 0.5);
}

/* Nút xem chi tiết - Thiết kế mới */
.design-service-button.secondary {
    background: linear-gradient(to right, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.2));
    border: 1px solid rgba(59, 130, 246, 0.3);
    box-shadow: 0 4px 8px -3px rgba(0, 0, 0, 0.2);
    color: #fff;
    position: relative;
    overflow: hidden;
}

.design-service-button.secondary:hover {
    background: linear-gradient(to right, rgba(59, 130, 246, 0.8), rgba(37, 99, 235, 0.9));
    transform: translateY(-3px);
    box-shadow: 0 6px 12px -4px rgba(0, 0, 0, 0.25);
    color: white;
}

/* Hiệu ứng ripple cho cả hai loại nút */
.design-service-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    z-index: -1;
}

.design-service-button:hover::after {
    animation: button-shine 1.5s ease;
}

@keyframes button-shine {
    100% {
        transform: translateX(100%);
    }
}

.design-service-button .icon {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
    font-size: 0.875rem;
}

.design-service-button:hover .icon {
    transform: translateX(3px);
}

/* Hiệu ứng khi click */
.design-service-button.button-clicked {
    transform: scale(0.97);
}

/* Hiệu ứng ripple khi click */
.design-service-button {
    position: relative;
    overflow: hidden;
}

.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    animation: ripple-animation 0.6s ease-out;
    pointer-events: none;
}

@keyframes ripple-animation {
    0% {
        transform: scale(0);
        opacity: 0.5;
    }
    100% {
        transform: scale(3);
        opacity: 0;
    }
}

/* Loại bỏ hiệu ứng đặc biệt cho card */

/* Badge chất lượng - Compact */
.quality-badges {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.quality-badge {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    transition: all 0.3s ease;
}

.quality-badge:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px -4px rgba(0, 0, 0, 0.2);
}

.quality-badge-icon {
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.625rem;
    transition: transform 0.3s ease;
}

.quality-badge:hover .quality-badge-icon {
    transform: scale(1.1);
}

.quality-badge-icon i {
    font-size: 0.875rem;
    transition: transform 0.3s ease;
}

.quality-badge:hover .quality-badge-icon i {
    transform: scale(1.1);
}

.quality-badge-text {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    letter-spacing: 0.01em;
    font-size: 0.875rem;
}

/* Responsive styles - Compact */
@media (max-width: 992px) {
    .design-services-section {
        padding: 3rem 0;
        margin-top: 1.5rem;
        margin-bottom: 1.5rem;
        border-radius: 0.875rem;
    }

    .design-services-heading {
        font-size: 1.75rem;
    }

    .design-services-description {
        font-size: 0.9375rem;
        padding: 0 1rem;
    }
}

@media (max-width: 768px) {
    .design-services-section {
        padding: 2.5rem 0;
        margin-top: 1rem;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
    }

    .design-services-heading {
        font-size: 1.5rem;
    }

    .design-services-badge {
        padding: 0.375rem 0.875rem;
        font-size: 0.8125rem;
    }

    .design-services-cards {
        flex-direction: column;
        gap: 1.25rem;
    }

    .design-service-card {
        flex: 1 1 100%;
        min-width: 100%;
    }

    .design-service-title {
        font-size: 1.25rem;
    }

    .quality-badges {
        gap: 0.75rem;
    }

    .quality-badge {
        padding: 0.375rem 0.75rem;
    }

    .quality-badge-icon {
        width: 1.5rem;
        height: 1.5rem;
    }

    .quality-badge-text {
        font-size: 0.8125rem;
    }
}

@media (max-width: 480px) {
    .design-services-section {
        padding: 2rem 0;
    }

    .design-services-heading {
        font-size: 1.375rem;
    }

    .design-service-image {
        padding-bottom: 40%; /* Tỷ lệ thấp hơn để giảm chiều cao */
    }

    .design-service-content {
        padding: 1rem;
    }

    .design-service-button {
        width: 100%;
        padding: 0.5rem 1rem;
    }

    .design-service-description {
        -webkit-line-clamp: 2;
        line-clamp: 2;
        max-height: 3em;
    }
}

/* Animation classes - Optimized */
.fade-in-up {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
    /* Đơn giản hóa transition timing function */
}

.fade-in-up.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Hiệu ứng nút khi click - Đơn giản hóa */
.design-service-button.button-clicked {
    transform: scale(0.98);
    transition: transform 0.2s ease;
}

/* Đảm bảo container có z-index phù hợp */
.design-services-section .container {
    position: relative;
    z-index: 10;
}

/* Loại bỏ hiệu ứng particle phức tạp */
