/**
 * Professional Pagination Styles for Nội Thất Băng Vũ
 * Consistent with admin design, adapted for frontend
 */

/* Pagination Section */
.pagination-section {
    /* background: #ffffff; */
    border-radius: 12px;
    padding: 1.5rem 2rem;
    margin: 2rem 0;
    /* box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); */
}

/* Results Summary */
.pagination-summary {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f3f4f6;
}

.results-info {
    text-align: center;
}

.results-text {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
}

.results-text strong {
    color: #374151;
    font-weight: 600;
}

/* Pagination Navigation */
.pagination-nav {
    display: flex;
    justify-content: center;
}

.pagination-list {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

/* Page Items */
.page-item {
    margin: 0;
}

/* Page Links */
.page-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: #ffffff;
    color: #6b7280;
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.page-link:hover {
    background: rgba(243, 115, 33, 0.05);
    border-color: rgba(243, 115, 33, 0.3);
    color: #F37321;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(243, 115, 33, 0.15);
}

/* Active Page */
.page-item.active .page-link {
    background: linear-gradient(135deg, #F37321 0%, #e06717 100%);
    border-color: #F37321;
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
    transform: translateY(-1px);
}

.page-item.active .page-link:hover {
    background: linear-gradient(135deg, #e06717 0%, #d45d13 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(243, 115, 33, 0.3);
}

/* Disabled State */
.page-item.disabled .page-link {
    background: #f9fafb;
    border-color: #e5e7eb;
    color: #9ca3af;
    cursor: not-allowed;
    box-shadow: none;
}

.page-item.disabled .page-link:hover {
    background: #f9fafb;
    border-color: #e5e7eb;
    color: #9ca3af;
    transform: none;
    box-shadow: none;
}

/* Navigation Buttons (Previous/Next) */
.page-link .page-text {
    font-weight: 500;
}

.page-link i {
    font-size: 0.75rem;
}

.page-link .page-text + i {
    margin-left: 0.5rem;
}

.page-link i + .page-text {
    margin-left: 0.5rem;
}

/* Ellipsis */
.page-link.ellipsis {
    background: transparent;
    border: none;
    color: #9ca3af;
    cursor: default;
    box-shadow: none;
}

.page-link.ellipsis:hover {
    background: transparent;
    border: none;
    color: #9ca3af;
    transform: none;
    box-shadow: none;
}

/* Focus States for Accessibility */
.page-link:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.3);
    z-index: 1;
}

.page-item.disabled .page-link:focus {
    box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .pagination-section {
        padding: 1rem 1.5rem;
        margin: 1.5rem 0;
    }

    .pagination-summary {
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
    }

    .results-text {
        font-size: 0.8rem;
    }

    .pagination-list {
        gap: 0.25rem;
    }

    .page-link {
        min-width: 40px;
        height: 40px;
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .page-text {
        display: none;
    }
}

@media (max-width: 480px) {
    .pagination-section {
        padding: 1rem;
        margin: 1rem 0;
    }

    .pagination-list {
        gap: 0.125rem;
    }

    .page-link {
        min-width: 36px;
        height: 36px;
        padding: 0.5rem;
        font-size: 0.75rem;
    }

    .page-link i {
        font-size: 0.7rem;
    }
}

/* Active State Animation */
.page-link:active {
    transform: translateY(0) scale(0.95);
}

/* Smooth Transitions */
.pagination-section {
    transition: all 0.3s ease;
}

.page-link {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading State (for JavaScript enhancement) */
.page-link.loading {
    opacity: 0.6;
    pointer-events: none;
}

.page-link.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
