-- T<PERSON><PERSON> bảng testimonials để lưu trữ cảm nhận khách hàng
CREATE TABLE IF NOT EXISTS `testimonials` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_name` varchar(255) NOT NULL,
  `customer_age` int(11) DEFAULT NULL,
  `customer_address` varchar(255) DEFAULT NULL,
  `customer_photo` varchar(255) DEFAULT NULL,
  `customer_video` varchar(255) DEFAULT NULL,
  `rating` int(11) NOT NULL DEFAULT 5,
  `content` text NOT NULL,
  `product_tags` varchar(255) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Thê<PERSON> một số dữ liệu mẫu
INSERT INTO `testimonials` (`customer_name`, `customer_age`, `customer_address`, `customer_photo`, `rating`, `content`, `product_tags`, `status`) VALUES
('Nguyễn Văn A', 35, 'Hà Nội', 'customer1.jpg', 5, 'Tôi rất hài lòng với chất lượng sản phẩm và dịch vụ của Nội Thất Bàng Vũ. Đội ngũ thiết kế rất chuyên nghiệp và tận tâm, giúp tôi có được không gian sống như mong muốn.', 'Tủ bếp, Bàn ăn', 1),
('Trần Thị B', 42, 'Hải Phòng', 'customer2.jpg', 5, 'Đã mua sản phẩm tại Nội Thất Bàng Vũ và rất hài lòng về chất lượng. Đặc biệt ấn tượng với dịch vụ bảo hành 10 năm và hỗ trợ khách hàng tận tình.', 'Sofa, Kệ tivi', 1),
('Lê Văn C', 28, 'Hà Nội', 'customer3.jpg', 4, 'Thiết kế 3D miễn phí giúp tôi hình dung rõ ràng về không gian trước khi quyết định. Sản phẩm hoàn thiện đúng như thiết kế và đúng tiến độ.', 'Phòng ngủ, Tủ quần áo', 1);
