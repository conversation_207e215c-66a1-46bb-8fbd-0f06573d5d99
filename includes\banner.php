<?php
/**
 * Banner Functions
 * <PERSON><PERSON><PERSON> hàm xử lý banner
 */

/**
 * <PERSON><PERSON>y tất cả banner active
 *
 * @param int $status Trạng thái banner (1: hiển thị, 0: ẩn)
 * @return array Mảng chứa thông tin các banner
 */
function get_active_banners($status = 1) {
    global $conn;

    try {
        $sql = "SELECT * FROM banners WHERE status = :status ORDER BY sort_order ASC, id DESC";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':status', $status, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Lỗi khi lấy banner: " . $e->getMessage());
        return [];
    }
}

/**
 * Lấy thông tin chi tiết banner theo ID
 *
 * @param int $id ID của banner
 * @return array|false Thông tin banner hoặc false nếu không tìm thấy
 */
function get_banner_by_id($id) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT * FROM banners WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

        $banner = $stmt->fetch();
        return $banner ? $banner : false;
    } catch (PDOException $e) {
        error_log("Lỗi khi lấy thông tin banner: " . $e->getMessage());
        return false;
    }
}

/**
 * Lấy danh sách tất cả banner
 *
 * @param int $limit Số lượng banner cần lấy
 * @param int $offset Vị trí bắt đầu
 * @return array Mảng chứa thông tin banner
 */
function get_all_banners($limit = null, $offset = 0) {
    global $conn;

    $sql = "SELECT * FROM banners ORDER BY sort_order ASC, id DESC";

    // Giới hạn số lượng
    if ($limit !== null) {
        $sql .= " LIMIT :offset, :limit";
    }

    try {
        $stmt = $conn->prepare($sql);

        if ($limit !== null) {
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        }

        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Lỗi khi lấy danh sách banner: " . $e->getMessage());
        return [];
    }
}

/**
 * Lấy danh sách tất cả banner có sắp xếp
 *
 * @param int $limit Số lượng banner cần lấy
 * @param int $offset Vị trí bắt đầu
 * @param string $sort_by Trường sắp xếp
 * @param string $sort_order Thứ tự sắp xếp (asc hoặc desc)
 * @return array Mảng chứa thông tin banner
 */
function get_all_banners_sorted($limit = null, $offset = 0, $sort_by = 'sort_order', $sort_order = 'asc') {
    global $conn;

    // Kiểm tra trường sort_by hợp lệ
    $allowed_fields = ['id', 'banner_type', 'status', 'created_at', 'sort_order'];
    if (!in_array($sort_by, $allowed_fields)) {
        $sort_by = 'sort_order';
    }

    // Kiểm tra sort_order hợp lệ
    $sort_order = strtolower($sort_order) === 'asc' ? 'ASC' : 'DESC';

    $sql = "SELECT * FROM banners ORDER BY {$sort_by} {$sort_order}";

    // Giới hạn số lượng
    if ($limit !== null) {
        $sql .= " LIMIT :offset, :limit";
    }

    try {
        $stmt = $conn->prepare($sql);

        if ($limit !== null) {
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        }

        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Lỗi khi lấy danh sách banner có sắp xếp: " . $e->getMessage());
        return [];
    }
}

/**
 * Thêm banner mới
 *
 * @param array $data Dữ liệu banner
 * @return array Kết quả thực hiện
 */
function add_banner($data) {
    global $conn;

    try {
        // Lấy thứ tự lớn nhất hiện tại để thêm banner mới vào cuối
        $max_order = 0;
        $stmt = $conn->prepare("SELECT MAX(sort_order) FROM banners");
        $stmt->execute();
        $max_result = $stmt->fetchColumn();
        if ($max_result !== false) {
            $max_order = (int)$max_result + 10;
        }

        $sql = "INSERT INTO banners (image, link, status, banner_type, sort_order,
                title_text, subtitle_text, description_text, button_text,
                badge_text, feature1_text, feature2_text, feature3_text,
                button2_text, contact_phone, contact_slogan,
                button_link, button2_link)
                VALUES (:image, :link, :status, :banner_type, :sort_order,
                :title_text, :subtitle_text, :description_text, :button_text,
                :badge_text, :feature1_text, :feature2_text, :feature3_text,
                :button2_text, :contact_phone, :contact_slogan,
                :button_link, :button2_link)";

        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':image', $data['image']);
        $stmt->bindParam(':link', $data['link']);
        $stmt->bindParam(':status', $data['status'], PDO::PARAM_INT);
        $stmt->bindParam(':banner_type', $data['banner_type']);
        $stmt->bindParam(':sort_order', $max_order, PDO::PARAM_INT);

        // Các trường nội dung tùy chỉnh
        $title_text = $data['title_text'] ?? 'Thiết Kế – Thi Công';
        $subtitle_text = $data['subtitle_text'] ?? 'Nội Thất Theo Yêu Cầu';
        $description_text = $data['description_text'] ?? 'Chúng tôi chuyên thiết kế và sản xuất nội thất thông minh, hiện đại, với chất lượng cao cấp phù hợp với mọi không gian sống và làm việc.';
        $button_text = $data['button_text'] ?? 'Xem các mẫu thiết kế';

        // Các trường nội dung tùy chỉnh bổ sung
        $badge_text = $data['badge_text'] ?? 'Nội Thất Cao Cấp Bàng Vũ';
        $feature1_text = $data['feature1_text'] ?? 'Miễn phí thiết kế 3D';
        $feature2_text = $data['feature2_text'] ?? 'Giao hàng toàn quốc';
        $feature3_text = $data['feature3_text'] ?? 'Bảo hành lên tới 10 năm';
        $button2_text = $data['button2_text'] ?? 'Nhận tư vấn miễn phí';
        $contact_phone = $data['contact_phone'] ?? '************';
        $contact_slogan = $data['contact_slogan'] ?? 'Thiết kế chuẩn gu – Giao hàng đúng hẹn';

        // Các trường URL cho nút CTA
        $button_link = $data['button_link'] ?? '#';
        $button2_link = $data['button2_link'] ?? 'contact.php';

        $stmt->bindParam(':title_text', $title_text);
        $stmt->bindParam(':subtitle_text', $subtitle_text);
        $stmt->bindParam(':description_text', $description_text);
        $stmt->bindParam(':button_text', $button_text);

        // Bind các tham số cho trường nội dung tùy chỉnh bổ sung
        $stmt->bindParam(':badge_text', $badge_text);
        $stmt->bindParam(':feature1_text', $feature1_text);
        $stmt->bindParam(':feature2_text', $feature2_text);
        $stmt->bindParam(':feature3_text', $feature3_text);
        $stmt->bindParam(':button2_text', $button2_text);
        $stmt->bindParam(':contact_phone', $contact_phone);
        $stmt->bindParam(':contact_slogan', $contact_slogan);

        // Bind các tham số cho URL nút CTA
        $stmt->bindParam(':button_link', $button_link);
        $stmt->bindParam(':button2_link', $button2_link);

        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Thêm banner thành công!',
            'id' => $conn->lastInsertId()
        ];
    } catch (PDOException $e) {
        error_log("Lỗi khi thêm banner: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Lỗi khi thêm banner: ' . $e->getMessage()
        ];
    }
}

/**
 * Cập nhật thông tin banner
 *
 * @param int $id ID của banner
 * @param array $data Dữ liệu cập nhật
 * @return array Kết quả thực hiện
 */
function update_banner($id, $data) {
    global $conn;

    try {
        // Tạo câu lệnh SQL động dựa trên các trường được cung cấp
        $sql = "UPDATE banners SET ";
        $params = [];

        if (isset($data['image'])) {
            $params[] = "image = :image";
        }

        if (isset($data['link'])) {
            $params[] = "link = :link";
        }

        if (isset($data['status'])) {
            $params[] = "status = :status";
        }

        if (isset($data['banner_type'])) {
            $params[] = "banner_type = :banner_type";
        }

        if (isset($data['sort_order'])) {
            $params[] = "sort_order = :sort_order";
        }

        // Thêm các trường nội dung tùy chỉnh
        if (isset($data['title_text'])) {
            $params[] = "title_text = :title_text";
        }

        if (isset($data['subtitle_text'])) {
            $params[] = "subtitle_text = :subtitle_text";
        }

        if (isset($data['description_text'])) {
            $params[] = "description_text = :description_text";
        }

        if (isset($data['button_text'])) {
            $params[] = "button_text = :button_text";
        }

        // Thêm các trường nội dung tùy chỉnh bổ sung
        if (isset($data['badge_text'])) {
            $params[] = "badge_text = :badge_text";
        }

        if (isset($data['feature1_text'])) {
            $params[] = "feature1_text = :feature1_text";
        }

        if (isset($data['feature2_text'])) {
            $params[] = "feature2_text = :feature2_text";
        }

        if (isset($data['feature3_text'])) {
            $params[] = "feature3_text = :feature3_text";
        }

        if (isset($data['button2_text'])) {
            $params[] = "button2_text = :button2_text";
        }

        if (isset($data['contact_phone'])) {
            $params[] = "contact_phone = :contact_phone";
        }

        if (isset($data['contact_slogan'])) {
            $params[] = "contact_slogan = :contact_slogan";
        }

        // Thêm các trường URL cho nút CTA
        if (isset($data['button_link'])) {
            $params[] = "button_link = :button_link";
        }

        if (isset($data['button2_link'])) {
            $params[] = "button2_link = :button2_link";
        }

        $sql .= implode(", ", $params);
        $sql .= " WHERE id = :id";

        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);

        if (isset($data['image'])) {
            $stmt->bindParam(':image', $data['image']);
        }

        if (isset($data['link'])) {
            $stmt->bindParam(':link', $data['link']);
        }

        if (isset($data['status'])) {
            $stmt->bindParam(':status', $data['status'], PDO::PARAM_INT);
        }

        if (isset($data['banner_type'])) {
            $stmt->bindParam(':banner_type', $data['banner_type']);
        }

        if (isset($data['sort_order'])) {
            $stmt->bindParam(':sort_order', $data['sort_order'], PDO::PARAM_INT);
        }

        // Bind các tham số cho trường nội dung tùy chỉnh
        if (isset($data['title_text'])) {
            $stmt->bindParam(':title_text', $data['title_text']);
        }

        if (isset($data['subtitle_text'])) {
            $stmt->bindParam(':subtitle_text', $data['subtitle_text']);
        }

        if (isset($data['description_text'])) {
            $stmt->bindParam(':description_text', $data['description_text']);
        }

        if (isset($data['button_text'])) {
            $stmt->bindParam(':button_text', $data['button_text']);
        }

        // Bind các tham số cho trường nội dung tùy chỉnh bổ sung
        if (isset($data['badge_text'])) {
            $stmt->bindParam(':badge_text', $data['badge_text']);
        }

        if (isset($data['feature1_text'])) {
            $stmt->bindParam(':feature1_text', $data['feature1_text']);
        }

        if (isset($data['feature2_text'])) {
            $stmt->bindParam(':feature2_text', $data['feature2_text']);
        }

        if (isset($data['feature3_text'])) {
            $stmt->bindParam(':feature3_text', $data['feature3_text']);
        }

        if (isset($data['button2_text'])) {
            $stmt->bindParam(':button2_text', $data['button2_text']);
        }

        if (isset($data['contact_phone'])) {
            $stmt->bindParam(':contact_phone', $data['contact_phone']);
        }

        if (isset($data['contact_slogan'])) {
            $stmt->bindParam(':contact_slogan', $data['contact_slogan']);
        }

        // Bind các tham số cho URL nút CTA
        if (isset($data['button_link'])) {
            $stmt->bindParam(':button_link', $data['button_link']);
        }

        if (isset($data['button2_link'])) {
            $stmt->bindParam(':button2_link', $data['button2_link']);
        }

        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Cập nhật banner thành công!'
        ];
    } catch (PDOException $e) {
        error_log("Lỗi khi cập nhật banner: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Lỗi khi cập nhật banner: ' . $e->getMessage()
        ];
    }
}

/**
 * Xóa banner
 *
 * @param int $id ID của banner
 * @return array Kết quả thực hiện
 */
function delete_banner($id) {
    global $conn;

    try {
        $stmt = $conn->prepare("DELETE FROM banners WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Xóa banner thành công!'
        ];
    } catch (PDOException $e) {
        error_log("Lỗi khi xóa banner: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Lỗi khi xóa banner: ' . $e->getMessage()
        ];
    }
}

/**
 * Lấy danh sách banner theo điều kiện
 *
 * @param array $conditions Các điều kiện lọc (banner_type, status, etc.)
 * @param int $limit Số lượng banner cần lấy
 * @param int $offset Vị trí bắt đầu
 * @return array Mảng chứa thông tin banner
 */
function get_banners($conditions = [], $limit = null, $offset = 0) {
    global $conn;

    $sql = "SELECT * FROM banners WHERE 1=1";
    $params = [];

    // Thêm điều kiện lọc
    if (!empty($conditions)) {
        foreach ($conditions as $key => $value) {
            $sql .= " AND {$key} = :{$key}";
            $params[":{$key}"] = $value;
        }
    }

    // Sắp xếp
    $sql .= " ORDER BY sort_order ASC, id DESC";

    // Giới hạn số lượng
    if ($limit !== null) {
        $sql .= " LIMIT :offset, :limit";
        $params[':offset'] = (int)$offset;
        $params[':limit'] = (int)$limit;
    }

    try {
        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            if ($key === ':offset' || $key === ':limit') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Lỗi khi lấy danh sách banner: " . $e->getMessage());
        return [];
    }
}

/**
 * Đếm số lượng banner
 *
 * @return int Số lượng banner
 */
function count_banners() {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM banners");
        $stmt->execute();
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        error_log("Lỗi khi đếm banner: " . $e->getMessage());
        return 0;
    }
}

/**
 * Cập nhật thứ tự của banner
 *
 * @param int $id ID của banner
 * @param int $new_order Thứ tự mới
 * @return array Kết quả thực hiện
 */
function update_banner_order($id, $new_order) {
    global $conn;

    try {
        $sql = "UPDATE banners SET sort_order = :sort_order WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':sort_order', $new_order, PDO::PARAM_INT);
        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Cập nhật thứ tự banner thành công!'
        ];
    } catch (PDOException $e) {
        error_log("Lỗi khi cập nhật thứ tự banner: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Lỗi khi cập nhật thứ tự banner: ' . $e->getMessage()
        ];
    }
}
