/**
 * Recently Viewed & Trending Products JavaScript
 * Xử lý tương tác cho phần sản phẩm xem gần đây và trending
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Recently viewed trending JS loaded');

    // Debug: Kiểm tra xem sections có tồn tại không
    const recentlyViewedSection = document.querySelector('.recently-viewed-section');
    const trendingSection = document.querySelector('.trending-section');

    console.log('Recently viewed section found:', !!recentlyViewedSection);
    console.log('Trending section found:', !!trendingSection);

    if (recentlyViewedSection) {
        console.log('Recently viewed section styles:', window.getComputedStyle(recentlyViewedSection));
    }

    if (trendingSection) {
        console.log('Trending section styles:', window.getComputedStyle(trendingSection));
    }

    initRecentlyViewedTrending();
});

function initRecentlyViewedTrending() {
    // Khởi tạo các tính năng
    initProductCardAnimations();
    initLazyLoading();
    initViewTracking();

    // Thêm intersection observer cho animations
    initScrollAnimations();
}

/**
 * Khởi tạo animations cho product cards
 */
function initProductCardAnimations() {
    const productCards = document.querySelectorAll('.recently-viewed-section .group, .trending-section .group');

    productCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
}

/**
 * Khởi tạo lazy loading cho hình ảnh
 */
function initLazyLoading() {
    const images = document.querySelectorAll('.recently-viewed-section img, .trending-section img');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.classList.add('loaded');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }
}

/**
 * Khởi tạo scroll animations
 */
function initScrollAnimations() {
    const sections = document.querySelectorAll('.recently-viewed-section, .trending-section');

    // Hiển thị ngay lập tức thay vì chờ IntersectionObserver
    sections.forEach(section => {
        section.classList.add('animate-in');
        section.style.opacity = '1';
        section.style.visibility = 'visible';
        section.style.display = 'block';
    });

    // Vẫn giữ IntersectionObserver cho các hiệu ứng khác nếu cần
    if ('IntersectionObserver' in window) {
        const sectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, {
            threshold: 0.1
        });

        sections.forEach(section => sectionObserver.observe(section));
    }
}

/**
 * Khởi tạo tracking lượt xem
 */
function initViewTracking() {
    const sections = document.querySelectorAll('.recently-viewed-section, .trending-section');

    if ('IntersectionObserver' in window) {
        const sectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const section = entry.target;
                    const sectionType = section.classList.contains('recently-viewed-section') ? 'recently_viewed' : 'trending';

                    // Track section view
                    trackSectionView(sectionType);
                }
            });
        }, {
            threshold: 0.5
        });

        sections.forEach(section => sectionObserver.observe(section));
    }
}

/**
 * Track section view
 */
function trackSectionView(sectionType) {
    // TODO: Gửi tracking data đến server
    console.log('Section viewed:', sectionType);
}

/**
 * Refresh recently viewed products (có thể gọi từ bên ngoài)
 */
function refreshRecentlyViewed() {
    const recentlyViewedSection = document.querySelector('.recently-viewed-section');
    if (recentlyViewedSection) {
        // TODO: Implement AJAX refresh
        console.log('Refreshing recently viewed products...');
    }
}

/**
 * Refresh trending products (có thể gọi từ bên ngoài)
 */
function refreshTrending() {
    const trendingSection = document.querySelector('.trending-section');
    if (trendingSection) {
        // TODO: Implement AJAX refresh
        console.log('Refreshing trending products...');
    }
}

// CSS animations cho sections
const style = document.createElement('style');
style.textContent = `
    .recently-viewed-section,
    .trending-section {
        opacity: 1;
        transform: translateY(0);
        transition: all 0.6s ease-out;
    }

    .recently-viewed-section.animate-in,
    .trending-section.animate-in {
        opacity: 1;
        transform: translateY(0);
    }

    .recently-viewed-section img,
    .trending-section img {
        transition: opacity 0.3s ease;
        opacity: 1;
    }

    .recently-viewed-section img.loaded,
    .trending-section img.loaded {
        opacity: 1;
    }

    .recently-viewed-section .group,
    .trending-section .group {
        transition: all 0.3s ease;
    }

    /* Đảm bảo sections luôn hiển thị */
    .recently-viewed-section,
    .trending-section {
        display: block !important;
        visibility: visible !important;
    }
`;
document.head.appendChild(style);