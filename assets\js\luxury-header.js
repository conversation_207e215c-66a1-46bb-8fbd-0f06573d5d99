/**
 * Luxury Header JavaScript for Nội Thất Bàng Vũ
 * Modern, elegant interactions for furniture industry
 */

document.addEventListener('DOMContentLoaded', function() {
    // Sticky header behavior
    const luxuryHeader = document.querySelector('.luxury-header');
    const topBar = document.querySelector('.top-bar');
    let lastScrollTop = 0;
    let isScrolled = false; // Biến theo dõi trạng thái hiện tại
    let scrollStateChangeTimer = null; // Timer để trì hoãn thay đổi trạng thái
    const scrollThreshold = 10; // Ngưỡng để thêm/xóa class scrolled
    const scrollHysteresis = 5; // Khoảng trễ để tránh nhấp nháy (hysteresis)

    if (luxuryHeader && topBar) {
        const topBarHeight = topBar.offsetHeight;

        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // <PERSON><PERSON> lý trạng thái scrolled với hysteresis để tránh nhấp nháy
            // Nếu đang ở trạng thái không scrolled, chỉ chuyển sang scrolled khi vượt qua ngưỡng
            // Nếu đang ở trạng thái scrolled, chỉ chuyển về không scrolled khi thấp hơn (ngưỡng - hysteresis)
            if (!isScrolled && scrollTop > scrollThreshold) {
                // Chuyển từ không scrolled sang scrolled
                clearTimeout(scrollStateChangeTimer);
                scrollStateChangeTimer = setTimeout(() => {
                    luxuryHeader.classList.add('scrolled');
                    isScrolled = true;
                }, 50); // Trì hoãn nhỏ để tránh thay đổi quá nhanh
            } else if (isScrolled && scrollTop < (scrollThreshold - scrollHysteresis)) {
                // Chuyển từ scrolled sang không scrolled
                clearTimeout(scrollStateChangeTimer);
                scrollStateChangeTimer = setTimeout(() => {
                    luxuryHeader.classList.remove('scrolled');
                    isScrolled = false;
                }, 50); // Trì hoãn nhỏ để tránh thay đổi quá nhanh
            }

            // Hide/show top bar based on scroll direction
            if (scrollTop > lastScrollTop && scrollTop > topBarHeight) {
                // Scrolling down
                topBar.style.transform = 'translateY(-100%)';
            } else {
                // Scrolling up
                topBar.style.transform = 'translateY(0)';
            }

            lastScrollTop = scrollTop;
        });

        // Kiểm tra trạng thái ban đầu khi trang tải
        const initialScrollTop = window.pageYOffset || document.documentElement.scrollTop;
        if (initialScrollTop > scrollThreshold) {
            luxuryHeader.classList.add('scrolled');
            isScrolled = true;
        }
    }

    // Search panel toggle
    const searchToggle = document.querySelector('.search-toggle');
    const searchPanel = document.querySelector('.search-panel');

    if (searchToggle && searchPanel) {
        searchToggle.addEventListener('click', function(e) {
            e.preventDefault();
            searchPanel.classList.toggle('active');

            // Focus search input when panel is opened
            if (searchPanel.classList.contains('active')) {
                setTimeout(() => {
                    const searchInput = searchPanel.querySelector('.search-input');
                    if (searchInput) searchInput.focus();
                }, 100);
            }
        });

        // Close search panel when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchToggle.contains(e.target) && !searchPanel.contains(e.target)) {
                searchPanel.classList.remove('active');
            }
        });
    }

    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    const mobileMenuClose = document.querySelector('.mobile-menu-close');

    if (mobileMenuToggle && mobileMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('active');
            document.body.classList.toggle('menu-open');

            // Change icon based on menu state
            const icon = this.querySelector('i');
            if (icon) {
                if (mobileMenu.classList.contains('active')) {
                    icon.classList.remove('fa-bars');
                    icon.classList.add('fa-times');
                } else {
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            }
        });
    }

    if (mobileMenuClose && mobileMenu) {
        mobileMenuClose.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
            document.body.classList.remove('menu-open');

            // Reset mobile menu toggle icon
            const icon = mobileMenuToggle.querySelector('i');
            if (icon) {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
    }

    // Mobile dropdown toggles
    const mobileDropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');

    mobileDropdownToggles.forEach(function(toggle) {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();

            const parent = this.parentElement;
            const submenu = this.nextElementSibling;

            // Toggle active class on parent
            parent.classList.toggle('active');

            // Toggle submenu height
            if (submenu) {
                if (parent.classList.contains('active')) {
                    submenu.style.maxHeight = submenu.scrollHeight + 'px';
                } else {
                    submenu.style.maxHeight = '0';
                }
            }

            // Toggle icon
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.toggle('fa-chevron-down');
                icon.classList.toggle('fa-chevron-up');
            }
        });
    });

    // Dropdown menu positioning
    const navItems = document.querySelectorAll('.nav-item');

    navItems.forEach(function(item) {
        const dropdown = item.querySelector('.dropdown-menu');

        if (dropdown) {
            // Check if dropdown would go off-screen
            item.addEventListener('mouseenter', function() {
                const rect = dropdown.getBoundingClientRect();
                const windowWidth = window.innerWidth;

                if (rect.right > windowWidth) {
                    dropdown.style.left = 'auto';
                    dropdown.style.right = '0';
                    dropdown.style.transform = 'translateY(10px)';
                }

                if (rect.left < 0) {
                    dropdown.style.left = '0';
                    dropdown.style.right = 'auto';
                    dropdown.style.transform = 'translateY(10px)';
                }
            });
        }
    });

    // Smooth scroll for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]:not([href="#"])');

    anchorLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                const headerHeight = luxuryHeader.offsetHeight;
                const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });

                // Close mobile menu if open
                if (mobileMenu && mobileMenu.classList.contains('active')) {
                    mobileMenu.classList.remove('active');
                    document.body.classList.remove('menu-open');

                    // Reset mobile menu toggle icon
                    const icon = mobileMenuToggle.querySelector('i');
                    if (icon) {
                        icon.classList.remove('fa-times');
                        icon.classList.add('fa-bars');
                    }
                }
            }
        });
    });

    // Add animation classes on scroll
    const animatedElements = document.querySelectorAll('.animate-on-scroll');

    if (animatedElements.length > 0) {
        const checkIfInView = function() {
            animatedElements.forEach(function(element) {
                const rect = element.getBoundingClientRect();
                const windowHeight = window.innerHeight;

                if (rect.top <= windowHeight * 0.8) {
                    element.classList.add('animated');
                }
            });
        };

        // Check on load
        checkIfInView();

        // Check on scroll
        window.addEventListener('scroll', checkIfInView);
    }
});
