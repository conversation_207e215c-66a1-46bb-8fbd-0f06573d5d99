# BÁO CÁO PHÂN TÍCH HỆ THỐNG BANNER TRONG DỰ ÁN

## 1. Tổng quan về hệ thống banner

Hệ thống banner trong dự án Nội Thất Bàng Vũ được xây dựng để quản lý và hiển thị các banner quảng cáo trên các vị trí khác nhau trong website. Banner đóng vai trò quan trọng trong việc thu hút sự chú ý của người dùng và quảng bá sản phẩm, dịch vụ của cửa hàng.

## 2. <PERSON><PERSON> sở dữ liệu

### Bảng banners
Bảng này lưu trữ thông tin về các banner trong hệ thống với các trường chính:

- **id**: Kh<PERSON><PERSON> chính
- **title**: Tiêu đề banner
- **description**: <PERSON><PERSON>ả chi tiết
- **image**: Đường dẫn đến hình ảnh banner
- **link**: Đ<PERSON>ờng dẫn khi người dùng nhấp vào banner
- **position**: <PERSON>ị trí hiển thị banner trên trang web
- **status**: Trạng thái hiển thị (1: hiển thị, 0: ẩn)
- **sort_order**: Thứ tự sắp xếp
- **start_date**: Thời gian bắt đầu hiển thị
- **end_date**: Thời gian kết thúc hiển thị
- **created_at**: Thời gian tạo
- **updated_at**: Thời gian cập nhật gần nhất
- **category_id**: ID danh mục liên kết (nếu có)
- **display_style**: Kiểu hiển thị (mặc định: 'default')
- **media_type**: Loại phương tiện (image, video)
- **video_url**: Đường dẫn đến video nếu media_type là video
- **template_id**: ID mẫu template nếu sử dụng
- **template_data**: Dữ liệu tùy chỉnh cho template

### Bảng banner_templates
Bảng này lưu trữ các mẫu template cho banner với các trường:

- **id**: Khóa chính
- **name**: Tên template
- **description**: Mô tả chi tiết
- **html_template**: Mã HTML của template
- **css_template**: Mã CSS của template
- **js_template**: Mã JavaScript của template
- **preview_image**: Ảnh xem trước của template
- **created_at**: Thời gian tạo
- **updated_at**: Thời gian cập nhật gần nhất

## 3. Các loại banner và vị trí

Hệ thống hỗ trợ các loại vị trí hiển thị banner sau:

1. **home_main**: Banner chính trên trang chủ
2. **home_secondary**: Banner phụ trên trang chủ
3. **category_page**: Banner trên trang danh mục

## 4. Mẫu banner (Templates)

Hệ thống cung cấp các mẫu banner có sẵn với các hiệu ứng khác nhau:

1. **Parallax Effect**: Banner với hiệu ứng parallax, các phần tử di chuyển với tốc độ khác nhau khi cuộn trang
2. **Particles Effect**: Banner với hiệu ứng particles (các hạt chuyển động) tạo cảm giác động và hiện đại

## 5. Các dạng phương tiện hỗ trợ

Hệ thống banner hỗ trợ các loại phương tiện:
- Hình ảnh (image): Hỗ trợ các định dạng JPG, JPEG, PNG, GIF, WebP
- Video (video): Hỗ trợ nhúng video từ URL

## 6. Quản lý banner trong admin

Giao diện quản lý banner trong admin cho phép:
- Thêm mới banner
- Chỉnh sửa banner
- Xóa banner
- Thay đổi trạng thái hiển thị (hiển thị/ẩn)
- Sắp xếp thứ tự hiển thị
- Thiết lập thời gian hiển thị

## 7. Hiển thị banner trên trang web

### 7.1. Banner chính trang chủ (home_main)
- Hiển thị ở phần đầu trang chủ
- Nếu có nhiều banner, hệ thống tự động tạo slide show
- Hỗ trợ nút điều hướng và chấm pagination

### 7.2. Banner phụ trang chủ (home_secondary)
- Hiển thị ở phần giữa trang chủ
- Thường được thiết kế dạng grid với 2 cột

### 7.3. Banner trang danh mục (category_page)
- Hiển thị ở đầu trang danh mục sản phẩm

## 8. Các tệp liên quan đến banner

### 8.1. Tệp cập nhật cơ sở dữ liệu
- update_banner_table.php: Tạo bảng banners
- update_banner_category.php: Thêm cột category_id vào bảng banners
- update_banner_display_style.php: Thêm cột display_style vào bảng banners
- update_banner_default_web.php: Thêm cột template_id và template_data vào bảng banners
- update_banner_video.php: Thêm cột video_url và media_type vào bảng banners

### 8.2. Tệp chức năng
- includes/banner.php: Chứa các hàm xử lý banner

### 8.3. Tệp quản lý admin
- admin/banners.php: Trang quản lý danh sách banner
- admin/banner-add.php: Trang thêm mới banner
- admin/banner-edit.php: Trang chỉnh sửa banner

## 9. Các hàm xử lý banner

- **get_banners($limit, $offset, $position, $status)**: Lấy danh sách banner
- **get_banner_by_id($id)**: Lấy thông tin chi tiết banner theo ID
- **add_banner($data)**: Thêm banner mới
- **update_banner($id, $data)**: Cập nhật thông tin banner
- **delete_banner($id)**: Xóa banner
- **count_banners($position, $status)**: Đếm số lượng banner

## 10. Đề xuất cải tiến

1. **Tích hợp tính năng responsive**: Đảm bảo banner hiển thị tốt trên tất cả các thiết bị
2. **Thêm hiệu ứng chuyển động**: Bổ sung các hiệu ứng animation cho banner
3. **Tích hợp chức năng phân tích hiệu quả**: Theo dõi số lượt click và hiển thị
4. **Tối ưu hóa tải trang**: Tối ưu kích thước hình ảnh và video để tăng tốc độ tải trang
5. **Đa dạng hóa mẫu banner**: Bổ sung nhiều mẫu banner với các hiệu ứng đặc biệt
6. **Tích hợp AI để tạo banner**: Sử dụng công nghệ AI để gợi ý và tạo banner
7. **Tích hợp với các chiến dịch marketing**: Liên kết banner với các chiến dịch marketing khác 