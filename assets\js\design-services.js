/**
 * Design Services Section JavaScript - Optimized
 * Xử lý hiệu ứng và tương tác cho phần Dịch vụ thiết kế
 */
document.addEventListener('DOMContentLoaded', function() {
    // Khởi tạo hiệu ứng xuất hiện khi cuộn
    initScrollAnimations();

    // Xử lý sự kiện click cho các nút
    handleButtonClicks();
});

/**
 * Khởi tạo hiệu ứng xuất hiện khi cuộn đến phần tử
 */
function initScrollAnimations() {
    // Lấy tất cả các phần tử cần animation
    const animatedElements = document.querySelectorAll('.design-services-section .fade-in-up');

    // Tạo Intersection Observer để theo dõi khi phần tử xuất hiện trong viewport
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Thêm class visible để kích hoạt animation
                entry.target.classList.add('visible');
                // Ngừng theo dõi phần tử này sau khi đã hiển thị
                observer.unobserve(entry.target);
            }
        });
    }, {
        root: null, // Viewport
        rootMargin: '0px',
        threshold: 0.1 // Kích hoạt khi 10% phần tử xuất hiện
    });

    // Theo dõi tất cả các phần tử
    animatedElements.forEach((element, index) => {
        // Thêm delay tăng dần cho mỗi phần tử (giới hạn ở 0.3s để tránh chờ quá lâu)
        element.style.transitionDelay = `${Math.min(index * 0.1, 0.3)}s`;
        observer.observe(element);
    });

    // Fallback cho trình duyệt không hỗ trợ Intersection Observer
    if (!('IntersectionObserver' in window)) {
        animatedElements.forEach(element => {
            setTimeout(() => {
                element.classList.add('visible');
            }, 100);
        });
    }
}

/**
 * Xử lý sự kiện click cho các nút
 */
function handleButtonClicks() {
    // Lấy tất cả các nút trong phần dịch vụ thiết kế
    const buttons = document.querySelectorAll('.design-service-button');

    buttons.forEach(button => {
        button.addEventListener('click', function() {
            // Thêm hiệu ứng khi click
            this.classList.add('button-clicked');

            // Xóa class sau khi hoàn thành animation
            setTimeout(() => {
                this.classList.remove('button-clicked');
            }, 300);
        });
    });

    // Thêm hiệu ứng ripple cho các nút
    addRippleEffect();
}

/**
 * Thêm hiệu ứng ripple khi click vào nút
 */
function addRippleEffect() {
    const buttons = document.querySelectorAll('.design-service-button');

    buttons.forEach(button => {
        button.addEventListener('mousedown', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const ripple = document.createElement('span');
            ripple.classList.add('ripple-effect');
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}
