/*
 * Luxury Mobile Menu CSS for Nội Thất Bàng <PERSON>ũ
 * Thiết kế menu sang trọng và hiện đại cho website nội thất
 */

/* <PERSON><PERSON><PERSON>n CSS */
:root {
    --mobile-header-height: 60px;
    --mobile-menu-transition: 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    --mobile-menu-bg: var(--white, #ffffff);
    --mobile-menu-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --mobile-menu-padding: 1.5rem;
    --mobile-menu-item-spacing: 1rem;
    --mobile-menu-border-color: rgba(229, 231, 235, 0.5);
    --mobile-menu-highlight: #f97316;
    --mobile-menu-highlight-bg: rgba(249, 115, 22, 0.08);
    --mobile-menu-text: #333333;
    --mobile-menu-text-light: #666666;
    --mobile-menu-icon-size: 1.25rem;
    --mobile-menu-font-size: 1rem;
    --mobile-menu-radius: 0.5rem;
    --mobile-menu-footer-bg: #f8fafc;
}

/* Mobile Menu Container */
.mobile-menu {
    position: fixed;
    top: var(--mobile-header-height);
    left: 0;
    width: 100%;
    height: calc(100vh - var(--mobile-header-height));
    background-color: var(--mobile-menu-bg);
    z-index: var(--z-modal, 1000);
    overflow: hidden;
    transform: translateY(-10px);
    opacity: 0;
    visibility: hidden;
    transition: transform var(--mobile-menu-transition),
        opacity var(--mobile-menu-transition),
        visibility 0s var(--mobile-menu-transition);
    box-shadow: var(--mobile-menu-shadow);
    display: flex;
    flex-direction: column;
    will-change: transform, opacity;
}

.mobile-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
    transition: transform var(--mobile-menu-transition),
        opacity var(--mobile-menu-transition),
        visibility 0s 0s;
    padding-bottom: 0px;
}

/* Menu chính */
.mobile-menu-main {
    position: relative;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding-top: 1rem;
    /* Đảm bảo có thể cuộn khi nội dung dài */
    -webkit-overflow-scrolling: touch;
}

/* Prevent body scroll when menu is open */
body.overflow-hidden {
    overflow: hidden;
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: var(--mobile-header-height);
    left: 0;
    width: 100%;
    height: calc(100vh - var(--mobile-header-height));
    background-color: rgba(0, 0, 0, 0.5);
    z-index: var(--z-fixed, 900);
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--mobile-menu-transition),
        visibility 0s var(--mobile-menu-transition);
    -webkit-backdrop-filter: blur(3px);
    backdrop-filter: blur(3px);
    will-change: opacity;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
    transition: opacity var(--mobile-menu-transition),
        visibility 0s 0s;
}

/* Mobile Menu Navigation */
.mobile-menu-nav {
    flex: 1;
    padding: 0 var(--mobile-menu-padding);
}

.mobile-menu-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.mobile-menu-item {
    margin-bottom: var(--mobile-menu-item-spacing);
    position: relative;
}

.mobile-menu-item:not(:last-child) {
    border-bottom: 1px solid var(--mobile-menu-border-color);
    padding-bottom: var(--mobile-menu-item-spacing);
}

.mobile-menu-link {
    display: flex;
    align-items: center;
    color: var(--mobile-menu-text);
    text-decoration: none;
    font-size: var(--mobile-menu-font-size);
    font-weight: 500;
    padding: 0.75rem 0;
    transition: all 0.3s ease;
    position: relative;
    border-radius: var(--mobile-menu-radius);
}

.mobile-menu-link i:first-child {
    font-size: var(--mobile-menu-icon-size);
    width: 2rem;
    text-align: center;
    margin-right: 0.75rem;
    color: var(--mobile-menu-highlight);
    transition: all 0.3s ease;
}

.mobile-menu-link i.fa-chevron-down {
    margin-left: auto;
    font-size: 0.875rem;
    transition: transform 0.3s ease;
}

.mobile-menu-item.active>.mobile-menu-link i.fa-chevron-down {
    transform: rotate(180deg);
}

.mobile-menu-link:hover,
.mobile-menu-item.active>.mobile-menu-link {
    color: var(--mobile-menu-highlight);
    background-color: var(--mobile-menu-highlight-bg);
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    border-radius: var(--mobile-menu-radius);
}

/* Mobile Badge */
.mobile-badge {
    background-color: var(--mobile-menu-highlight);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 5px;
    margin-left: 8px;
}

/* Mobile Submenu */
.mobile-submenu {
    list-style: none;
    margin: 0.5rem 0 0 2.75rem;
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94), margin-bottom 0.3s ease;
    border-left: 2px solid var(--mobile-menu-border-color);
    will-change: max-height;
    transform-origin: top;
}

/* Đảm bảo submenu có thể mở rộng đúng cách */
.mobile-submenu-item.active>.mobile-submenu {
    margin-bottom: 0.5rem;
    animation: submenuFadeIn 0.3s ease forwards;
}

/* Submenu Header with Back Button */
.mobile-submenu-header {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    margin-bottom: 0.5rem;
    border-bottom: 1px solid var(--mobile-menu-border-color);
}

.mobile-menu-back {
    background: none;
    border: none;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--mobile-menu-text);
    margin-right: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: rgba(0, 0, 0, 0.05);
}

.mobile-menu-back:hover {
    background-color: var(--mobile-menu-highlight-bg);
    color: var(--mobile-menu-highlight);
}

.mobile-submenu-header span {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--mobile-menu-text);
    flex: 1;
}

/* Submenu Items */
.mobile-submenu-item {
    margin-bottom: 0.25rem;
}

.mobile-submenu-link {
    display: flex;
    align-items: center;
    color: var(--mobile-menu-text-light);
    text-decoration: none;
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
    border-radius: var(--mobile-menu-radius);
}

.mobile-submenu-link i {
    font-size: 0.875rem;
    width: 1.5rem;
    text-align: center;
    margin-right: 0.5rem;
    color: var(--mobile-menu-highlight);
    opacity: 0.8;
}

.mobile-submenu-link i.fa-chevron-down {
    margin-left: auto;
    margin-right: 0;
    font-size: 0.75rem;
    transition: transform 0.3s ease;
}

.mobile-submenu-item.active>.mobile-submenu-link i.fa-chevron-down {
    transform: rotate(180deg);
}

.mobile-submenu-link:hover {
    color: var(--mobile-menu-highlight);
    background-color: var(--mobile-menu-highlight-bg);
    padding-left: 1rem;
}

/* Mobile Menu Footer */
.mobile-menu-footer {
    padding: var(--mobile-menu-padding);
    background-color: var(--mobile-menu-footer-bg);
    position: relative;
    border-top: 1px solid var(--mobile-menu-border-color);
}

.mobile-contact-info {
    margin-bottom: 1.5rem;
}

.mobile-contact-info h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--mobile-menu-text);
    margin: 0 0 1rem;
    position: relative;
    display: inline-block;
}

.mobile-contact-info h3:after {
    content: '';
    position: absolute;
    bottom: -0.25rem;
    left: 0;
    width: 2rem;
    height: 2px;
    background-color: var(--mobile-menu-highlight);
}

.mobile-contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.mobile-contact-item i {
    font-size: 1rem;
    color: var(--mobile-menu-highlight);
    margin-right: 0.75rem;
    margin-top: 0.25rem;
}

.mobile-contact-item a,
.mobile-contact-item span {
    font-size: 0.875rem;
    color: var(--mobile-menu-text-light);
    text-decoration: none;
    flex: 1;
    line-height: 1.5;
}

.mobile-contact-item a:hover {
    color: var(--mobile-menu-highlight);
}

/* Mobile Social Links */
.mobile-social {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.mobile-social-link {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--mobile-menu-text);
    margin-right: 0.75rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.mobile-social-link:hover {
    background-color: var(--mobile-menu-highlight);
    color: white;
    transform: translateY(-3px);
}

/* Mobile CTA Button */
.mobile-cta {
    margin-top: 1rem;
}

.mobile-cta-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0.875rem 1rem;
    background-color: var(--mobile-menu-highlight);
    color: white;
    font-weight: 600;
    text-decoration: none;
    border-radius: var(--mobile-menu-radius);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mobile-cta-button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.mobile-cta-button:hover {
    background-color: #e86207;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.25);
}

.mobile-cta-button:hover:before {
    left: 100%;
}

.mobile-cta-button i {
    margin-right: 0.5rem;
    font-size: 1.125rem;
}

/* Animation for mobile menu items */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation for submenu */
@keyframes submenuFadeIn {
    from {
        opacity: 0.8;
        transform: translateY(-5px) scale(0.98);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.mobile-menu.active .mobile-menu-item {
    animation: fadeInUp 0.4s ease forwards;
    opacity: 0;
}

.mobile-menu.active .mobile-menu-item:nth-child(1) {
    animation-delay: 0.05s;
}

.mobile-menu.active .mobile-menu-item:nth-child(2) {
    animation-delay: 0.1s;
}

.mobile-menu.active .mobile-menu-item:nth-child(3) {
    animation-delay: 0.15s;
}

.mobile-menu.active .mobile-menu-item:nth-child(4) {
    animation-delay: 0.2s;
}

.mobile-menu.active .mobile-menu-item:nth-child(5) {
    animation-delay: 0.25s;
}

.mobile-menu.active .mobile-menu-item:nth-child(6) {
    animation-delay: 0.3s;
}

.mobile-menu.active .mobile-menu-item:nth-child(7) {
    animation-delay: 0.35s;
}

.mobile-menu.active .mobile-menu-item:nth-child(8) {
    animation-delay: 0.4s;
}

.mobile-menu.active .mobile-menu-item:nth-child(9) {
    animation-delay: 0.45s;
}

.mobile-menu.active .mobile-menu-item:nth-child(10) {
    animation-delay: 0.5s;
}

/* Hiệu ứng cho mobile-menu-footer */
.mobile-menu.active .mobile-menu-footer {
    animation: fadeInUp 0.5s ease forwards;
    animation-delay: 0.55s;
    opacity: 0;
}

/* Đảm bảo menu không bị che khuất bởi bottom navigation */
@media (max-width: 576px) {
    .mobile-menu {
        padding-bottom: var(--mobile-bottom-nav-height, 60px);
    }

    /* Đảm bảo submenu cấp 3 hiển thị đúng */
    .mobile-submenu-item.active>.mobile-submenu {
        /* Khi được mở, đảm bảo hiển thị đúng */
        transition: max-height 0.8s ease;
    }

    /* Đảm bảo submenu cấp 3 hiển thị đúng khi active */
    .mobile-submenu .mobile-submenu-item.active>.mobile-submenu {
        /* Khi được mở, đảm bảo hiển thị đúng */
        transition: max-height 0.8s ease;
    }
}