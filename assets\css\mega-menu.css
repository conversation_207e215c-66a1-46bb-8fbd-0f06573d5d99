/*
 * Mega Menu CSS for Nội Thất Bàng Vũ
 * Modern, professional mega menu for furniture industry
 */

/* Mega Menu Container */
.mega-menu {
    position: absolute;
    top: 100%;
    left: 0;
    transform: translateY(10px);
    background-color: var(--white);
    width: 800px;
    max-width: 90vw;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-xl);
    padding: 0;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    z-index: var(--z-dropdown);
    display: flex;
    overflow: hidden;
    /* Thêm transition cho max-height */
    transition: var(--transition-normal), max-height 0.3s ease;
}

.mega-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 40px;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid var(--white);
}

.nav-item:hover .mega-menu,
.mega-menu:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Giữ hiệu ứng gạch chân khi hover vào mega menu */
.nav-item:hover .nav-link::after,
.nav-item:has(.mega-menu:hover) .nav-link::after {
    width: 100%;
}

/* Mega Menu Left Column - Categories */
.mega-menu-categories {
    width: 30%;
    background-color: var(--ultra-light-gray);
    padding: 0;
    border-right: 1px solid var(--light-gray);
    max-height: 100%;
    overflow-y: auto;
}

.mega-menu-category {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    border-left: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

/* Ripple effect for desktop categories */
.mega-menu-category::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(249, 115, 22, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.mega-menu-category:active::before {
    width: 300px;
    height: 300px;
}

.mega-menu-category:hover,
.mega-menu-category.active {
    background-color: var(--white);
    border-left-color: var(--primary);
}

.mega-menu-category:active {
    transform: translateY(1px);
    background-color: var(--primary-ultra-light);
}

.mega-menu-category-icon {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary);
}

.mega-menu-category-name {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--dark-gray);
}

.mega-menu-category:hover .mega-menu-category-name,
.mega-menu-category.active .mega-menu-category-name {
    color: var(--primary);
}

/* Mega Menu Right Column - Content */
.mega-menu-content {
    width: 70%;
    padding: 20px;
    display: none;
    max-height: 100%;
    overflow-y: auto;
    /* Animation properties */
    opacity: 0;
    transform: translateX(30px) scale(0.98);
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.mega-menu-content.active {
    display: block;
    opacity: 1;
    transform: translateX(0) scale(1);
    /* Overflow will be controlled by JavaScript to prevent scrollbar flicker */
}

/* Staggered animation for desktop content elements */
.mega-menu-content.active .mega-menu-title {
    animation: slideInFadeDesktop 0.5s ease-out 0.1s both;
}

.mega-menu-content.active .mega-menu-subcategories {
    animation: slideInFadeDesktop 0.6s ease-out 0.2s both;
}

.mega-menu-content.active .mega-menu-bestsellers {
    animation: slideInFadeDesktop 0.7s ease-out 0.3s both;
}

.mega-menu-content.active .mega-menu-view-all {
    animation: slideInFadeDesktop 0.8s ease-out 0.4s both;
}

/* Keyframes for desktop animations */
@keyframes slideInFadeDesktop {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.mega-menu-title {
    font-size: var(--text-md);
    font-weight: 600;
    color: var(--dark-gray);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--light-gray);
}

/* Subcategories Grid */
.mega-menu-subcategories {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.mega-menu-subcategory {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    padding: 10px;
    border-radius: 8px; /* Giống tablet - vừa phải */
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    text-align: center;
    position: relative;
    overflow: hidden;
}

/* Subtle glow effect for subcategories */
.mega-menu-subcategory::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.1), rgba(249, 115, 22, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 8px;
}

.mega-menu-subcategory:hover {
    background-color: var(--primary-ultra-light);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(249, 115, 22, 0.15);
}

.mega-menu-subcategory:hover::after {
    opacity: 1;
}

.mega-menu-subcategory-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary);
    background-color: var(--primary-ultra-light);
    border-radius: var(--radius-full);
}

.mega-menu-subcategory-image {
    width: 60px;
    height: 60px;
    margin-bottom: 8px;
    border-radius: 10px; /* Giống tablet - vừa phải */
    overflow: hidden;
}

.mega-menu-subcategory-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.mega-menu-subcategory-name {
    font-size: var(--text-xs);
    font-weight: 500;
    color: var(--dark-gray);
}

.mega-menu-subcategory:hover .mega-menu-subcategory-name {
    color: var(--primary);
}

/* Best Sellers Section */
.mega-menu-bestsellers {
    margin-top: 20px;
}

.mega-menu-bestsellers-title {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--dark-gray);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.mega-menu-bestsellers-title i {
    color: var(--primary);
    margin-right: 8px;
}

.mega-menu-products {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.mega-menu-product {
    text-decoration: none;
    display: block;
}

.mega-menu-product-image {
    width: 100%;
    height: 0;
    padding-bottom: 100%; /* Tạo tỷ lệ 1:1 (hình vuông) */
    border-radius: 8px; /* Giống tablet - vừa phải */
    overflow: hidden;
    margin-bottom: 8px;
    position: relative;
    background: #f3f4f6;
}

.mega-menu-product-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.mega-menu-product {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
}

/* Product hover glow effect */
.mega-menu-product::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.08), rgba(16, 185, 129, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 8px;
}

.mega-menu-product:hover {
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 6px 20px rgba(249, 115, 22, 0.1);
}

.mega-menu-product:hover::before {
    opacity: 1;
}

.mega-menu-product:hover .mega-menu-product-image img {
    transform: scale(1.05);
}

.mega-menu-product-name {
    font-size: var(--text-xs);
    font-weight: 500;
    color: var(--dark-gray);
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.mega-menu-product-price {
    font-size: var(--text-xs);
    font-weight: 600;
    color: var(--primary);
}

.mega-menu-view-all {
    display: inline-block;
    margin-top: 15px;
    font-size: var(--text-xs);
    font-weight: 500;
    color: var(--primary);
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 8px; /* Bo góc consistent với design system */
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    border: 1px solid var(--primary-light);
    position: relative;
    overflow: hidden;
}

/* Button shine effect */
.mega-menu-view-all::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.mega-menu-view-all:hover::before {
    left: 100%;
}

.mega-menu-view-all:hover {
    background-color: var(--primary);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(249, 115, 22, 0.3);
}

/* Tùy chỉnh thanh cuộn cho mega menu */
.mega-menu::-webkit-scrollbar {
    width: 6px;
}

.mega-menu::-webkit-scrollbar-track {
    background: var(--ultra-light-gray);
}

.mega-menu::-webkit-scrollbar-thumb {
    background: var(--primary-light);
    border-radius: 3px;
}

.mega-menu::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
}

/* Tùy chỉnh thanh cuộn cho các phần con */
.mega-menu-categories::-webkit-scrollbar,
.mega-menu-content::-webkit-scrollbar {
    width: 4px;
}

.mega-menu-categories::-webkit-scrollbar-track,
.mega-menu-content::-webkit-scrollbar-track {
    background: var(--ultra-light-gray);
}

.mega-menu-categories::-webkit-scrollbar-thumb,
.mega-menu-content::-webkit-scrollbar-thumb {
    background: var(--primary-light);
    border-radius: 2px;
}

.mega-menu-categories::-webkit-scrollbar-thumb:hover,
.mega-menu-content::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .mega-menu {
        display: none;
    }
}