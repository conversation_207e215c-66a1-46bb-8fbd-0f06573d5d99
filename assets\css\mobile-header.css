/*
 * Mobile Header CSS for Nội Thất Bàng <PERSON>ũ
 * Thiết kế header chuyên nghiệp cho giao diện điện thoại
 */

/* <PERSON><PERSON><PERSON>n CSS */
:root {
    --mobile-header-height: 60px;
    --mobile-bottom-nav-height: 60px;
    --mobile-menu-width: 85%;
    --mobile-header-bg: var(--white);
    --mobile-header-scrolled-bg: var(--header-dark);
    --mobile-header-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --mobile-header-scrolled-shadow: 0 2px 15px rgba(0, 0, 0, 0.15);
    --mobile-menu-transition: 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    --mobile-header-transition: 0.3s ease;
    --mobile-bottom-nav-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    --mobile-submenu-header-bg: #f8f9fa;
    --mobile-submenu-header-border: 1px solid #eaeaea;
    --mobile-submenu-child-bg: #f5f7fa;
}

/* Mobile Header Container */
.mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--mobile-header-height);
    background-color: var(--mobile-header-bg);
    box-shadow: var(--mobile-header-shadow);
    z-index: var(--z-sticky);
    display: none;
    will-change: transform, background-color, box-shadow;

    /* Biến CSS cho hiệu ứng mượt mà */
    --header-opacity: 0;
    --header-scale: 1;
    --header-translate-y: 0px;
}

/* Thêm hiệu ứng transition mượt mà */
.mobile-header.smooth-transition {
    transition:
        transform 0.3s cubic-bezier(0.33, 1, 0.68, 1),
        box-shadow 0.3s cubic-bezier(0.33, 1, 0.68, 1),
        background-color 0.3s cubic-bezier(0.33, 1, 0.68, 1);
}

/* Áp dụng hiệu ứng transform dựa trên biến CSS */
.mobile-header.smooth-transition::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, #202834);
    opacity: var(--header-opacity);
    pointer-events: none;
    z-index: -1;
    transition: opacity 0.3s cubic-bezier(0.33, 1, 0.68, 1);
}

/* Hiệu ứng transform mượt mà khi cuộn */
.mobile-header.smooth-transition:not(.scrolled) {
    transform: translateY(var(--header-translate-y)) scale(var(--header-scale));
}

/* Hiển thị mobile header trên màn hình tablet và điện thoại */
@media (max-width: 768px) {
    .mobile-header {
        display: flex;
        animation: fadeInDown 0.3s cubic-bezier(0.33, 1, 0.68, 1);
    }

    /* Ẩn header thông thường */
    .premium-header {
        display: none;
    }

    /* Thêm padding-top cho body để tránh nội dung bị che khuất */
    body {
        padding-top: var(--mobile-header-height);
        padding-bottom: var(--mobile-bottom-nav-height);
        transition: padding 0.3s cubic-bezier(0.33, 1, 0.68, 1);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Header khi cuộn */
.mobile-header.scrolled {
    background-color: #202834;
    box-shadow: var(--mobile-header-scrolled-shadow);
}

/* Mobile Header Content */
.mobile-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    padding: 0 15px;
}

/* Mobile Logo */
.mobile-header-logo {
    display: flex;
    align-items: center;
    height: 100%;
}

.mobile-header-logo img {
    height: 40px;
    width: auto;
    transition: all var(--mobile-header-transition);
}

/* Mobile Header khi cuộn - logo trắng */
.mobile-header.scrolled .mobile-header-logo img {
    /* Logo sẽ được thay đổi bằng JavaScript */
}

/* Mobile Menu Toggle Button */
.mobile-header-menu-toggle {
    background: none;
    border: none;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--mobile-header-transition);
    border-radius: 50%;
    position: relative;
    padding: 0;
    outline: none;
}

/* Hamburger Icon */
.menu-toggle-icon {
    width: 24px;
    height: 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.menu-toggle-icon span {
    display: block;
    height: 2px;
    width: 100%;
    background-color: var(--dark-gray);
    border-radius: 2px;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    transform-origin: center;
}

/* Hover effect */
.mobile-header-menu-toggle:hover .menu-toggle-icon span {
    background-color: var(--primary);
}

/* Scrolled state */
.mobile-header.scrolled .menu-toggle-icon span {
    background-color: var(--white);
}

.mobile-header.scrolled .mobile-header-menu-toggle:hover .menu-toggle-icon span {
    background-color: var(--primary-light);
}

/* Active state (when menu is open) */
.mobile-header-menu-toggle.active .menu-toggle-icon span:nth-child(1) {
    transform: translateY(9px) rotate(45deg);
}

.mobile-header-menu-toggle.active .menu-toggle-icon span:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

.mobile-header-menu-toggle.active .menu-toggle-icon span:nth-child(3) {
    transform: translateY(-9px) rotate(-45deg);
}

/* Background effect on hover */
.mobile-header-menu-toggle::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: rgba(243, 115, 33, 0.1);
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.mobile-header-menu-toggle:hover::before {
    opacity: 1;
    transform: scale(1);
}

.mobile-header.scrolled .mobile-header-menu-toggle::before {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Ripple effect when clicking */
.menu-toggle-ripple {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(243, 115, 33, 0.3);
    border-radius: 50%;
    transform: scale(0);
    animation: ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    will-change: transform, opacity;
}

.mobile-header.scrolled .menu-toggle-ripple {
    background-color: rgba(255, 255, 255, 0.3);
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }

    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* Bottom Navigation Bar */
.mobile-bottom-nav {
    position: fixed;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 16px);
    height: 62px;
    background-color: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    z-index: var(--z-sticky);
    display: none;
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid rgba(243, 115, 33, 0.1);
}

@media (max-width: 768px) {
    .mobile-bottom-nav {
        display: flex;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px) translateX(-50%);
    }
    to {
        opacity: 1;
        transform: translateY(0) translateX(-50%);
    }
}

.mobile-bottom-nav-content {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    height: 100%;
    padding: 0 8px;
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    /* flex: 1; */
    height: 100%;
    text-decoration: none;
    color: var(--medium-gray);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    padding: 6px 0;
    margin: 0 2px;
}

.mobile-nav-item.active {
    color: var(--primary);
    position: relative;
    z-index: 1;
    background: linear-gradient(to bottom, rgba(243, 115, 33, 0.05), rgba(243, 115, 33, 0.1));
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.mobile-nav-item.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 2px;
    background-color: var(--primary);
    border-radius: 0 0 3px 3px;
    transition: all 0.3s ease;
}

.mobile-nav-item i {
    font-size: 18px;
    margin-bottom: 7px;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.mobile-nav-item.active i {
    transform: scale(1.05);
    text-shadow: 0 2px 4px rgba(243, 115, 33, 0.2);
}

.mobile-nav-item span {
    font-size: 11px;
    font-weight: 400;
    transition: all 0.3s ease;
    letter-spacing: 0.2px;
}

.mobile-nav-item.active span {
    font-weight: 500;
}

.mobile-nav-item:hover {
    color: var(--primary);
}

.mobile-nav-item:hover i {
    transform: translateY(-1px);
}

/* Avatar cho người dùng đã đăng nhập */
.account-nav-item {
    padding-top: 6px !important;
    padding-bottom: 6px !important;
}

.mobile-nav-avatar {
    height: 44px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-inner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background-color: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--primary);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.mobile-nav-item.active .avatar-inner {
    border-color: var(--primary);
}

.mobile-nav-item.account-nav-item {
    overflow: visible;
    padding-left: 4px;
    padding-right: 4px;
    margin-left: 2px;
    margin-right: 2px;
    z-index: 2;
}

.mobile-nav-item.account-nav-item.active {
    border-radius: 16px;
    padding-left: 8px;
    padding-right: 8px;
    width: auto;
    min-width: 60px;
    background: linear-gradient(to bottom, rgba(243, 115, 33, 0.08), rgba(243, 115, 33, 0.12));
}

.default-avatar .avatar-inner {
    background: linear-gradient(145deg, #f8f8f8, #eeeeee);
    color: #555;
    border: none;
    overflow: hidden;
}

.default-avatar i {
    font-size: 22px;
    margin: 0;
    color: var(--primary);
    opacity: 0.8;
    background: linear-gradient(135deg, var(--primary-ultra-light), var(--white));
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.mobile-nav-item:hover .default-avatar i {
    opacity: 1;
    color: var(--primary-dark);
    transform: scale(1.1);
}

.avatar-inner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Chỉ báo trạng thái online */
.avatar-indicators {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
}

.avatar-status-dot {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    background-color: #10b981;
    /* Màu xanh lá */
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
    z-index: 3;
    transition: all 0.3s ease;
}

.mobile-nav-item:hover .avatar-inner {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
}

.mobile-nav-item:hover .avatar-status-dot {
    transform: scale(1.1);
    background-color: #0ea271;
}

.mobile-nav-item.active .avatar-inner {
    box-shadow: 0 0 0 2px var(--primary), 0 4px 10px rgba(243, 115, 33, 0.2);
    transform: scale(1.05);
}

/* Hiệu ứng glow khi active */
@keyframes avatar-glow {
    0% {
        box-shadow: 0 0 0 2px var(--primary), 0 4px 10px rgba(243, 115, 33, 0.2);
        transform: translateY(0);
    }

    50% {
        box-shadow: 0 0 0 2px var(--primary), 0 4px 15px rgba(243, 115, 33, 0.4);
        transform: translateY(-1px);
    }

    100% {
        box-shadow: 0 0 0 2px var(--primary), 0 4px 10px rgba(243, 115, 33, 0.2);
        transform: translateY(0);
    }
}

.mobile-nav-item.active .avatar-inner {
    animation: avatar-glow 2s infinite ease-in-out;
}

/* Badge cho giỏ hàng */
.mobile-nav-badge {
    position: absolute;
    top: 1px;
    right: calc(50% - 20px);
    background-color: #e05e00;
    color: var(--white);
    font-size: 8px;
    font-weight: 600;
    min-width: 14px;
    height: 14px;
    border-radius: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 3px;
    box-shadow: 0 1px 4px rgba(224, 94, 0, 0.3);
    transform: scale(1);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    z-index: 5;
    /* animation: badge-pulse 2s infinite cubic-bezier(0.66, 0, 0, 1); */
    pointer-events: none;
    will-change: transform, box-shadow;
}

/* Hiệu ứng pulse cho badge */
@keyframes badge-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(224, 94, 0, 0.4);
    }

    70% {
        box-shadow: 0 0 0 6px rgba(224, 94, 0, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(224, 94, 0, 0);
    }
}

/* Hiệu ứng nhấp nháy khi thêm sản phẩm vào giỏ hàng - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT */
.mobile-nav-badge.badge-pulse {
    /* animation: mobile-badge-pulse 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) 2; */
}

/* @keyframes mobile-badge-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(224, 94, 0, 0.7);
    }

    70% {
        transform: scale(1.5);
        box-shadow: 0 0 0 8px rgba(224, 94, 0, 0);
    }

    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(224, 94, 0, 0);
    }
} */

/* Điều chỉnh kích thước cho badge khi có 2 chữ số */
.mobile-nav-badge[data-count="10"],
.mobile-nav-badge[data-count="11"],
.mobile-nav-badge[data-count="12"],
.mobile-nav-badge[data-count="13"],
.mobile-nav-badge[data-count="14"],
.mobile-nav-badge[data-count="15"],
.mobile-nav-badge[data-count="16"],
.mobile-nav-badge[data-count="17"],
.mobile-nav-badge[data-count="18"],
.mobile-nav-badge[data-count="19"],
.mobile-nav-badge[data-count="20"],
.mobile-nav-badge[data-count="99+"] {
    min-width: 18px;
    border-radius: 9px;
    padding: 0 4px;
    right: calc(50% - 24px);
}

.mobile-nav-item:hover .mobile-nav-badge {
    transform: scale(1.1);
}

.mobile-nav-item.active .mobile-nav-badge {
    background-color: #d04d00;
    box-shadow: 0 2px 8px rgba(224, 94, 0, 0.4);
}

/* Hiệu ứng ripple khi nhấn vào các nút */
.mobile-header-menu-toggle::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(243, 115, 33, 0.2);
    border-radius: 50%;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.mobile-header-menu-toggle:active::after {
    opacity: 1;
    transform: scale(1);
    transition: all 0s;
}

@keyframes nav-ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }

    70% {
        transform: scale(1.5);
        opacity: 0.5;
    }

    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* Hiệu ứng ripple cho mobile-nav-item */
.nav-ripple-effect {
    position: absolute;
    background-color: rgba(243, 115, 33, 0.2);
    border-radius: 50%;
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    animation: nav-ripple-effect 0.6s ease-out;
    opacity: 0;
    pointer-events: none;
    transform: scale(0);
    z-index: 0;
}

@keyframes nav-ripple-effect {
    0% {
        transform: scale(0);
        opacity: 0.5;
    }

    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* Hiệu ứng khi cuộn */
.mobile-bottom-nav.scrolled {
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.12);
    background-color: rgba(255, 255, 255, 0.98);
    border-color: rgba(243, 115, 33, 0.15);
}

/* Hiệu ứng chuyển đổi mượt mà */
.mobile-header {
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}