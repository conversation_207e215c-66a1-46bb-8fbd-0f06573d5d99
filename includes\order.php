<?php
/**
 * File xử lý các thao tác với đơn hàng
 */

/**
 * L<PERSON><PERSON> thông tin trạng thái đơn hàng
 *
 * @param string $status Mã trạng thái đơn hàng
 * @param string $for <PERSON><PERSON><PERSON> tượng hiển thị ('admin' hoặc 'user')
 * @return array Thông tin trạng thái đơn hàng
 */
function get_order_status_info($status, $for = 'admin') {
    $status_info = [
        'pending' => [
            'admin' => [
                'text' => 'Chờ xử lý',
                'class' => 'badge-warning'
            ],
            'user' => [
                'text' => 'Đơn hàng đang chờ xử lý',
                'class' => 'bg-yellow-100 text-yellow-800'
            ]
        ],
        'processing' => [
            'admin' => [
                'text' => 'Đang xử lý',
                'class' => 'badge-info'
            ],
            'user' => [
                'text' => 'Đơn hàng đang được xử lý',
                'class' => 'bg-blue-100 text-blue-800'
            ]
        ],
        'shipping' => [
            'admin' => [
                'text' => 'Đang giao hàng',
                'class' => 'badge-primary'
            ],
            'user' => [
                'text' => 'Đơn hàng đang được giao',
                'class' => 'bg-indigo-100 text-indigo-800'
            ]
        ],
        'completed' => [
            'admin' => [
                'text' => 'Hoàn thành',
                'class' => 'badge-success'
            ],
            'user' => [
                'text' => 'Đơn hàng đã giao thành công',
                'class' => 'bg-green-100 text-green-800'
            ]
        ],
        'cancelled' => [
            'admin' => [
                'text' => 'Hủy đơn hàng',
                'class' => 'badge-danger'
            ],
            'user' => [
                'text' => 'Đơn hàng đã bị hủy',
                'class' => 'bg-red-100 text-red-800'
            ]
        ]
    ];

    // Nếu trạng thái không tồn tại, trả về trạng thái mặc định
    if (!isset($status_info[$status])) {
        return [
            'text' => $for === 'admin' ? 'Không xác định' : 'Trạng thái không xác định',
            'class' => $for === 'admin' ? 'badge-secondary' : 'bg-gray-100 text-gray-800'
        ];
    }

    return $status_info[$status][$for];
}

/**
 * Lấy danh sách đơn hàng
 */
function get_orders($limit = null, $offset = 0, $user_id = null, $status = null) {
    global $conn;

    try {
        $sql = "SELECT * FROM orders WHERE 1=1";
        $params = [];

        // Lọc theo người dùng
        if ($user_id !== null) {
            $sql .= " AND user_id = :user_id";
            $params[':user_id'] = $user_id;
        }

        // Lọc theo trạng thái
        if ($status !== null) {
            $sql .= " AND status = :status";
            $params[':status'] = $status;
        }

        // Sắp xếp
        $sql .= " ORDER BY created_at DESC";

        // Giới hạn số lượng
        if ($limit !== null) {
            $sql .= " LIMIT :offset, :limit";
            $params[':offset'] = $offset;
            $params[':limit'] = $limit;
        }

        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Lỗi khi lấy danh sách đơn hàng: " . $e->getMessage());
        return [];
    }
}

/**
 * Đếm tổng số đơn hàng
 */
function count_orders($user_id = null, $status = null) {
    global $conn;

    try {
        $sql = "SELECT COUNT(*) as total FROM orders WHERE 1=1";
        $params = [];

        // Lọc theo người dùng
        if ($user_id !== null) {
            $sql .= " AND user_id = :user_id";
            $params[':user_id'] = $user_id;
        }

        // Lọc theo trạng thái
        if ($status !== null) {
            $sql .= " AND status = :status";
            $params[':status'] = $status;
        }

        $stmt = $conn->prepare($sql);

        // Bind các tham số
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch();

        return $result['total'];
    } catch (PDOException $e) {
        error_log("Lỗi khi đếm số đơn hàng: " . $e->getMessage());
        return 0;
    }
}

/**
 * Đếm tổng số đơn hàng theo trạng thái cụ thể
 */
function count_orders_by_status($status) {
    global $conn;

    try {
        $sql = "SELECT COUNT(*) as total FROM orders WHERE status = :status";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':status', $status, PDO::PARAM_STR);
        $stmt->execute();
        $result = $stmt->fetch();

        return $result ? (int)$result['total'] : 0;
    } catch (PDOException $e) {
        error_log("Lỗi khi đếm số đơn hàng theo trạng thái ({$status}): " . $e->getMessage());
        return 0;
    }
}

/**
 * Lấy thông tin đơn hàng theo ID
 */
function get_order_by_id($order_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT * FROM orders WHERE id = :id");
        $stmt->bindParam(':id', $order_id, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return null;
        }

        $order = $stmt->fetch();

        // Lấy chi tiết đơn hàng
        $stmt = $conn->prepare("SELECT oi.*, p.name as product_name, p.image as product_image
                                FROM order_items oi
                                LEFT JOIN products p ON oi.product_id = p.id
                                WHERE oi.order_id = :order_id");
        $stmt->bindParam(':order_id', $order_id, PDO::PARAM_INT);
        $stmt->execute();

        $order['items'] = $stmt->fetchAll();

        return $order;
    } catch (PDOException $e) {
        error_log("Lỗi khi lấy thông tin đơn hàng ID {$order_id}: " . $e->getMessage());
        return null;
    }
}

/**
 * Tạo đơn hàng mới
 */
function create_order($data, $cart_items) {
    global $conn;

    try {
        $conn->beginTransaction();

        // Tính tổng tiền
        $total = 0;
        foreach ($cart_items as $item) {
            $total += $item['price'] * $item['quantity'];
        }

        // Thêm đơn hàng mới
        $stmt = $conn->prepare("INSERT INTO orders (user_id, full_name, email, phone, address, note, total, status)
                                VALUES (:user_id, :full_name, :email, :phone, :address, :note, :total, 'pending')");

        $stmt->bindParam(':user_id', $data['user_id']);
        $stmt->bindParam(':full_name', $data['full_name']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':phone', $data['phone']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':note', $data['note']);
        $stmt->bindParam(':total', $total);

        $stmt->execute();

        $order_id = $conn->lastInsertId();

        // Thêm chi tiết đơn hàng
        foreach ($cart_items as $item) {
            $stmt = $conn->prepare("INSERT INTO order_items (order_id, product_id, price, quantity)
                                    VALUES (:order_id, :product_id, :price, :quantity)");

            $stmt->bindParam(':order_id', $order_id);
            $stmt->bindParam(':product_id', $item['product_id']);
            $stmt->bindParam(':price', $item['price']);
            $stmt->bindParam(':quantity', $item['quantity']);

            $stmt->execute();

            // Cập nhật số lượng sản phẩm
            $stmt = $conn->prepare("UPDATE products SET quantity = quantity - :quantity WHERE id = :product_id");
            $stmt->bindParam(':quantity', $item['quantity']);
            $stmt->bindParam(':product_id', $item['product_id']);
            $stmt->execute();
        }

        $conn->commit();

        return [
            'success' => true,
            'message' => 'Đặt hàng thành công',
            'order_id' => $order_id
        ];
    } catch (PDOException $e) {
        $conn->rollBack();

        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Cập nhật trạng thái đơn hàng
 * Nếu trạng thái là 'completed', cập nhật số lượng đã bán của sản phẩm
 *
 * @param int $order_id ID của đơn hàng cần cập nhật
 * @param string $status Trạng thái mới của đơn hàng
 * @return array Kết quả cập nhật
 */
function update_order_status($order_id, $status) {
    global $conn;

    try {
        // Kiểm tra tham số đầu vào
        if (!is_numeric($order_id) || $order_id <= 0) {
            return [
                'success' => false,
                'message' => 'ID đơn hàng không hợp lệ'
            ];
        }

        $valid_statuses = ['pending', 'processing', 'shipping', 'completed', 'cancelled'];
        if (!in_array($status, $valid_statuses)) {
            return [
                'success' => false,
                'message' => 'Trạng thái không hợp lệ'
            ];
        }

        // Bắt đầu transaction để đảm bảo tính toàn vẹn dữ liệu
        $conn->beginTransaction();

        // Lấy trạng thái hiện tại của đơn hàng
        $stmt = $conn->prepare("SELECT status FROM orders WHERE id = :id");
        $stmt->bindParam(':id', $order_id, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            // Rollback transaction
            $conn->rollBack();
            return [
                'success' => false,
                'message' => 'Đơn hàng không tồn tại'
            ];
        }

        $current_status = $stmt->fetch(PDO::FETCH_ASSOC)['status'];

        // Nếu trạng thái không thay đổi, trả về thành công luôn
        if ($current_status === $status) {
            // Commit transaction
            $conn->commit();
            return [
                'success' => true,
                'message' => 'Trạng thái đơn hàng không thay đổi'
            ];
        }

        // Ghi log để debug
        error_log("Chuẩn bị cập nhật trạng thái đơn hàng ID: {$order_id}, Trạng thái mới: {$status}");

        // Cập nhật trạng thái đơn hàng
        $stmt = $conn->prepare("UPDATE orders SET status = :status WHERE id = :id");
        $stmt->bindParam(':status', $status, PDO::PARAM_STR);
        $stmt->bindParam(':id', $order_id, PDO::PARAM_INT);
        $result = $stmt->execute();

        // Kiểm tra kết quả cập nhật
        if (!$result) {
            // Rollback transaction
            $conn->rollBack();
            error_log("Lỗi SQL khi cập nhật trạng thái đơn hàng: " . print_r($stmt->errorInfo(), true));
            return [
                'success' => false,
                'message' => 'Không thể cập nhật trạng thái đơn hàng: ' . $stmt->errorInfo()[2]
            ];
        }

        // Kiểm tra số dòng bị ảnh hưởng
        if ($stmt->rowCount() === 0) {
            // Không có dòng nào bị ảnh hưởng, có thể là do ID không tồn tại hoặc trạng thái không thay đổi
            error_log("Cập nhật trạng thái đơn hàng không có dòng nào bị ảnh hưởng. ID: {$order_id}, Trạng thái: {$status}");
        } else {
            error_log("Cập nhật trạng thái đơn hàng thành công. ID: {$order_id}, Trạng thái: {$status}, Số dòng bị ảnh hưởng: " . $stmt->rowCount());
        }

        // Nếu trạng thái mới là 'completed' và trạng thái cũ không phải 'completed'
        if ($status === 'completed' && $current_status !== 'completed') {
            // Kiểm tra xem bảng order_product_sold đã tồn tại chưa
            $table_exists = false;
            try {
                $check_table = $conn->query("SHOW TABLES LIKE 'order_product_sold'");
                $table_exists = ($check_table->rowCount() > 0);
            } catch (PDOException $e) {
                // Bảng chưa tồn tại
                $table_exists = false;
            }

            // Nếu bảng chưa tồn tại, tạo bảng
            if (!$table_exists) {
                $sql = "
                CREATE TABLE IF NOT EXISTS order_product_sold (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    order_id INT NOT NULL,
                    product_id INT NOT NULL,
                    quantity INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_order_product (order_id, product_id)
                )";

                $conn->exec($sql);

                // Tạo chỉ mục riêng biệt
                try {
                    $conn->exec("CREATE INDEX idx_order_product_sold_order_id ON order_product_sold(order_id)");
                    $conn->exec("CREATE INDEX idx_order_product_sold_product_id ON order_product_sold(product_id)");
                } catch (PDOException $e) {
                    // Bỏ qua lỗi nếu chỉ mục đã tồn tại
                }
            }

            // Lấy chi tiết đơn hàng
            $stmt = $conn->prepare("SELECT * FROM order_items WHERE order_id = :order_id");
            $stmt->bindParam(':order_id', $order_id);
            $stmt->execute();
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Cập nhật số lượng đã bán cho từng sản phẩm
            foreach ($items as $item) {
                // Kiểm tra xem đơn hàng này đã được cập nhật số lượng đã bán chưa
                $stmt = $conn->prepare("SELECT id FROM order_product_sold
                                       WHERE order_id = :order_id
                                       AND product_id = :product_id");
                $stmt->bindParam(':order_id', $order_id);
                $stmt->bindParam(':product_id', $item['product_id']);
                $stmt->execute();

                // Nếu chưa cập nhật số lượng đã bán
                if ($stmt->rowCount() === 0) {
                    // Thêm bản ghi vào bảng order_product_sold
                    $stmt = $conn->prepare("INSERT INTO order_product_sold (order_id, product_id, quantity)
                                           VALUES (:order_id, :product_id, :quantity)");
                    $stmt->bindParam(':order_id', $order_id);
                    $stmt->bindParam(':product_id', $item['product_id']);
                    $stmt->bindParam(':quantity', $item['quantity']);
                    $stmt->execute();

                    // Cập nhật số lượng đã bán trong bảng products
                    $stmt = $conn->prepare("UPDATE products SET sold = sold + :quantity WHERE id = :product_id");
                    $stmt->bindParam(':quantity', $item['quantity']);
                    $stmt->bindParam(':product_id', $item['product_id']);
                    $stmt->execute();

                    // Ghi log để debug
                    error_log("Đã tăng số lượng đã bán cho sản phẩm ID: {$item['product_id']} với số lượng: {$item['quantity']}");
                }
            }
        }

        // Nếu trạng thái mới không phải là 'completed' nhưng trạng thái cũ là 'completed'
        // thì cần giảm số lượng đã bán
        if ($status !== 'completed' && $current_status === 'completed') {
            // Kiểm tra xem bảng order_product_sold đã tồn tại chưa
            $table_exists = false;
            try {
                $check_table = $conn->query("SHOW TABLES LIKE 'order_product_sold'");
                $table_exists = ($check_table->rowCount() > 0);
            } catch (PDOException $e) {
                // Bảng chưa tồn tại
                $table_exists = false;
            }

            if ($table_exists) {
                // Lấy thông tin về số lượng đã bán từ bảng order_product_sold
                $stmt = $conn->prepare("SELECT * FROM order_product_sold WHERE order_id = :order_id");
                $stmt->bindParam(':order_id', $order_id);
                $stmt->execute();
                $sold_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Giảm số lượng đã bán cho từng sản phẩm
                foreach ($sold_items as $item) {
                    // Cập nhật số lượng đã bán trong bảng products
                    $stmt = $conn->prepare("UPDATE products SET sold = sold - :quantity WHERE id = :product_id");
                    $stmt->bindParam(':quantity', $item['quantity']);
                    $stmt->bindParam(':product_id', $item['product_id']);
                    $stmt->execute();

                    // Xóa bản ghi từ bảng order_product_sold
                    $stmt = $conn->prepare("DELETE FROM order_product_sold WHERE id = :id");
                    $stmt->bindParam(':id', $item['id']);
                    $stmt->execute();

                    // Ghi log để debug
                    error_log("Đã giảm số lượng đã bán cho sản phẩm ID: {$item['product_id']} với số lượng: {$item['quantity']}");
                }
            }
        }

        // Commit transaction
        $conn->commit();

        // Ghi log thành công
        error_log("Cập nhật trạng thái đơn hàng thành công. ID: {$order_id}, Trạng thái cũ: {$current_status}, Trạng thái mới: {$status}");

        return [
            'success' => true,
            'message' => 'Cập nhật trạng thái đơn hàng thành công'
        ];
    } catch (PDOException $e) {
        // Rollback transaction nếu có lỗi
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }

        // Ghi log lỗi chi tiết
        error_log("Lỗi PDO khi cập nhật trạng thái đơn hàng: " . $e->getMessage());
        error_log("SQL State: " . $e->getCode());
        error_log("Trace: " . $e->getTraceAsString());

        return [
            'success' => false,
            'message' => 'Lỗi cơ sở dữ liệu: ' . $e->getMessage()
        ];
    } catch (Exception $e) {
        // Rollback transaction nếu có lỗi
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }

        // Ghi log lỗi
        error_log("Lỗi không xác định khi cập nhật trạng thái đơn hàng: " . $e->getMessage());
        error_log("Trace: " . $e->getTraceAsString());

        return [
            'success' => false,
            'message' => 'Lỗi không xác định: ' . $e->getMessage()
        ];
    }
}

/**
 * Hủy đơn hàng
 */
function cancel_order($order_id) {
    global $conn;

    try {
        $conn->beginTransaction();

        // Lấy thông tin đơn hàng
        $stmt = $conn->prepare("SELECT status FROM orders WHERE id = :id");
        $stmt->bindParam(':id', $order_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return [
                'success' => false,
                'message' => 'Đơn hàng không tồn tại'
            ];
        }

        $order = $stmt->fetch();

        // Chỉ cho phép hủy đơn hàng ở trạng thái pending hoặc processing
        if ($order['status'] !== 'pending' && $order['status'] !== 'processing') {
            return [
                'success' => false,
                'message' => 'Không thể hủy đơn hàng ở trạng thái này'
            ];
        }

        // Lấy chi tiết đơn hàng
        $stmt = $conn->prepare("SELECT * FROM order_items WHERE order_id = :order_id");
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();

        $items = $stmt->fetchAll();

        // Cập nhật lại số lượng sản phẩm
        foreach ($items as $item) {
            $stmt = $conn->prepare("UPDATE products SET quantity = quantity + :quantity WHERE id = :product_id");
            $stmt->bindParam(':quantity', $item['quantity']);
            $stmt->bindParam(':product_id', $item['product_id']);
            $stmt->execute();
        }

        // Cập nhật trạng thái đơn hàng
        $stmt = $conn->prepare("UPDATE orders SET status = 'cancelled' WHERE id = :id");
        $stmt->bindParam(':id', $order_id);
        $stmt->execute();

        $conn->commit();

        return [
            'success' => true,
            'message' => 'Hủy đơn hàng thành công'
        ];
    } catch (PDOException $e) {
        $conn->rollBack();

        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}

/**
 * Gửi email xác nhận đơn hàng
 *
 * @param int $order_id ID của đơn hàng
 * @return array Kết quả gửi email
 */
function send_order_confirmation_email($order_id) {

    try {
        // Lấy thông tin đơn hàng
        $order = get_order_by_id($order_id);

        if (!$order) {
            return [
                'success' => false,
                'message' => 'Đơn hàng không tồn tại'
            ];
        }

        // Nạp thư viện PHPMailer
        require_once ROOT_PATH . 'includes/PHPMailer/src/Exception.php';
        require_once ROOT_PATH . 'includes/PHPMailer/src/PHPMailer.php';
        require_once ROOT_PATH . 'includes/PHPMailer/src/SMTP.php';

        // Tạo đối tượng PHPMailer
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);

        try {
            // Cấu hình SMTP
            $mail->isSMTP();
            $mail->Host = 'smtp.gmail.com';
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>';
            $mail->Password = 'dpld dxeq pwmu taxz'; // Mật khẩu ứng dụng
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS; // Sử dụng SSL
            $mail->Port = 465; // Port cho SSL
            $mail->CharSet = 'UTF-8';

            // Bật chế độ debug (0: tắt, 1: thông báo client, 2: thông báo client và server)
            $mail->SMTPDebug = 0; // Đặt thành 2 khi cần debug

            // Người gửi và người nhận
            $mail->setFrom('<EMAIL>', SITE_NAME);
            $mail->addAddress($order['email'], $order['full_name']);

            // Nội dung email
            $mail->isHTML(true);
            $mail->Subject = 'Xác nhận đơn hàng #' . $order['id'] . ' - ' . SITE_NAME;

            // Lấy nội dung email từ template
            ob_start();
            include ROOT_PATH . 'includes/email-templates/order-confirmation.php';
            $mail_body = ob_get_clean();

            $mail->Body = $mail_body;
            $mail->AltBody = "Xin chào {$order['full_name']},\n\nCảm ơn bạn đã đặt hàng tại " . SITE_NAME . ".\n\nMã đơn hàng: #{$order['id']}\nNgày đặt hàng: " . date('d/m/Y H:i', strtotime($order['created_at'])) . "\nTổng tiền: " . format_currency($order['total']) . "\n\nChúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất để xác nhận đơn hàng.\n\nTrân trọng,\n" . SITE_NAME;

            // Gửi email
            $mail->send();

            return [
                'success' => true,
                'message' => 'Đã gửi email xác nhận đơn hàng thành công'
            ];
        } catch (Exception $e) {
            // Ghi log lỗi để debug
            $error_message = "Lỗi gửi email xác nhận đơn hàng: " . $e->getMessage();
            if (isset($mail) && is_object($mail)) {
                $error_message .= " - " . $mail->ErrorInfo;
            }
            error_log($error_message);

            return [
                'success' => false,
                'message' => 'Không thể gửi email xác nhận đơn hàng. Vui lòng thử lại sau hoặc liên hệ quản trị viên.',
                'error_details' => [
                    'message' => $e->getMessage(),
                    'smtp_error' => isset($mail) && is_object($mail) ? $mail->ErrorInfo : null
                ]
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ];
    }
}
?>
