/*
 * Dropdown Consistency CSS for Nội Thất Bàng Vũ
 * Đồng bộ hóa vị trí và hiệu ứng cho các dropdown menu
 */

/* Biến CSS chung cho tất cả dropdown */
:root {
    --dropdown-margin-top: 15px;
    --dropdown-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 0 1px rgba(0, 0, 0, 0.1);
    --dropdown-border-radius: var(--radius-lg);
    --dropdown-arrow-size: 10px;
    --dropdown-arrow-position: 20px;
    --dropdown-transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
    --dropdown-transform-hidden: translateY(10px) scale(0.98);
    --dropdown-transform-visible: translateY(0) scale(1);
    --dropdown-background: var(--white);
    --dropdown-border: 1px solid rgba(0, 0, 0, 0.05);
    --dropdown-z-index: 1000; /* Z-index cao hơn để đảm bảo hiển thị trên các phần tử khác */
    --dropdown-padding: var(--spacing-md); /* Padding đồng nhất cho tất cả dropdown */
}

/* Mega Menu - Điều chỉnh vị trí và hiệu ứng */
.mega-menu {
    margin-top: var(--dropdown-margin-top);
    box-shadow: var(--dropdown-shadow);
    border-radius: var(--dropdown-border-radius);
    transform: var(--dropdown-transform-hidden);
    transition: var(--dropdown-transition);
    background-color: var(--dropdown-background);
    border: var(--dropdown-border);
    z-index: var(--dropdown-z-index);
    /* Ghi đè positioning từ mega-menu.css để đồng nhất với user-dropdown và mini-cart */
    top: 100% !important;
    left: 0 !important;
}

.nav-item:hover .mega-menu,
.mega-menu:hover {
    transform: var(--dropdown-transform-visible);
}

.mega-menu::before {
    content: '';
    position: absolute;
    top: -5px;
    left: var(--dropdown-arrow-position);
    width: var(--dropdown-arrow-size);
    height: var(--dropdown-arrow-size);
    background-color: var(--dropdown-background);
    transform: rotate(45deg);
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.05);
    z-index: 1;
    /* Ghi đè arrow positioning từ mega-menu.css để đồng nhất */
    top: -5px !important;
    left: var(--dropdown-arrow-position) !important;
}

/* User Dropdown Menu - Điều chỉnh vị trí và hiệu ứng */
.user-dropdown-menu {
    margin-top: var(--dropdown-margin-top);
    box-shadow: var(--dropdown-shadow);
    border-radius: var(--dropdown-border-radius);
    transform: var(--dropdown-transform-hidden);
    transition: var(--dropdown-transition);
    background-color: var(--dropdown-background);
    border: var(--dropdown-border);
    z-index: var(--dropdown-z-index);
}

.user-dropdown:hover .user-dropdown-menu,
.user-dropdown-menu:hover {
    transform: var(--dropdown-transform-visible);
}

.user-dropdown-menu::before {
    content: '';
    position: absolute;
    top: -5px;
    right: var(--dropdown-arrow-position);
    width: var(--dropdown-arrow-size);
    height: var(--dropdown-arrow-size);
    background-color: var(--dropdown-background);
    transform: rotate(45deg);
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.05);
    z-index: 1;
}

/* Mini Cart - Điều chỉnh vị trí và hiệu ứng */
.mini-cart {
    margin-top: var(--dropdown-margin-top);
    box-shadow: var(--dropdown-shadow);
    border-radius: var(--dropdown-border-radius);
    transform: var(--dropdown-transform-hidden);
    transition: var(--dropdown-transition);
    background-color: var(--dropdown-background);
    border: var(--dropdown-border);
    z-index: var(--dropdown-z-index);
}

.cart-container:hover .mini-cart,
.mini-cart:hover {
    transform: var(--dropdown-transform-visible);
}

.mini-cart::before {
    content: '';
    position: absolute;
    top: -5px;
    right: var(--dropdown-arrow-position);
    width: var(--dropdown-arrow-size);
    height: var(--dropdown-arrow-size);
    background-color: var(--dropdown-background);
    transform: rotate(45deg);
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.05);
    z-index: 1;
}

/* Đảm bảo nội dung dropdown nằm trên arrow indicator */
.mega-menu-categories,
.mega-menu-content,
.user-dropdown-header,
.user-dropdown-item,
.mini-cart-header,
.mini-cart-items,
.mini-cart-footer {
    position: relative;
    z-index: 2;
}

/* Quy tắc hiệu ứng hover đã được thay thế bởi DROPDOWN ALIGNMENT CONSISTENCY ở cuối file */

/* Điều chỉnh hiệu ứng hover cho các dropdown item */
.user-dropdown-item:hover,
.mini-cart-button:hover {
    transform: translateY(-1px);
}

/* Đảm bảo chiều cao tối đa nhất quán cho các dropdown */
.mega-menu,
.user-dropdown-menu,
.mini-cart {
    max-height: calc(100vh - 150px);
    overflow-y: auto;
}

/* Các quy tắc cũ đã được thay thế bởi DROPDOWN ALIGNMENT CONSISTENCY ở cuối file */

/* Tùy chỉnh thanh cuộn cho tất cả dropdown */
.mega-menu::-webkit-scrollbar,
.user-dropdown-menu::-webkit-scrollbar,
.mini-cart::-webkit-scrollbar {
    width: 6px;
}

.mega-menu::-webkit-scrollbar-track,
.user-dropdown-menu::-webkit-scrollbar-track,
.mini-cart::-webkit-scrollbar-track {
    background: var(--ultra-light-gray);
}

.mega-menu::-webkit-scrollbar-thumb,
.user-dropdown-menu::-webkit-scrollbar-thumb,
.mini-cart::-webkit-scrollbar-thumb {
    background: var(--primary-light);
    border-radius: 3px;
}

.mega-menu::-webkit-scrollbar-thumb:hover,
.user-dropdown-menu::-webkit-scrollbar-thumb:hover,
.mini-cart::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
}

/* Đảm bảo hiệu ứng đồng nhất khi header cuộn */
.premium-header.scrolled .mega-menu,
.premium-header.scrolled .user-dropdown-menu,
.premium-header.scrolled .mini-cart {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15), 0 0 1px rgba(0, 0, 0, 0.1);
}

/* Đảm bảo các arrow indicators có cùng kích thước và vị trí */
.mega-menu::before,
.user-dropdown-menu::before,
.mini-cart::before {
    width: var(--dropdown-arrow-size);
    height: var(--dropdown-arrow-size);
    top: -5px;
    transform: rotate(45deg);
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.05);
    z-index: 1;
}

/* Thêm hiệu ứng hover mượt mà cho các dropdown */
.mega-menu,
.user-dropdown-menu,
.mini-cart {
    will-change: transform, opacity, visibility;
}

/* Đảm bảo padding đồng nhất cho các dropdown */
.user-dropdown-menu {
    padding: var(--dropdown-padding) 0;
}

.mini-cart {
    padding: var(--dropdown-padding);
}

/* Đảm bảo các dropdown có cùng hiệu ứng khi hover */
.mega-menu:hover,
.user-dropdown-menu:hover,
.mini-cart:hover {
    transform: var(--dropdown-transform-visible) translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15), 0 0 1px rgba(0, 0, 0, 0.1);
}

/* ===== DROPDOWN ALIGNMENT CONSISTENCY - SIMPLIFIED APPROACH ===== */
/* Đặt tất cả dropdown ở cùng một vị trí cố định từ mép dưới header */

@media (min-width: 769px) {
    /* Đặt tất cả dropdown ở cùng vị trí: 25px từ mép dưới header
       (tương đương với vị trí ban đầu của user-dropdown và mini-cart: 100% + margin-top 15px + translateY 10px) */
    .mega-menu,
    .user-dropdown-menu,
    .mini-cart {
        top: calc(100% + 25px) !important;
        margin-top: 0 !important;
    }

    /* Đảm bảo mega-menu không sử dụng transform translateY khi ẩn */
    .mega-menu {
        transform: scale(0.98) !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }

    /* Đảm bảo user-dropdown-menu không sử dụng transform translateY khi ẩn */
    .user-dropdown-menu {
        transform: scale(0.98) !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }

    /* Đảm bảo mini-cart không sử dụng transform translateY khi ẩn */
    .mini-cart {
        transform: scale(0.98) !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }

    /* Hiệu ứng khi hiển thị - tất cả dropdown */
    .nav-item:hover .mega-menu,
    .mega-menu:hover {
        transform: scale(1) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .user-dropdown:hover .user-dropdown-menu,
    .user-dropdown-menu:hover {
        transform: scale(1) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .cart-container:hover .mini-cart,
    .mini-cart:hover {
        transform: scale(1) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Đảm bảo arrow của tất cả dropdown ở cùng vị trí */
    .mega-menu::before,
    .user-dropdown-menu::before,
    .mini-cart::before {
        top: -5px !important;
    }

    /* Điều chỉnh vị trí arrow cho mega-menu */
    .mega-menu::before {
        left: 20px !important;
    }

    /* Điều chỉnh vị trí arrow cho user-dropdown và mini-cart */
    .user-dropdown-menu::before,
    .mini-cart::before {
        right: 20px !important;
    }
}
