<?php
/**
 * Template email xác nhận đơn hàng
 *
 * C<PERSON>c biến có sẵn:
 * - $order: Thông tin đơn hàng
 * - $order_items: Danh sách sản phẩm trong đơn hàng
 */
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xác nhận đơn hàng #<?php echo $order['id']; ?> - <PERSON><PERSON><PERSON> Thất Bàng Vũ</title>
    <style>
        body {
            font-family: Arial, Helvetica, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }
        .logo {
            max-width: 250px;
            height: auto;
        }

        @media only screen and (max-width: 480px) {
            .logo {
                max-width: 200px;
            }
        }
        .order-confirmed {
            background-color: #f97316;
            color: white;
            text-align: center;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .order-confirmed h2 {
            margin: 0;
            font-size: 22px;
        }
        .order-confirmed p {
            margin: 10px 0 0;
            font-size: 16px;
        }
        .order-info {
            background-color: #fff7ed;
            border: 1px solid #ffedd5;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .order-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .order-info-item:last-child {
            margin-bottom: 0;
        }
        .order-info-label {
            font-weight: bold;
            color: #666;
        }
        .order-info-value {
            text-align: right;
        }
        .order-total {
            font-weight: bold;
            color: #f97316;
        }
        .section-title {
            font-size: 18px;
            margin: 25px 0 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f97316;
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th {
            background-color: #f8f9fa;
            text-align: left;
            padding: 10px;
            font-size: 14px;
            border-bottom: 2px solid #eee;
        }
        td {
            padding: 10px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        .product-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 4px;
            vertical-align: middle;
            margin-right: 10px;
        }
        .product-name {
            vertical-align: middle;
        }
        .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #777;
            font-size: 12px;
        }
        .button {
            display: inline-block;
            background-color: #f97316;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 10px;
            font-weight: bold;
        }
        .contact-info {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
        }
        .contact-info p {
            margin: 5px 0;
        }
        .text-center {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://res.cloudinary.com/dpwsaqvl9/image/upload/v1747505891/noithatbangvu/logo-email/logo_f9sars.png" alt="Nội Thất Bàng Vũ" class="logo" width="250" style="max-width: 250px; height: auto;">
        </div>

        <div class="order-confirmed">
            <h2>Đơn hàng đã được xác nhận!</h2>
            <p>Cảm ơn bạn đã mua sắm tại Nội Thất Bàng Vũ</p>
        </div>

        <p>Xin chào <strong><?php echo $order['full_name']; ?></strong>,</p>

        <p>Chúng tôi đã nhận được đơn đặt hàng của bạn và đang xử lý. Dưới đây là thông tin chi tiết về đơn hàng của bạn:</p>

        <h3 class="section-title">Thông tin đơn hàng</h3>

        <div class="order-info">
            <div class="order-info-item">
                <div class="order-info-label">Mã đơn hàng:</div>
                <div class="order-info-value">#<?php echo $order['id']; ?></div>
            </div>
            <div class="order-info-item">
                <div class="order-info-label">Ngày đặt hàng:</div>
                <div class="order-info-value"><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></div>
            </div>
            <div class="order-info-item">
                <div class="order-info-label">Trạng thái:</div>
                <div class="order-info-value">
                    <?php
                    $status_info = get_order_status_info($order['status'], 'user');
                    echo $status_info['text'];
                    ?>
                </div>
            </div>
            <div class="order-info-item">
                <div class="order-info-label">Tổng tiền:</div>
                <div class="order-info-value order-total"><?php echo format_currency($order['total']); ?></div>
            </div>
        </div>

        <h3 class="section-title">Chi tiết đơn hàng</h3>

        <table>
            <thead>
                <tr>
                    <th>Sản phẩm</th>
                    <th>Giá</th>
                    <th>SL</th>
                    <th>Tổng</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($order['items'] as $item): ?>
                <tr>
                    <td>
                        <div style="display: flex; align-items: center;">
                            <?php if ($item['product_image']): ?>
                            <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $item['product_image']; ?>" alt="<?php echo $item['product_name']; ?>" class="product-image">
                            <?php endif; ?>
                            <span class="product-name"><?php echo $item['product_name']; ?></span>
                        </div>
                    </td>
                    <td><?php echo format_currency($item['price']); ?></td>
                    <td><?php echo $item['quantity']; ?></td>
                    <td><?php echo format_currency($item['price'] * $item['quantity']); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="3" style="text-align: right;"><strong>Tạm tính:</strong></td>
                    <td><?php echo format_currency($order['total']); ?></td>
                </tr>
                <tr>
                    <td colspan="3" style="text-align: right;"><strong>Phí vận chuyển:</strong></td>
                    <td>Miễn phí</td>
                </tr>
                <tr>
                    <td colspan="3" style="text-align: right;"><strong>Tổng cộng:</strong></td>
                    <td class="order-total"><?php echo format_currency($order['total']); ?></td>
                </tr>
            </tfoot>
        </table>

        <h3 class="section-title">Thông tin giao hàng</h3>

        <div class="order-info">
            <div class="order-info-item">
                <div class="order-info-label">Họ và tên:</div>
                <div class="order-info-value"><?php echo $order['full_name']; ?></div>
            </div>
            <div class="order-info-item">
                <div class="order-info-label">Email:</div>
                <div class="order-info-value"><?php echo $order['email']; ?></div>
            </div>
            <div class="order-info-item">
                <div class="order-info-label">Số điện thoại:</div>
                <div class="order-info-value"><?php echo $order['phone']; ?></div>
            </div>
            <div class="order-info-item">
                <div class="order-info-label">Địa chỉ:</div>
                <div class="order-info-value"><?php echo $order['address']; ?></div>
            </div>
            <?php if (!empty($order['note'])): ?>
            <div class="order-info-item">
                <div class="order-info-label">Ghi chú:</div>
                <div class="order-info-value"><?php echo $order['note']; ?></div>
            </div>
            <?php endif; ?>
        </div>

        <div class="text-center">
            <a href="<?php echo BASE_URL; ?>/account/orders.php" class="button">Theo dõi đơn hàng</a>
        </div>

        <div class="contact-info">
            <p><strong>Cần hỗ trợ?</strong></p>
            <p>Hotline tư vấn khách hàng: ************</p>
            <p>Kỹ thuật viên trực tiếp hỗ trợ: ************</p>
            <p>Email: <EMAIL></p>
        </div>

        <div class="footer">
            <p>© <?php echo date('Y'); ?> Nội Thất Bàng Vũ. Tất cả các quyền được bảo lưu.</p>
            <p>Địa chỉ: 123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh</p>
        </div>
    </div>
</body>
</html>
