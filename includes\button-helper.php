<?php
/**
 * Hàm xác định biểu tượng phù hợp dựa trên nội dung nút
 * 
 * @param string $button_text Nội dung nút
 * @return string Lớp CSS của biểu tượng Font Awesome
 */
function get_button_icon($button_text) {
    // Chuyển đổi nội dung nút thành chữ thường để dễ so sánh
    $text = mb_strtolower(trim($button_text), 'UTF-8');
    
    // Mảng các từ khóa và biểu tượng tương ứng
    $icon_mapping = [
        // Sản phẩm, thiết kế, mẫu
        'sản phẩm' => 'fa-box',
        'thiết kế' => 'fa-drafting-compass',
        'mẫu' => 'fa-images',
        'xem' => 'fa-eye',
        'danh sách' => 'fa-list',
        'bộ sưu tập' => 'fa-layer-group',
        'bst' => 'fa-layer-group',
        'gallery' => 'fa-images',
        'thư viện' => 'fa-images',
        
        // <PERSON><PERSON><PERSON> hệ, tư vấn
        'liên hệ' => 'fa-envelope',
        'tư vấn' => 'fa-headset',
        'gọi' => 'fa-phone-alt',
        'hotline' => 'fa-phone-alt',
        'chat' => 'fa-comments',
        'hỗ trợ' => 'fa-life-ring',
        'support' => 'fa-life-ring',
        'nhắn tin' => 'fa-comment-dots',
        'message' => 'fa-comment-dots',
        
        // Mua hàng, giỏ hàng
        'mua' => 'fa-shopping-cart',
        'giỏ hàng' => 'fa-shopping-cart',
        'đặt hàng' => 'fa-shopping-bag',
        'thanh toán' => 'fa-credit-card',
        'order' => 'fa-shopping-bag',
        'cart' => 'fa-shopping-cart',
        'checkout' => 'fa-credit-card',
        
        // Thông tin
        'thông tin' => 'fa-info-circle',
        'chi tiết' => 'fa-info-circle',
        'báo giá' => 'fa-file-invoice-dollar',
        'bảng giá' => 'fa-tags',
        'giá' => 'fa-tag',
        'price' => 'fa-tag',
        'info' => 'fa-info-circle',
        'details' => 'fa-info-circle',
        
        // Tài liệu, download
        'tài liệu' => 'fa-file-alt',
        'download' => 'fa-download',
        'tải' => 'fa-download',
        'catalogue' => 'fa-book',
        'hướng dẫn' => 'fa-book-reader',
        'guide' => 'fa-book-reader',
        'manual' => 'fa-book-reader',
        
        // Địa chỉ, bản đồ
        'địa chỉ' => 'fa-map-marker-alt',
        'bản đồ' => 'fa-map',
        'chỉ đường' => 'fa-directions',
        'vị trí' => 'fa-map-marker-alt',
        'location' => 'fa-map-marker-alt',
        'map' => 'fa-map',
        
        // Đăng ký, đăng nhập
        'đăng ký' => 'fa-user-plus',
        'đăng nhập' => 'fa-sign-in-alt',
        'tài khoản' => 'fa-user',
        'register' => 'fa-user-plus',
        'login' => 'fa-sign-in-alt',
        'account' => 'fa-user',
        
        // Khuyến mãi, ưu đãi
        'khuyến mãi' => 'fa-percent',
        'ưu đãi' => 'fa-gift',
        'giảm giá' => 'fa-percent',
        'sale' => 'fa-percent',
        'discount' => 'fa-percent',
        'promotion' => 'fa-gift',
        
        // Đánh giá, phản hồi
        'đánh giá' => 'fa-star',
        'phản hồi' => 'fa-comment',
        'review' => 'fa-star',
        'feedback' => 'fa-comment',
        'rating' => 'fa-star',
        
        // Tìm kiếm
        'tìm kiếm' => 'fa-search',
        'search' => 'fa-search',
        'find' => 'fa-search',
        
        // Quay lại, tiếp tục
        'quay lại' => 'fa-arrow-left',
        'trở về' => 'fa-arrow-left',
        'tiếp tục' => 'fa-arrow-right',
        'back' => 'fa-arrow-left',
        'continue' => 'fa-arrow-right',
        'next' => 'fa-arrow-right',
        'previous' => 'fa-arrow-left',
        
        // Xác nhận, hủy
        'xác nhận' => 'fa-check',
        'đồng ý' => 'fa-check',
        'hủy' => 'fa-times',
        'confirm' => 'fa-check',
        'cancel' => 'fa-times',
        'agree' => 'fa-check',
        
        // Mặc định
        'default' => 'fa-angle-right'
    ];
    
    // Kiểm tra từng từ khóa trong nội dung nút
    foreach ($icon_mapping as $keyword => $icon) {
        if (mb_strpos($text, $keyword, 0, 'UTF-8') !== false) {
            return $icon;
        }
    }
    
    // Trả về biểu tượng mặc định nếu không tìm thấy từ khóa phù hợp
    return 'fa-angle-right';
}
