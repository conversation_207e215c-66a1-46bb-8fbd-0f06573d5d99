{"version": 3, "file": "lang/summernote-sr-RS.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,sBAAsB;QAC7BC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,aAAa;QACnBC,aAAa,EAAE,WAAW;QAC1BC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,aAAa;QAC1BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,cAAc;QACtBC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,cAAc;QAC1BC,aAAa,EAAE,cAAc;QAC7BC,SAAS,EAAE,eAAe;QAC1BC,UAAU,EAAE,gBAAgB;QAC5BC,SAAS,EAAE,aAAa;QACxBC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,oBAAoB;QACnCC,SAAS,EAAE,oBAAoB;QAC/BC,eAAe,EAAE,qBAAqB;QACtCC,eAAe,EAAE,mBAAmB;QACpCC,oBAAoB,EAAE,6BAA6B;QACnDC,GAAG,EAAE,cAAc;QACnBC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,eAAe;QAC1BpB,MAAM,EAAE,cAAc;QACtBgB,GAAG,EAAE,WAAW;QAChBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,aAAa;QACrBuB,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,OAAO;QACbC,aAAa,EAAE,iBAAiB;QAChCT,GAAG,EAAE,iBAAiB;QACtBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,eAAe;QAC5BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,UAAU;QACbC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,KAAK;QACVC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,cAAc;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,oBAAoB;QAChCC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,gBAAgB;QACzBC,MAAM,EAAE,kBAAkB;QAC1BC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAE,YAAY;QACpBC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,WAAW;QACjBC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,aAAa;QACzBC,WAAW,EAAE,UAAU;QACvBC,cAAc,EAAE,UAAU;QAC1BC,KAAK,EAAE,QAAQ;QACfC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,sBAAsB;QACjCC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE,oBAAoB;QACpCC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,uBAAuB;QAC5CC,aAAa,EAAE,gBAAgB;QAC/BC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,kBAAkB;QACrC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,yBAAyB;QACjC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,kBAAkB;QAC1B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-sr-RS.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'sr-RS': {\n      font: {\n        bold: 'Подебљано',\n        italic: 'Курзив',\n        underline: 'Подвучено',\n        clear: 'Уклони стилове фонта',\n        height: 'Висина линије',\n        name: '<PERSON>ont <PERSON>',\n        strikethrough: 'Прецртано',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Величина фонта',\n      },\n      image: {\n        image: 'Слика',\n        insert: 'Уметни слику',\n        resizeFull: 'Пуна величина',\n        resizeHalf: 'Умањи на 50%',\n        resizeQuarter: 'Умањи на 25%',\n        floatLeft: 'Уз леву ивицу',\n        floatRight: 'Уз десну ивицу',\n        floatNone: 'Без равнања',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Превуци слику овде',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Изабери из датотеке',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'Адреса слике',\n        remove: 'Уклони слику',\n        original: 'Original',\n      },\n      video: {\n        video: 'Видео',\n        videoLink: 'Веза ка видеу',\n        insert: 'Уметни видео',\n        url: 'URL видео',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion или Youku)',\n      },\n      link: {\n        link: 'Веза',\n        insert: 'Уметни везу',\n        unlink: 'Уклони везу',\n        edit: 'Уреди',\n        textToDisplay: 'Текст за приказ',\n        url: 'Интернет адреса',\n        openInNewWindow: 'Отвори у новом прозору',\n      },\n      table: {\n        table: 'Табела',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Уметни хоризонталну линију',\n      },\n      style: {\n        style: 'Стил',\n        p: 'Нормални',\n        blockquote: 'Цитат',\n        pre: 'Код',\n        h1: 'Заглавље 1',\n        h2: 'Заглавље 2',\n        h3: 'Заглавље 3',\n        h4: 'Заглавље 4',\n        h5: 'Заглавље 5',\n        h6: 'Заглавље 6',\n      },\n      lists: {\n        unordered: 'Обична листа',\n        ordered: 'Нумерисана листа',\n      },\n      options: {\n        help: 'Помоћ',\n        fullscreen: 'Преко целог екрана',\n        codeview: 'Изворни код',\n      },\n      paragraph: {\n        paragraph: 'Параграф',\n        outdent: 'Смањи увлачење',\n        indent: 'Повечај увлачење',\n        left: 'Поравнај у лево',\n        center: 'Центрирано',\n        right: 'Поравнај у десно',\n        justify: 'Поравнај обострано',\n      },\n      color: {\n        recent: 'Последња боја',\n        more: 'Више боја',\n        background: 'Боја позадине',\n        foreground: 'Боја текста',\n        transparent: 'Провидна',\n        setTransparent: 'Провидна',\n        reset: 'Опозив',\n        resetToDefault: 'Подразумевана',\n      },\n      shortcut: {\n        shortcuts: 'Пречице са тастатуре',\n        close: 'Затвори',\n        textFormatting: 'Форматирање текста',\n        action: 'Акција',\n        paragraphFormatting: 'Форматирање параграфа',\n        documentStyle: 'Стил документа',\n        extraKeys: 'Додатне комбинације',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Поништи',\n        redo: 'Понови',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}