<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra quyền admin
require_once 'partials/check_admin.php';

// Thiết lập tiêu đề trang
$page_title = 'Chỉnh sửa banner';

// Lấy ID banner từ URL
$banner_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Lấy thông tin banner
$banner = get_banner_by_id($banner_id);

// Kiểm tra banner tồn tại
if (!$banner) {
    set_flash_message('error', 'Banner không tồn tại!');
    redirect('banners.php');
}

// Xử lý form cập nhật banner
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $link = trim($_POST['link'] ?? '');
    $status = isset($_POST['status']) ? 1 : 0;
    $banner_type = trim($_POST['banner_type'] ?? 'default');
    $sort_order = isset($_POST['sort_order']) ? intval($_POST['sort_order']) : 0;

    // Lấy dữ liệu nội dung tùy chỉnh
    $title_text = trim($_POST['title_text'] ?? '');
    $subtitle_text = trim($_POST['subtitle_text'] ?? '');
    $description_text = trim($_POST['description_text'] ?? '');
    $button_text = trim($_POST['button_text'] ?? '');

    // Lấy dữ liệu nội dung tùy chỉnh bổ sung
    $badge_text = trim($_POST['badge_text'] ?? '');
    $feature1_text = trim($_POST['feature1_text'] ?? '');
    $feature2_text = trim($_POST['feature2_text'] ?? '');
    $feature3_text = trim($_POST['feature3_text'] ?? '');
    $button2_text = trim($_POST['button2_text'] ?? '');
    $contact_phone = trim($_POST['contact_phone'] ?? '');
    $contact_slogan = trim($_POST['contact_slogan'] ?? '');

    // Lấy dữ liệu URL cho nút CTA
    $button_link = trim($_POST['button_link'] ?? '#');
    $button2_link = trim($_POST['button2_link'] ?? 'contact.php');

    // Validate dữ liệu
    $errors = [];

    // Xử lý upload hình ảnh mới (nếu có)
    $image = $banner['image']; // Giữ nguyên hình ảnh cũ nếu không upload mới
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../uploads/banners/';

        // Tạo thư mục uploads/banners nếu chưa tồn tại
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        // Lấy thông tin file
        $file_name = $_FILES['image']['name'];
        $file_tmp = $_FILES['image']['tmp_name'];
        $file_size = $_FILES['image']['size'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        // Kiểm tra định dạng file
        $allowed_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!in_array($file_ext, $allowed_exts)) {
            $errors[] = 'Chỉ cho phép upload file hình ảnh (jpg, jpeg, png, gif, webp)';
        }

        // Kiểm tra kích thước file (giới hạn 5MB)
        $max_size = 5 * 1024 * 1024; // 5MB
        if ($file_size > $max_size) {
            $errors[] = 'Kích thước file không được vượt quá 5MB';
        }

        // Nếu không có lỗi, tiến hành upload
        if (empty($errors)) {
            // Tạo tên file mới để tránh trùng lặp
            $new_file_name = 'banner_' . time() . '_' . uniqid() . '.' . $file_ext;
            $upload_path = $upload_dir . $new_file_name;

            // Upload file
            if (move_uploaded_file($file_tmp, $upload_path)) {
                // Xóa hình ảnh cũ nếu có
                if (!empty($banner['image'])) {
                    $old_image_path = $upload_dir . $banner['image'];
                    if (file_exists($old_image_path)) {
                        unlink($old_image_path);
                    }
                }

                $image = $new_file_name;
            } else {
                $errors[] = 'Có lỗi xảy ra khi upload file';
            }
        }
    } else if (empty($banner['image'])) {
        $errors[] = 'Vui lòng chọn hình ảnh cho banner';
    }

    // Nếu không có lỗi, cập nhật banner
    if (empty($errors)) {
        $banner_data = [
            'image' => $image,
            'link' => $link,
            'status' => $status,
            'banner_type' => $banner_type,
            'sort_order' => $sort_order,
            'title_text' => $title_text,
            'subtitle_text' => $subtitle_text,
            'description_text' => $description_text,
            'button_text' => $button_text,
            'badge_text' => $badge_text,
            'feature1_text' => $feature1_text,
            'feature2_text' => $feature2_text,
            'feature3_text' => $feature3_text,
            'button2_text' => $button2_text,
            'contact_phone' => $contact_phone,
            'contact_slogan' => $contact_slogan,
            'button_link' => $button_link,
            'button2_link' => $button2_link
        ];

        $result = update_banner($banner_id, $banner_data);

        if ($result['success']) {
            set_flash_message('success', $result['message']);
            redirect('banners.php');
        } else {
            $errors[] = $result['message'];
        }
    }
}

// Include header
include_once 'partials/header.php';
?>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chỉnh sửa banner</h1>
        <a href="banners.php" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Quay lại
        </a>
    </div>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Thông tin banner hiện tại</div>
                            <div class="row mt-2">
                                <div class="col-md-2">
                                    <p class="mb-0 font-weight-bold">ID:</p>
                                </div>
                                <div class="col-md-10">
                                    <p class="mb-0">#<?php echo $banner_id; ?></p>
                                </div>
                            </div>
                            <div class="row mt-1">
                                <div class="col-md-2">
                                    <p class="mb-0 font-weight-bold">Loại:</p>
                                </div>
                                <div class="col-md-10">
                                    <p class="mb-0">
                                        <?php if (isset($banner['banner_type']) && $banner['banner_type'] === 'marketing'): ?>
                                            <span class="badge badge-info">Banner Marketing</span>
                                        <?php else: ?>
                                            <span class="badge badge-success">Banner Mặc định</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                            <div class="row mt-1">
                                <div class="col-md-2">
                                    <p class="mb-0 font-weight-bold">Trạng thái:</p>
                                </div>
                                <div class="col-md-10">
                                    <p class="mb-0">
                                        <?php if ($banner['status'] == 1): ?>
                                            <span class="badge badge-success">Hiển thị</span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">Ẩn</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                            <div class="row mt-1">
                                <div class="col-md-2">
                                    <p class="mb-0 font-weight-bold">Thứ tự:</p>
                                </div>
                                <div class="col-md-10">
                                    <p class="mb-0">
                                        <span class="badge badge-info"><?php echo isset($banner['sort_order']) ? $banner['sort_order'] : 0; ?></span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-edit fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form chỉnh sửa banner -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Chỉnh sửa thông tin banner</h6>
            <div>
                <span class="small text-muted">ID: #<?php echo $banner_id; ?></span>
            </div>
        </div>
        <div class="card-body">
            <form method="POST" action="banner-edit.php?id=<?php echo $banner_id; ?>" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-7">
                        <div class="form-group">
                            <label>Loại banner <span class="text-danger">*</span></label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body banner-type-card" data-type="default">
                                            <div class="custom-control custom-radio">
                                                <input class="custom-control-input" type="radio" name="banner_type" id="banner_type_default" value="default" <?php echo !isset($banner['banner_type']) || $banner['banner_type'] === 'default' ? 'checked' : ''; ?>>
                                                <label class="custom-control-label font-weight-bold" for="banner_type_default">
                                                    Banner Mặc định <i class="fas fa-code text-success ml-1"></i>
                                                </label>
                                            </div>
                                            <p class="text-muted small mb-0 mt-2">Hiển thị hình ảnh kèm nội dung HTML/CSS/JS (slogan, nút CTA)</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body banner-type-card" data-type="marketing">
                                            <div class="custom-control custom-radio">
                                                <input class="custom-control-input" type="radio" name="banner_type" id="banner_type_marketing" value="marketing" <?php echo isset($banner['banner_type']) && $banner['banner_type'] === 'marketing' ? 'checked' : ''; ?>>
                                                <label class="custom-control-label font-weight-bold" for="banner_type_marketing">
                                                    Banner Marketing <i class="fas fa-ad text-info ml-1"></i>
                                                </label>
                                            </div>
                                            <p class="text-muted small mb-0 mt-2">Chỉ hiển thị hình ảnh, không có nội dung bổ sung</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-light border mt-3">
                                <div class="d-flex">
                                    <div class="mr-3">
                                        <i class="fas fa-lightbulb text-warning fa-2x"></i>
                                    </div>
                                    <div>
                                        <h6 class="font-weight-bold mb-1">Thông tin bổ sung</h6>
                                        <p class="mb-0 small" id="banner_type_info">
                                            Banner mặc định sẽ hiển thị kèm theo phần nội dung HTML/CSS/JS đã được thiết kế sẵn trong mã nguồn, bao gồm slogan, nút CTA và các hiệu ứng.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="link">Liên kết</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-link"></i></span>
                                </div>
                                <input type="text" class="form-control" id="link" name="link" value="<?php echo htmlspecialchars($banner['link'] ?? ''); ?>" placeholder="https://example.com/trang-dich">
                            </div>
                            <small class="form-text text-muted">Đường dẫn khi người dùng nhấp vào banner (để trống nếu không cần)</small>
                        </div>
                    </div>

                    <div class="col-md-5">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-image mr-1"></i> Hình ảnh banner</h6>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($banner['image'])): ?>
                                <div class="mb-3">
                                    <div class="img-container border rounded overflow-hidden" style="max-height: 300px;">
                                        <img src="<?php echo BASE_URL . '/uploads/banners/' . $banner['image']; ?>" alt="Banner #<?php echo $banner_id; ?>" class="img-fluid">
                                    </div>
                                    <small class="text-muted d-block mt-2">Hình ảnh hiện tại</small>
                                </div>
                                <?php endif; ?>

                                <div class="form-group">
                                    <div class="custom-file mb-3">
                                        <input type="file" class="custom-file-input" id="image" name="image" accept="image/*">
                                        <label class="custom-file-label" for="image">Chọn file mới...</label>
                                    </div>
                                    <small class="form-text text-muted">Để trống nếu không muốn thay đổi hình ảnh</small>
                                    <small class="form-text text-muted">Hỗ trợ các định dạng: JPG, JPEG, PNG, GIF, WebP.</small>
                                    <small class="form-text text-muted">Khuyến nghị kích thước 1920x1080px hoặc lớn hơn. Tỷ lệ 16:9.</small>
                                </div>

                                <div id="image-preview" class="text-center mt-3 d-none">
                                    <div class="img-container" style="max-height: 300px; overflow: hidden; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.12);">
                                        <img src="" alt="Preview" class="img-fluid">
                                    </div>
                                    <small class="text-muted d-block mt-2">Xem trước hình ảnh mới</small>
                                </div>

                                <?php if (empty($banner['image'])): ?>
                                <div id="no-image-preview" class="text-center py-5 border rounded bg-light mt-3">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-gray-300 mb-2"></i>
                                    <p class="text-muted mb-0">Tải lên hình ảnh</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-cog mr-1"></i> Cài đặt</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-group mb-0">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="status" name="status" <?php echo $banner['status'] == 1 ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="status">Hiển thị banner</label>
                                    </div>
                                    <small class="form-text text-muted">Banner sẽ xuất hiện trên trang chủ nếu được bật.</small>
                                </div>

                                <hr class="my-3">

                                <div class="form-group mb-0">
                                    <label for="sort_order">Thứ tự hiển thị</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fas fa-sort-numeric-down"></i></span>
                                        </div>
                                        <input type="number" class="form-control" id="sort_order" name="sort_order" value="<?php echo isset($banner['sort_order']) ? intval($banner['sort_order']) : 0; ?>" min="0" max="999">
                                    </div>
                                    <small class="form-text text-muted">Số thứ tự hiển thị của banner (số nhỏ hơn sẽ hiển thị trước)</small>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer bg-light text-center">
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-save mr-1"></i> Lưu thay đổi
                            </button>
                            <a href="banners.php" class="btn btn-outline-secondary btn-block mt-2">
                                <i class="fas fa-times mr-1"></i> Hủy
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Phần chỉnh sửa nội dung banner mặc định -->
                <div class="col-md-12 mt-4" id="default_banner_content" style="display: <?php echo !isset($banner['banner_type']) || $banner['banner_type'] === 'default' ? 'block' : 'none'; ?>;">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Nội dung banner mặc định</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-1"></i> Các thông tin dưới đây sẽ được hiển thị trên banner mặc định. Bạn có thể tùy chỉnh theo ý muốn.
                            </div>

                            <div class="form-group">
                                <label for="badge_text">Badge trên tiêu đề</label>
                                <input type="text" class="form-control" id="badge_text" name="badge_text" value="<?php echo htmlspecialchars($banner['badge_text'] ?? 'Nội Thất Cao Cấp Bàng Vũ'); ?>" placeholder="Văn bản hiển thị trong badge">
                                <small class="form-text text-muted">Văn bản hiển thị trong badge trên tiêu đề (ví dụ: "Nội Thất Cao Cấp Bàng Vũ")</small>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="title_text">Tiêu đề chính</label>
                                        <input type="text" class="form-control" id="title_text" name="title_text" value="<?php echo htmlspecialchars($banner['title_text'] ?? 'Thiết Kế – Thi Công'); ?>" placeholder="Tiêu đề chính của banner">
                                        <small class="form-text text-muted">Dòng tiêu đề đầu tiên (ví dụ: "Thiết Kế – Thi Công")</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="subtitle_text">Tiêu đề phụ</label>
                                        <input type="text" class="form-control" id="subtitle_text" name="subtitle_text" value="<?php echo htmlspecialchars($banner['subtitle_text'] ?? 'Nội Thất Theo Yêu Cầu'); ?>" placeholder="Tiêu đề phụ của banner">
                                        <small class="form-text text-muted">Dòng tiêu đề thứ hai (ví dụ: "Nội Thất Theo Yêu Cầu")</small>
                                    </div>
                                </div>
                            </div>

                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Tính năng</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="feature1_text">Tính năng 1</label>
                                                <input type="text" class="form-control" id="feature1_text" name="feature1_text" value="<?php echo htmlspecialchars($banner['feature1_text'] ?? 'Miễn phí thiết kế 3D'); ?>" placeholder="Tính năng 1">
                                                <small class="form-text text-muted">Tính năng thứ nhất</small>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="feature2_text">Tính năng 2</label>
                                                <input type="text" class="form-control" id="feature2_text" name="feature2_text" value="<?php echo htmlspecialchars($banner['feature2_text'] ?? 'Giao hàng toàn quốc'); ?>" placeholder="Tính năng 2">
                                                <small class="form-text text-muted">Tính năng thứ hai</small>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="feature3_text">Tính năng 3</label>
                                                <input type="text" class="form-control" id="feature3_text" name="feature3_text" value="<?php echo htmlspecialchars($banner['feature3_text'] ?? 'Bảo hành lên tới 10 năm'); ?>" placeholder="Tính năng 3">
                                                <small class="form-text text-muted">Tính năng thứ ba</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="description_text">Mô tả</label>
                                <textarea class="form-control" id="description_text" name="description_text" rows="3" placeholder="Mô tả chi tiết về dịch vụ"><?php echo htmlspecialchars($banner['description_text'] ?? 'Chúng tôi chuyên thiết kế và sản xuất nội thất thông minh, hiện đại, với chất lượng cao cấp phù hợp với mọi không gian sống và làm việc.'); ?></textarea>
                                <small class="form-text text-muted">Đoạn mô tả ngắn về dịch vụ của bạn</small>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="button_text">Nội dung nút CTA chính</label>
                                        <input type="text" class="form-control" id="button_text" name="button_text" value="<?php echo htmlspecialchars($banner['button_text'] ?? 'Xem các mẫu thiết kế'); ?>" placeholder="Nội dung nút CTA chính">
                                        <small class="form-text text-muted">Văn bản hiển thị trên nút kêu gọi hành động chính</small>
                                    </div>
                                    <div class="form-group">
                                        <label for="button_link">URL nút CTA chính</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="fas fa-link"></i></span>
                                            </div>
                                            <input type="text" class="form-control" id="button_link" name="button_link" value="<?php echo htmlspecialchars($banner['button_link'] ?? '#'); ?>" placeholder="URL cho nút CTA chính">
                                        </div>
                                        <small class="form-text text-muted">Đường dẫn khi người dùng nhấp vào nút CTA chính</small>
                                        <ul class="text-muted mt-2 small">
                                            <li><code>products.php</code> - Trang sản phẩm</li>
                                            <li><code>category.php?id=1</code> - Danh mục cụ thể</li>
                                            <li><code>https://example.com</code> - Liên kết ngoài</li>
                                            <li><code>#</code> - Không chuyển hướng</li>
                                        </ul>
                                    </div>
                                    <div class="alert alert-info mt-2">
                                        <i class="fas fa-info-circle mr-1"></i> Biểu tượng sẽ được tự động chọn dựa trên nội dung nút. Ví dụ: "Xem sản phẩm" sẽ hiển thị <i class="fas fa-box"></i>, "Liên hệ" sẽ hiển thị <i class="fas fa-envelope"></i>.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="button2_text">Nội dung nút CTA phụ</label>
                                        <input type="text" class="form-control" id="button2_text" name="button2_text" value="<?php echo htmlspecialchars($banner['button2_text'] ?? 'Nhận tư vấn miễn phí'); ?>" placeholder="Nội dung nút CTA phụ">
                                        <small class="form-text text-muted">Văn bản hiển thị trên nút kêu gọi hành động phụ</small>
                                    </div>
                                    <div class="form-group">
                                        <label for="button2_link">URL nút CTA phụ</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="fas fa-link"></i></span>
                                            </div>
                                            <input type="text" class="form-control" id="button2_link" name="button2_link" value="<?php echo htmlspecialchars($banner['button2_link'] ?? 'contact.php'); ?>" placeholder="URL cho nút CTA phụ">
                                        </div>
                                        <small class="form-text text-muted">Đường dẫn khi người dùng nhấp vào nút CTA phụ</small>
                                        <ul class="text-muted mt-2 small">
                                            <li><code>contact.php</code> - Trang liên hệ</li>
                                            <li><code>tel:0972774646</code> - Gọi điện thoại</li>
                                            <li><code>https://example.com</code> - Liên kết ngoài</li>
                                            <li><code>#</code> - Không chuyển hướng</li>
                                        </ul>
                                    </div>
                                    <div class="alert alert-info mt-2">
                                        <i class="fas fa-info-circle mr-1"></i> Biểu tượng sẽ được tự động chọn dựa trên nội dung nút. Ví dụ: "Tư vấn" sẽ hiển thị <i class="fas fa-headset"></i>, "Gọi ngay" sẽ hiển thị <i class="fas fa-phone-alt"></i>.
                                    </div>
                                </div>
                            </div>

                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Thông tin liên hệ</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="contact_phone">Số điện thoại</label>
                                                <input type="text" class="form-control" id="contact_phone" name="contact_phone" value="<?php echo htmlspecialchars($banner['contact_phone'] ?? '************'); ?>" placeholder="Số điện thoại liên hệ">
                                                <small class="form-text text-muted">Số điện thoại hiển thị trên banner</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="contact_slogan">Slogan</label>
                                                <input type="text" class="form-control" id="contact_slogan" name="contact_slogan" value="<?php echo htmlspecialchars($banner['contact_slogan'] ?? 'Thiết kế chuẩn gu – Giao hàng đúng hẹn'); ?>" placeholder="Slogan">
                                                <small class="form-text text-muted">Slogan hiển thị bên cạnh số điện thoại</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-light border mt-3">
                                <div class="d-flex">
                                    <div class="mr-3">
                                        <i class="fas fa-lightbulb text-warning fa-2x"></i>
                                    </div>
                                    <div>
                                        <h6 class="font-weight-bold mb-1">Mẹo</h6>
                                        <p class="mb-0 small">
                                            Nội dung banner nên ngắn gọn, súc tích và truyền tải thông điệp chính của dịch vụ. Tiêu đề nên dưới 50 ký tự, mô tả nên dưới 150 ký tự để đảm bảo hiển thị tốt trên mọi thiết bị.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Custom file input
    document.querySelector('.custom-file-input').addEventListener('change', function(e) {
        var fileName = this.files[0] ? this.files[0].name : 'Chọn file mới...';
        var nextSibling = e.target.nextElementSibling;
        nextSibling.innerText = fileName;
    });

    // Xử lý khi chọn loại banner
    const bannerTypeRadios = document.querySelectorAll('input[name="banner_type"]');
    const bannerTypeCards = document.querySelectorAll('.banner-type-card');
    const bannerTypeInfo = document.getElementById('banner_type_info');
    const defaultBannerContent = document.getElementById('default_banner_content');

    // Highlight card khi được chọn
    bannerTypeCards.forEach(card => {
        card.addEventListener('click', function() {
            const bannerType = this.dataset.type;
            const radio = document.getElementById('banner_type_' + bannerType);
            radio.checked = true;

            // Trigger change event
            const event = new Event('change');
            radio.dispatchEvent(event);
        });
    });

    function toggleBannerTypeInfo() {
        const isMarketing = document.getElementById('banner_type_marketing').checked;

        // Highlight selected card
        bannerTypeCards.forEach(card => {
            if (card.dataset.type === (isMarketing ? 'marketing' : 'default')) {
                card.classList.add('border-primary');
                card.style.backgroundColor = '#f8f9ff';
            } else {
                card.classList.remove('border-primary');
                card.style.backgroundColor = '';
            }
        });

        // Update info text
        if (isMarketing) {
            bannerTypeInfo.textContent = 'Banner marketing sẽ chỉ hiển thị hình ảnh đơn thuần, không kèm theo bất kỳ nội dung HTML/CSS/JS nào. Phù hợp cho các hình ảnh quảng cáo có đầy đủ thông tin.';
            // Ẩn phần chỉnh sửa nội dung banner mặc định
            defaultBannerContent.style.display = 'none';
        } else {
            bannerTypeInfo.textContent = 'Banner mặc định sẽ hiển thị kèm theo phần nội dung HTML/CSS/JS đã được thiết kế sẵn trong mã nguồn, bao gồm slogan, nút CTA và các hiệu ứng.';
            // Hiển thị phần chỉnh sửa nội dung banner mặc định
            defaultBannerContent.style.display = 'block';
        }
    }

    bannerTypeRadios.forEach(function(radio) {
        radio.addEventListener('change', toggleBannerTypeInfo);
    });

    // Gọi hàm lần đầu để thiết lập trạng thái ban đầu
    toggleBannerTypeInfo();

    // Xử lý xem trước hình ảnh
    const imagePreview = document.getElementById('image-preview');
    const noImagePreview = document.getElementById('no-image-preview');
    const imageField = document.getElementById('image');

    imageField.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                imagePreview.classList.remove('d-none');
                imagePreview.querySelector('img').src = e.target.result;
                if (noImagePreview) noImagePreview.classList.add('d-none');
            }

            reader.readAsDataURL(this.files[0]);
        } else {
            imagePreview.classList.add('d-none');
            if (noImagePreview) noImagePreview.classList.remove('d-none');
        }
    });
});
</script>

<style>
.banner-type-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.banner-type-card:hover {
    background-color: #f8f9fa;
}

.custom-file-label::after {
    content: "Chọn";
}

.img-container {
    position: relative;
}
</style>

<?php include_once 'partials/footer.php'; ?>
