/*
 * Tablet Dropdown Force CSS
 * File CSS với độ ưu tiên cao nhất để FORCE hiển thị dropdown trên tablet
 * Override tất cả các file CSS khác
 */

/* FORCE OVERRIDE cho tablet - Độ ưu tiên cao nhất */
@media (min-width: 768px) and (max-width: 1024px) {

    /* STEP 1: Tắt hoàn toàn hover effects - NGĂN NHẤP NHÁY */
    .user-dropdown:hover .user-dropdown-menu,
    .user-dropdown-menu:hover,
    .cart-container:hover .mini-cart,
    .mini-cart:hover {
        opacity: 0 !important;
        visibility: hidden !important;
        transform: scale(0.95) !important;
        pointer-events: none !important;
    }

    /* NGĂN hover effect khi đã có class active */
    .user-dropdown.has-active:hover .user-dropdown-menu,
    .cart-container.has-active:hover .mini-cart {
        opacity: 1 !important;
        visibility: visible !important;
        transform: scale(1) !important;
        pointer-events: auto !important;
    }
    
    /* STEP 2: FORCE hiển thị khi có class active - Giống hệt desktop */
    .user-dropdown-menu.active {
        /* Display */
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;

        /* Transform - giống desktop */
        transform: scale(1) !important;

        /* Interaction */
        pointer-events: auto !important;

        /* Z-index */
        z-index: 1000 !important;

        /* Positioning - khoảng cách như desktop */
        position: absolute !important;
        top: calc(100% + 10px) !important;
        right: 0 !important;
        margin-top: 5px !important;

        /* Giữ nguyên styling từ desktop */
        transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1) !important;
    }

    .mini-cart.active {
        /* Display */
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;

        /* Transform - giống desktop */
        transform: scale(1) !important;

        /* Interaction */
        pointer-events: auto !important;

        /* Z-index */
        z-index: 1000 !important;

        /* Positioning - khoảng cách như desktop */
        position: absolute !important;
        top: calc(100% + 15px) !important;
        right: 0 !important;
        margin-top: 5px !important;

        /* Giữ nguyên styling từ desktop */
        transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1) !important;
    }
    
    /* STEP 3: Đảm bảo container có position relative */
    .user-dropdown,
    .cart-container {
        position: relative !important;
    }

    /* STEP 4: Áp dụng hiệu ứng desktop cho tablet (click thay vì hover) */

    /* User Account Button - Copy hiệu ứng từ desktop */
    .user-dropdown.has-active .action-btn,
    .user-dropdown .action-btn[aria-expanded="true"] {
        color: var(--primary) !important;
        /* Bỏ outline và focus ring */
        outline: none !important;
        outline-color: transparent !important;
        outline-width: 0 !important;
        outline-style: none !important;
        -webkit-focus-ring-color: transparent !important;
        -moz-outline: none !important;
    }

    /* User Account Icon - Scale effect và rotate logic */
    .user-dropdown.has-active .action-btn i,
    .user-dropdown .action-btn[aria-expanded="true"] i {
        transform: scale(1.1) rotate(180deg) !important; /* Quay lên khi box hiển thị */
    }

    /* Icon mặc định khi box ẩn - quay xuống */
    .user-dropdown .action-btn i {
        transform: rotate(0deg) !important; /* Quay xuống khi box ẩn */
        transition: transform 0.3s ease !important;
    }

    /* Cart Button - Copy hiệu ứng từ desktop NHƯNG BỎ BORDER */
    .cart-container.has-active .action-btn.cart-btn {
        background: var(--primary-lightest) !important;
        background-image: linear-gradient(to bottom, #ffedd5, #fed7aa) !important;
        box-shadow: 0 4px 16px 0 rgba(249, 115, 22, 0.15) !important;
        transform: translateY(-2px) !important;
        /* BỎ BORDER - không có viền */
        border: none !important;
        border-color: transparent !important;
        /* Bỏ outline và focus ring */
        outline: none !important;
        outline-color: transparent !important;
        outline-width: 0 !important;
        outline-style: none !important;
        -webkit-focus-ring-color: transparent !important;
        -moz-outline: none !important;
    }

    /* Cart Badge - Animation như desktop */
    .cart-container.has-active .cart-badge {
        animation: cart-badge-bounce 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
        box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.5) inset !important;
        background: linear-gradient(135deg, #ea580c, #f97316) !important;
    }
    

    
    /* STEP 5: Đảm bảo nội dung dropdown hiển thị đúng */
    .user-dropdown-menu.active .user-dropdown-header,
    .user-dropdown-menu.active .user-dropdown-divider,
    .mini-cart.active .mini-cart-header,
    .mini-cart.active .mini-cart-items,
    .mini-cart.active .mini-cart-footer {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* STEP 5.1: Đảm bảo user-dropdown-divider có style đúng */
    .user-dropdown-menu.active .user-dropdown-divider {
        height: 1px !important;
        background-color: #e2e8f0 !important;
        margin: var(--spacing-xs, 8px) 0 !important;
        border: none !important;
        opacity: 1 !important;
    }

    /* STEP 6: FORCE layout ngang cho user-dropdown-item */
    .user-dropdown-menu.active .user-dropdown-item {
        display: flex !important;
        align-items: center !important;
        gap: var(--spacing-sm, 8px) !important;
        flex-direction: row !important;
        white-space: nowrap !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* STEP 7: Đảm bảo icon và text không xuống dòng */
    .user-dropdown-menu.active .user-dropdown-item i {
        flex-shrink: 0 !important;
        width: 24px !important;
        height: 24px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* STEP 8: Đảm bảo dropdown có width đủ rộng */
    .user-dropdown-menu.active {
        width: 250px !important;
        min-width: 250px !important;
    }

    /* STEP 9: Sửa layout mini-cart-header - title và count cùng hàng */
    .mini-cart.active .mini-cart-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        flex-wrap: nowrap !important;
    }

    .mini-cart.active .mini-cart-title {
        flex: 1 1 auto !important;
        white-space: nowrap !important;
        margin-right: var(--spacing-sm, 8px) !important;
    }

    .mini-cart.active .mini-cart-count {
        flex: 0 0 auto !important;
        white-space: nowrap !important;
        margin-left: auto !important;
    }
    

}

/* BỎ FOCUS OUTLINE và BOX-SHADOW cho TẤT CẢ thiết bị - Desktop và Tablet */
.user-dropdown .action-btn,
.user-dropdown .action-btn:focus,
.user-dropdown .action-btn:focus-visible,
.user-dropdown .action-btn:active,
.cart-container .action-btn,
.cart-container .action-btn:focus,
.cart-container .action-btn:focus-visible,
.cart-container .action-btn:active,
.cart-btn,
.cart-btn:focus,
.cart-btn:focus-visible,
.cart-btn:active,
.action-btn.cart-btn,
.action-btn.cart-btn:focus,
.action-btn.cart-btn:focus-visible,
.action-btn.cart-btn:active,
/* BỎ focus-within box-shadow từ premium-header.css */
.user-actions > .user-dropdown:focus-within,
.user-actions > .cart-container:focus-within,
.user-dropdown:focus-within,
.cart-container:focus-within {
    outline: none !important;
    outline-color: transparent !important;
    outline-width: 0 !important;
    outline-style: none !important;
    outline-offset: 0 !important;
    -webkit-focus-ring-color: transparent !important;
    -moz-outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
    /* BỎ box-shadow cam từ .user-actions > *:focus-within */
    box-shadow: none !important;
}

/* CSS cho header khi cuộn (scrolled) - Phối màu như desktop */
@media (min-width: 768px) and (max-width: 1024px) {
    /* User Account Button khi header scrolled */
    .premium-header.scrolled .user-dropdown.has-active .action-btn,
    .premium-header.scrolled .user-dropdown .action-btn[aria-expanded="true"] {
        color: var(--white) !important;
    }

    /* User Account Icon khi header scrolled */
    .premium-header.scrolled .user-dropdown.has-active .action-btn i,
    .premium-header.scrolled .user-dropdown .action-btn[aria-expanded="true"] i {
        color: var(--white) !important;
        transform: scale(1.1) rotate(180deg) !important; /* Quay lên khi box hiển thị */
    }

    /* Icon mặc định khi header scrolled và box ẩn */
    .premium-header.scrolled .user-dropdown .action-btn i {
        color: var(--white) !important;
        transform: rotate(0deg) !important; /* Quay xuống khi box ẩn */
        transition: transform 0.3s ease !important;
    }

    /* Cart Button khi header scrolled */
    .premium-header.scrolled .cart-container.has-active .action-btn.cart-btn {
        background: rgba(255, 255, 255, 0.2) !important;
        background-image: none !important;
        box-shadow: 0 4px 16px 0 rgba(249, 115, 22, 0.15) !important;
        transform: translateY(-2px) !important;
        color: var(--white) !important;
        /* BỎ BORDER */
        border: none !important;
        border-color: transparent !important;
        /* Bỏ outline */
        outline: none !important;
        outline-color: transparent !important;
        outline-width: 0 !important;
        outline-style: none !important;
        -webkit-focus-ring-color: transparent !important;
        -moz-outline: none !important;
    }
}

/* Fallback cho tất cả kích thước tablet */
@media (max-width: 1024px) {
    .user-dropdown-menu.active,
    .mini-cart.active {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        z-index: 99999 !important;
    }
}
