/*
 * Responsive Profile CSS for Nội T<PERSON>t <PERSON>ng <PERSON>
 * T<PERSON>i ưu hiển thị responsive cho trang profile
 */

:root {
    --primary: #F37321;
    --primary-dark: #E05E00;
    --primary-light: #FF9D5C;
    --primary-ultra-light: rgba(243, 115, 33, 0.05);
    --transition-normal: all 0.3s ease;
    --shadow-hover: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-normal: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* T<PERSON>i <PERSON>u cho tất cả các kích thước màn hình */
.profile-container {
    width: 100%;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
    transition: padding var(--transition-normal);
}

/* <PERSON><PERSON>i <PERSON> cho sidebar và content */
.profile-layout {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    transition: all 0.3s ease;
}

.profile-sidebar {
    width: 100%;
    transition: all 0.3s ease;
}

.profile-content {
    width: 100%;
    transition: all 0.3s ease;
}

/* Tối ưu cho các card */
.profile-card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-normal);
    border: 1px solid rgba(229, 231, 235, 0.8);
    overflow: hidden;
    transition: all 0.3s ease;
}

.profile-card:hover {
    box-shadow: var(--shadow-hover);
    border-color: rgba(243, 115, 33, 0.2);
}

/* Tối ưu cho avatar */
.profile-avatar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    position: relative;
}

.profile-avatar {
    width: 8rem;
    height: 8rem;
    border-radius: 0.75rem;
    overflow: hidden;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.profile-avatar:hover {
    transform: scale(1.05);
}

/* Tối ưu cho form upload avatar */
.avatar-upload-form {
    width: 100%;
    max-width: 100%;
    transition: all 0.3s ease;
}

/* Tối ưu cho form thông tin */
.profile-form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    transition: all 0.3s ease;
}

/* Tối ưu cho các input */
.profile-input-group {
    position: relative;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.profile-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.profile-input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.2);
    outline: none;
}

.profile-input-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    transition: color 0.3s ease;
}

.profile-input:focus + .profile-input-icon {
    color: var(--primary);
}

/* Tối ưu cho nút submit */
.profile-submit-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary);
    color: white;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.profile-submit-button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 10px 15px rgba(243, 115, 33, 0.2);
}

.profile-submit-button:active {
    transform: translateY(0);
}

/* Tối ưu cho tiêu đề trang */
.profile-page-title {
    text-align: center;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

/* Responsive cho màn hình trung bình và lớn */
@media (min-width: 768px) {
    .profile-layout {
        flex-direction: row;
    }

    .profile-sidebar {
        width: 30%;
        max-width: 300px;
    }

    .profile-content {
        width: 70%;
        flex: 1;
    }

    .profile-form-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .profile-page-title {
        text-align: left;
    }

    .avatar-upload-form {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .profile-container {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

/* Responsive cho màn hình nhỏ */
@media (max-width: 767px) {
    .profile-avatar {
        width: 7rem;
        height: 7rem;
    }

    .profile-card {
        margin-bottom: 1rem;
    }

    .profile-submit-container {
        display: flex;
        justify-content: center;
    }

    .profile-container {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
}

/* Responsive cho màn hình rất nhỏ */
@media (max-width: 480px) {
    .profile-avatar {
        width: 6rem;
        height: 6rem;
    }

    .profile-card {
        border-radius: 0.5rem;
    }

    .profile-input {
        padding: 0.625rem 0.875rem 0.625rem 2.25rem;
    }

    .profile-input-icon {
        left: 0.625rem;
    }

    .profile-container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
}

/* Tối ưu cho màn hình siêu nhỏ */
@media (max-width: 360px) {
    .profile-avatar {
        width: 5rem;
        height: 5rem;
    }

    .profile-submit-button {
        width: 100%;
    }
}

/* Hiệu ứng chuyển đổi mượt mà */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Tối ưu cho dark mode (nếu có) */
@media (prefers-color-scheme: dark) {
    .profile-card {
        background-color: #1f2937;
        border-color: #374151;
    }

    .profile-input {
        background-color: #111827;
        border-color: #374151;
        color: #e5e7eb;
    }

    .profile-input-icon {
        color: #9ca3af;
    }
}
