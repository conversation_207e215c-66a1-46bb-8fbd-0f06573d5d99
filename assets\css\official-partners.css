/* Official Partners Section CSS - Elegant & Refined Design */
.official-partners-section {
    position: relative;
    overflow: hidden;
    background: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.03);
    margin-top: 3rem;
}

/* Background pattern - subtle and elegant */
.official-partners-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0.02;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23000000' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
}

/* Subtle top border accent */
.official-partners-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, rgba(243, 115, 33, 0.7), rgba(243, 115, 33, 0.3), rgba(243, 115, 33, 0));
    z-index: 1;
}

/* Content container - clean and spacious */
.official-partners-container {
    position: relative;
    z-index: 10;
    padding: 3.5rem 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
}

@media (min-width: 768px) {
    .official-partners-container {
        padding: 4rem 2rem;
    }
}

@media (min-width: 1024px) {
    .official-partners-container {
        padding: 4.5rem 2.5rem;
    }
}

/* Section title - elegant and minimal */
.official-partners-title-container {
    text-align: center;
    margin-bottom: 3.5rem;
    position: relative;
}

.official-partners-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: rgba(243, 115, 33, 0.05);
    color: #F37321;
    font-weight: 500;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    border: 1px solid rgba(243, 115, 33, 0.1);
}

.official-partners-badge .badge-icon {
    margin-right: 0.5rem;
    font-size: 0.875rem;
    color: #F37321;
}

.official-partners-heading {
    font-size: 1.875rem;
    font-weight: 600;
    color: #1F2937;
    position: relative;
    display: inline-block;
    letter-spacing: -0.01em;
    line-height: 1.3;
    margin-bottom: 1.25rem;
}

@media (min-width: 768px) {
    .official-partners-heading {
        font-size: 2rem;
    }
}

@media (min-width: 1024px) {
    .official-partners-heading {
        font-size: 2.25rem;
    }
}

.official-partners-heading::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 3rem;
    height: 2px;
    background: #F37321;
    opacity: 0.7;
}

.official-partners-description {
    max-width: 36rem;
    margin: 0 auto;
    color: #6B7280;
    font-size: 1rem;
    line-height: 1.6;
    padding: 0 1rem;
    font-weight: 400;
}

/* Partners grid - 2x2 grid for 4 partners */
.partners-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1.5rem;
    max-width: 1000px;
    margin: 0 auto;
}

@media (min-width: 640px) {
    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

/* Partner categories */
.partner-category {
    margin-bottom: 3rem;
    position: relative;
    padding-top: 1rem;
}

.partner-category:last-child {
    margin-bottom: 0;
}

.partner-category:not(:first-child) {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.partner-category-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
    display: inline-block;
}

.partner-category-title i {
    margin-right: 0.5rem;
    font-size: 1rem;
    vertical-align: middle;
    opacity: 0.9;
}

/* Partner item - Elegant & Refined design */
.partner-item {
    background: white;
    border-radius: 0.375rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
    padding: 1.5rem 1.25rem 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
    height: 100%;
    min-height: 200px;
}

.partner-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.04);
    border-color: rgba(243, 115, 33, 0.08);
}

.partner-logo-container {
    margin-bottom: 1rem;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center; /* Căn logo ở giữa */
    width: 100%;
    padding: 0.75rem;
    transition: all 0.3s ease;
    position: relative;
    background-color: #f9fafb;
    border-radius: 0.25rem;
}

.partner-item:hover .partner-logo-container {
    background-color: #f3f4f6;
}

.partner-logo {
    max-width: 85%;
    max-height: 75px;
    object-fit: contain;
    transition: all 0.3s ease;
    filter: none;
    opacity: 1;
}

.partner-item:hover .partner-logo {
    transform: scale(1.05);
}

/* Partner info */
.partner-info {
    text-align: center;
    width: 100%;
    position: relative;
    margin-top: 0.5rem;
}

.partner-description {
    font-size: 0.875rem;
    color: #6B7280;
    line-height: 1.5;
    max-width: 95%;
    margin: 0 auto;
    transition: all 0.3s ease;
}

.partner-item:hover .partner-description {
    color: #4B5563;
}

/* Partner type badge */
.partner-type {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.25rem 0.625rem;
    border-radius: 0 0 0 0.25rem; /* Bo góc dưới bên trái */
    font-size: 0.6875rem;
    font-weight: 500;
    background: rgba(243, 115, 33, 0.08);
    color: #F37321;
    border-left: 1px solid rgba(243, 115, 33, 0.15);
    border-bottom: 1px solid rgba(243, 115, 33, 0.15);
    transition: all 0.3s ease;
    z-index: 5;
}

.partner-type.wood {
    background: rgba(59, 130, 246, 0.08);
    color: #3B82F6;
    border-left: 1px solid rgba(59, 130, 246, 0.15);
    border-bottom: 1px solid rgba(59, 130, 246, 0.15);
}

.partner-type.plastic {
    background: rgba(16, 185, 129, 0.08);
    color: #10B981;
    border-left: 1px solid rgba(16, 185, 129, 0.15);
    border-bottom: 1px solid rgba(16, 185, 129, 0.15);
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .official-partners-section {
        border-radius: 0.25rem;
        margin-top: 2rem;
    }

    .official-partners-container {
        padding: 2.5rem 1rem;
    }

    .official-partners-title-container {
        margin-bottom: 2.5rem;
    }

    .official-partners-heading {
        font-size: 1.5rem;
    }

    .official-partners-badge {
        font-size: 0.6875rem;
        padding: 0.375rem 0.75rem;
    }

    .official-partners-description {
        font-size: 0.875rem;
        padding: 0 0.5rem;
        line-height: 1.5;
    }

    .partner-category {
        margin-bottom: 2.5rem;
    }

    .partner-category:not(:first-child) {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
    }

    .partner-category-title {
        font-size: 1.125rem;
        margin-bottom: 1.25rem;
    }

    .partners-grid {
        gap: 1rem;
    }

    .partner-item {
        padding: 1.5rem 1.25rem;
        min-height: 200px;
    }

    .partner-logo-container {
        height: 70px;
        margin-bottom: 1.25rem;
    }

    .partner-logo {
        max-height: 60px;
        max-width: 85%;
    }

    .partner-name {
        font-size: 1rem;
        margin-bottom: 0.375rem;
    }

    .partner-description {
        font-size: 0.8125rem;
        line-height: 1.4;
    }

    .partner-type {
        top: 0.5rem;
        right: 0.5rem;
        padding: 0.1875rem 0.5rem;
        font-size: 0.625rem;
        background: rgba(255, 255, 255, 0.9);
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .official-partners-container {
        padding: 2rem 0.75rem;
    }

    .official-partners-heading {
        font-size: 1.375rem;
    }

    .official-partners-description {
        font-size: 0.8125rem;
    }

    .partner-item {
        padding: 1.25rem 1rem 1.5rem;
        min-height: 190px;
        position: relative;
    }

    .partner-logo-container {
        height: 70px;
        margin-bottom: 0.75rem;
        padding: 0.5rem;
    }

    .partner-logo {
        max-height: 55px;
        max-width: 80%;
    }



    .partner-description {
        font-size: 0.8125rem;
        max-width: 100%;
        line-height: 1.4;
    }

    .partner-type {
        top: 0;
        right: 0;
        padding: 0.125rem 0.5rem;
        font-size: 0.625rem;
        white-space: nowrap; /* Đảm bảo text không bị ngắt dòng */
        max-width: 50%; /* Giới hạn chiều rộng để không chồng lên logo */
        text-align: center;
    }

    /* Optimize for very small screens */
    @media (max-width: 360px) {
        .partner-item {
            padding: 1.25rem 0.75rem 1.25rem;
            min-height: 170px;
        }

        .partner-logo-container {
            height: 60px;
            padding: 0.375rem;
        }

        .partner-logo {
            max-height: 45px;
            max-width: 85%;
        }

        .partner-description {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 0.75rem;
        }

        .partner-type {
            font-size: 0.5625rem;
            padding: 0.125rem 0.375rem;
            max-width: 55%; /* Tăng chiều rộng tối đa cho badge */
        }
    }
}
