/**
 * Center Notifications JS - <PERSON>ội Thất Băng <PERSON>ũ
 * Hệ thống thông báo hiển thị giữa màn hình
 */

// Biến lưu trữ timeout ID
let notificationTimeout = null;
let showNotificationCallCount = 0; // Biến đếm số lần gọi

// Hàm chuyển đổi flash message thành thông báo trung tâm
function convertFlashMessageToCenterNotification() {
    console.log('convertFlashMessageToCenterNotification CALLED');

    // THỬ NGHIỆM: Gọi showNotification trực tiếp để test duration
    /* // Bình luận lại khối này
    showNotification({
        type: 'info',
        title: 'Test Notification',
        message: 'This is a test. Should disappear in 2s.',
        duration: 2000
    });
    console.log('TEST showNotification called directly.');
    */

    // Kiểm tra xem có thông báo đư<PERSON><PERSON> lưu trữ trong window.flashMessage không
    console.log('Checking window.flashMessage:', window.flashMessage);
    if (window.flashMessage && window.flashMessage.message) {
        console.log('Using stored flash message:', window.flashMessage);

        // Hiển thị thông báo trung tâm từ dữ liệu đã lưu
        showNotification({
            type: window.flashMessage.type,
            title: window.flashMessage.type === 'success' ? 'Thành công' : 'Lỗi',
            message: window.flashMessage.message,
            duration: 2000 // Đặt thời gian là 2 giây
        });

        // Xóa thông báo đã lưu để tránh hiển thị lại
        window.flashMessage = null;
        return;
    }

    // Kiểm tra trang đăng nhập/đăng ký
    const isAuthPage = window.location.pathname.includes('/auth.php');

    // Tìm flash message container
    const flashMessageContainer = document.getElementById('flash-message-container');
    console.log('Flash message container (new):', flashMessageContainer);

    if (!flashMessageContainer) {
        // Tìm container cũ (cho tương thích ngược)
        const oldFlashContainer = document.querySelector('.container.mx-auto.px-4.mt-4');
        console.log('Flash message container (old):', oldFlashContainer);

        if (oldFlashContainer && !oldFlashContainer.id) {
            // Tìm alert trong container cũ
            const alertElement = oldFlashContainer.querySelector('[role="alert"]');
            if (alertElement) {
                // Lấy nội dung và loại thông báo
                const messageContentElement = alertElement.querySelector('.alert-content');
                if (!messageContentElement) {
                    console.error('Could not find .alert-content inside alertElement');
                    return;
                }
                const messageText = messageContentElement.textContent;

                // Xác định type dựa trên class của custom-alert (ví dụ: custom-alert-success)
                let type = 'info'; // Mặc định
                if (alertElement.classList.contains('custom-alert-success')) {
                    type = 'success';
                } else if (alertElement.classList.contains('custom-alert-danger')) {
                    type = 'error';
                } else if (alertElement.classList.contains('custom-alert-warning')) {
                    type = 'warning';
                }

                // ẨN THÔNG BÁO GỐC
                // oldFlashContainer.style.display = 'none'; // Tạm thời bình luận dòng này

                // Hiển thị thông báo trung tâm
                showNotification({
                    type: type,
                    title: type === 'success' ? 'Thành công' : (type === 'error' ? 'Lỗi' : (type === 'warning' ? 'Cảnh báo' : 'Thông báo')),
                    message: messageText,
                    duration: 2000 // Đặt thời gian là 2 giây
                });

                console.log('Converted old flash message to center notification:', messageText, type);
                return;
            }
        }

        // Nếu không tìm thấy container thông thường, kiểm tra thông báo đăng ký thành công trong trang auth
        if (isAuthPage) {
            const registerSuccessAlert = document.querySelector('.custom-alert-register-success');
            if (registerSuccessAlert) {
                // Đã xử lý riêng trong trang auth, không cần làm gì thêm
                return;
            }
        }
        return;
    }

    // Tìm alert trong container
    const alertElement = flashMessageContainer.querySelector('[role="alert"]');
    if (!alertElement) return;

    // Lấy nội dung và loại thông báo
    const messageContentElement = alertElement.querySelector('.alert-content');
    if (!messageContentElement) {
        console.error('Could not find .alert-content inside alertElement');
        return;
    }
    const messageText = messageContentElement.textContent;

    // Xác định type dựa trên class của custom-alert (ví dụ: custom-alert-success)
    let type = 'info'; // Mặc định
    if (alertElement.classList.contains('custom-alert-success')) {
        type = 'success';
    } else if (alertElement.classList.contains('custom-alert-danger')) {
        type = 'error';
    } else if (alertElement.classList.contains('custom-alert-warning')) {
        type = 'warning';
    }

    // ẨN THÔNG BÁO GỐC
    // flashMessageContainer.style.display = 'none'; // Tạm thời bình luận dòng này

    // Hiển thị thông báo trung tâm
    showNotification({
        type: type,
        title: type === 'success' ? 'Thành công' : (type === 'error' ? 'Lỗi' : (type === 'warning' ? 'Cảnh báo' : 'Thông báo')),
        message: messageText,
        duration: 2000 // Đặt thời gian là 2 giây
    });

    console.log('Converted flash message to center notification:', messageText, type);
}

// Kiểm tra xem container đã tồn tại chưa
function ensureNotificationContainer() {
    let container = document.getElementById('notification-container');

    if (!container) {
        // Tạo container nếu chưa tồn tại
        container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'notification-container hidden';

        // Tạo HTML cho container
        container.innerHTML = `
            <div class="notification-overlay"></div>
            <div class="notification-box">
                <div class="notification-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-header">
                        <h3 class="notification-title">Thông báo</h3>
                        <button class="notification-close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="notification-body">
                        <p>Nội dung thông báo</p>
                    </div>
                    <div class="notification-footer">
                        <button class="notification-action">OK</button>
                    </div>
                </div>
                <div class="notification-progress"></div>
            </div>
        `;

        // Thêm vào body
        document.body.appendChild(container);
    }

    return container;
}

/**
 * Hiển thị thông báo
 * @param {Object} options - Các tùy chọn cho thông báo
 * @param {string} options.type - Loại thông báo: 'success', 'error', 'warning', 'info'
 * @param {string} options.title - Tiêu đề thông báo
 * @param {string} options.message - Nội dung thông báo
 * @param {number} options.duration - Thời gian hiển thị (ms)
 * @param {boolean} options.action - Có hiển thị nút hành động không
 * @param {string} options.actionText - Text của nút hành động
 * @param {Function} options.actionCallback - Callback khi nhấn nút hành động
 * @param {Function} options.onClose - Callback khi đóng thông báo
 */
function showNotification(options) {
    // Các tùy chọn mặc định
    const defaults = {
        type: 'info',           // 'success', 'error', 'warning', 'info'
        title: 'Thông báo',     // Tiêu đề
        message: '',            // Nội dung
        duration: 5000,         // Thời gian hiển thị (ms)
        action: false,          // Có hiển thị nút hành động không
        actionText: 'OK',       // Text của nút hành động
        actionCallback: null,   // Callback khi nhấn nút hành động
        onClose: null           // Callback khi đóng thông báo
    };

    // Kết hợp tùy chọn mặc định với tùy chọn người dùng
    const settings = Object.assign({}, defaults, options);

    showNotificationCallCount++;
    const currentCallId = showNotificationCallCount;
    console.log(`showNotification call #${currentCallId}`, { options_passed: options, settings_resolved: settings });

    // Đảm bảo container tồn tại
    const container = ensureNotificationContainer();

    // Xóa timeout cũ nếu có
    if (notificationTimeout) {
        clearTimeout(notificationTimeout);
    }

    // Lấy các phần tử
    const box = container.querySelector('.notification-box');
    const iconElement = container.querySelector('.notification-icon i');
    const titleElement = container.querySelector('.notification-title');
    const messageElement = container.querySelector('.notification-body p');
    const actionButton = container.querySelector('.notification-action');
    const closeButton = container.querySelector('.notification-close');
    const overlay = container.querySelector('.notification-overlay');
    const progressBar = container.querySelector('.notification-progress');

    // Đặt class cho box theo loại thông báo
    box.className = 'notification-box notification-' + settings.type;

    // Cập nhật icon
    iconElement.className = '';

    switch(settings.type) {
        case 'success':
            iconElement.className = 'fas fa-check-circle';
            break;
        case 'error':
            iconElement.className = 'fas fa-exclamation-circle';
            break;
        case 'warning':
            iconElement.className = 'fas fa-exclamation-triangle';
            break;
        case 'info':
        default:
            iconElement.className = 'fas fa-info-circle';
            break;
    }

    // Cập nhật tiêu đề và nội dung
    titleElement.textContent = settings.title;
    messageElement.textContent = settings.message;

    // Xử lý nút hành động
    if (settings.action) {
        actionButton.textContent = settings.actionText;
        actionButton.style.display = 'block';
        actionButton.onclick = function() {
            if (typeof settings.actionCallback === 'function') {
                settings.actionCallback();
            }
            closeNotification();
        };
    } else {
        actionButton.style.display = 'none';
    }

    // Xử lý thanh tiến trình
    progressBar.style.animation = 'none';
    // Trigger reflow
    void progressBar.offsetWidth;
    
    let currentDuration = parseInt(settings.duration, 10);
    if (isNaN(currentDuration) || currentDuration <= 0) {
        console.warn(`Invalid duration (${settings.duration}), falling back to default (${defaults.duration}ms) for call #${currentCallId}`);
        currentDuration = defaults.duration; 
    }
    
    progressBar.style.animation = `progress ${currentDuration / 1000}s linear forwards`;

    // Hiển thị thông báo
    container.classList.remove('hidden');

    // Xử lý đóng thông báo
    function closeNotification() {
        // Thêm animation biến mất
        box.style.animation = 'notification-disappear 0.3s forwards';

        // Đợi animation kết thúc rồi ẩn container
        setTimeout(() => {
            container.classList.add('hidden');
            // Reset animation
            box.style.animation = 'notification-appear 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)';

            if (typeof settings.onClose === 'function') {
                settings.onClose();
            }
        }, 300);

        // Xóa event listener
        document.removeEventListener('keydown', handleEscKey);
    }

    // Xử lý sự kiện click nút đóng và overlay
    closeButton.onclick = closeNotification;
    overlay.onclick = closeNotification;

    // Xử lý phím Esc
    function handleEscKey(e) {
        if (e.key === 'Escape') {
            console.log(`Escape key pressed for call #${currentCallId}`);
            closeNotification();
        }
    }

    document.addEventListener('keydown', handleEscKey);

    // Tự động đóng sau thời gian
    console.log(`Setting timeout for call #${currentCallId} with duration: ${currentDuration}ms`);
    notificationTimeout = setTimeout(function() {
        console.log(`Timeout executed for call #${currentCallId}, closing notification.`);
        closeNotification();
    }, currentDuration);

    // Trả về hàm đóng để có thể đóng thông báo từ bên ngoài
    return closeNotification;
}

/**
 * Hiển thị thông báo thành công
 * @param {string} message - Nội dung thông báo
 * @param {string} title - Tiêu đề thông báo (mặc định: "Thành công")
 * @param {Object} options - Các tùy chọn khác
 */
function showSuccess(message, title = 'Thành công', options = {}) {
    console.log('showSuccess called with:', { message, title, options });
    return showNotification({
        type: 'success',
        title: title,
        message: message,
        ...options
    });
}

/**
 * Hiển thị thông báo lỗi
 * @param {string} message - Nội dung thông báo
 * @param {string} title - Tiêu đề thông báo (mặc định: "Lỗi")
 * @param {Object} options - Các tùy chọn khác
 */
function showError(message, title = 'Lỗi', options = {}) {
    console.log('showError called with:', { message, title, options });
    return showNotification({
        type: 'error',
        title: title,
        message: message,
        duration: 8000, // Lỗi hiển thị lâu hơn
        ...options
    });
}

/**
 * Hiển thị thông báo cảnh báo
 * @param {string} message - Nội dung thông báo
 * @param {string} title - Tiêu đề thông báo (mặc định: "Cảnh báo")
 * @param {Object} options - Các tùy chọn khác
 */
function showWarning(message, title = 'Cảnh báo', options = {}) {
    console.log('showWarning called with:', { message, title, options });
    return showNotification({
        type: 'warning',
        title: title,
        message: message,
        duration: 7000, // Cảnh báo hiển thị lâu hơn một chút
        ...options
    });
}

/**
 * Hiển thị thông báo thông tin
 * @param {string} message - Nội dung thông báo
 * @param {string} title - Tiêu đề thông báo (mặc định: "Thông tin")
 * @param {Object} options - Các tùy chọn khác
 */
function showInfo(message, title = 'Thông tin', options = {}) {
    console.log('showInfo called with:', { message, title, options });
    return showNotification({
        type: 'info',
        title: title,
        message: message,
        ...options
    });
}

// Chạy khi trang đã tải xong
document.addEventListener('DOMContentLoaded', function() {
    console.log('Center notifications system initialized');

    // Chuyển đổi flash message thành thông báo trung tâm
    setTimeout(convertFlashMessageToCenterNotification, 300);
});
