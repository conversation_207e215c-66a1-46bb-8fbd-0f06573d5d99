<?php
// Include init
require_once '../includes/init.php';

// Ki<PERSON>m tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

// Thiết lập tiêu đề trang
$page_title = 'Quản lý danh mục';

// X<PERSON> lý thêm danh mục mới
$errors = [];
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];

    // Thêm danh mục mới
    if ($action === 'add') {
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
        $status = isset($_POST['status']) ? 1 : 0;
        $show_on_homepage = isset($_POST['show_on_homepage']) ? 1 : 0;
        $show_in_popular_search = isset($_POST['show_in_popular_search']) ? 1 : 0;
        $popular_search_order = !empty($_POST['popular_search_order']) ? intval($_POST['popular_search_order']) : 0;

        // Validate dữ liệu
        if (empty($name)) {
            $errors[] = 'Tên danh mục không được để trống';
        }

        // Xử lý upload hình ảnh
        $image = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/categories/';

            // Tạo thư mục uploads/categories nếu chưa tồn tại
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_name = $_FILES['image']['name'];
            $file_tmp = $_FILES['image']['tmp_name'];
            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

            // Kiểm tra định dạng file
            $allowed_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            if (!in_array($file_ext, $allowed_exts)) {
                $errors[] = 'Chỉ cho phép upload file hình ảnh (jpg, jpeg, png, gif, webp)';
            } else {
                // Tạo tên file mới để tránh trùng lặp
                $new_file_name = 'category_' . time() . '_' . uniqid() . '.' . $file_ext;
                $upload_path = $upload_dir . $new_file_name;

                if (move_uploaded_file($file_tmp, $upload_path)) {
                    $image = $new_file_name;
                } else {
                    $errors[] = 'Có lỗi xảy ra khi upload file';
                }
            }
        }

        // Nếu không có lỗi, thêm danh mục vào database
        if (empty($errors)) {
            $category_data = [
                'name' => $name,
                'description' => $description,
                'image' => $image,
                'parent_id' => $parent_id,
                'status' => $status,
                'show_on_homepage' => $show_on_homepage,
                'show_in_popular_search' => $show_in_popular_search,
                'popular_search_order' => $popular_search_order
            ];

            $result = add_category($category_data);

            if ($result['success']) {
                $success_message = $result['message'];
            } else {
                $errors[] = $result['message'];
            }
        }
    }

    // Cập nhật danh mục
    else if ($action === 'edit') {
        $category_id = intval($_POST['category_id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
        $status = isset($_POST['status']) ? 1 : 0;
        $show_on_homepage = isset($_POST['show_on_homepage']) ? 1 : 0;
        $show_in_popular_search = isset($_POST['show_in_popular_search']) ? 1 : 0;
        $popular_search_order = !empty($_POST['popular_search_order']) ? intval($_POST['popular_search_order']) : 0;

        // Validate dữ liệu
        if (empty($name)) {
            $errors[] = 'Tên danh mục không được để trống';
        }

        // Kiểm tra danh mục cha không phải là danh mục con của danh mục hiện tại
        if (!empty($parent_id)) {
            $current_parent = $parent_id;
            while ($current_parent) {
                if ($current_parent == $category_id) {
                    $errors[] = 'Không thể chọn danh mục con làm danh mục cha';
                    break;
                }
                $parent = get_category_by_id($current_parent);
                $current_parent = $parent ? $parent['parent_id'] : null;
            }
        }

        // Lấy thông tin danh mục hiện tại
        $current_category = get_category_by_id($category_id);
        if (!$current_category) {
            $errors[] = 'Danh mục không tồn tại';
        }

        // Xử lý upload hình ảnh
        $image = $current_category['image']; // Giữ nguyên hình ảnh cũ nếu không upload mới
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/categories/';

            // Tạo thư mục uploads/categories nếu chưa tồn tại
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_name = $_FILES['image']['name'];
            $file_tmp = $_FILES['image']['tmp_name'];
            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

            // Kiểm tra định dạng file
            $allowed_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            if (!in_array($file_ext, $allowed_exts)) {
                $errors[] = 'Chỉ cho phép upload file hình ảnh (jpg, jpeg, png, gif, webp)';
            } else {
                // Tạo tên file mới để tránh trùng lặp
                $new_file_name = 'category_' . time() . '_' . uniqid() . '.' . $file_ext;
                $upload_path = $upload_dir . $new_file_name;

                if (move_uploaded_file($file_tmp, $upload_path)) {
                    // Xóa file cũ nếu có
                    if (!empty($current_category['image']) && file_exists($upload_dir . $current_category['image'])) {
                        unlink($upload_dir . $current_category['image']);
                    }
                    $image = $new_file_name;
                } else {
                    $errors[] = 'Có lỗi xảy ra khi upload file';
                }
            }
        }

        // Nếu không có lỗi, cập nhật danh mục
        if (empty($errors)) {
            $category_data = [
                'name' => $name,
                'description' => $description,
                'image' => $image,
                'parent_id' => $parent_id,
                'status' => $status,
                'show_on_homepage' => $show_on_homepage,
                'show_in_popular_search' => $show_in_popular_search,
                'popular_search_order' => $popular_search_order
            ];

            $result = update_category($category_id, $category_data);

            if ($result['success']) {
                $success_message = $result['message'];
            } else {
                $errors[] = $result['message'];
            }
        }
    }
}

// Lấy danh sách danh mục theo cấu trúc cây
$category_tree = get_category_tree();
$all_categories = get_categories(); // Lấy tất cả danh mục để dùng cho dropdown

// Include header
include_once 'partials/header.php';
?>

<!-- Content -->
<div class="container-fluid">
    <!-- Modern Categories Header -->
    <div class="categories-header">
        <div class="categories-header-content">
            <div class="categories-title-section">
                <div class="categories-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="categories-title-text">
                    <h1>Quản lý danh mục</h1>
                    <p class="categories-subtitle">Tổ chức và quản lý danh mục sản phẩm</p>
                </div>
            </div>
            <div class="categories-actions">
                <a href="<?php echo BASE_URL; ?>/admin/product-add.php" class="btn-modern-primary">
                    <i class="fas fa-plus"></i>
                    <span>Thêm sản phẩm</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
        <div class="stat-card categories">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Tổng danh mục</h3>
                    <div class="stat-value"><?php echo count($all_categories); ?></div>
                    <div class="stat-change neutral">
                        <i class="fas fa-layer-group"></i>
                        <span>Tất cả danh mục</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-tags"></i>
                </div>
            </div>
        </div>

        <div class="stat-card active">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Đang hiển thị</h3>
                    <div class="stat-value"><?php echo count(array_filter($all_categories, function($cat) { return $cat['status'] == 1; })); ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span><?php echo round((count(array_filter($all_categories, function($cat) { return $cat['status'] == 1; })) / max(count($all_categories), 1)) * 100, 1); ?>% tổng số</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-eye"></i>
                </div>
            </div>
        </div>

        <div class="stat-card homepage">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Trang chủ</h3>
                    <div class="stat-value"><?php echo count(array_filter($all_categories, function($cat) { return isset($cat['show_on_homepage']) && $cat['show_on_homepage'] == 1; })); ?></div>
                    <div class="stat-change info">
                        <i class="fas fa-home"></i>
                        <span>Hiển thị trang chủ</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
            </div>
        </div>

        <div class="stat-card tree">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Danh mục gốc</h3>
                    <div class="stat-value"><?php echo count($category_tree); ?></div>
                    <div class="stat-change neutral">
                        <i class="fas fa-sitemap"></i>
                        <span>Cấp độ đầu tiên</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-folder-tree"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Thông báo -->
    <?php display_flash_message(); ?>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger modern-alert">
        <div class="alert-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="alert-content">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>

    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success modern-alert">
        <div class="alert-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="alert-content">
            <?php echo $success_message; ?>
        </div>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- Form thêm/sửa danh mục -->
        <div class="col-md-4">
            <div class="modern-card category-form-card">
                <div class="modern-card-header">
                    <div class="card-header-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="card-header-content">
                        <h6 class="card-title" id="form-title">Thêm danh mục mới</h6>
                        <p class="card-subtitle">Điền thông tin danh mục</p>
                    </div>
                </div>
                <div class="modern-card-body">
                    <form id="category-form" method="POST" action="categories.php" enctype="multipart/form-data">
                        <input type="hidden" name="action" id="form-action" value="add">
                        <input type="hidden" name="category_id" id="category-id" value="">

                        <div class="modern-form-group">
                            <label for="name" class="modern-label">
                                <i class="fas fa-tag"></i>
                                Tên danh mục <span class="required">*</span>
                            </label>
                            <input type="text" class="modern-input" id="name" name="name" required placeholder="Nhập tên danh mục">
                            <div class="input-focus-border"></div>
                        </div>

                        <div class="modern-form-group">
                            <label for="parent_id" class="modern-label">
                                <i class="fas fa-sitemap"></i>
                                Danh mục cha
                            </label>
                            <select class="modern-select" id="parent_id" name="parent_id">
                                <option value="">-- Không có danh mục cha (Danh mục gốc) --</option>
                                <?php
                                // Hàm đệ quy để hiển thị danh mục theo cấu trúc cây trong dropdown
                                function display_category_options($categories, $selected_id = '', $level = 0, $exclude_id = null) {
                                    foreach ($categories as $category) {
                                        // Bỏ qua danh mục cần loại trừ (khi sửa)
                                        if ($exclude_id !== null && $category['id'] == $exclude_id) {
                                            continue;
                                        }

                                        $prefix = str_repeat('&mdash; ', $level);
                                        $selected = ($selected_id == $category['id']) ? 'selected' : '';
                                        echo '<option value="' . $category['id'] . '" ' . $selected . '>';
                                        echo $prefix . htmlspecialchars($category['name']);
                                        echo '</option>';

                                        if (!empty($category['children'])) {
                                            display_category_options($category['children'], $selected_id, $level + 1, $exclude_id);
                                        }
                                    }
                                }

                                // Hiển thị danh mục
                                display_category_options($category_tree);
                                ?>
                            </select>
                            <small class="form-text text-muted">Chọn danh mục cha nếu đây là danh mục con. Để trống nếu
                                đây là danh mục gốc.</small>
                        </div>

                        <div class="modern-form-group">
                            <label for="description" class="modern-label">
                                <i class="fas fa-align-left"></i>
                                Mô tả
                            </label>
                            <textarea class="modern-textarea" id="description" name="description" rows="3" placeholder="Nhập mô tả danh mục (tùy chọn)"></textarea>
                            <div class="input-focus-border"></div>
                        </div>

                        <div class="modern-form-group">
                            <label for="image" class="modern-label">
                                <i class="fas fa-image"></i>
                                Hình ảnh
                            </label>
                            <div class="image-upload-container">
                                <div id="image-preview-container" class="image-preview-wrapper d-none">
                                    <img id="image-preview" src="" alt="Xem trước hình ảnh" class="image-preview">
                                    <div class="image-preview-overlay">
                                        <button type="button" class="btn-remove-image" id="remove-image">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="file-input-wrapper">
                                    <input type="file" class="file-input" id="image" name="image" accept="image/*">
                                    <label for="image" class="file-input-label">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <span>Chọn hình ảnh</span>
                                    </label>
                                </div>
                                <small class="file-input-hint">Hỗ trợ: JPG, JPEG, PNG, GIF, WebP. Khuyến nghị WebP để tối ưu hiệu suất.</small>
                            </div>
                        </div>

                        <div class="modern-form-group">
                            <div class="modern-checkbox-group">
                                <div class="modern-checkbox">
                                    <input type="checkbox" id="status" name="status" value="1" checked>
                                    <label for="status">
                                        <span class="checkbox-icon">
                                            <i class="fas fa-check"></i>
                                        </span>
                                        <span class="checkbox-text">
                                            <strong>Hiển thị danh mục</strong>
                                            <small>Danh mục sẽ được hiển thị trên website</small>
                                        </span>
                                    </label>
                                </div>
                                <div class="modern-checkbox">
                                    <input type="checkbox" id="show_on_homepage" name="show_on_homepage" value="1">
                                    <label for="show_on_homepage">
                                        <span class="checkbox-icon">
                                            <i class="fas fa-check"></i>
                                        </span>
                                        <span class="checkbox-text">
                                            <strong>Hiển thị ở trang chủ</strong>
                                            <small>Sản phẩm thuộc danh mục này sẽ hiển thị ở trang chủ</small>
                                        </span>
                                    </label>
                                </div>
                                <div class="modern-checkbox">
                                    <input type="checkbox" id="show_in_popular_search" name="show_in_popular_search" value="1">
                                    <label for="show_in_popular_search">
                                        <span class="checkbox-icon">
                                            <i class="fas fa-check"></i>
                                        </span>
                                        <span class="checkbox-text">
                                            <strong>Hiển thị tìm kiếm phổ biến</strong>
                                            <small>Danh mục sẽ xuất hiện trong phần "Tìm kiếm phổ biến" ở trang sản phẩm</small>
                                        </span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="modern-form-group" id="popular-search-order-group" style="display: none;">
                            <label for="popular_search_order" class="modern-label">
                                <i class="fas fa-sort-numeric-up"></i>
                                Thứ tự hiển thị
                            </label>
                            <input type="number" class="modern-input" id="popular_search_order" name="popular_search_order"
                                   min="0" max="100" value="0" placeholder="Nhập thứ tự hiển thị (0-100)">
                            <div class="input-focus-border"></div>
                            <small class="form-text text-muted">
                                Thứ tự hiển thị trong danh sách tìm kiếm phổ biến. Số nhỏ hơn sẽ hiển thị trước.
                            </small>
                        </div>

                        <div class="modern-form-actions">
                            <button type="submit" class="btn-modern-primary" id="submit-btn">
                                <i class="fas fa-save"></i>
                                <span>Thêm danh mục</span>
                            </button>
                            <button type="button" class="btn-modern-secondary" id="reset-btn">
                                <i class="fas fa-redo"></i>
                                <span>Làm mới</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="modern-card guide-card">
                <div class="modern-card-header">
                    <div class="card-header-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <div class="card-header-content">
                        <h6 class="card-title">Hướng dẫn sử dụng</h6>
                        <p class="card-subtitle">Cách thao tác với danh mục</p>
                    </div>
                </div>
                <div class="modern-card-body">
                    <div class="guide-list">
                        <div class="guide-item">
                            <div class="guide-icon add">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="guide-content">
                                <strong>Thêm danh mục</strong>
                                <p>Điền thông tin và nhấn "Thêm danh mục"</p>
                            </div>
                        </div>
                        <div class="guide-item">
                            <div class="guide-icon edit">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="guide-content">
                                <strong>Sửa danh mục</strong>
                                <p>Nhấn nút sửa bên cạnh danh mục</p>
                            </div>
                        </div>
                        <div class="guide-item">
                            <div class="guide-icon tree">
                                <i class="fas fa-sitemap"></i>
                            </div>
                            <div class="guide-content">
                                <strong>Danh mục con</strong>
                                <p>Chọn danh mục cha từ dropdown</p>
                            </div>
                        </div>
                        <div class="guide-item warning">
                            <div class="guide-icon warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="guide-content">
                                <strong>Lưu ý quan trọng</strong>
                                <p>Xóa danh mục cha sẽ xóa tất cả danh mục con</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Danh sách danh mục -->
        <div class="col-md-8">
            <div class="modern-card categories-list-card">
                <div class="modern-card-header">
                    <div class="card-header-icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="card-header-content">
                        <h6 class="card-title">Danh sách danh mục</h6>
                        <p class="card-subtitle">Quản lý tất cả danh mục sản phẩm</p>
                    </div>
                    <div class="card-header-actions">
                        <div class="table-controls">
                            <div class="toggle-control">
                                <input type="checkbox" id="toggle-id-column" checked>
                                <label for="toggle-id-column" class="toggle-label">
                                    <span class="toggle-switch"></span>
                                    <span class="toggle-text">Hiện ID</span>
                                </label>
                            </div>
                            <div class="search-box">
                                <input type="text" id="category-search" placeholder="Tìm kiếm danh mục..." class="search-input">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modern-card-body">
                    <div class="modern-table-container">
                        <table class="modern-table" width="100%" cellspacing="0">
                            <thead class="modern-table-header">
                                <tr>
                                    <th width="5%" class="text-center id-column">ID</th>
                                    <th width="40%">Tên danh mục</th>
                                    <th width="12%" class="text-center">Trạng thái</th>
                                    <th width="12%" class="text-center">Trang chủ</th>
                                    <th width="16%" class="text-center">Tìm kiếm phổ biến</th>
                                    <th width="15%" class="text-center">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (count($category_tree) > 0): ?>
                                <?php
                                    // Hàm đệ quy để hiển thị danh mục theo cấu trúc cây
                                    function display_category_tree($categories, $level = 0) {
                                        foreach ($categories as $category):
                                    ?>
                                <tr class="category-row" data-level="<?php echo $level; ?>">
                                    <td class="text-center id-column">
                                        <span class="category-id-badge"><?php echo $category['id']; ?></span>
                                    </td>
                                    <td>
                                        <div class="category-info-combined" style="padding-left: <?php echo $level * 25; ?>px;">
                                            <div class="category-main-row">
                                                <!-- Hình ảnh danh mục -->
                                                <div class="category-image-wrapper">
                                                    <?php if (!empty($category['image'])): ?>
                                                        <img src="<?php echo BASE_URL . '/uploads/categories/' . $category['image']; ?>"
                                                            alt="<?php echo htmlspecialchars($category['name']); ?>"
                                                            class="category-image-inline">
                                                    <?php else: ?>
                                                        <div class="no-image-placeholder-inline">
                                                            <i class="fas fa-image"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Thông tin danh mục -->
                                                <div class="category-content">
                                                    <div class="category-name-row">
                                                        <?php
                                                        // Hiển thị cấp độ của danh mục bằng dấu gạch ngang
                                                        if ($level > 0) {
                                                            echo '<span class="category-level">' . str_repeat('└─ ', $level) . '</span>';
                                                        }

                                                        // Hiển thị biểu tượng cho danh mục cha
                                                        if (!empty($category['children'])) {
                                                            echo '<span class="category-type-icon parent"><i class="fas fa-folder"></i></span>';
                                                        } else {
                                                            echo '<span class="category-type-icon child"><i class="fas fa-tag"></i></span>';
                                                        }
                                                        ?>
                                                        <span class="category-name"><?php echo htmlspecialchars($category['name']); ?></span>
                                                        <?php if (!empty($category['children'])): ?>
                                                            <span class="children-count"><?php echo count($category['children']); ?> con</span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <?php if (!empty($category['description'])): ?>
                                                        <div class="category-description">
                                                            <?php echo htmlspecialchars($category['description']); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="hidden" class="category-id" value="<?php echo $category['id']; ?>">
                                        <input type="hidden" class="category-parent-id" value="<?php echo $category['parent_id'] ?? ''; ?>">
                                        <input type="hidden" class="category-status" value="<?php echo $category['status']; ?>">
                                        <input type="hidden" class="category-image" value="<?php echo $category['image']; ?>">
                                    </td>
                                    <td class="text-center status-column">
                                        <?php if ($category['status'] == 1): ?>
                                            <div class="status-badge active">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Hiển thị</span>
                                            </div>
                                        <?php else: ?>
                                            <div class="status-badge inactive">
                                                <i class="fas fa-eye-slash"></i>
                                                <span>Ẩn</span>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center homepage-column">
                                        <?php if (isset($category['show_on_homepage']) && $category['show_on_homepage'] == 1): ?>
                                            <div class="status-badge featured">
                                                <i class="fas fa-star"></i>
                                                <span>Trang chủ</span>
                                            </div>
                                        <?php else: ?>
                                            <div class="status-badge inactive">
                                                <i class="fas fa-minus-circle"></i>
                                                <span>Không</span>
                                            </div>
                                        <?php endif; ?>
                                        <input type="hidden" class="category-show-on-homepage" value="<?php echo isset($category['show_on_homepage']) ? $category['show_on_homepage'] : '0'; ?>">
                                    </td>
                                    <td class="text-center popular-search-column">
                                        <?php if (isset($category['show_in_popular_search']) && $category['show_in_popular_search'] == 1): ?>
                                            <div class="status-badge popular-search">
                                                <i class="fas fa-search"></i>
                                                <span>Phổ biến</span>
                                                <?php if (isset($category['popular_search_order']) && $category['popular_search_order'] > 0): ?>
                                                    <small class="order-badge">#<?php echo $category['popular_search_order']; ?></small>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="status-badge inactive">
                                                <i class="fas fa-minus-circle"></i>
                                                <span>Không</span>
                                            </div>
                                        <?php endif; ?>
                                        <input type="hidden" class="category-show-in-popular-search" value="<?php echo isset($category['show_in_popular_search']) ? $category['show_in_popular_search'] : '0'; ?>">
                                        <input type="hidden" class="category-popular-search-order" value="<?php echo isset($category['popular_search_order']) ? $category['popular_search_order'] : '0'; ?>">
                                    </td>
                                    <td class="text-center">
                                        <div class="table-actions">
                                            <button type="button" class="action-btn edit-btn edit-category" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="action-btn add-btn add-subcategory" title="Thêm danh mục con" data-id="<?php echo $category['id']; ?>">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <a href="category-delete.php?id=<?php echo $category['id']; ?>" class="action-btn delete-btn" title="Xóa" onclick="return confirm('Bạn có chắc chắn muốn xóa danh mục này?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                                        // Hiển thị danh mục con nếu có
                                        if (!empty($category['children'])) {
                                            display_category_tree($category['children'], $level + 1);
                                        }
                                        ?>
                                <?php
                                        endforeach;
                                    }

                                    // Hiển thị danh mục
                                    display_category_tree($category_tree);
                                    ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center">Không có danh mục nào</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript để xử lý form -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('category-form');
    const formTitle = document.getElementById('form-title');
    const formAction = document.getElementById('form-action');
    const categoryId = document.getElementById('category-id');
    const nameInput = document.getElementById('name');
    const parentIdSelect = document.getElementById('parent_id');
    const descriptionTextarea = document.getElementById('description');
    const statusCheckbox = document.getElementById('status');
    const submitBtn = document.getElementById('submit-btn');
    const resetBtn = document.getElementById('reset-btn');

    // Xử lý nút sửa danh mục
    document.querySelectorAll('.edit-category').forEach(function(button) {
        button.addEventListener('click', function() {
            const row = this.closest('tr');
            const id = row.querySelector('.category-id').value;
            const name = row.querySelector('.category-name').textContent;
            const parentId = row.querySelector('.category-parent-id').value;
            const status = row.querySelector('.category-status').value;
            const showOnHomepage = row.querySelector('.category-show-on-homepage') ?
                row.querySelector('.category-show-on-homepage').value : '0';
            const showInPopularSearch = row.querySelector('.category-show-in-popular-search') ?
                row.querySelector('.category-show-in-popular-search').value : '0';
            const popularSearchOrder = row.querySelector('.category-popular-search-order') ?
                row.querySelector('.category-popular-search-order').value : '0';
            const description = row.querySelector('.category-description') ?
                row.querySelector('.category-description').textContent : '';
            const image = row.querySelector('.category-image') ?
                row.querySelector('.category-image').value : '';

            // Cập nhật form
            formTitle.textContent = 'Chỉnh sửa danh mục';
            formAction.value = 'edit';
            categoryId.value = id;
            nameInput.value = name;
            descriptionTextarea.value = description;
            statusCheckbox.checked = (status == '1');
            document.getElementById('show_on_homepage').checked = (showOnHomepage == '1');
            document.getElementById('show_in_popular_search').checked = (showInPopularSearch == '1');
            document.getElementById('popular_search_order').value = popularSearchOrder;
            submitBtn.querySelector('span').textContent = 'Cập nhật danh mục';

            // Trigger hiển thị/ẩn trường thứ tự
            togglePopularSearchOrderField();

            // Hiển thị hình ảnh nếu có
            const imagePreviewContainer = document.getElementById('image-preview-container');
            const imagePreview = document.getElementById('image-preview');

            if (image) {
                imagePreview.src = '<?php echo BASE_URL; ?>/uploads/categories/' + image;
                imagePreviewContainer.classList.remove('d-none');
            } else {
                imagePreviewContainer.classList.add('d-none');
            }

            // Cập nhật select2 (cần loại bỏ danh mục hiện tại và các danh mục con)
            if ($.fn.select2) {
                // Xóa tất cả các option hiện tại
                parentIdSelect.innerHTML =
                    '<option value="">-- Không có danh mục cha (Danh mục gốc) --</option>';

                // Thêm lại các option, loại trừ danh mục hiện tại
                <?php
                echo "const categories = " . json_encode($all_categories) . ";\n";
                ?>

                function addOptions(categories, level = 0, excludeId = null) {
                    categories.forEach(function(category) {
                        if (category.id != excludeId) {
                            const option = document.createElement('option');
                            option.value = category.id;
                            option.innerHTML = '&mdash;'.repeat(level) + ' ' + category
                                .name;
                            option.selected = (category.id == parentId);
                            parentIdSelect.appendChild(option);
                        }
                    });
                }

                addOptions(categories, 0, id);
                $(parentIdSelect).trigger('change');
            }

            // Cuộn lên đầu form
            form.scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // Xử lý nút thêm danh mục con
    document.querySelectorAll('.add-subcategory').forEach(function(button) {
        button.addEventListener('click', function() {
            const parentId = this.getAttribute('data-id');

            // Reset form
            resetForm();

            // Thiết lập danh mục cha
            if ($.fn.select2) {
                $(parentIdSelect).val(parentId).trigger('change');
            } else {
                parentIdSelect.value = parentId;
            }

            // Cuộn lên đầu form
            form.scrollIntoView({
                behavior: 'smooth'
            });

            // Focus vào ô tên
            nameInput.focus();
        });
    });

    // Xử lý nút reset
    resetBtn.addEventListener('click', function() {
        resetForm();
    });

    // Xử lý xem trước hình ảnh khi chọn file
    const imageInput = document.getElementById('image');
    const imagePreviewContainer = document.getElementById('image-preview-container');
    const imagePreview = document.getElementById('image-preview');
    const removeImageBtn = document.getElementById('remove-image');

    imageInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                imagePreview.src = e.target.result;
                imagePreviewContainer.classList.remove('d-none');
            }

            reader.readAsDataURL(this.files[0]);
        }
    });

    // Xử lý nút xóa hình ảnh
    removeImageBtn.addEventListener('click', function() {
        imageInput.value = '';
        imagePreviewContainer.classList.add('d-none');
    });

    // Hàm reset form
    function resetForm() {
        formTitle.textContent = 'Thêm danh mục mới';
        formAction.value = 'add';
        categoryId.value = '';
        form.reset();
        submitBtn.querySelector('span').textContent = 'Thêm danh mục';
        imagePreviewContainer.classList.add('d-none');

        if ($.fn.select2) {
            $(parentIdSelect).val('').trigger('change');
        }
    }

    // Chức năng toggle cột ID với localStorage
    const toggleIdColumn = document.getElementById('toggle-id-column');
    if (toggleIdColumn) {
        // Khôi phục trạng thái từ localStorage
        const savedState = localStorage.getItem('categories-show-id-column');
        const showIdColumn = savedState !== null ? savedState === 'true' : true; // Mặc định hiển thị

        toggleIdColumn.checked = showIdColumn;
        updateIdColumnVisibility(showIdColumn);

        toggleIdColumn.addEventListener('change', function() {
            const isChecked = this.checked;
            updateIdColumnVisibility(isChecked);
            // Lưu trạng thái vào localStorage
            localStorage.setItem('categories-show-id-column', isChecked.toString());
        });
    }

    // Hàm cập nhật hiển thị cột ID
    function updateIdColumnVisibility(show) {
        const idColumns = document.querySelectorAll('.id-column');
        const toggleText = document.querySelector('.toggle-text');

        if (show) {
            idColumns.forEach(col => col.style.display = '');
            if (toggleText) toggleText.textContent = 'Hiện ID';
        } else {
            idColumns.forEach(col => col.style.display = 'none');
            if (toggleText) toggleText.textContent = 'Ẩn ID';
        }
    }

    // Chức năng tìm kiếm danh mục
    const searchInput = document.getElementById('category-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            const categoryRows = document.querySelectorAll('.category-row');
            let visibleCount = 0;

            categoryRows.forEach(function(row) {
                const categoryName = row.querySelector('.category-name');
                const categoryDescription = row.querySelector('.category-description');

                let textContent = '';
                if (categoryName) {
                    textContent += categoryName.textContent.toLowerCase();
                }
                if (categoryDescription) {
                    textContent += ' ' + categoryDescription.textContent.toLowerCase();
                }

                if (textContent.includes(searchTerm)) {
                    row.classList.remove('hidden');
                    visibleCount++;
                } else {
                    row.classList.add('hidden');
                }
            });

            // Hiển thị thông báo nếu không tìm thấy kết quả
            let noResultsRow = document.querySelector('.no-results-row');
            if (visibleCount === 0 && searchTerm !== '') {
                if (!noResultsRow) {
                    const tbody = document.querySelector('.modern-table tbody');
                    noResultsRow = document.createElement('tr');
                    noResultsRow.className = 'no-results-row';
                    noResultsRow.innerHTML = '<td colspan="5" class="no-results">Không tìm thấy danh mục nào phù hợp với từ khóa "' + searchTerm + '"</td>';
                    tbody.appendChild(noResultsRow);
                }
                noResultsRow.style.display = 'table-row';
            } else if (noResultsRow) {
                noResultsRow.style.display = 'none';
            }
        });
    }

    // Xử lý hiển thị/ẩn trường thứ tự tìm kiếm phổ biến
    const showInPopularSearchCheckbox = document.getElementById('show_in_popular_search');
    const popularSearchOrderGroup = document.getElementById('popular-search-order-group');

    if (showInPopularSearchCheckbox && popularSearchOrderGroup) {
        // Kiểm tra trạng thái ban đầu
        togglePopularSearchOrderField();

        // Lắng nghe sự kiện thay đổi
        showInPopularSearchCheckbox.addEventListener('change', togglePopularSearchOrderField);

        function togglePopularSearchOrderField() {
            if (showInPopularSearchCheckbox.checked) {
                popularSearchOrderGroup.style.display = 'block';
                popularSearchOrderGroup.style.animation = 'fadeIn 0.3s ease-in-out';
            } else {
                popularSearchOrderGroup.style.display = 'none';
                // Reset giá trị về 0 khi ẩn
                document.getElementById('popular_search_order').value = '0';
            }
        }
    }
});
</script>

<style>
/* Modern Categories Page Styles - Nội Thất Băng Vũ Theme */
:root {
    /* Primary Colors - Cam chính từ brand */
    --primary: #F37321;
    --primary-dark: #D65A0F;
    --primary-darker: #D35400;
    --primary-light: #FF8A3D;
    --primary-lighter: #FFA66B;
    --primary-lightest: #FFD0AD;
    --primary-ultra-light: #FFF4EC;

    /* Secondary Colors - Xanh đậm */
    --secondary: #2A3B47;
    --secondary-dark: #1E2A32;
    --secondary-light: #435868;

    /* Status Colors */
    --success: #10B981;
    --warning: #F59E0B;
    --danger: #EF4444;
    --info: #3B82F6;

    /* Neutral Colors */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;

    /* Gradients */
    --primary-gradient: linear-gradient(135deg, #F37321 0%, #D65A0F 100%);
    --secondary-gradient: linear-gradient(135deg, #2A3B47 0%, #1E2A32 100%);
    --success-gradient: linear-gradient(135deg, #10B981 0%, #059669 100%);
    --warning-gradient: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    --danger-gradient: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    --info-gradient: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);

    /* Shadows */
    --card-shadow: 0 10px 30px rgba(243, 115, 33, 0.08);
    --card-shadow-hover: 0 20px 40px rgba(243, 115, 33, 0.12);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

    /* Border Radius */
    --border-radius: 1.25rem;
    --border-radius-sm: 0.5rem;
    --border-radius-md: 0.75rem;
    --border-radius-lg: 1rem;

    /* Transitions */
    --transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    --transition-fast: all 0.2s ease;
}

/* Categories Page Header */
.categories-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(243, 115, 33, 0.1);
    position: relative;
    overflow: hidden;
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.08),
        0 4px 6px -2px rgba(0, 0, 0, 0.03),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    transition: var(--transition);
}

.categories-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(243, 115, 33, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(42, 59, 71, 0.05) 0%, transparent 40%),
        linear-gradient(135deg, rgba(243, 115, 33, 0.02) 0%, transparent 50%);
    pointer-events: none;
    opacity: 0.8;
}

.categories-header::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(243, 115, 33, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.categories-header:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.categories-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.categories-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.categories-icon {
    font-size: 1.5rem;
    color: white;
    background: var(--primary-gradient);
    padding: 1rem;
    border-radius: var(--border-radius);
    width: 3.5rem;
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    position: relative;
    box-shadow:
        0 6px 10px -2px rgba(243, 115, 33, 0.25),
        0 2px 4px -1px rgba(243, 115, 33, 0.15);
}

.categories-icon::before {
    content: '';
    position: absolute;
    inset: -3px;
    border-radius: inherit;
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.categories-icon::after {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: calc(var(--border-radius) - 2px);
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);
    pointer-events: none;
}

.categories-header:hover .categories-icon {
    transform: rotate(15deg) scale(1.1);
    box-shadow:
        0 20px 25px -5px rgba(243, 115, 33, 0.4),
        0 10px 10px -5px rgba(243, 115, 33, 0.3);
}

.categories-header:hover .categories-icon::before {
    opacity: 1;
    inset: -5px;
}

.categories-title-text h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.categories-title-text h1::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    border-radius: 1px;
    transition: width 0.6s ease;
}

.categories-header:hover .categories-title-text h1::before {
    width: 100%;
}

.categories-subtitle {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
    opacity: 0.9;
    transition: var(--transition-fast);
}

.categories-header:hover .categories-subtitle {
    color: var(--gray-700);
    opacity: 1;
}

.categories-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

.btn-modern-primary {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
}

.btn-modern-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-modern-primary:hover::before {
    left: 100%;
}

.btn-modern-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-darker) 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(243, 115, 33, 0.3);
    text-decoration: none;
}

.btn-modern-primary i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.btn-modern-primary:hover i {
    transform: scale(1.1);
}

/* Modern Stats Grid - Consistent with dashboard */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.08),
        0 4px 6px -2px rgba(0, 0, 0, 0.03);
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(243, 115, 33, 0.08);
}

.stat-card:hover {
    transform: translateY(-6px) scale(1.01);
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.12),
        0 10px 10px -5px rgba(0, 0, 0, 0.08);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--primary-gradient);
    transition: all 0.4s ease;
    border-radius: 1.5rem 1.5rem 0 0;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -30%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(243, 115, 33, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    transition: all 0.4s ease;
}

.stat-card:hover::after {
    transform: scale(1.2);
    opacity: 0.8;
}

.stat-card.categories::before { background: var(--primary-gradient); }
.stat-card.active::before { background: var(--success-gradient); }
.stat-card.homepage::before { background: var(--info-gradient); }
.stat-card.tree::before { background: var(--secondary-gradient); }

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.stat-content h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    margin: 0 0 0.5rem 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--danger);
}

.stat-change.neutral {
    color: var(--gray-500);
}

.stat-change.info {
    color: var(--info);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    background: rgba(243, 115, 33, 0.1);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    background: rgba(243, 115, 33, 0.15);
}

/* Modern Alerts */
.modern-alert {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    overflow: hidden;
}

.modern-alert::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary);
}

.modern-alert.alert-success::before {
    background: var(--success);
}

.modern-alert.alert-danger::before {
    background: var(--danger);
}

.alert-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    color: white;
}

.alert-success .alert-icon {
    background: var(--success-gradient);
}

.alert-danger .alert-icon {
    background: var(--danger-gradient);
}

.alert-content {
    flex: 1;
    color: var(--gray-700);
    font-weight: 500;
}

/* Modern Cards */
.modern-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(243, 115, 33, 0.08);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    margin-bottom: 2rem;
}

.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(243, 115, 33, 0.02) 0%, transparent 50%);
    opacity: 0;
    transition: var(--transition);
    pointer-events: none;
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
    border-color: rgba(243, 115, 33, 0.12);
}

.modern-card:hover::before {
    opacity: 1;
}

.modern-card-header {
    padding: 1.5rem 2rem 1rem;
    border-bottom: 1px solid rgba(243, 115, 33, 0.08);
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

.card-header-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    box-shadow: 0 4px 8px rgba(243, 115, 33, 0.2);
    transition: var(--transition);
}

.modern-card:hover .card-header-icon {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(243, 115, 33, 0.3);
}

.card-header-content {
    flex: 1;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
}

.card-subtitle {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0;
    font-weight: 400;
}

.card-header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modern-card-body {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

/* Table Controls */
.table-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Toggle Control */
.toggle-control {
    display: flex;
    align-items: center;
}

.toggle-control input[type="checkbox"] {
    display: none;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--gray-700);
    font-weight: 500;
}

.toggle-switch {
    position: relative;
    width: 2.5rem;
    height: 1.25rem;
    background: var(--gray-300);
    border-radius: 1rem;
    transition: var(--transition-fast);
}

.toggle-switch::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 1rem;
    height: 1rem;
    background: white;
    border-radius: 50%;
    transition: var(--transition-fast);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-control input[type="checkbox"]:checked + .toggle-label .toggle-switch {
    background: var(--primary);
}

.toggle-control input[type="checkbox"]:checked + .toggle-label .toggle-switch::before {
    transform: translateX(1.25rem);
}

.toggle-text {
    font-weight: 600;
    white-space: nowrap;
}

/* Search Box */
.search-box {
    position: relative;
    width: 250px;
}

.search-input {
    width: 100%;
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: var(--transition-fast);
    background: white;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    font-size: 0.875rem;
    pointer-events: none;
}

/* Modern Form Elements */
.modern-form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.modern-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.modern-label i {
    color: var(--primary);
    font-size: 0.875rem;
}

.required {
    color: var(--danger);
    font-weight: 700;
}

.modern-input,
.modern-textarea,
.modern-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: var(--transition-fast);
    background: white;
    position: relative;
    z-index: 1;
}

.modern-input:focus,
.modern-textarea:focus,
.modern-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
}

.input-focus-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
    border-radius: 1px;
}

.modern-input:focus + .input-focus-border,
.modern-textarea:focus + .input-focus-border {
    width: 100%;
}

.modern-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Image Upload */
.image-upload-container {
    position: relative;
}

.image-preview-wrapper {
    position: relative;
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    overflow: hidden;
    display: inline-block;
}

.image-preview {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 0.5rem;
    box-shadow: var(--shadow-md);
}

.image-preview-overlay {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.5rem;
}

.btn-remove-image {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: var(--danger);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.btn-remove-image:hover {
    background: #dc2626;
    transform: scale(1.1);
}

.file-input-wrapper {
    position: relative;
    display: inline-block;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-input-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border: 2px dashed var(--gray-300);
    border-radius: 0.5rem;
    color: var(--gray-600);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    text-align: center;
}

.file-input-label:hover {
    border-color: var(--primary);
    background: linear-gradient(135deg, var(--primary-ultra-light) 0%, var(--primary-lightest) 100%);
    color: var(--primary-dark);
}

.file-input-hint {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--gray-500);
    font-style: italic;
}

/* Modern Checkboxes */
.modern-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.modern-checkbox {
    position: relative;
}

.modern-checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.modern-checkbox label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 2px solid var(--gray-200);
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    transition: var(--transition-fast);
    position: relative;
}

.modern-checkbox label:hover {
    border-color: var(--primary-light);
    background: linear-gradient(135deg, var(--primary-ultra-light) 0%, #ffffff 100%);
}

.modern-checkbox input[type="checkbox"]:checked + label {
    border-color: var(--primary);
    background: linear-gradient(135deg, var(--primary-ultra-light) 0%, #ffffff 100%);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
}

.checkbox-icon {
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 0.25rem;
    border: 2px solid var(--gray-300);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.checkbox-icon i {
    font-size: 0.75rem;
    color: white;
    opacity: 0;
    transition: var(--transition-fast);
}

.modern-checkbox input[type="checkbox"]:checked + label .checkbox-icon {
    background: var(--primary);
    border-color: var(--primary);
}

.modern-checkbox input[type="checkbox"]:checked + label .checkbox-icon i {
    opacity: 1;
}

.checkbox-text {
    flex: 1;
}

.checkbox-text strong {
    display: block;
    color: var(--gray-800);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.checkbox-text small {
    color: var(--gray-600);
    font-size: 0.75rem;
    line-height: 1.4;
}

/* Form Actions */
.modern-form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(243, 115, 33, 0.08);
}

.btn-modern-secondary {
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    color: var(--gray-700);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-modern-secondary:hover {
    background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-300) 100%);
    color: var(--gray-800);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    text-decoration: none;
}

/* Guide List */
.guide-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.guide-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border: 1px solid rgba(243, 115, 33, 0.08);
    transition: var(--transition-fast);
}

.guide-item:hover {
    transform: translateX(4px);
    border-color: rgba(243, 115, 33, 0.15);
    box-shadow: 0 2px 8px rgba(243, 115, 33, 0.1);
}

.guide-item.warning {
    border-color: rgba(245, 158, 11, 0.2);
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.guide-item.warning:hover {
    border-color: rgba(245, 158, 11, 0.3);
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.15);
}

.guide-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: white;
    flex-shrink: 0;
}

.guide-icon.add {
    background: var(--success-gradient);
}

.guide-icon.edit {
    background: var(--info-gradient);
}

.guide-icon.tree {
    background: var(--primary-gradient);
}

.guide-icon.warning {
    background: var(--warning-gradient);
}

.guide-content {
    flex: 1;
}

.guide-content strong {
    display: block;
    color: var(--gray-800);
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.guide-content p {
    color: var(--gray-600);
    font-size: 0.75rem;
    margin: 0;
    line-height: 1.4;
}

/* Modern Table */
.modern-table-container {
    border-radius: 0.75rem;
    overflow: hidden;
    border: 1px solid rgba(243, 115, 33, 0.08);
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.modern-table-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
}

.modern-table-header th {
    padding: 1rem 0.5rem;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 2px solid rgba(243, 115, 33, 0.1);
    position: relative;
    white-space: nowrap;
    vertical-align: middle;
}

.modern-table-header th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
}

.modern-table-header th:hover::after {
    width: 100%;
}

.category-row {
    border-bottom: 1px solid rgba(243, 115, 33, 0.05);
    transition: var(--transition-fast);
}

.category-row:hover {
    background: linear-gradient(135deg, var(--primary-ultra-light) 0%, #ffffff 100%);
}

.category-row td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    white-space: nowrap;
}

/* Cho phép cột tên danh mục xuống dòng */
.category-row td:nth-child(3) {
    white-space: normal;
    word-wrap: break-word;
}

/* Visual hierarchy cho danh mục con */
.category-row[data-level="1"] {
    background: rgba(243, 115, 33, 0.02);
}

.category-row[data-level="2"] {
    background: rgba(243, 115, 33, 0.04);
}

.category-row[data-level="3"] {
    background: rgba(243, 115, 33, 0.06);
}

/* Hiệu ứng hover cho danh mục con */
.category-row[data-level="1"]:hover {
    background: rgba(243, 115, 33, 0.08);
}

.category-row[data-level="2"]:hover {
    background: rgba(243, 115, 33, 0.10);
}

.category-row[data-level="3"]:hover {
    background: rgba(243, 115, 33, 0.12);
}

.category-id-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background: var(--primary-gradient);
    color: white;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.75rem;
    box-shadow: 0 2px 4px rgba(243, 115, 33, 0.2);
}

/* Combined Category Info Layout */
.category-info-combined {
    transition: var(--transition-fast);
}

.category-main-row {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.category-image-wrapper {
    flex-shrink: 0;
}

.category-image-inline {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 0.5rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);
    border: 2px solid rgba(243, 115, 33, 0.1);
}

.category-image-inline:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
    border-color: rgba(243, 115, 33, 0.3);
}

.no-image-placeholder-inline {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    font-size: 1.25rem;
    border: 2px solid rgba(243, 115, 33, 0.1);
    transition: var(--transition-fast);
}

.no-image-placeholder-inline:hover {
    background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-300) 100%);
    color: var(--gray-500);
    border-color: rgba(243, 115, 33, 0.2);
}

.category-content {
    flex: 1;
    min-width: 0; /* Cho phép text truncate nếu cần */
}

/* Legacy styles - kept for compatibility */

.category-name-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.category-level {
    color: var(--gray-400);
    font-size: 0.75rem;
    font-family: monospace;
}

.category-type-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.category-type-icon.parent {
    background: var(--warning-gradient);
    color: white;
}

.category-type-icon.child {
    background: var(--info-gradient);
    color: white;
}

.category-name {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 0.875rem;
}

.children-count {
    background: var(--info);
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.625rem;
    font-weight: 600;
}

.category-description {
    color: var(--gray-600);
    font-size: 0.75rem;
    line-height: 1.4;
    font-style: italic;
}

/* Status Badges - Consistent with products.php */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.5rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.status-badge.active {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.inactive {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.featured {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    color: #ffffff;
    border: 1px solid #ff8f00;
}

.status-badge.popular-search {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1565c0;
    border: 1px solid #bbdefb;
    position: relative;
}

.status-badge.popular-search .order-badge {
    background: #1565c0;
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 8px;
    font-size: 0.625rem;
    font-weight: 700;
    margin-left: 0.25rem;
    line-height: 1;
}

.status-badge i {
    font-size: 0.75rem;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Column Styling */
.status-column,
.homepage-column {
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
}

/* Modern Action Buttons - Consistent with products.php */
.table-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
    color: var(--gray-600);
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 0.8rem;
}

.action-btn:hover {
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.edit-btn:hover {
    background: var(--info);
    color: white;
    border-color: var(--info);
}

.add-btn:hover {
    background: var(--success);
    color: white;
    border-color: var(--success);
}

.delete-btn:hover {
    background: var(--danger);
    color: white;
    border-color: var(--danger);
}

/* Responsive Design */
@media (max-width: 768px) {
    .categories-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .table-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        width: 100%;
    }

    .search-box {
        width: 100%;
    }

    .modern-form-actions {
        flex-direction: column;
    }

    .table-actions {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }

    .category-name-row {
        flex-direction: column;
        align-items: flex-start;
    }

    /* Ẩn cột ID trên mobile để tiết kiệm không gian */
    .id-column {
        display: none !important;
    }

    .toggle-control {
        display: none;
    }

    /* Giảm thụt lề trên mobile */
    .category-info-combined {
        padding-left: 0 !important;
    }

    .category-main-row {
        gap: 0.5rem;
    }

    .category-image-inline,
    .no-image-placeholder-inline {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Animation for container-fluid */
.container-fluid {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Search functionality */
.category-row.hidden {
    display: none;
}

.no-results {
    text-align: center;
    padding: 2rem;
    color: var(--gray-500);
    font-style: italic;
}
</style>

<?php
// Include footer
include_once 'partials/footer.php';
?>