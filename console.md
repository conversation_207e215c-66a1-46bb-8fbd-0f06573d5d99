(index):64 cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation
(anonymous) @ (index):64
(anonymous) @ (index):64
search-z-index-helper.js:159 Search Z-Index Helper initialized
search-button-debug.js:9 🔍 Search Button Debug: Starting analysis...
search-button-debug.js:25 ✅ Search button found: button#search-submit-btn.search-submit-btn
search-button-debug.js:41 📊 Initial State Analysis
search-button-debug.js:45 Button dimensions: {width: 113.140625, height: 32, left: 1046.859375, top: 587}
search-button-debug.js:54 Button computed styles: {width: '113.141px', minWidth: '90px', maxWidth: 'none', padding: '0px 12px', margin: '0px', …}
search-button-debug.js:76 Button elements: {btnContent: true, btnText: true, btnSpinner: true, btnSuccess: true, btnSearchIcon: true}
search-button-debug.js:87 Button content: {dimensions: {…}, styles: {…}}
search-button-debug.js:103 Button text: {content: 'Tìm kiếm', dimensions: {…}, styles: {…}}
search-button-debug.js:121 👀 Monitoring Button Changes
search-button-debug.js:208 🎮 Adding Test Controls
search-button-debug.js:324 📡 Monitoring AJAX Events
search-button-debug.js:347 ⚠️ AJAX Filter not found, will check periodically
monitorAjaxEvents @ search-button-debug.js:347
startDebug @ search-button-debug.js:37
(anonymous) @ search-button-debug.js:15
(anonymous) @ search-button-debug.js:378
search-button-debug.js:376 🔍 Search Button Debug: Ready! Use SearchButtonDebug.analyzeButton() or SearchButtonDebug.measureText()
simple-notification-modal.js:8 Simple Notification Modal Enhancement loaded
search-overflow-solution1.js:50 Search Overflow Solution 1: Initialized successfully
products.php:5823 ✅ Giải pháp 1: Overflow Visible đã được tích hợp thành công!
products.php:6067 ✅ Smooth dropdown animations initialized successfully!
products.php:6372 Initializing filter micro-interactions...
products.php:6569 Filter micro-interactions initialized successfully!
cart-realtime.js:16 Cart realtime: Initializing...
recently-viewed-trending.js:7 Recently viewed trending JS loaded
recently-viewed-trending.js:13 Recently viewed section found: false
recently-viewed-trending.js:14 Trending section found: true
recently-viewed-trending.js:21 Trending section styles: CSSStyleDeclaration {0: 'accent-color', 1: 'align-content', 2: 'align-items', 3: 'align-self', 4: 'alignment-baseline', 5: 'anchor-name', 6: 'anchor-scope', 7: 'animation-composition', 8: 'animation-delay', 9: 'animation-direction', 10: 'animation-duration', 11: 'animation-fill-mode', 12: 'animation-iteration-count', 13: 'animation-name', 14: 'animation-play-state', 15: 'animation-range-end', 16: 'animation-range-start', 17: 'animation-timeline', 18: 'animation-timing-function', 19: 'app-region', 20: 'appearance', 21: 'backdrop-filter', 22: 'backface-visibility', 23: 'background-attachment', 24: 'background-blend-mode', 25: 'background-clip', 26: 'background-color', 27: 'background-image', 28: 'background-origin', 29: 'background-position', 30: 'background-repeat', 31: 'background-size', 32: 'baseline-shift', 33: 'baseline-source', 34: 'block-size', 35: 'border-block-end-color', 36: 'border-block-end-style', 37: 'border-block-end-width', 38: 'border-block-start-color', 39: 'border-block-start-style', 40: 'border-block-start-width', 41: 'border-bottom-color', 42: 'border-bottom-left-radius', 43: 'border-bottom-right-radius', 44: 'border-bottom-style', 45: 'border-bottom-width', 46: 'border-collapse', 47: 'border-end-end-radius', 48: 'border-end-start-radius', 49: 'border-image-outset', 50: 'border-image-repeat', 51: 'border-image-slice', 52: 'border-image-source', 53: 'border-image-width', 54: 'border-inline-end-color', 55: 'border-inline-end-style', 56: 'border-inline-end-width', 57: 'border-inline-start-color', 58: 'border-inline-start-style', 59: 'border-inline-start-width', 60: 'border-left-color', 61: 'border-left-style', 62: 'border-left-width', 63: 'border-right-color', 64: 'border-right-style', 65: 'border-right-width', 66: 'border-start-end-radius', 67: 'border-start-start-radius', 68: 'border-top-color', 69: 'border-top-left-radius', 70: 'border-top-right-radius', 71: 'border-top-style', 72: 'border-top-width', 73: 'bottom', 74: 'box-decoration-break', 75: 'box-shadow', 76: 'box-sizing', 77: 'break-after', 78: 'break-before', 79: 'break-inside', 80: 'buffered-rendering', 81: 'caption-side', 82: 'caret-color', 83: 'clear', 84: 'clip', 85: 'clip-path', 86: 'clip-rule', 87: 'color', 88: 'color-interpolation', 89: 'color-interpolation-filters', 90: 'color-rendering', 91: 'column-count', 92: 'column-gap', 93: 'column-rule-color', 94: 'column-rule-style', 95: 'column-rule-width', 96: 'column-span', 97: 'column-width', 98: 'contain-intrinsic-block-size', 99: 'contain-intrinsic-height', …}
products.php:6577 Initializing AJAX Filter...
ajax-filter.js:8 🚀 AJAX Filter: Constructor called
ajax-filter.js:12 ✅ AJAX Filter: Properties initialized
ajax-filter.js:17 AJAX Filter: Init called
ajax-filter.js:24 AJAX Filter: bindEvents called
ajax-filter.js:28 AJAX Filter: ajaxFilterActive flag set
ajax-filter.js:132 AJAX Filter: Overriding apply filters button
ajax-filter.js:56 🔍 AJAX Filter: bindMainSearchEvents called
ajax-filter.js:63 🔍 AJAX Filter: Elements found: {mainSearchForm: true, mainSearchInput: true, searchSubmitBtn: true}
ajax-filter.js:74 ✅ AJAX Filter: Found main search form, binding events
ajax-filter.js:122 AJAX Filter: Main search events bound successfully
ajax-filter.js:2118 AJAX Filter: Binding sort and pagination events
ajax-filter.js:2127 AJAX Filter: Sort select bound
ajax-filter.js:2137 AJAX Filter: Items per page select bound
ajax-filter.js:41 AJAX Filter: bindEvents completed
ajax-filter.js:20 AJAX Filter: Init completed
products.php:6582 AJAX Filter initialized successfully
center-notifications.js:401 Center notifications system initialized
products.php:6905 Đã xóa lớp phủ: div.mobile-menu-overlay
products.php:6905 Đã xóa lớp phủ: div
mobile-menu-toggle-fix.js:7 Mobile Menu Toggle Fix loaded
mobile-menu-toggle-fix.js:16 Mobile menu toggle: button.mobile-header-menu-toggle
mobile-menu-toggle-fix.js:17 Mobile menu: div#mobile-menu.mobile-menu
mobile-menu-toggle-fix.js:18 Mobile menu overlay: null
mobile-menu-toggle-fix.js:19 Contact buttons container: div.contact-buttons-container
mobile-menu-toggle-fix.js:20 Scroll top container: div.scroll-top-container
mobile-dropdown-fix-v4.js:9 Mobile Dropdown Fix V4 loaded
mobile-bottom-nav.js:7 Mobile Bottom Nav JS loaded
mobile-bottom-nav.js:13 BASE_URL: http://localhost/noithatbangvu
mobile-bottom-nav.js:14 Mobile nav items: 5
mobile-bottom-nav.js:18 Item 0 href: http://localhost/noithatbangvu
mobile-bottom-nav.js:18 Item 1 href: http://localhost/noithatbangvu/products.php
mobile-bottom-nav.js:18 Item 2 href: http://localhost/noithatbangvu/search.php
mobile-bottom-nav.js:18 Item 3 href: http://localhost/noithatbangvu/cart.php
mobile-bottom-nav.js:18 Item 4 href: http://localhost/noithatbangvu/account/profile.php
mobile-bottom-nav.js:73 Current path: /noithatbangvu/products.php
mobile-bottom-nav.js:127 Active items: 1
mobile-cart-animation-fix.js:7 Mobile cart animation fix loaded
mobile-cart-animation-fix.js:179 New cart animation implementation applied
cart-sync.js:13 Cart sync initialized
cart-sync.js:210 Registering add to cart events for 0 buttons
mobile-cart-badge-fix.js:7 Mobile Cart Badge Fix loaded
mobile-cart-badge-fix.js:23 Registering mobile cart events for 0 buttons
mobile-cart-badge-fix.js:32 Existing cart handlers detected, skipping direct event registration
mobile-cart-badge-fix.js:205 Existing cart handlers detected, skipping initialization
mini-cart-update.js:7 Mini Cart Update JS loaded
user-actions.js:7 User Actions JS loaded
tablet-mega-menu.js:7 Tablet mega menu script loaded
tablet-mega-menu.js:25 Tablet status check: {width: 1440, height: 844, isTabletSize: true, isTouchDevice: true, result: true}
tablet-mega-menu.js:40 Tablet mega menu initialized for touch device
tablet-mega-menu.js:80 Tablet mega menu created successfully
tablet-mega-menu.js:739 Tablet mega menu setup complete with auto-scroll features
mobile-filter-modal.js:101 ✅ Modal created successfully: {overlay: div.filter-modal-overlay, modal: div.filter-modal, overlayInDOM: true, modalInDOM: true}
cart-realtime.js:696 Initializing cart badges from localStorage with count: 8
cart-realtime.js:716 updateAllCartBadgesImmediate: Updating all cart badges with count: 8
cart-realtime.js:805 Cart badges updated immediately: {count: 8, cartBadges: 1, hasMobileBadge: true, hasMobileNavBadge: true, hasMobileCartBadge: true}
center-notifications.js:12 convertFlashMessageToCenterNotification CALLED
center-notifications.js:26 Checking window.flashMessage: undefined
center-notifications.js:48 Flash message container (new): null
center-notifications.js:53 Flash message container (old): null
ajax-filter.js:2231 Initializing AJAX Filter...
ajax-filter.js:8 🚀 AJAX Filter: Constructor called
ajax-filter.js:12 ✅ AJAX Filter: Properties initialized
ajax-filter.js:17 AJAX Filter: Init called
ajax-filter.js:24 AJAX Filter: bindEvents called
ajax-filter.js:28 AJAX Filter: ajaxFilterActive flag set
ajax-filter.js:132 AJAX Filter: Overriding apply filters button
ajax-filter.js:56 🔍 AJAX Filter: bindMainSearchEvents called
ajax-filter.js:63 🔍 AJAX Filter: Elements found: {mainSearchForm: true, mainSearchInput: true, searchSubmitBtn: true}
ajax-filter.js:74 ✅ AJAX Filter: Found main search form, binding events
ajax-filter.js:122 AJAX Filter: Main search events bound successfully
ajax-filter.js:2118 AJAX Filter: Binding sort and pagination events
ajax-filter.js:2127 AJAX Filter: Sort select bound
ajax-filter.js:2137 AJAX Filter: Items per page select bound
ajax-filter.js:41 AJAX Filter: bindEvents completed
ajax-filter.js:20 AJAX Filter: Init completed
ajax-filter.js:2233 AJAX Filter initialized successfully
cart-realtime.js:25 Cart count initialized: 8
cart-sync.js:74 Syncing cart count: {serverCount: 8, localCount: 8, lastKnownCount: -1}
cart-sync.js:111 Updating all cart badges: {count: 8, cartBadges: 1, hasMobileBadge: true, hasMobileNavBadge: true}
mobile-cart-badge-fix.js:153 Updating mobile cart badge: {count: 8, hasMobileNavBadge: true, hasMobileNavItem: true}
mobile-dropdown-fix-v4.js:267 Mobile dropdown toggles: 6
mobile-dropdown-fix-v4.js:283 Mobile back buttons: 6
search-button-debug.js:352 ✅ AJAX Filter found after delay
ajax-filter.js:2237 AJAX Filter: Final override...
ajax-filter.js:132 AJAX Filter: Overriding apply filters button
ajax-filter.js:2243 🔧 AJAX Filter: Re-defining removeKeywordFilter...
ajax-filter.js:2296 🔧 AJAX Filter: Overriding onclick handler for keyword remove button...
ajax-filter.js:2312 ❌ Keyword remove button not found for override
(anonymous) @ ajax-filter.js:2312
setTimeout
(anonymous) @ ajax-filter.js:2236
setTimeout
(anonymous) @ ajax-filter.js:2230
ajax-filter.js:2315 ✅ removeKeywordFilter re-defined and event delegation set up successfully
mobile-dropdown-fix-v4.js:115 Opening submenu, height: 305
cart-realtime.js:827 Updating cart count: {count: 8, cartBadges: 1, hasMobileBadge: true, hasMobileNavBadge: true}
cart-realtime.js:924 Cart count updated successfully: 8
mini-cart-update.js:28 Updating mini cart: {cartCount: 8, cartItems: Array(3), cartTotal: '81.000.000 đ'}
cart-realtime.js:827 Updating cart count: {count: 8, cartBadges: 1, hasMobileBadge: true, hasMobileNavBadge: true}
cart-realtime.js:924 Cart count updated successfully: 8
tablet-mega-menu.js:25 Tablet status check: {width: 1440, height: 844, isTabletSize: true, isTouchDevice: true, result: true}
products.php:5676 Uncaught ReferenceError: currentRequestId is not defined
    at fetchRealSuggestions (products.php:5676:39)
    at products.php:5591:37
fetchRealSuggestions @ products.php:5676
(anonymous) @ products.php:5591
setTimeout
handleSearch @ products.php:5588
(anonymous) @ products.php:5609
ajax-filter.js:94 AJAX Filter: Search submit button clicked
ajax-filter.js:300 🔍 AJAX Filter: handleMainSearchSubmit called
ajax-filter.js:589 🔍 AJAX Filter: Hiding search suggestions
ajax-filter.js:595 ✅ Search suggestions hidden
ajax-filter.js:319 AJAX Filter: Search keyword: tủ
ajax-filter.js:611 🔍 AJAX Filter: Showing search button loading state
ajax-filter.js:192 🔍 AJAX Filter: collectFilterData called, ignoreUrlKeyword: true
ajax-filter.js:199 ✅ Using keyword from main search input: tủ
ajax-filter.js:216 🔍 AJAX Filter: Final keyword in data: tủ
ajax-filter.js:339 AJAX Filter: Loading products with search data (preserving current filters): {keyword: 'tủ', sort: 'newest', items_per_page: 12}
ajax-filter.js:846 AJAX Filter: loadProducts called {filterData: {…}, page: 1, updateHistory: true}
ajax-filter.js:975 AJAX Filter: Showing sophisticated loading state
ajax-filter.js:870 AJAX Filter: Sending request to api/filter-products.php with data {keyword: 'tủ', sort: 'newest', items_per_page: 12, page: 1}
ajax-filter.js:570 AJAX Filter: Syncing search inputs with keyword: tủ
ajax-filter.js:585 AJAX Filter: Search inputs synced
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: disabled New value: 
search-button-debug.js:127 🔄 Attribute changed: class New value: search-submit-btn loading
search-button-debug.js:173 🔍 State Change Analysis (class change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn loading', disabled: true, dimensions: {…}, text: 'Tìm kiếm', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:127 🔄 Attribute changed: class New value: search-submit-btn loading
search-button-debug.js:173 🔍 State Change Analysis (class change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn loading', disabled: true, dimensions: {…}, text: 'Tìm kiếm', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:134 🔄 Child nodes changed
search-button-debug.js:173 🔍 State Change Analysis (child change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn loading', disabled: true, dimensions: {…}, text: 'Tìm kiếm', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:127 🔄 Attribute changed: style New value: null
ajax-filter.js:881 AJAX Filter: Response received 200 true
search-button-debug.js:158 ⚠️ WIDTH CHANGE DETECTED! {from: 113.140625, to: 134.39794921875, difference: 21.25732421875, timestamp: '2025-07-28T17:11:08.990Z'}
(anonymous) @ search-button-debug.js:158
setInterval
monitorButtonChanges @ search-button-debug.js:155
startDebug @ search-button-debug.js:31
(anonymous) @ search-button-debug.js:15
(anonymous) @ search-button-debug.js:378
search-button-debug.js:173 🔍 State Change Analysis (width change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn loading', disabled: true, dimensions: {…}, text: 'Tìm kiếm', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:134 🔄 Child nodes changed
search-button-debug.js:173 🔍 State Change Analysis (child change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn loading', disabled: true, dimensions: {…}, text: 'Đang tìm kiếm...', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
products.php:6905 Đã xóa lớp phủ: <div class=​"ajax-loading-overlay" style=​"position:​ absolute;​ inset:​ 0px;​ background:​ rgba(255, 255, 255, 0.95)​;​ backdrop-filter:​ blur(1px)​;​ display:​ flex;​ align-items:​ center;​ justify-content:​ center;​ z-index:​ 10;​ opacity:​ 1;​ transition:​ opacity 0.3s;​">​…​</div>​
ajax-filter.js:890 AJAX Filter: API response {success: true, data: {…}}
ajax-filter.js:897 AJAX Filter: Processing completed in 1313ms, waiting additional 0ms
search-button-debug.js:158 ⚠️ WIDTH CHANGE DETECTED! {from: 134.39794921875, to: 161.79193115234375, difference: 27.39398193359375, timestamp: '2025-07-28T17:11:09.059Z'}
(anonymous) @ search-button-debug.js:158
setInterval
monitorButtonChanges @ search-button-debug.js:155
startDebug @ search-button-debug.js:31
(anonymous) @ search-button-debug.js:15
(anonymous) @ search-button-debug.js:378
search-button-debug.js:173 🔍 State Change Analysis (width change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn loading', disabled: true, dimensions: {…}, text: 'Đang tìm kiếm...', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
ajax-filter.js:1682 AJAX Filter: updateFilterResultsHeader called {products: Array(12), pagination: {…}, filters: {…}, filter_results_header_html: '    <div class="filter-results-header mb-6 bg-grad…            </div>\n        </div>\n    </div>\n    '}
ajax-filter.js:1703 AJAX Filter: Using HTML from API for filter results header
ajax-filter.js:1763 AJAX Filter: Inserting new filter results header
ajax-filter.js:1721 AJAX Filter: Filter results header updated successfully
ajax-filter.js:1470 AJAX Filter: updateProductsGrid called {products: Array(12), pagination: {…}, filters: {…}, filter_results_header_html: '    <div class="filter-results-header mb-6 bg-grad…            </div>\n        </div>\n    </div>\n    '}
ajax-filter.js:1478 AJAX Filter: productsGrid element found <div class=​"products-grid grid grid-cols-1 gap-6" id=​"productsGrid">​…​</div>​grid
ajax-filter.js:1483 AJAX Filter: Rendering 12 products
ajax-filter.js:1508 AJAX Filter: Fading out old products
ajax-filter.js:2168 AJAX Filter: updateProductsStats called {current_page: 1, total_pages: 2, total_products: 18, items_per_page: 12, has_next: true, …}
ajax-filter.js:2189 AJAX Filter: Products stats calculation {currentPage: 1, itemsPerPage: 12, totalProducts: 18, startItem: 1, endItem: 12, …}
ajax-filter.js:2202 AJAX Filter: Products stats updated successfully
ajax-filter.js:1809 AJAX Filter: updateFilterBadge called {keyword: 'tủ', categories: Array(0), price_min: null, price_max: null, promotions: Array(0), …}
ajax-filter.js:1835 AJAX Filter: Active filters count: 1
ajax-filter.js:1911 AJAX Filter: Filter badge updated successfully
ajax-filter.js:1915 AJAX Filter: Syncing sidebar UI with current filter state
ajax-filter.js:192 🔍 AJAX Filter: collectFilterData called, ignoreUrlKeyword: false
ajax-filter.js:199 ✅ Using keyword from main search input: tủ
ajax-filter.js:216 🔍 AJAX Filter: Final keyword in data: tủ
ajax-filter.js:570 AJAX Filter: Syncing search inputs with keyword: tủ
ajax-filter.js:585 AJAX Filter: Search inputs synced
ajax-filter.js:1939 AJAX Filter: Syncing price preset buttons {priceMin: undefined, priceMax: undefined}
ajax-filter.js:1971 AJAX Filter: Price preset buttons synced
ajax-filter.js:1975 AJAX Filter: Syncing category checkboxes []
ajax-filter.js:1985 AJAX Filter: Category checkboxes synced
ajax-filter.js:1989 AJAX Filter: Syncing promotion checkboxes []
ajax-filter.js:1999 AJAX Filter: Promotion checkboxes synced
ajax-filter.js:2003 AJAX Filter: Syncing price inputs {priceMin: undefined, priceMax: undefined}
ajax-filter.js:2017 AJAX Filter: Price inputs synced
ajax-filter.js:1935 AJAX Filter: Sidebar UI sync completed
ajax-filter.js:1107 AJAX Filter: Hiding sophisticated loading state
ajax-filter.js:681 🔍 AJAX Filter: Hiding search button loading state
ajax-filter.js:707 🔍 AJAX Filter: Showing search button success state
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:134 🔄 Child nodes changed
search-button-debug.js:173 🔍 State Change Analysis (child change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn success', disabled: true, dimensions: {…}, text: 'Đang tìm kiếm...', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:127 🔄 Attribute changed: class New value: search-submit-btn success
search-button-debug.js:173 🔍 State Change Analysis (class change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn success', disabled: true, dimensions: {…}, text: 'Đang tìm kiếm...', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:127 🔄 Attribute changed: class New value: search-submit-btn success
search-button-debug.js:173 🔍 State Change Analysis (class change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn success', disabled: true, dimensions: {…}, text: 'Đang tìm kiếm...', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:158 ⚠️ WIDTH CHANGE DETECTED! {from: 161.79193115234375, to: 161.19476318359375, difference: -0.59716796875, timestamp: '2025-07-28T17:11:09.315Z'}
(anonymous) @ search-button-debug.js:158
setInterval
monitorButtonChanges @ search-button-debug.js:155
startDebug @ search-button-debug.js:31
(anonymous) @ search-button-debug.js:15
(anonymous) @ search-button-debug.js:378
search-button-debug.js:173 🔍 State Change Analysis (width change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn success', disabled: true, dimensions: {…}, text: 'Đang tìm kiếm...', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
ajax-filter.js:920 AJAX Filter: UI synced with URL state
ajax-filter.js:1306 AJAX Filter: Auto-scrolling to products section
ajax-filter.js:1347 AJAX Filter: Desktop header calculation: {totalHeight: 179, topBarHeight: 36, finalHeaderHeight: 143, calculation: '179 - 36 = 143'}
ajax-filter.js:1370 AJAX Filter: Final scroll calculation: {isMobile: false, productsOffset: 543, finalHeaderHeight: 143, targetPosition: 400, calculation: '543 - 143 = 400'}
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:134 🔄 Child nodes changed
search-button-debug.js:173 🔍 State Change Analysis (child change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn success', disabled: true, dimensions: {…}, text: 'Tìm kiếm thành công!', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:158 ⚠️ WIDTH CHANGE DETECTED! {from: 161.19476318359375, to: 206.37469482421875, difference: 45.179931640625, timestamp: '2025-07-28T17:11:10.344Z'}
(anonymous) @ search-button-debug.js:158
setInterval
monitorButtonChanges @ search-button-debug.js:155
startDebug @ search-button-debug.js:31
(anonymous) @ search-button-debug.js:15
(anonymous) @ search-button-debug.js:378
search-button-debug.js:173 🔍 State Change Analysis (width change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn success', disabled: true, dimensions: {…}, text: 'Tìm kiếm thành công!', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
ajax-filter.js:1519 AJAX Filter: Products transition completed
ajax-filter.js:751 🔍 AJAX Filter: Resetting search button to original state
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: disabled New value: null
search-button-debug.js:127 🔄 Attribute changed: class New value: search-submit-btn
search-button-debug.js:173 🔍 State Change Analysis (class change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn', disabled: false, dimensions: {…}, text: 'Tìm kiếm thành công!', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:158 ⚠️ WIDTH CHANGE DETECTED! {from: 206.37469482421875, to: 206.99615478515625, difference: 0.6214599609375, timestamp: '2025-07-28T17:11:12.117Z'}
(anonymous) @ search-button-debug.js:158
setInterval
monitorButtonChanges @ search-button-debug.js:155
startDebug @ search-button-debug.js:31
(anonymous) @ search-button-debug.js:15
(anonymous) @ search-button-debug.js:378
search-button-debug.js:173 🔍 State Change Analysis (width change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn', disabled: false, dimensions: {…}, text: 'Tìm kiếm thành công!', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:134 🔄 Child nodes changed
search-button-debug.js:173 🔍 State Change Analysis (child change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn', disabled: false, dimensions: {…}, text: 'Tìm kiếm', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:127 🔄 Attribute changed: style New value: null
search-button-debug.js:158 ⚠️ WIDTH CHANGE DETECTED! {from: 206.99615478515625, to: 115.4033203125, difference: -91.59283447265625, timestamp: '2025-07-28T17:11:13.140Z'}
(anonymous) @ search-button-debug.js:158
setInterval
monitorButtonChanges @ search-button-debug.js:155
startDebug @ search-button-debug.js:31
(anonymous) @ search-button-debug.js:15
(anonymous) @ search-button-debug.js:378
search-button-debug.js:173 🔍 State Change Analysis (width change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn', disabled: false, dimensions: {…}, text: 'Tìm kiếm', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
search-button-debug.js:158 ⚠️ WIDTH CHANGE DETECTED! {from: 115.4033203125, to: 118.78955078125, difference: 3.38623046875, timestamp: '2025-07-28T17:11:13.243Z'}
(anonymous) @ search-button-debug.js:158
setInterval
monitorButtonChanges @ search-button-debug.js:155
startDebug @ search-button-debug.js:31
(anonymous) @ search-button-debug.js:15
(anonymous) @ search-button-debug.js:378
search-button-debug.js:173 🔍 State Change Analysis (width change)
search-button-debug.js:181 Current state: {classes: 'search-submit-btn', disabled: false, dimensions: {…}, text: 'Tìm kiếm', visibleElements: {…}}
search-button-debug.js:198 Parent container: {dimensions: {…}, overflow: 'visible'}
