<?php
// Bắt đầu session và include các file cần thiết
require_once '../includes/init.php';

// Xử lý đổi mật khẩu trước khi xuất bất kỳ HTML nào
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Kiểm tra CSRF token
    if (!check_csrf_token($_POST['csrf_token'])) {
        set_flash_message('error', 'Phiên làm việc đã hết hạn. Vui lòng thử lại.');
        redirect(BASE_URL . '/account/change-password.php');
    }

    // Lấy dữ liệu từ form
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Kiểm tra dữ liệu
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        set_flash_message('error', '<PERSON><PERSON> lòng nhập đ<PERSON>y đủ thông tin.');
        redirect(BASE_URL . '/account/change-password.php');
    }

    if (strlen($new_password) < 6) {
        set_flash_message('error', 'Mật khẩu mới phải có ít nhất 6 ký tự.');
        redirect(BASE_URL . '/account/change-password.php');
    }

    if ($new_password !== $confirm_password) {
        set_flash_message('error', 'Mật khẩu xác nhận không khớp.');
        redirect(BASE_URL . '/account/change-password.php');
    }

    // Đổi mật khẩu
    $result = change_password($_SESSION['user_id'], $current_password, $new_password);

    if ($result['success']) {
        set_flash_message('success', $result['message']);
    } else {
        set_flash_message('error', $result['message']);
    }

    redirect(BASE_URL . '/account/change-password.php');
}

// Kiểm tra đăng nhập
if (!is_logged_in()) {
    // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
    $_SESSION['redirect_url'] = current_url();

    set_flash_message('error', 'Vui lòng đăng nhập để truy cập trang này.');
    redirect(BASE_URL . '/login.php');
}

// Lấy thông tin người dùng
$user = get_user_by_id($_SESSION['user_id']);

// Thiết lập tiêu đề trang
$page_title = 'Đổi mật khẩu';
$page_description = 'Đổi mật khẩu tài khoản của bạn tại Nội Thất Băng Vũ';

// Include header
include_once '../partials/header.php';
?>

<!-- Breadcrumb - Thiết kế hiện đại -->
<div class="modern-breadcrumb">
    <div class="container mx-auto px-4">
        <div class="breadcrumb-wrapper">
            <div class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>" class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-home"></i>
                    </span>
                    <span>Trang chủ</span>
                </a>
            </div>
            <div class="breadcrumb-divider">
                <i class="fas fa-chevron-right"></i>
            </div>
            <div class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>/account/profile.php" class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-user-circle"></i>
                    </span>
                    <span>Tài khoản của tôi</span>
                </a>
            </div>
            <div class="breadcrumb-divider">
                <i class="fas fa-chevron-right"></i>
            </div>
            <div class="breadcrumb-item active">
                <span class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-key"></i>
                    </span>
                    <span>Đổi mật khẩu</span>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Link CSS cho breadcrumb hiện đại -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-breadcrumb.css">

<!-- Account - Thiết kế hiện đại -->
<div class="py-10 bg-gradient-to-b from-white to-gray-50">
    <div class="container mx-auto px-4">
        <!-- Tiêu đề trang với badge giống trang chủ -->
        <div class="mb-8 text-center md:text-left">
            <div class="section-title-badge inline-flex items-center bg-primary/10 text-primary text-sm font-medium px-4 py-2 rounded-full mb-4">
                <span class="flex w-2 h-2 bg-primary rounded-full mr-2 animate-pulse"></span>
                Bảo mật tài khoản
            </div>
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 relative inline-block">
                <span class="relative z-10">Đổi mật khẩu</span>
                <span class="absolute bottom-0 left-0 w-full h-1 bg-primary/30 rounded-full"></span>
            </h1>
            <p class="text-gray-600 mt-2 max-w-2xl mx-auto md:mx-0">Cập nhật mật khẩu mới để bảo vệ tài khoản của bạn</p>
        </div>

        <div class="flex flex-col md:flex-row md:space-x-6">
            <!-- Sidebar - Thiết kế hiện đại -->
            <div class="md:w-1/4 mb-6 md:mb-0">
                <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-primary/20 transition-all duration-300">
                    <!-- Phần header với avatar và thông tin - Thiết kế mới -->
                    <div class="relative">
                        <!-- Background pattern -->
                        <div class="absolute inset-0 bg-gradient-to-br from-primary to-primary-dark opacity-90"></div>

                        <!-- Pattern overlay -->
                        <div class="absolute inset-0 opacity-10 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xIj48cGF0aCBkPSJNMzYgMzRjMC0yLjIgMS44LTQgNC00czQgMS44IDQgNC0xLjggNC00IDQtNC0xLjgtNC00eiIvPjxwYXRoIGQ9Ik0xNiAxNmMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00em0xNiAwYzIuMiAwIDQgMS44IDQgNHMtMS44IDQtNCA0LTQtMS44LTQtNCAxLjgtNCA0LTR6bTE2IDBjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHpNMTYgMzJjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHptMTYgMGMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00eiIvPjxwYXRoIGQ9Ik0xNiA0OGMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00em0xNiAwYzIuMiAwIDQgMS44IDQgNHMtMS44IDQtNCA0LTQtMS44LTQtNCAxLjgtNCA0LTR6bTE2IDBjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHpNMTYgMTZjMi4yIDAgNCAxLjggNCA0cy0xLjggNC00IDQtNC0xLjgtNC00IDEuOC00IDQtNHptMTYgMGMyLjIgMCA0IDEuOCA0IDRzLTEuOCA0LTQgNC00LTEuOC00LTQgMS44LTQgNC00em0xNiAwYzIuMiAwIDQgMS44IDQgNHMtMS44IDQtNCA0LTQtMS44LTQtNCAxLjgtNCA0LTR6TTAgMGg2MHY2MEgweiIvPjwvZz48L2c+PC9zdmc+')]"></div>
                    </div>

                    <!-- Avatar lớn căn giữa -->
                    <div class="flex flex-col items-center px-6 pt-8 pb-6 relative">
                        <div class="w-40 h-40 rounded-xl bg-white p-1.5 shadow-lg mb-5 transform transition-transform hover:scale-105 duration-300 overflow-hidden relative z-10">
                            <div class="w-full h-full rounded-xl overflow-hidden bg-gradient-to-r from-primary/20 to-primary/10 flex items-center justify-center text-4xl font-bold text-primary">
                                <?php if (!empty($user['avatar'])): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $user['avatar']; ?>" alt="Avatar" class="w-full h-full object-cover">
                                <?php else: ?>
                                    <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Thông tin người dùng -->
                        <div class="text-center w-full">
                            <h2 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $user['full_name']; ?></h2>
                            <p class="text-gray-500 text-sm flex items-center justify-center mb-3">
                                <i class="fas fa-envelope mr-2 text-primary/70"></i>
                                <?php echo $user['email']; ?>
                            </p>

                            <!-- Badge thành viên -->
                            <div class="inline-flex items-center bg-primary/10 text-primary text-xs font-medium px-3 py-1.5 rounded-full">
                                <i class="fas fa-user-check mr-1.5"></i> Thành viên
                            </div>
                        </div>
                    </div>

                    <!-- Đường phân cách -->
                    <div class="w-full h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>

                    <!-- Menu tài khoản -->
                    <div class="p-4">
                        <ul class="space-y-1">
                            <li>
                                <a href="<?php echo BASE_URL; ?>/account/profile.php"
                                   class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-primary transition-all duration-300">
                                    <i class="fas fa-user-circle mr-3 w-5 text-center"></i>
                                    <span>Thông tin tài khoản</span>
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/account/orders.php"
                                   class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-primary transition-all duration-300">
                                    <i class="fas fa-shopping-bag mr-3 w-5 text-center"></i>
                                    <span>Đơn hàng của tôi</span>
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo BASE_URL; ?>/account/change-password.php"
                                   class="flex items-center px-4 py-3 rounded-lg bg-primary/10 text-primary font-medium transition-all duration-300">
                                    <i class="fas fa-key mr-3 w-5 text-center"></i>
                                    <span>Đổi mật khẩu</span>
                                </a>
                            </li>
                            <li class="pt-2 mt-2 border-t border-gray-100">
                                <a href="<?php echo BASE_URL; ?>/logout.php"
                                   class="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-red-50 hover:text-red-600 transition-all duration-300">
                                    <i class="fas fa-sign-out-alt mr-3 w-5 text-center"></i>
                                    <span>Đăng xuất</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="md:w-3/4">
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 hover:border-primary/20 transition-all duration-300 overflow-hidden">
                    <!-- Header với gradient -->
                    <div class="bg-gradient-to-r from-primary/5 to-primary/10 px-6 py-5 border-b border-gray-100">
                        <div class="flex items-center">
                            <span class="w-12 h-12 rounded-lg bg-white flex items-center justify-center text-primary shadow-sm mr-4">
                                <i class="fas fa-shield-alt text-xl"></i>
                            </span>
                            <div>
                                <h2 class="text-xl font-bold text-gray-800">Đổi mật khẩu</h2>
                                <p class="text-gray-500 text-sm">Cập nhật mật khẩu mới để bảo vệ tài khoản của bạn</p>
                            </div>
                        </div>
                    </div>

                    <!-- Form content với padding -->
                    <div class="p-6">
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-r-lg">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700">
                                        Để bảo mật tài khoản, vui lòng chọn mật khẩu mạnh và không sử dụng lại mật khẩu từ các trang web khác.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <form action="<?php echo BASE_URL; ?>/account/change-password.php" method="POST" class="validate-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                            <div class="space-y-6">
                                <!-- Mật khẩu hiện tại -->
                                <div>
                                    <label for="current_password" class="flex items-center text-gray-700 font-medium mb-2">
                                        Mật khẩu hiện tại
                                        <span class="text-red-500 ml-1">*</span>
                                        <span class="ml-2 text-xs text-gray-400">(bắt buộc)</span>
                                    </label>
                                    <div class="relative group">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-lock text-gray-400 group-focus-within:text-primary transition-colors duration-200"></i>
                                        </div>
                                        <input type="password" name="current_password" id="current_password"
                                               class="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:border-primary focus:ring focus:ring-primary/30 transition-all duration-300"
                                               required>
                                        <button type="button" class="toggle-password absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <div class="absolute bottom-0 left-0 h-0.5 bg-primary scale-x-0 group-focus-within:scale-x-100 transition-transform duration-300 origin-left"></div>
                                    </div>
                                </div>

                                <!-- Mật khẩu mới -->
                                <div>
                                    <label for="new_password" class="flex items-center text-gray-700 font-medium mb-2">
                                        Mật khẩu mới
                                        <span class="text-red-500 ml-1">*</span>
                                        <span class="ml-2 text-xs text-gray-400">(bắt buộc)</span>
                                    </label>
                                    <div class="relative group">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-key text-gray-400 group-focus-within:text-primary transition-colors duration-200"></i>
                                        </div>
                                        <input type="password" name="new_password" id="new_password"
                                               class="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:border-primary focus:ring focus:ring-primary/30 transition-all duration-300"
                                               required>
                                        <button type="button" class="toggle-password absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <div class="absolute bottom-0 left-0 h-0.5 bg-primary scale-x-0 group-focus-within:scale-x-100 transition-transform duration-300 origin-left"></div>
                                    </div>
                                    <p class="text-gray-600 text-xs mt-2 flex items-center">
                                        <i class="fas fa-info-circle mr-1 text-primary/70"></i>
                                        Mật khẩu phải có ít nhất 6 ký tự.
                                    </p>

                                    <!-- Chỉ số mạnh yếu mật khẩu -->
                                    <div class="mt-3">
                                        <div class="flex items-center justify-between mb-1">
                                            <span class="text-xs font-medium text-gray-500">Độ mạnh mật khẩu:</span>
                                            <span class="text-xs font-medium password-strength-text">Chưa nhập</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                                            <div class="password-strength-bar h-1.5 rounded-full" style="width: 0%"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Xác nhận mật khẩu mới -->
                                <div>
                                    <label for="confirm_password" class="flex items-center text-gray-700 font-medium mb-2">
                                        Xác nhận mật khẩu mới
                                        <span class="text-red-500 ml-1">*</span>
                                        <span class="ml-2 text-xs text-gray-400">(bắt buộc)</span>
                                    </label>
                                    <div class="relative group">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-check-circle text-gray-400 group-focus-within:text-primary transition-colors duration-200"></i>
                                        </div>
                                        <input type="password" name="confirm_password" id="confirm_password"
                                               class="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:border-primary focus:ring focus:ring-primary/30 transition-all duration-300"
                                               required>
                                        <button type="button" class="toggle-password absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <div class="absolute bottom-0 left-0 h-0.5 bg-primary scale-x-0 group-focus-within:scale-x-100 transition-transform duration-300 origin-left"></div>
                                    </div>
                                    <p class="password-match-message hidden text-green-600 text-xs mt-2">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Mật khẩu khớp
                                    </p>
                                    <p class="password-mismatch-message hidden text-red-600 text-xs mt-2">
                                        <i class="fas fa-times-circle mr-1"></i>
                                        Mật khẩu không khớp
                                    </p>
                                </div>
                            </div>

                            <!-- Nút cập nhật - Thiết kế mới -->
                            <div class="mt-8 flex justify-end">
                                <button type="submit"
                                        class="relative overflow-hidden bg-primary hover:bg-primary-dark text-white font-medium py-3 px-8 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-300 inline-flex items-center shadow-md hover:shadow-lg group">
                                    <!-- Hiệu ứng ripple -->
                                    <span class="absolute inset-0 bg-white/20 transform scale-0 group-hover:scale-100 rounded-lg transition-transform duration-500 origin-center"></span>

                                    <i class="fas fa-key mr-2 group-hover:animate-bounce"></i>
                                    <span class="relative z-10">Đổi mật khẩu</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Script cho hiển thị/ẩn mật khẩu và kiểm tra độ mạnh -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // Xử lý hiển thị/ẩn mật khẩu
                    const toggleButtons = document.querySelectorAll('.toggle-password');
                    toggleButtons.forEach(button => {
                        button.addEventListener('click', function() {
                            const input = this.previousElementSibling.previousElementSibling;
                            const icon = this.querySelector('i');

                            if (input.type === 'password') {
                                input.type = 'text';
                                icon.classList.remove('fa-eye');
                                icon.classList.add('fa-eye-slash');
                            } else {
                                input.type = 'password';
                                icon.classList.remove('fa-eye-slash');
                                icon.classList.add('fa-eye');
                            }
                        });
                    });

                    // Kiểm tra độ mạnh mật khẩu
                    const newPasswordInput = document.getElementById('new_password');
                    const strengthBar = document.querySelector('.password-strength-bar');
                    const strengthText = document.querySelector('.password-strength-text');

                    newPasswordInput.addEventListener('input', function() {
                        const password = this.value;
                        let strength = 0;
                        let color = '';
                        let text = '';

                        if (password.length === 0) {
                            strength = 0;
                            color = 'bg-gray-200';
                            text = 'Chưa nhập';
                        } else if (password.length < 6) {
                            strength = 20;
                            color = 'bg-red-500';
                            text = 'Rất yếu';
                        } else {
                            // Tính điểm dựa trên các tiêu chí
                            if (password.length >= 8) strength += 20;
                            if (/[A-Z]/.test(password)) strength += 20;
                            if (/[a-z]/.test(password)) strength += 20;
                            if (/[0-9]/.test(password)) strength += 20;
                            if (/[^A-Za-z0-9]/.test(password)) strength += 20;

                            // Xác định màu và văn bản dựa trên điểm
                            if (strength <= 40) {
                                color = 'bg-red-500';
                                text = 'Yếu';
                            } else if (strength <= 60) {
                                color = 'bg-yellow-500';
                                text = 'Trung bình';
                            } else if (strength <= 80) {
                                color = 'bg-blue-500';
                                text = 'Mạnh';
                            } else {
                                color = 'bg-green-500';
                                text = 'Rất mạnh';
                            }
                        }

                        // Cập nhật thanh độ mạnh
                        strengthBar.style.width = strength + '%';
                        strengthBar.className = 'password-strength-bar h-1.5 rounded-full ' + color;
                        strengthText.textContent = text;
                        strengthText.className = 'text-xs font-medium ' + (color.replace('bg-', 'text-'));
                    });

                    // Kiểm tra mật khẩu khớp
                    const confirmPasswordInput = document.getElementById('confirm_password');
                    const matchMessage = document.querySelector('.password-match-message');
                    const mismatchMessage = document.querySelector('.password-mismatch-message');

                    function checkPasswordMatch() {
                        const newPassword = newPasswordInput.value;
                        const confirmPassword = confirmPasswordInput.value;

                        if (confirmPassword.length === 0) {
                            matchMessage.classList.add('hidden');
                            mismatchMessage.classList.add('hidden');
                        } else if (newPassword === confirmPassword) {
                            matchMessage.classList.remove('hidden');
                            mismatchMessage.classList.add('hidden');
                        } else {
                            matchMessage.classList.add('hidden');
                            mismatchMessage.classList.remove('hidden');
                        }
                    }

                    newPasswordInput.addEventListener('input', checkPasswordMatch);
                    confirmPasswordInput.addEventListener('input', checkPasswordMatch);
                });
            </script>
        </div>
    </div>
</div>

<!-- CSS tùy chỉnh cho trang đổi mật khẩu -->
<style>
    /* Biến CSS cho màu sắc chính */
    :root {
        --primary: #F37321;
        --primary-dark: #E05E00;
        --primary-light: #FF9D5C;
    }

    /* Hiệu ứng hover cho các card */
    .bg-white.rounded-xl {
        transition: all 0.3s ease;
    }

    .bg-white.rounded-xl:hover {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Hiệu ứng cho input khi focus */
    input:focus, textarea:focus, select:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.2);
    }

    /* Hiệu ứng cho nút */
    button[type="submit"] {
        transition: all 0.3s ease;
    }

    button[type="submit"]:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px rgba(243, 115, 33, 0.2);
    }

    button[type="submit"]:active {
        transform: translateY(0);
    }

    /* Hiệu ứng cho nút hiển thị mật khẩu */
    .toggle-password:hover {
        color: var(--primary);
    }

    /* Hiệu ứng cho thanh độ mạnh mật khẩu */
    .password-strength-bar {
        transition: width 0.3s ease, background-color 0.3s ease;
    }

    /* Responsive cho màn hình nhỏ */
    @media (max-width: 768px) {
        .section-title-badge {
            justify-content: center;
            margin-left: auto;
            margin-right: auto;
        }

        .section-title {
            text-align: center;
        }

        .mt-8.flex.justify-end {
            justify-content: center;
        }
    }

    /* Hiệu ứng khi hiển thị thông báo mật khẩu khớp/không khớp */
    .password-match-message, .password-mismatch-message {
        display: flex;
        align-items: center;
        opacity: 0;
        max-height: 0;
        overflow: hidden;
        transition: opacity 0.3s ease, max-height 0.3s ease;
    }

    .password-match-message:not(.hidden), .password-mismatch-message:not(.hidden) {
        opacity: 1;
        max-height: 50px;
    }
</style>

<?php
// Include footer
include_once '../partials/footer.php';
?>
