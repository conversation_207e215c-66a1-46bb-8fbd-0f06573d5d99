/* 
 * Top Bar Color Fix CSS
 * Kh<PERSON><PERSON> phục vấn đề top-bar hiển thị màu sắc khác nhau giữa các trang
 * Gi<PERSON>i pháp nhẹ nhàng không ảnh hưởng đến hiệu ứng
 */

/* <PERSON><PERSON><PERSON> bảo top-bar luôn có màu sắc đúng */
.top-bar {
    background-color: #202834 !important;
    background-image: linear-gradient(to bottom, #2a3441, #202834) !important;
    /* <PERSON><PERSON><PERSON> nguyên các thuộc tính khác để không ảnh hưởng đến hiệu ứng */
}

/* Đ<PERSON><PERSON> bảo không có pseudo-element nào che khuất */
.top-bar::before,
.top-bar::after {
    background: transparent !important;
}

/* Đảm bảo các element con không có background che khuất */
.top-bar-content,
.top-bar-contact,
.top-bar-address {
    background: transparent !important;
}

/* <PERSON><PERSON>m bảo text và icon có màu đúng */
.top-bar a,
.top-bar span {
    color: rgba(255, 255, 255, 0.9) !important;
}

.top-bar a:hover,
.top-bar span:hover {
    color: #ffffff !important;
}

.top-bar i {
    color: #fdba74 !important; /* primary-light */
}

.top-bar a:hover i {
    color: #f97316 !important; /* primary */
}

/* Đảm bảo top-bar-address-link có styling giống top-bar-contact a */
.top-bar-address-link {
    color: rgba(255, 255, 255, 0.9) !important;
}

.top-bar-address-link:hover {
    color: #ffffff !important;
}

.top-bar-address-link i {
    color: #fdba74 !important; /* primary-light */
}

.top-bar-address-link:hover i {
    color: #f97316 !important; /* primary */
}

/* Override CSS từ các file khác có thể ảnh hưởng */
.premium-header .top-bar {
    background-color: #202834 !important;
    background-image: linear-gradient(to bottom, #2a3441, #202834) !important;
}

/* Đảm bảo không bị ảnh hưởng bởi CSS inline */
.top-bar[style*="background"] {
    background-color: #202834 !important;
    background-image: linear-gradient(to bottom, #2a3441, #202834) !important;
}

/* Đảm bảo không bị ảnh hưởng bởi CSS variables */
.top-bar {
    --top-bar-bg: #202834 !important;
    --top-bar-gradient: linear-gradient(to bottom, #2a3441, #202834) !important;
}

/* Đảm bảo hiệu ứng ẩn/hiện vẫn hoạt động bình thường */
.premium-header.scrolled .top-bar {
    /* Giữ nguyên transform để hiệu ứng ẩn vẫn hoạt động */
    background-color: #202834 !important;
    background-image: linear-gradient(to bottom, #2a3441, #202834) !important;
}

/* Đảm bảo khi compact vẫn có màu đúng */
.premium-header.compact .top-bar {
    background-color: #202834 !important;
    background-image: linear-gradient(to bottom, #2a3441, #202834) !important;
}

/* Đảm bảo responsive */
@media (max-width: 768px) {
    .top-bar {
        background-color: #202834 !important;
        background-image: linear-gradient(to bottom, #2a3441, #202834) !important;
    }
}

/* Tablet vừa và nhỏ: Ẩn email, layout trái-phải đơn giản */
@media (max-width: 992px) {
    .top-bar-email {
        display: none !important;
    }

    /* Layout đơn giản: trái số điện thoại, phải địa chỉ */
    .top-bar-content {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .top-bar-contact {
        flex: 0 0 auto !important; /* Không co giãn, kích thước tự nhiên */
    }

    .top-bar-address {
        flex: 1 1 auto !important; /* Chiếm hết không gian còn lại */
        justify-content: flex-end !important;
    }

    .top-bar-address-link {
        /* Không giới hạn chiều rộng, để tự nhiên */
        max-width: none !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        padding: var(--spacing-xs) var(--spacing-sm) !important;
    }

    /* Sửa search container bị tràn trên tablet */
    .search-container {
        width: auto !important;
        flex: 1 1 auto !important;
        max-width: 300px !important;
        min-width: 150px !important;
    }
}

/* Tablet UX: Click/tap để hiển thị dropdown thay vì hover - FORCE OVERRIDE */
@media (min-width: 768px) and (max-width: 1024px) {
    /* FORCE tắt hover effect trên tablet - override tất cả file khác */
    .user-dropdown:hover .user-dropdown-menu,
    .user-dropdown-menu:hover,
    .cart-container:hover .mini-cart,
    .mini-cart:hover {
        opacity: 0 !important;
        visibility: hidden !important;
        transform: scale(0.95) !important;
        pointer-events: none !important;
    }

    /* FORCE hiển thị khi có class active - override tất cả file khác */
    .user-dropdown-menu.active,
    .mini-cart.active {
        opacity: 1 !important;
        visibility: visible !important;
        transform: scale(1) !important;
        pointer-events: auto !important;
        display: block !important;
        z-index: 9999 !important;
    }

    /* FORCE transition mượt mà */
    .user-dropdown-menu,
    .mini-cart {
        transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
    }



    /* Debug: Đảm bảo dropdown có vị trí đúng */
    .user-dropdown-menu.active,
    .mini-cart.active {
        position: absolute !important;
        top: calc(100% + 10px) !important;
        right: 0 !important;
    }
}



/* Đảm bảo khi có class khác */
.top-bar.scrolled,
.top-bar.compact,
.top-bar.hidden {
    background-color: #202834 !important;
    background-image: linear-gradient(to bottom, #2a3441, #202834) !important;
}
