/**
 * Cart Header Update
 * Script để cập nhật thông tin giỏ hàng ở header khi thay đổi số lượng sản phẩm
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Cart header update script loaded');

    // Lắng nghe sự kiện cập nhật giỏ hàng
    document.addEventListener('cart:updated', function(event) {
        console.log('Cart updated event received:', event.detail);
        
        // Cập nhật tổng giá tiền ở mini-cart trong header
        updateMiniCartTotal(event.detail.total);
    });

    // Ghi đè hàm updateCartItemManual để thêm sự kiện cập nhật
    const originalUpdateCartItemManual = window.updateCartItemManual;
    if (typeof originalUpdateCartItemManual === 'function') {
        window.updateCartItemManual = function(productId, button) {
            // G<PERSON><PERSON> hàm gốc
            const result = originalUpdateCartItemManual(productId, button);
            
            // <PERSON>u khi cập nhật, đảm bảo mini-cart được cập nhật
            setTimeout(function() {
                syncMiniCart();
            }, 500);
            
            return result;
        };
    }

    // Hàm cập nhật tổng giá tiền ở mini-cart
    function updateMiniCartTotal(total) {
        const miniCartTotalValue = document.querySelector('.mini-cart-total-value');
        if (miniCartTotalValue && total) {
            miniCartTotalValue.textContent = total;
            
            // Thêm hiệu ứng highlight
            miniCartTotalValue.classList.add('highlight-change');
            setTimeout(() => miniCartTotalValue.classList.remove('highlight-change'), 2000);
        }
    }

    // Đồng bộ hóa mini-cart với giỏ hàng hiện tại
    function syncMiniCart() {
        fetch(`${BASE_URL}/ajax/get_cart_data.php`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Syncing mini cart with data:', data);
                    
                    // Cập nhật tổng tiền ở mini-cart
                    updateMiniCartTotal(data.total);
                    
                    // Cập nhật số lượng sản phẩm
                    updateCartCount(data.count);
                    
                    // Cập nhật danh sách sản phẩm nếu cần
                    if (typeof window.updateMiniCart === 'function') {
                        window.updateMiniCart(data.count, data.items, data.total);
                    }
                }
            })
            .catch(error => console.error('Error syncing mini cart:', error));
    }

    // Cập nhật số lượng giỏ hàng
    function updateCartCount(count) {
        // Cập nhật badge ở header
        const cartBadges = document.querySelectorAll('.cart-badge');
        cartBadges.forEach(badge => {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'flex' : 'none';
        });

        // Cập nhật badge ở mobile
        const mobileBadges = document.querySelectorAll('.mobile-nav-badge, .mobile-badge');
        mobileBadges.forEach(badge => {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'flex' : 'none';
        });

        // Cập nhật số lượng hiển thị trong mini-cart
        const miniCartCount = document.querySelector('.mini-cart-count');
        if (miniCartCount) {
            miniCartCount.textContent = count + ' sản phẩm';
        }
    }
});
