<?php
/**
 * Notification Helper - Nội Thất Băng Vũ
 * Hệ thống thông báo hiển thị giữa màn hình
 */

/**
 * Thiết lập thông báo để hiển thị ở giữa màn hình
 * 
 * @param string $type Loại thông báo: success, error, warning, info
 * @param string $message Nội dung thông báo
 * @param string $title Tiêu đề thông báo (tùy chọn)
 * @param array $options Các tùy chọn khác (tùy chọn)
 * @return void
 */
function set_center_notification($type, $message, $title = null, $options = []) {
    if (!isset($_SESSION['center_notifications'])) {
        $_SESSION['center_notifications'] = [];
    }
    
    // Xác định tiêu đề mặc định dựa trên loại thông báo
    if ($title === null) {
        switch ($type) {
            case 'success':
                $title = 'Thành công';
                break;
            case 'error':
                $title = 'Lỗi';
                break;
            case 'warning':
                $title = 'Cảnh báo';
                break;
            case 'info':
            default:
                $title = 'Thông tin';
                break;
        }
    }
    
    // Thêm thông báo vào session
    $_SESSION['center_notifications'][] = [
        'type' => $type,
        'title' => $title,
        'message' => $message,
        'options' => $options
    ];
}

/**
 * Thiết lập thông báo thành công
 * 
 * @param string $message Nội dung thông báo
 * @param string $title Tiêu đề thông báo (tùy chọn)
 * @param array $options Các tùy chọn khác (tùy chọn)
 * @return void
 */
function set_success_notification($message, $title = 'Thành công', $options = []) {
    set_center_notification('success', $message, $title, $options);
}

/**
 * Thiết lập thông báo lỗi
 * 
 * @param string $message Nội dung thông báo
 * @param string $title Tiêu đề thông báo (tùy chọn)
 * @param array $options Các tùy chọn khác (tùy chọn)
 * @return void
 */
function set_error_notification($message, $title = 'Lỗi', $options = []) {
    set_center_notification('error', $message, $title, $options);
}

/**
 * Thiết lập thông báo cảnh báo
 * 
 * @param string $message Nội dung thông báo
 * @param string $title Tiêu đề thông báo (tùy chọn)
 * @param array $options Các tùy chọn khác (tùy chọn)
 * @return void
 */
function set_warning_notification($message, $title = 'Cảnh báo', $options = []) {
    set_center_notification('warning', $message, $title, $options);
}

/**
 * Thiết lập thông báo thông tin
 * 
 * @param string $message Nội dung thông báo
 * @param string $title Tiêu đề thông báo (tùy chọn)
 * @param array $options Các tùy chọn khác (tùy chọn)
 * @return void
 */
function set_info_notification($message, $title = 'Thông tin', $options = []) {
    set_center_notification('info', $message, $title, $options);
}

/**
 * Chuyển đổi từ flash message sang center notification
 * Hàm này giúp tương thích với hệ thống flash message hiện tại
 * 
 * @return void
 */
function convert_flash_to_center_notifications() {
    if (isset($_SESSION['flash_messages']) && !empty($_SESSION['flash_messages'])) {
        foreach ($_SESSION['flash_messages'] as $type => $message) {
            // Chuyển đổi loại thông báo
            $notificationType = 'info';
            switch ($type) {
                case 'success':
                    $notificationType = 'success';
                    break;
                case 'error':
                    $notificationType = 'error';
                    break;
                case 'warning':
                    $notificationType = 'warning';
                    break;
            }
            
            // Thiết lập center notification
            set_center_notification($notificationType, $message);
        }
        
        // Xóa flash messages sau khi chuyển đổi
        unset($_SESSION['flash_messages']);
    }
}

/**
 * Hiển thị tất cả center notifications
 * Hàm này sẽ tạo ra JavaScript để hiển thị các thông báo
 * 
 * @return string JavaScript code để hiển thị thông báo
 */
function display_center_notifications() {
    if (!isset($_SESSION['center_notifications']) || empty($_SESSION['center_notifications'])) {
        return '';
    }
    
    $output = '<script>';
    $output .= 'document.addEventListener("DOMContentLoaded", function() {';
    
    foreach ($_SESSION['center_notifications'] as $notification) {
        $type = json_encode($notification['type']);
        $title = json_encode($notification['title']);
        $message = json_encode($notification['message']);
        $options = json_encode($notification['options']);
        
        $output .= "showNotification({
            type: {$type},
            title: {$title},
            message: {$message},
            ...{$options}
        });";
    }
    
    $output .= '});';
    $output .= '</script>';
    
    // Xóa notifications sau khi hiển thị
    unset($_SESSION['center_notifications']);
    
    return $output;
}

/**
 * Chuyển đổi từ flash message sang center notification và hiển thị
 * Hàm này kết hợp cả hai hàm trên để dễ sử dụng
 * 
 * @return string JavaScript code để hiển thị thông báo
 */
function process_and_display_notifications() {
    // Chuyển đổi flash messages sang center notifications
    convert_flash_to_center_notifications();
    
    // Hiển thị center notifications
    return display_center_notifications();
}
?>
