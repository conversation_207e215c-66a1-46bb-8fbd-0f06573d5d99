<?php
/**
 * File xử lý các hàm liên quan đến Mega Menu
 */

/**
 * L<PERSON><PERSON> danh sách sản phẩm bán chạy theo danh mục
 *
 * @param int $category_id ID của danh mục
 * @param int $limit Số lượng sản phẩm tối đa
 * @return array Danh sách sản phẩm bán chạy
 */
function get_bestsellers_by_category($category_id, $limit = 4) {
    global $conn;

    try {
        // Lấy tất cả ID danh mục con (bao gồm cả danh mục hiện tại)
        $category_ids = [$category_id];
        $subcategories = get_all_subcategories($category_id);
        
        foreach ($subcategories as $subcat) {
            $category_ids[] = $subcat['id'];
        }
        
        // Chuyển mảng ID thành chuỗi để sử dụng trong câu truy vấn
        $category_ids_str = implode(',', $category_ids);
        
        // Truy vấn lấy sản phẩm bán chạy
        $sql = "SELECT p.*, c.name as category_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.category_id IN ($category_ids_str)
                AND p.status = 1
                ORDER BY p.sold DESC, p.views DESC
                LIMIT :limit";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Lấy danh sách danh mục con trực tiếp của một danh mục
 *
 * @param int $category_id ID của danh mục cha
 * @return array Danh sách danh mục con
 */
function get_direct_subcategories($category_id, $status = 1) {
    global $conn;
    
    try {
        $sql = "SELECT * FROM categories WHERE parent_id = :parent_id";
        
        if ($status !== null) {
            $sql .= " AND status = :status";
        }
        
        $sql .= " ORDER BY name ASC";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':parent_id', $category_id);
        
        if ($status !== null) {
            $stmt->bindParam(':status', $status);
        }
        
        $stmt->execute();
        
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Lấy danh sách các icon cho danh mục
 * 
 * @return array Mảng các icon theo danh mục
 */
function get_category_icons() {
    // Mảng các icon mặc định cho các danh mục phổ biến
    return [
        'Tủ quần áo' => 'fa-door-closed',
        'Bàn trang điểm' => 'fa-paint-brush',
        'Bàn học' => 'fa-book',
        'Giường ngủ' => 'fa-bed',
        'Tủ giày' => 'fa-shoe-prints',
        'Kệ tivi' => 'fa-tv',
        'Kệ sách' => 'fa-book-open',
        'Tủ sách' => 'fa-bookmark',
        'Bàn ăn' => 'fa-utensils',
        'Ghế' => 'fa-chair',
        'Sofa' => 'fa-couch',
        'Bàn làm việc' => 'fa-briefcase',
        'Tủ bếp' => 'fa-kitchen-set',
        'Phòng khách' => 'fa-couch',
        'Phòng ngủ' => 'fa-bed',
        'Phòng bếp' => 'fa-utensils',
        'Phòng làm việc' => 'fa-briefcase',
        'default' => 'fa-chair'
    ];
}

/**
 * Lấy icon cho danh mục
 * 
 * @param string $category_name Tên danh mục
 * @return string Tên class của icon
 */
function get_category_icon($category_name) {
    $icons = get_category_icons();
    
    // Tìm icon phù hợp dựa trên tên danh mục
    foreach ($icons as $key => $icon) {
        if (stripos($category_name, $key) !== false) {
            return $icon;
        }
    }
    
    // Trả về icon mặc định nếu không tìm thấy
    return $icons['default'];
}
