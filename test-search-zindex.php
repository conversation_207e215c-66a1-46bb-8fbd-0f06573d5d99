<?php
// Test file để demo các gi<PERSON>i pháp cho vấn đề z-index search suggestions
$page_title = 'Test Search Z-Index Solutions';
$page_description = 'Demo các gi<PERSON>i pháp khắc phục vấn đề search suggestions bị đè';

// Include header
include_once 'partials/header.php';
?>

<style>
/* Base styles cho demo */
.demo-section {
    margin: 2rem 0;
    padding: 2rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: #f9fafb;
}

.demo-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 1rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, #f59e0b, #f97316);
    color: white;
    border-radius: 8px;
    display: inline-block;
}

.demo-description {
    background: #fff;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border-left: 4px solid #f59e0b;
}

.demo-container {
    position: relative;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* <PERSON><PERSON><PERSON> lập phần header với overflow hidden */
.mock-header-section {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    padding: 2rem;
    border-bottom: 1px solid #e5e7eb;
}

/* Giả lập phần search */
.mock-search-container {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.mock-search-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.mock-search-input:focus {
    outline: none;
    border-color: #f59e0b;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

/* Giả lập search suggestions */
.mock-search-suggestions {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}

.mock-search-suggestions.show {
    display: block;
}

.mock-suggestion-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.mock-suggestion-item:hover {
    background-color: #f9fafb;
}

.mock-suggestion-item:last-child {
    border-bottom: none;
}

/* Giả lập phần products */
.mock-products-section {
    background: #ffffff;
    padding: 2rem;
    position: relative;
    z-index: 1;
}

.mock-product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.mock-product-card {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    text-align: center;
}

/* Controls */
.demo-controls {
    margin: 1rem 0;
    text-align: center;
}

.demo-btn {
    background: #f59e0b;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    margin: 0 4px;
    transition: background-color 0.2s ease;
}

.demo-btn:hover {
    background: #d97706;
}

.demo-btn.active {
    background: #dc2626;
}

/* Status indicator */
.status-indicator {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    margin-left: 8px;
}

.status-problem {
    background: #fecaca;
    color: #dc2626;
}

.status-fixed {
    background: #bbf7d0;
    color: #059669;
}

/* Solution 1: Overflow visible có điều kiện */
.solution1-header.search-active {
    overflow: visible !important;
}

/* Solution 2: Position fixed */
.solution2-suggestions {
    position: fixed !important;
    z-index: 9999 !important;
}

/* Solution 3: CSS contain */
.solution3-header {
    contain: layout style !important;
}

/* Solution 4: Portal pattern */
.solution4-suggestions {
    position: fixed !important;
    z-index: 9999 !important;
}
</style>

<!-- Breadcrumb -->
<div class="modern-breadcrumb">
    <div class="breadcrumb-wrapper">
        <div class="breadcrumb-item">
            <a href="<?php echo BASE_URL; ?>" class="breadcrumb-link">
                <span class="breadcrumb-icon"><i class="fas fa-home"></i></span>
                <span>Trang chủ</span>
            </a>
        </div>
        <div class="breadcrumb-divider"><i class="fas fa-chevron-right"></i></div>
        <div class="breadcrumb-item active">
            <span class="breadcrumb-link">
                <span class="breadcrumb-icon"><i class="fas fa-bug"></i></span>
                <span>Test Z-Index Solutions</span>
            </span>
        </div>
    </div>
</div>

<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
        Demo Giải Pháp Z-Index cho Search Suggestions
    </h1>

    <div class="text-center mb-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p class="text-yellow-800">
            <strong>Vấn đề:</strong> Khi search suggestions nằm trong container có <code>overflow: hidden</code>,
            nó bị cắt bỏ và phần Products sẽ đè lên trên.
        </p>
    </div>

    <!-- Demo Problem -->
    <div class="demo-section">
        <div class="demo-title">
            🚨 Vấn đề gốc (Problem Demo)
            <span class="status-indicator status-problem">PROBLEM</span>
        </div>
        <div class="demo-description">
            <p><strong>Mô tả:</strong> Container có <code>overflow: hidden</code> sẽ cắt bỏ search suggestions,
            khiến phần Products đè lên trên.</p>
            <p><strong>Cách test:</strong> Click vào ô tìm kiếm để hiển thị suggestions, bạn sẽ thấy nó bị cắt.</p>
        </div>

        <div class="demo-controls">
            <button class="demo-btn" onclick="toggleSuggestions('problem')">Toggle Search Suggestions</button>
        </div>

        <div class="demo-container">
            <div class="mock-header-section" id="problem-header">
                <h3 class="text-lg font-semibold mb-4 text-center">Header Section (overflow: hidden)</h3>
                <div class="mock-search-container">
                    <input type="text" class="mock-search-input" placeholder="Tìm kiếm sản phẩm..."
                           onfocus="showSuggestions('problem')" onblur="hideSuggestions('problem')">
                    <div class="mock-search-suggestions" id="problem-suggestions">
                        <div class="mock-suggestion-item">🛋️ Sofa da thật cao cấp</div>
                        <div class="mock-suggestion-item">🪑 Ghế ăn gỗ sồi</div>
                        <div class="mock-suggestion-item">🛏️ Giường ngủ hiện đại</div>
                        <div class="mock-suggestion-item">📚 Tủ sách gỗ tự nhiên</div>
                        <div class="mock-suggestion-item">🍽️ Bàn ăn gia đình</div>
                    </div>
                </div>
            </div>
            <div class="mock-products-section">
                <h3 class="text-lg font-semibold mb-4">Products Section (z-index: 1)</h3>
                <div class="mock-product-grid">
                    <div class="mock-product-card">Sản phẩm 1</div>
                    <div class="mock-product-card">Sản phẩm 2</div>
                    <div class="mock-product-card">Sản phẩm 3</div>
                    <div class="mock-product-card">Sản phẩm 4</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Solution 1: Overflow Visible -->
    <div class="demo-section">
        <div class="demo-title">
            ✅ Giải pháp 1: Overflow Visible có điều kiện
            <span class="status-indicator status-fixed">FIXED</span>
        </div>
        <div class="demo-description">
            <p><strong>Mô tả:</strong> Thay đổi <code>overflow: hidden</code> thành <code>overflow: visible</code>
            khi search suggestions đang hiển thị.</p>
            <p><strong>Ưu điểm:</strong> Đơn giản, dễ implement</p>
            <p><strong>Nhược điểm:</strong> Có thể ảnh hưởng đến thiết kế background</p>
        </div>

        <div class="demo-controls">
            <button class="demo-btn" onclick="toggleSuggestions('solution1')">Toggle Search Suggestions</button>
        </div>

        <div class="demo-container">
            <div class="mock-header-section solution1-header" id="solution1-header">
                <h3 class="text-lg font-semibold mb-4 text-center">Header Section (overflow: visible when active)</h3>
                <div class="mock-search-container">
                    <input type="text" class="mock-search-input" placeholder="Tìm kiếm sản phẩm..."
                           onfocus="showSuggestions('solution1')" onblur="hideSuggestions('solution1')">
                    <div class="mock-search-suggestions" id="solution1-suggestions">
                        <div class="mock-suggestion-item">🛋️ Sofa da thật cao cấp</div>
                        <div class="mock-suggestion-item">🪑 Ghế ăn gỗ sồi</div>
                        <div class="mock-suggestion-item">🛏️ Giường ngủ hiện đại</div>
                        <div class="mock-suggestion-item">📚 Tủ sách gỗ tự nhiên</div>
                        <div class="mock-suggestion-item">🍽️ Bàn ăn gia đình</div>
                    </div>
                </div>
            </div>
            <div class="mock-products-section">
                <h3 class="text-lg font-semibold mb-4">Products Section (z-index: 1)</h3>
                <div class="mock-product-grid">
                    <div class="mock-product-card">Sản phẩm 1</div>
                    <div class="mock-product-card">Sản phẩm 2</div>
                    <div class="mock-product-card">Sản phẩm 3</div>
                    <div class="mock-product-card">Sản phẩm 4</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Solution 2: Position Fixed -->
    <div class="demo-section">
        <div class="demo-title">
            ✅ Giải pháp 2: Position Fixed với JS Positioning
            <span class="status-indicator status-fixed">FIXED</span>
        </div>
        <div class="demo-description">
            <p><strong>Mô tả:</strong> Sử dụng <code>position: fixed</code> cho search suggestions và
            tính toán vị trí bằng JavaScript.</p>
            <p><strong>Ưu điểm:</strong> Hiệu quả nhất, không ảnh hưởng thiết kế</p>
            <p><strong>Nhược điểm:</strong> Cần JavaScript để tính toán vị trí</p>
        </div>

        <div class="demo-controls">
            <button class="demo-btn" onclick="toggleSuggestions('solution2')">Toggle Search Suggestions</button>
        </div>

        <div class="demo-container">
            <div class="mock-header-section" id="solution2-header">
                <h3 class="text-lg font-semibold mb-4 text-center">Header Section (overflow: hidden)</h3>
                <div class="mock-search-container">
                    <input type="text" class="mock-search-input solution2-input" placeholder="Tìm kiếm sản phẩm..."
                           onfocus="showSuggestions('solution2')" onblur="hideSuggestions('solution2')">
                    <div class="mock-search-suggestions solution2-suggestions" id="solution2-suggestions">
                        <div class="mock-suggestion-item">🛋️ Sofa da thật cao cấp</div>
                        <div class="mock-suggestion-item">🪑 Ghế ăn gỗ sồi</div>
                        <div class="mock-suggestion-item">🛏️ Giường ngủ hiện đại</div>
                        <div class="mock-suggestion-item">📚 Tủ sách gỗ tự nhiên</div>
                        <div class="mock-suggestion-item">🍽️ Bàn ăn gia đình</div>
                    </div>
                </div>
            </div>
            <div class="mock-products-section">
                <h3 class="text-lg font-semibold mb-4">Products Section (z-index: 1)</h3>
                <div class="mock-product-grid">
                    <div class="mock-product-card">Sản phẩm 1</div>
                    <div class="mock-product-card">Sản phẩm 2</div>
                    <div class="mock-product-card">Sản phẩm 3</div>
                    <div class="mock-product-card">Sản phẩm 4</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Solution 3: CSS Contain -->
    <div class="demo-section">
        <div class="demo-title">
            ✅ Giải pháp 3: CSS Contain Property
            <span class="status-indicator status-fixed">FIXED</span>
        </div>
        <div class="demo-description">
            <p><strong>Mô tả:</strong> Sử dụng CSS <code>contain</code> property để kiểm soát containment
            mà không clip search suggestions.</p>
            <p><strong>Ưu điểm:</strong> Hiện đại, performance tốt</p>
            <p><strong>Nhược điểm:</strong> Hỗ trợ browser hạn chế</p>
        </div>

        <div class="demo-controls">
            <button class="demo-btn" onclick="toggleSuggestions('solution3')">Toggle Search Suggestions</button>
        </div>

        <div class="demo-container">
            <div class="mock-header-section solution3-header" id="solution3-header">
                <h3 class="text-lg font-semibold mb-4 text-center">Header Section (contain: layout style)</h3>
                <div class="mock-search-container">
                    <input type="text" class="mock-search-input" placeholder="Tìm kiếm sản phẩm..."
                           onfocus="showSuggestions('solution3')" onblur="hideSuggestions('solution3')">
                    <div class="mock-search-suggestions" id="solution3-suggestions">
                        <div class="mock-suggestion-item">🛋️ Sofa da thật cao cấp</div>
                        <div class="mock-suggestion-item">🪑 Ghế ăn gỗ sồi</div>
                        <div class="mock-suggestion-item">🛏️ Giường ngủ hiện đại</div>
                        <div class="mock-suggestion-item">📚 Tủ sách gỗ tự nhiên</div>
                        <div class="mock-suggestion-item">🍽️ Bàn ăn gia đình</div>
                    </div>
                </div>
            </div>
            <div class="mock-products-section">
                <h3 class="text-lg font-semibold mb-4">Products Section (z-index: 1)</h3>
                <div class="mock-product-grid">
                    <div class="mock-product-card">Sản phẩm 1</div>
                    <div class="mock-product-card">Sản phẩm 2</div>
                    <div class="mock-product-card">Sản phẩm 3</div>
                    <div class="mock-product-card">Sản phẩm 4</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Solution 4: Portal Pattern -->
    <div class="demo-section">
        <div class="demo-title">
            ✅ Giải pháp 4: Portal Pattern
            <span class="status-indicator status-fixed">FIXED</span>
        </div>
        <div class="demo-description">
            <p><strong>Mô tả:</strong> Di chuyển search suggestions ra ngoài DOM tree và quản lý bằng JavaScript portal pattern.</p>
            <p><strong>Ưu điểm:</strong> Tối ưu nhất, hoàn toàn tách biệt</p>
            <p><strong>Nhược điểm:</strong> Phức tạp nhất để implement</p>
        </div>

        <div class="demo-controls">
            <button class="demo-btn" onclick="toggleSuggestions('solution4')">Toggle Search Suggestions</button>
        </div>

        <div class="demo-container">
            <div class="mock-header-section" id="solution4-header">
                <h3 class="text-lg font-semibold mb-4 text-center">Header Section (overflow: hidden)</h3>
                <div class="mock-search-container">
                    <input type="text" class="mock-search-input solution4-input" placeholder="Tìm kiếm sản phẩm..."
                           onfocus="showSuggestions('solution4')" onblur="hideSuggestions('solution4')">
                    <!-- Suggestions sẽ được render ở body level -->
                </div>
            </div>
            <div class="mock-products-section">
                <h3 class="text-lg font-semibold mb-4">Products Section (z-index: 1)</h3>
                <div class="mock-product-grid">
                    <div class="mock-product-card">Sản phẩm 1</div>
                    <div class="mock-product-card">Sản phẩm 2</div>
                    <div class="mock-product-card">Sản phẩm 3</div>
                    <div class="mock-product-card">Sản phẩm 4</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary -->
    <div class="demo-section">
        <div class="demo-title">
            📊 Tổng kết và So sánh
        </div>
        <div class="demo-description">
            <table class="w-full border-collapse border border-gray-300">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="border border-gray-300 p-2">Giải pháp</th>
                        <th class="border border-gray-300 p-2">Độ khó</th>
                        <th class="border border-gray-300 p-2">Hiệu quả</th>
                        <th class="border border-gray-300 p-2">Tương thích</th>
                        <th class="border border-gray-300 p-2">Khuyến nghị</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="border border-gray-300 p-2">Overflow Visible</td>
                        <td class="border border-gray-300 p-2">⭐</td>
                        <td class="border border-gray-300 p-2">⭐⭐⭐</td>
                        <td class="border border-gray-300 p-2">⭐⭐⭐⭐⭐</td>
                        <td class="border border-gray-300 p-2">Tốt cho prototype</td>
                    </tr>
                    <tr>
                        <td class="border border-gray-300 p-2">Position Fixed</td>
                        <td class="border border-gray-300 p-2">⭐⭐</td>
                        <td class="border border-gray-300 p-2">⭐⭐⭐⭐⭐</td>
                        <td class="border border-gray-300 p-2">⭐⭐⭐⭐⭐</td>
                        <td class="border border-gray-300 p-2"><strong>Khuyến nghị</strong></td>
                    </tr>
                    <tr>
                        <td class="border border-gray-300 p-2">CSS Contain</td>
                        <td class="border border-gray-300 p-2">⭐⭐</td>
                        <td class="border border-gray-300 p-2">⭐⭐⭐⭐</td>
                        <td class="border border-gray-300 p-2">⭐⭐⭐</td>
                        <td class="border border-gray-300 p-2">Tương lai</td>
                    </tr>
                    <tr>
                        <td class="border border-gray-300 p-2">Portal Pattern</td>
                        <td class="border border-gray-300 p-2">⭐⭐⭐⭐</td>
                        <td class="border border-gray-300 p-2">⭐⭐⭐⭐⭐</td>
                        <td class="border border-gray-300 p-2">⭐⭐⭐⭐⭐</td>
                        <td class="border border-gray-300 p-2">Cho hệ thống lớn</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Portal container for solution 4 -->
<div id="portal-container"></div>

<script>
function showSuggestions(demo) {
    const suggestions = document.getElementById(demo + '-suggestions');
    if (suggestions) {
        suggestions.classList.add('show');

        // Solution 1: Add search-active class
        if (demo === 'solution1') {
            const header = document.getElementById('solution1-header');
            header.classList.add('search-active');
        }

        // Solution 2: Position fixed with JS positioning
        if (demo === 'solution2') {
            const input = document.querySelector('.solution2-input');
            const rect = input.getBoundingClientRect();
            suggestions.style.position = 'fixed';
            suggestions.style.top = (rect.bottom + 8) + 'px';
            suggestions.style.left = rect.left + 'px';
            suggestions.style.width = rect.width + 'px';
        }

        // Solution 4: Portal pattern
        if (demo === 'solution4') {
            createPortalSuggestions();
        }
    }
}

function hideSuggestions(demo) {
    setTimeout(() => {
        const suggestions = document.getElementById(demo + '-suggestions');
        if (suggestions) {
            suggestions.classList.remove('show');

            // Solution 1: Remove search-active class
            if (demo === 'solution1') {
                const header = document.getElementById('solution1-header');
                header.classList.remove('search-active');
            }

            // Solution 4: Remove portal
            if (demo === 'solution4') {
                removePortalSuggestions();
            }
        }
    }, 200);
}

function toggleSuggestions(demo) {
    const suggestions = document.getElementById(demo + '-suggestions');
    if (suggestions) {
        if (suggestions.classList.contains('show')) {
            hideSuggestions(demo);
        } else {
            showSuggestions(demo);
        }
    }
}

function createPortalSuggestions() {
    const input = document.querySelector('.solution4-input');
    const rect = input.getBoundingClientRect();
    const portal = document.getElementById('portal-container');

    const suggestions = document.createElement('div');
    suggestions.className = 'mock-search-suggestions solution4-suggestions show';
    suggestions.id = 'solution4-portal-suggestions';
    suggestions.style.position = 'fixed';
    suggestions.style.top = (rect.bottom + 8) + 'px';
    suggestions.style.left = rect.left + 'px';
    suggestions.style.width = rect.width + 'px';
    suggestions.style.zIndex = '9999';

    suggestions.innerHTML = `
        <div class="mock-suggestion-item">🛋️ Sofa da thật cao cấp</div>
        <div class="mock-suggestion-item">🪑 Ghế ăn gỗ sồi</div>
        <div class="mock-suggestion-item">🛏️ Giường ngủ hiện đại</div>
        <div class="mock-suggestion-item">📚 Tủ sách gỗ tự nhiên</div>
        <div class="mock-suggestion-item">🍽️ Bàn ăn gia đình</div>
    `;

    portal.appendChild(suggestions);
}

function removePortalSuggestions() {
    const portalSuggestions = document.getElementById('solution4-portal-suggestions');
    if (portalSuggestions) {
        portalSuggestions.remove();
    }
}
</script>

<?php include_once 'partials/footer.php'; ?>