-- SQL Update cho phần footer
-- Cập nhật tiêu đề cột 3 từ "Danh mục sản phẩm" thành "Hỗ trợ khách hàng"

-- Ki<PERSON>m tra xem bảng site_settings có tồn tại không
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'site_settings';

-- N<PERSON>u bảng tồn tại, thực hiện cập nhật
SET @sql = IF(@table_exists > 0, 
    'INSERT INTO site_settings (setting_key, setting_value) 
     VALUES (\'footer_col3_title\', \'Hỗ trợ khách hàng\') 
     ON DUPLICATE KEY UPDATE setting_value = \'Hỗ trợ khách hàng\'',
    'SELECT \'Bảng site_settings không tồn tại\' AS message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cập nhật liên kết cho cột "Hỗ trợ khách hàng"
-- Tạo JSON cho các liên kết hỗ trợ khách hàng
SET @customer_support_links = '[
    {"text": "Đổi trả bảo hành", "url": "/warranty.php"},
    {"text": "Hình thức thanh toán", "url": "/payment.php"},
    {"text": "Vận chuyển giao hàng", "url": "/shipping.php"},
    {"text": "Chính sách bảo mật", "url": "/privacy-policy.php"},
    {"text": "Điều khoản sử dụng", "url": "/terms-of-service.php"}
]';

-- Kiểm tra xem bảng site_settings có tồn tại không
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'site_settings';

-- Nếu bảng tồn tại, thực hiện cập nhật
SET @sql = IF(@table_exists > 0, 
    CONCAT('INSERT INTO site_settings (setting_key, setting_value) 
     VALUES (\'footer_col3_links\', \'', @customer_support_links, '\') 
     ON DUPLICATE KEY UPDATE setting_value = \'', @customer_support_links, '\''),
    'SELECT \'Bảng site_settings không tồn tại\' AS message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Thông báo hoàn thành
SELECT 'Cập nhật footer links thành công' AS message;
