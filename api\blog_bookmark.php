<?php
/**
 * API đánh dấu/bỏ đánh dấu bài viết blog
 */

// Include các file cần thiết
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/auth.php';

// Kiểm tra đăng nhập
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Bạn cần đăng nhập để thực hiện chức năng này.']);
    exit;
}

// Kiểm tra phương thức request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Phương thức không được hỗ trợ.']);
    exit;
}

// Lấy dữ liệu từ request
$post_id = isset($_POST['post_id']) ? (int)$_POST['post_id'] : 0;
$action = isset($_POST['action']) ? $_POST['action'] : '';
$user_id = $_SESSION['user_id'];

// Kiểm tra dữ liệu
if (empty($post_id) || empty($action)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Dữ liệu không hợp lệ.']);
    exit;
}

// Kiểm tra bài viết tồn tại
try {
    $stmt = $conn->prepare("SELECT id FROM blog_posts WHERE id = :post_id");
    $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Bài viết không tồn tại.']);
        exit;
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Lỗi hệ thống: ' . $e->getMessage()]);
    exit;
}

// Xử lý đánh dấu/bỏ đánh dấu
try {
    if ($action === 'add') {
        // Kiểm tra xem đã đánh dấu chưa
        $stmt = $conn->prepare("SELECT id FROM blog_bookmarks WHERE user_id = :user_id AND post_id = :post_id");
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => 'Bài viết đã được đánh dấu trước đó.']);
            exit;
        }
        
        // Thêm đánh dấu mới
        $stmt = $conn->prepare("INSERT INTO blog_bookmarks (user_id, post_id) VALUES (:user_id, :post_id)");
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->execute();
        
        echo json_encode(['success' => true, 'message' => 'Đã đánh dấu bài viết.']);
    } elseif ($action === 'remove') {
        // Xóa đánh dấu
        $stmt = $conn->prepare("DELETE FROM blog_bookmarks WHERE user_id = :user_id AND post_id = :post_id");
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->execute();
        
        echo json_encode(['success' => true, 'message' => 'Đã bỏ đánh dấu bài viết.']);
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Hành động không hợp lệ.']);
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Lỗi hệ thống: ' . $e->getMessage()]);
}
