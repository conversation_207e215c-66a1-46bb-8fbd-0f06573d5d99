<?php
// Include các file cần thiết
require_once '../includes/init.php';

// Thiết lập tiêu đề trang
$page_title = 'Thêm/Sửa bài viết';

// Include file header
include_once 'partials/header.php';

// Kiểm tra quyền truy cập
if (!is_admin()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/index.php');
}

// Include blog functions
include_once '../includes/blog-functions.php';

// Khởi tạo biến
$errors = [];
$success = '';
$post = [
    'id' => '',
    'title' => '',
    'slug' => '',
    'content' => '',
    'excerpt' => '',
    'featured_image' => '',
    'author_id' => $_SESSION['user_id'],
    'blog_author_id' => null,
    'status' => 1,
    'is_featured' => 0,
    'show_on_homepage' => 0,
    'meta_title' => '',
    'meta_description' => '',
    'meta_keywords' => '',
    'published_at' => date('Y-m-d H:i:s')
];

// Lấy danh sách danh mục
$categories = get_blog_categories();

// Lấy danh sách tag
$tags = get_blog_tags();

// Lấy danh sách tác giả
try {
    $stmt = $conn->prepare("
        SELECT *
        FROM blog_authors
        ORDER BY name ASC
    ");
    $stmt->execute();
    $authors = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $authors = [];
}

// Kiểm tra xem đang thêm mới hay cập nhật
$is_edit = isset($_GET['id']) && !empty($_GET['id']);

// Nếu là cập nhật, lấy thông tin bài viết
if ($is_edit) {
    $post_id = (int)$_GET['id'];

    try {
        $stmt = $conn->prepare("SELECT * FROM blog_posts WHERE id = :id");
        $stmt->bindParam(':id', $post_id, PDO::PARAM_INT);
        $stmt->execute();

        $post_data = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($post_data) {
            $post = array_merge($post, $post_data);

            // Lấy danh mục của bài viết
            $stmt = $conn->prepare("SELECT category_id FROM blog_post_categories WHERE post_id = :post_id");
            $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
            $stmt->execute();
            $post_categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Lấy tag của bài viết
            $stmt = $conn->prepare("SELECT tag_id FROM blog_post_tags WHERE post_id = :post_id");
            $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
            $stmt->execute();
            $post_tags = $stmt->fetchAll(PDO::FETCH_COLUMN);
        } else {
            set_flash_message('error', 'Không tìm thấy bài viết.');
            redirect(BASE_URL . '/admin/blog-posts.php');
        }
    } catch (PDOException $e) {
        set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        redirect(BASE_URL . '/admin/blog-posts.php');
    }
}

// Xử lý khi form được submit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Cập nhật biến is_edit từ form
    $is_edit = isset($_POST['is_edit']) && $_POST['is_edit'] === '1';

    // Lấy dữ liệu từ form
    $post['title'] = $_POST['title'] ?? '';
    $post['slug'] = $_POST['slug'] ?? '';
    $post['content'] = $_POST['content'] ?? '';
    $post['excerpt'] = $_POST['excerpt'] ?? '';
    $post['status'] = isset($_POST['status']) ? 1 : 0;
    $post['is_featured'] = isset($_POST['is_featured']) ? 1 : 0;
    $post['show_on_homepage'] = isset($_POST['show_on_homepage']) ? 1 : 0;
    $post['meta_title'] = $_POST['meta_title'] ?? '';
    $post['meta_description'] = $_POST['meta_description'] ?? '';
    $post['meta_keywords'] = $_POST['meta_keywords'] ?? '';
    $post['published_at'] = $_POST['published_at'] ?? date('Y-m-d H:i:s');
    $post['blog_author_id'] = isset($_POST['blog_author_id']) ? (int)$_POST['blog_author_id'] : null;

    // Sử dụng author_id mặc định nếu không có blog_author_id
    if (empty($post['blog_author_id'])) {
        $post['author_id'] = $_SESSION['user_id'];
    }

    // Lấy giá trị lượt xem nếu là chỉnh sửa
    if ($is_edit && isset($_POST['view_count'])) {
        $post['view_count'] = max(0, (int)$_POST['view_count']); // Đảm bảo giá trị không âm
    }

    $selected_categories = $_POST['categories'] ?? [];
    $selected_tags = $_POST['tags'] ?? [];

    // Validate dữ liệu
    if (empty($post['title'])) {
        $errors[] = 'Vui lòng nhập tiêu đề bài viết.';
    }

    // Tạo slug nếu không có
    if (empty($post['slug'])) {
        $post['slug'] = create_slug($post['title']);
    } else {
        $post['slug'] = create_slug($post['slug']);
    }

    // Kiểm tra slug đã tồn tại chưa
    try {
        $stmt = $conn->prepare("SELECT id FROM blog_posts WHERE slug = :slug AND id != :id");
        $stmt->bindParam(':slug', $post['slug']);
        $stmt->bindParam(':id', $post['id'], PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $errors[] = 'Slug đã tồn tại. Vui lòng chọn slug khác.';
        }
    } catch (PDOException $e) {
        $errors[] = 'Có lỗi xảy ra: ' . $e->getMessage();
    }

    // Xử lý upload ảnh đại diện
    if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
        $upload_result = upload_image($_FILES['featured_image'], 'blog');

        if ($upload_result['success']) {
            // Nếu là cập nhật và đã có ảnh cũ, xóa ảnh cũ
            if ($is_edit && !empty($post['featured_image'])) {
                $old_image_path = UPLOADS_PATH . 'blog/' . $post['featured_image'];
                if (file_exists($old_image_path)) {
                    unlink($old_image_path);
                }
            }

            $post['featured_image'] = $upload_result['filename'];
        } else {
            $errors[] = $upload_result['message'];
        }
    }

    // Nếu không có lỗi, lưu vào database
    if (empty($errors)) {
        try {
            $conn->beginTransaction();

            if ($is_edit) {
                // Cập nhật bài viết
                $stmt = $conn->prepare("
                    UPDATE blog_posts SET
                    title = :title,
                    slug = :slug,
                    content = :content,
                    excerpt = :excerpt,
                    featured_image = :featured_image,
                    author_id = :author_id,
                    blog_author_id = :blog_author_id,
                    status = :status,
                    is_featured = :is_featured,
                    show_on_homepage = :show_on_homepage,
                    meta_title = :meta_title,
                    meta_description = :meta_description,
                    meta_keywords = :meta_keywords,
                    published_at = :published_at,
                    view_count = :view_count,
                    updated_at = NOW()
                    WHERE id = :id
                ");
                $stmt->bindParam(':id', $post['id'], PDO::PARAM_INT);
            } else {
                // Thêm bài viết mới
                $stmt = $conn->prepare("
                    INSERT INTO blog_posts (
                    title, slug, content, excerpt, featured_image, author_id, blog_author_id,
                    status, is_featured, show_on_homepage, meta_title, meta_description, meta_keywords, published_at, created_at, updated_at
                    ) VALUES (
                    :title, :slug, :content, :excerpt, :featured_image, :author_id, :blog_author_id,
                    :status, :is_featured, :show_on_homepage, :meta_title, :meta_description, :meta_keywords, :published_at, NOW(), NOW()
                    )
                ");
                $stmt->bindParam(':author_id', $post['author_id'], PDO::PARAM_INT);
            }

            $stmt->bindParam(':title', $post['title']);
            $stmt->bindParam(':slug', $post['slug']);
            $stmt->bindParam(':content', $post['content']);
            $stmt->bindParam(':excerpt', $post['excerpt']);
            $stmt->bindParam(':featured_image', $post['featured_image']);
            $stmt->bindParam(':author_id', $post['author_id'], PDO::PARAM_INT);
            $stmt->bindParam(':blog_author_id', $post['blog_author_id'], PDO::PARAM_INT);
            $stmt->bindParam(':status', $post['status'], PDO::PARAM_INT);
            $stmt->bindParam(':is_featured', $post['is_featured'], PDO::PARAM_INT);
            $stmt->bindParam(':show_on_homepage', $post['show_on_homepage'], PDO::PARAM_INT);
            $stmt->bindParam(':meta_title', $post['meta_title']);
            $stmt->bindParam(':meta_description', $post['meta_description']);
            $stmt->bindParam(':meta_keywords', $post['meta_keywords']);
            $stmt->bindParam(':published_at', $post['published_at']);

            // Bind view_count nếu là chỉnh sửa
            if ($is_edit) {
                $stmt->bindParam(':view_count', $post['view_count'], PDO::PARAM_INT);
            }

            $stmt->execute();

            if (!$is_edit) {
                $post['id'] = $conn->lastInsertId();
            }

            // Xóa danh mục cũ (nếu là cập nhật)
            if ($is_edit) {
                $stmt = $conn->prepare("DELETE FROM blog_post_categories WHERE post_id = :post_id");
                $stmt->bindParam(':post_id', $post['id'], PDO::PARAM_INT);
                $stmt->execute();

                $stmt = $conn->prepare("DELETE FROM blog_post_tags WHERE post_id = :post_id");
                $stmt->bindParam(':post_id', $post['id'], PDO::PARAM_INT);
                $stmt->execute();
            }

            // Thêm danh mục mới
            if (!empty($selected_categories)) {
                $values = [];
                $params = [];

                foreach ($selected_categories as $i => $category_id) {
                    $values[] = "(:post_id, :category_id_$i)";
                    $params[":category_id_$i"] = $category_id;
                }

                $sql = "INSERT INTO blog_post_categories (post_id, category_id) VALUES " . implode(', ', $values);
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':post_id', $post['id'], PDO::PARAM_INT);

                foreach ($params as $key => $value) {
                    $stmt->bindValue($key, $value, PDO::PARAM_INT);
                }

                $stmt->execute();
            }

            // Thêm tag mới
            if (!empty($selected_tags)) {
                $values = [];
                $params = [];

                foreach ($selected_tags as $i => $tag_id) {
                    $values[] = "(:post_id, :tag_id_$i)";
                    $params[":tag_id_$i"] = $tag_id;
                }

                $sql = "INSERT INTO blog_post_tags (post_id, tag_id) VALUES " . implode(', ', $values);
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':post_id', $post['id'], PDO::PARAM_INT);

                foreach ($params as $key => $value) {
                    $stmt->bindValue($key, $value, PDO::PARAM_INT);
                }

                $stmt->execute();
            }

            $conn->commit();

            $success = $is_edit ? 'Cập nhật bài viết thành công.' : 'Thêm bài viết mới thành công.';

            // Nếu là thêm mới, chuyển hướng đến trang sửa
            if (!$is_edit) {
                set_flash_message('success', $success);
                redirect(BASE_URL . '/admin/blog-post-edit.php?id=' . $post['id']);
            } else {
                // Hiển thị thông báo thành công khi cập nhật
                set_flash_message('success', $success);
            }
        } catch (PDOException $e) {
            $conn->rollBack();
            $errors[] = 'Có lỗi xảy ra: ' . $e->getMessage();
        }
    }
}
?>

<!-- Phần HTML của trang -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary"><?php echo $is_edit ? 'Sửa bài viết' : 'Thêm bài viết mới'; ?></h6>
            <a href="<?php echo BASE_URL; ?>/admin/blog-posts.php" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
        <div class="card-body">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <form method="post" enctype="multipart/form-data">
                <!-- Trường ẩn để lưu trạng thái chỉnh sửa -->
                <input type="hidden" name="is_edit" value="<?php echo $is_edit ? '1' : '0'; ?>">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label for="title">Tiêu đề <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($post['title']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="slug">Slug</label>
                            <input type="text" class="form-control" id="slug" name="slug" value="<?php echo htmlspecialchars($post['slug']); ?>">
                            <small class="form-text text-muted">Để trống để tự động tạo từ tiêu đề.</small>
                        </div>

                        <div class="form-group">
                            <label for="content">Nội dung <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="summernote" name="content" rows="15" required><?php echo htmlspecialchars($post['content']); ?></textarea>
                            <small class="form-text text-muted">Sử dụng trình soạn thảo để định dạng nội dung.</small>
                        </div>

                        <div class="form-group">
                            <label for="excerpt">Tóm tắt</label>
                            <textarea class="form-control" id="excerpt" name="excerpt" rows="3"><?php echo htmlspecialchars($post['excerpt']); ?></textarea>
                            <small class="form-text text-muted">Hiển thị ở trang danh sách bài viết. Để trống sẽ tự động lấy từ nội dung.</small>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card mb-3">
                            <div class="card-header">Thông tin xuất bản</div>
                            <div class="card-body">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="status" name="status" <?php echo $post['status'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="status">Hiển thị</label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_featured" name="is_featured" <?php echo $post['is_featured'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="is_featured">Bài viết nổi bật</label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="show_on_homepage" name="show_on_homepage" <?php echo isset($post['show_on_homepage']) && $post['show_on_homepage'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="show_on_homepage">Hiển thị trên trang chủ</label>
                                        <small class="form-text text-muted">Nếu được chọn, bài viết này sẽ được hiển thị trong phần "Cẩm nang nội thất" trên trang chủ.</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="published_at">Ngày xuất bản</label>
                                    <input type="datetime-local" class="form-control" id="published_at" name="published_at" value="<?php echo date('Y-m-d\TH:i', strtotime($post['published_at'])); ?>">
                                </div>

                                <div class="form-group">
                                    <label for="blog_author_id">Tác giả</label>
                                    <select class="form-control" id="blog_author_id" name="blog_author_id">
                                        <option value="">-- Không chọn tác giả --</option>
                                        <?php if (!empty($authors)): ?>
                                            <?php foreach ($authors as $author): ?>
                                                <option value="<?php echo $author['id']; ?>" <?php echo $post['blog_author_id'] == $author['id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($author['name']); ?>
                                                    <?php if (!empty($author['position'])): ?>
                                                        - <?php echo htmlspecialchars($author['position']); ?>
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                    <small class="form-text text-muted">
                                        Chọn tác giả cho bài viết.
                                        <a href="<?php echo BASE_URL; ?>/admin/blog-authors.php">Quản lý tác giả</a>
                                    </small>
                                </div>

                                <?php if ($is_edit): ?>
                                <div class="form-group">
                                    <label for="view_count">Lượt xem</label>
                                    <input type="number" class="form-control" id="view_count" name="view_count" value="<?php echo (int)$post['view_count']; ?>" min="0">
                                    <small class="form-text text-muted">Số lượt xem hiện tại của bài viết.</small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-header">Ảnh đại diện</div>
                            <div class="card-body">
                                <?php if (!empty($post['featured_image'])): ?>
                                    <div class="mb-3">
                                        <img src="<?php echo BASE_URL; ?>/uploads/blog/<?php echo $post['featured_image']; ?>" alt="Featured Image" class="img-fluid">
                                    </div>
                                <?php endif; ?>

                                <div class="form-group">
                                    <input type="file" class="form-control-file" id="featured_image" name="featured_image">
                                    <small class="form-text text-muted">Kích thước tối đa: 2MB. Định dạng: JPG, PNG, GIF.</small>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-header">Danh mục</div>
                            <div class="card-body">
                                <?php foreach ($categories as $category): ?>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="category_<?php echo $category['id']; ?>" name="categories[]" value="<?php echo $category['id']; ?>" <?php echo $is_edit && in_array($category['id'], $post_categories) ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="category_<?php echo $category['id']; ?>"><?php echo $category['name']; ?></label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-header">Tags</div>
                            <div class="card-body">
                                <?php foreach ($tags as $tag): ?>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="tag_<?php echo $tag['id']; ?>" name="tags[]" value="<?php echo $tag['id']; ?>" <?php echo $is_edit && in_array($tag['id'], $post_tags) ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="tag_<?php echo $tag['id']; ?>"><?php echo $tag['name']; ?></label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-header">SEO</div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="meta_title">Meta Title</label>
                                    <input type="text" class="form-control" id="meta_title" name="meta_title" value="<?php echo htmlspecialchars($post['meta_title']); ?>">
                                    <small class="form-text text-muted">Để trống sẽ sử dụng tiêu đề bài viết.</small>
                                </div>

                                <div class="form-group">
                                    <label for="meta_description">Meta Description</label>
                                    <textarea class="form-control" id="meta_description" name="meta_description" rows="3"><?php echo htmlspecialchars($post['meta_description']); ?></textarea>
                                    <small class="form-text text-muted">Để trống sẽ sử dụng tóm tắt bài viết.</small>
                                </div>

                                <div class="form-group">
                                    <label for="meta_keywords">Meta Keywords</label>
                                    <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" value="<?php echo htmlspecialchars($post['meta_keywords']); ?>">
                                    <small class="form-text text-muted">Phân cách bằng dấu phẩy.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> <?php echo $is_edit ? 'Cập nhật' : 'Thêm mới'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>



<!-- Script đã được di chuyển vào file summernote-config.js -->
<script>
    // Thêm debug để kiểm tra
    console.log('blog-post-edit.php loaded');
    $(document).ready(function() {
        console.log('Document ready in blog-post-edit.php');
        // Kiểm tra xem Summernote đã được tải chưa
        if (typeof $.summernote === 'undefined') {
            console.error('Summernote is not loaded!');
        } else {
            console.log('Summernote is available');
        }
    });
</script>

<?php
// Include file footer
include_once 'partials/footer.php';
?>
