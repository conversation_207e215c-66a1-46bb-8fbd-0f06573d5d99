-- C<PERSON><PERSON> nhật cấu trú<PERSON> bảng orders để đảm bảo có thể liên kết với người dùng
-- Ki<PERSON><PERSON> tra xem cột user_id đã tồn tại trong bảng orders chưa
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'orders' 
AND COLUMN_NAME = 'user_id';

-- Nếu cột user_id chưa tồn tại, thêm cột này vào bảng orders
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE orders ADD COLUMN user_id INT NULL, ADD CONSTRAINT orders_user_id_fk FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL', 
    'SELECT "Cột user_id đã tồn tại trong bảng orders" AS message');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- <PERSON><PERSON><PERSON> bảo có chỉ mục cho cột user_id để tối ưu truy vấn
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'orders' 
AND INDEX_NAME = 'idx_orders_user_id';

-- Nếu chỉ mục chưa tồn tại, tạo chỉ mục
SET @query = IF(@index_exists = 0, 
    'CREATE INDEX idx_orders_user_id ON orders(user_id)', 
    'SELECT "Chỉ mục idx_orders_user_id đã tồn tại" AS message');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Ghi log cập nhật
INSERT INTO system_logs (action, description, created_by) 
VALUES ('DATABASE_UPDATE', 'Cập nhật cấu trúc bảng orders để hỗ trợ liên kết đơn hàng với tài khoản', 'SYSTEM')
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;
