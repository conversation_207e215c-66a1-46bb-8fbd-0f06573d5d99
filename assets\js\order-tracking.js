/**
 * Order Tracking JavaScript
 * <PERSON><PERSON> lý cập nhật trạng thái đơn hàng theo thời gian thực cho người dùng
 */

document.addEventListener('DOMContentLoaded', function() {
    // Kiểm tra xem người dùng đã đăng nhập chưa
    const isLoggedIn = document.body.classList.contains('logged-in');

    if (!isLoggedIn) return;

    // Lấy CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    // Lấy BASE_URL
    const BASE_URL = document.querySelector('meta[name="base-url"]')?.getAttribute('content') || '';

    /**
     * Kiểm tra cập nhật đơn hàng
     */
    function checkOrderUpdates() {
        // Lấy thời gian cập nhật cuối cùng từ localStorage
        const lastUpdate = localStorage.getItem('lastOrderUpdate') || 0;

        // <PERSON><PERSON><PERSON> request kiểm tra cập nhật
        fetch(`${BASE_URL}/api/check_order_updates.php?last_update=${lastUpdate}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.updates) {
                    // Lưu thời gian cập nhật mới nhất
                    localStorage.setItem('lastOrderUpdate', data.timestamp);

                    // Cập nhật UI cho mỗi đơn hàng có thay đổi
                    data.orders.forEach(order => {
                        updateOrderUI(order);
                    });

                    // Hiển thị thông báo nếu có cập nhật
                    if (data.orders.length > 0) {
                        showNotification('Đơn hàng của bạn đã được cập nhật', 'info');
                    }
                }
            })
            .catch(error => {
                console.error('Error checking order updates:', error);
            });
    }

    /**
     * Cập nhật giao diện đơn hàng
     */
    function updateOrderUI(order) {
        // Cập nhật trạng thái đơn hàng trong trang danh sách đơn hàng
        const orderStatusElement = document.querySelector(`.order-status[data-order-id="${order.id}"]`);
        if (orderStatusElement) {
            // Giữ lại các class cần thiết và thêm class mới
            orderStatusElement.className = 'px-3 py-1.5 inline-flex items-center text-xs font-medium rounded-full order-status ' + order.status_class;
            // Thêm class updated để hiển thị hiệu ứng cập nhật
            orderStatusElement.classList.add('updated');
            // Cập nhật nội dung
            orderStatusElement.textContent = order.status_text;

            // Xóa class updated sau khi hiệu ứng hoàn thành
            setTimeout(() => {
                orderStatusElement.classList.remove('updated');
            }, 1000);
        }

        // Cập nhật trạng thái đơn hàng trong trang chi tiết đơn hàng
        const orderDetailStatusElement = document.querySelector(`.order-detail-status[data-order-id="${order.id}"]`);
        if (orderDetailStatusElement) {
            // Giữ lại các class cần thiết và thêm class mới
            orderDetailStatusElement.className = 'px-3 py-1.5 inline-flex items-center text-xs font-medium rounded-full order-detail-status ' + order.status_class;
            // Thêm class updated để hiển thị hiệu ứng cập nhật
            orderDetailStatusElement.classList.add('updated');
            // Cập nhật nội dung
            orderDetailStatusElement.textContent = order.status_text;

            // Xóa class updated sau khi hiệu ứng hoàn thành
            setTimeout(() => {
                orderDetailStatusElement.classList.remove('updated');
            }, 1000);
        }

        // Cập nhật tiến trình đơn hàng nếu có
        updateOrderProgress(order.id, order.status);

        // Hiển thị nút đánh giá sản phẩm nếu đơn hàng đã hoàn thành
        if (order.status === 'completed') {
            showReviewButtons(order.id, order.items);
        }
    }

    /**
     * Cập nhật tiến trình đơn hàng
     */
    function updateOrderProgress(orderId, status) {
        const progressElement = document.querySelector(`.order-progress[data-order-id="${orderId}"]`);
        if (!progressElement) return;

        // Xác định các bước trong tiến trình
        const steps = ['pending', 'processing', 'shipping', 'completed'];

        // Nếu đơn hàng bị hủy, hiển thị thông báo hủy
        if (status === 'cancelled') {
            progressElement.innerHTML = `
                <div class="order-cancelled">
                    <i class="fas fa-times-circle text-danger"></i>
                    <span class="ml-2">Đơn hàng đã bị hủy</span>
                </div>
            `;
            return;
        }

        // Tìm vị trí của trạng thái hiện tại trong tiến trình
        const currentStepIndex = steps.indexOf(status);

        // Cập nhật class cho mỗi bước
        const progressSteps = progressElement.querySelectorAll('.progress-step');
        progressSteps.forEach((step, index) => {
            // Xóa tất cả các class trạng thái
            step.classList.remove('completed', 'active', 'pending');

            if (index < currentStepIndex) {
                // Các bước đã hoàn thành
                step.classList.add('completed');
            } else if (index === currentStepIndex) {
                // Bước hiện tại
                step.classList.add('active');
            } else {
                // Các bước chưa thực hiện
                step.classList.add('pending');
            }
        });
    }

    /**
     * Hiển thị nút đánh giá sản phẩm
     */
    function showReviewButtons(orderId, items) {
        // Kiểm tra xem có container đánh giá không
        const reviewContainer = document.querySelector(`.review-container[data-order-id="${orderId}"]`);
        if (!reviewContainer) return;

        // Hiển thị container đánh giá
        reviewContainer.style.display = 'block';

        // Cập nhật nút đánh giá cho từng sản phẩm
        items.forEach(item => {
            const reviewButton = reviewContainer.querySelector(`.review-button[data-product-id="${item.product_id}"]`);
            if (reviewButton) {
                reviewButton.style.display = 'inline-flex';
            }
        });
    }

    /**
     * Hiển thị thông báo
     */
    function showNotification(message, type = 'info') {
        // Kiểm tra xem đã có container thông báo chưa
        let notificationContainer = document.querySelector('.notification-container');

        if (!notificationContainer) {
            // Tạo container thông báo
            notificationContainer = document.createElement('div');
            notificationContainer.className = 'notification-container fixed bottom-4 right-4 z-50';
            document.body.appendChild(notificationContainer);
        }

        // Xác định class theo loại thông báo
        let bgColor = 'bg-blue-500';
        if (type === 'success') bgColor = 'bg-green-500';
        if (type === 'error') bgColor = 'bg-red-500';
        if (type === 'warning') bgColor = 'bg-yellow-500';

        // Tạo thông báo
        const notification = document.createElement('div');
        notification.className = `notification ${bgColor} text-white rounded-lg shadow-lg p-4 mb-3 flex items-center justify-between transform transition-all duration-300 ease-in-out translate-x-full`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-info-circle mr-2"></i>
                <span>${message}</span>
            </div>
            <button class="ml-4 text-white focus:outline-none notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Thêm vào container
        notificationContainer.appendChild(notification);

        // Hiệu ứng hiển thị
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 10);

        // Thêm sự kiện đóng thông báo
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });

        // Tự động ẩn sau 5 giây
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }

    // Kiểm tra cập nhật khi trang được tải
    checkOrderUpdates();

    // Kiểm tra cập nhật mỗi 30 giây
    setInterval(checkOrderUpdates, 30000);
});
