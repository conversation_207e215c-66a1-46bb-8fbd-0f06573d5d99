/**
 * <PERSON><PERSON><PERSON><PERSON> pháp 1: Overflow Visible có điều kiện
 * Thay đổi overflow từ hidden sang visible khi search suggestions đang hiển thị
 */

/*
 * CSS Variables cho solution 1
 */
:root {
    --search-transition-duration: 0.3s;
    --search-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

/*
 * Base state: Container có overflow hidden nh<PERSON> bình thường
 */
.header-section-with-search {
    position: relative;
    overflow: hidden;
    transition: overflow var(--search-transition-duration) var(--search-transition-timing);
}

/*
 * Active state: Khi search suggestions đang hiển thị
 * Thay đổi overflow thành visible
 */
.header-section-with-search.search-suggestions-active {
    overflow: visible !important;
}

/*
 * Tương thích với cấu trúc hiện tại
 */
.search-suggestions-container.search-suggestions-solution1 {
    z-index: 1200 !important;
}

/*
 * Đ<PERSON>m bảo search container có z-index phù hợp
 */
.search-container-solution1 {
    position: relative;
    z-index: 100;
}

/*
 * Search suggestions với z-index cao
 */
.search-suggestions-solution1 {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    max-height: 400px;
    overflow-y: auto;

    /* Animation */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--search-transition-duration) var(--search-transition-timing);
}

/*
 * Khi suggestions được hiển thị
 */
.search-suggestions-solution1.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/*
 * Đảm bảo products section không đè lên
 */
.products-section {
    position: relative;
    z-index: 1;
}

/*
 * JavaScript helper classes
 */
.js-search-input-solution1:focus + .search-suggestions-solution1,
.js-search-input-solution1:focus ~ .search-suggestions-solution1 {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/*
 * Responsive adjustments
 */
@media (max-width: 768px) {
    .search-suggestions-solution1 {
        max-height: 60vh;
        border-radius: 8px;
    }
}

/*
 * Performance optimizations
 */
.header-section-with-search {
    /* Tối ưu rendering performance */
    will-change: overflow;
    backface-visibility: hidden;
}

.search-suggestions-solution1 {
    /* Tối ưu animation performance */
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
}

/*
 * Accessibility improvements
 */
.search-suggestions-solution1[aria-hidden="false"] {
    opacity: 1 !important;
    visibility: visible !important;
}

/*
 * Debug mode - uncomment để debug
 */
/*
.header-section-with-search {
    border: 2px solid red !important;
}

.header-section-with-search.search-suggestions-active {
    border: 2px solid green !important;
}

.search-suggestions-solution1 {
    border: 3px solid blue !important;
}
*/