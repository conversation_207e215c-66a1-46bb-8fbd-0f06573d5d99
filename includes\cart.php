<?php
/**
 * File xử lý giỏ hàng
 */

/**
 * Khởi tạo giỏ hàng
 */
function initialize_cart() {
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];

        // Nếu người dùng đã đăng nhập, tải giỏ hàng từ database
        if (isset($_SESSION['user_id'])) {
            load_cart_from_database($_SESSION['user_id']);
        }
    }
}

/**
 * Tải giỏ hàng từ database
 */
function load_cart_from_database($user_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT * FROM cart_items WHERE user_id = :user_id");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $_SESSION['cart'] = [];

            while ($item = $stmt->fetch(PDO::FETCH_ASSOC)) {
                // Lấy thông tin slug từ bảng products
                $product = get_product_by_id($item['product_id']);
                $slug = $product ? $product['slug'] : '';

                $_SESSION['cart'][] = [
                    'product_id' => $item['product_id'],
                    'name' => $item['name'],
                    'price' => $item['price'],
                    'image' => $item['image'],
                    'quantity' => $item['quantity'],
                    'slug' => $slug
                ];
            }
        }

        return true;
    } catch (PDOException $e) {
        // Ghi log lỗi nếu cần
        error_log("Lỗi khi tải giỏ hàng từ database: " . $e->getMessage());
        return false;
    }
}

/**
 * Lưu giỏ hàng vào database
 */
function save_cart_to_database($user_id, $product_id, $name, $price, $image, $quantity) {
    global $conn;

    try {
        // Kiểm tra xem sản phẩm đã có trong giỏ hàng chưa
        $stmt = $conn->prepare("SELECT * FROM cart_items WHERE user_id = :user_id AND product_id = :product_id");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Nếu đã có, cập nhật số lượng
            $item = $stmt->fetch(PDO::FETCH_ASSOC);
            $new_quantity = $item['quantity'] + $quantity;

            $stmt = $conn->prepare("UPDATE cart_items SET quantity = :quantity, updated_at = NOW() WHERE id = :id");
            $stmt->bindParam(':quantity', $new_quantity);
            $stmt->bindParam(':id', $item['id']);
            $stmt->execute();
        } else {
            // Nếu chưa có, thêm mới
            $stmt = $conn->prepare("INSERT INTO cart_items (user_id, product_id, name, price, image, quantity) VALUES (:user_id, :product_id, :name, :price, :image, :quantity)");
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':product_id', $product_id);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':price', $price);
            $stmt->bindParam(':image', $image);
            $stmt->bindParam(':quantity', $quantity);
            $stmt->execute();
        }

        return true;
    } catch (PDOException $e) {
        // Ghi log lỗi nếu cần
        error_log("Lỗi khi lưu giỏ hàng vào database: " . $e->getMessage());
        return false;
    }
}

/**
 * Cập nhật số lượng sản phẩm trong database
 */
function update_cart_item_in_database($user_id, $product_id, $quantity) {
    global $conn;

    try {
        $stmt = $conn->prepare("UPDATE cart_items SET quantity = :quantity, updated_at = NOW() WHERE user_id = :user_id AND product_id = :product_id");
        $stmt->bindParam(':quantity', $quantity);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        // Ghi log lỗi nếu cần
        error_log("Lỗi khi cập nhật giỏ hàng trong database: " . $e->getMessage());
        return false;
    }
}

/**
 * Xóa sản phẩm khỏi giỏ hàng trong database
 */
function remove_from_cart_in_database($user_id, $product_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("DELETE FROM cart_items WHERE user_id = :user_id AND product_id = :product_id");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        // Ghi log lỗi nếu cần
        error_log("Lỗi khi xóa sản phẩm khỏi giỏ hàng trong database: " . $e->getMessage());
        return false;
    }
}

/**
 * Xóa toàn bộ giỏ hàng trong database
 */
function clear_cart_in_database($user_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("DELETE FROM cart_items WHERE user_id = :user_id");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        // Ghi log lỗi nếu cần
        error_log("Lỗi khi xóa giỏ hàng trong database: " . $e->getMessage());
        return false;
    }
}

/**
 * Thêm sản phẩm vào giỏ hàng
 */
function add_to_cart($product_id, $quantity = 1) {

    // Lấy thông tin sản phẩm
    $product = get_product_by_id($product_id);

    if (!$product) {
        return [
            'success' => false,
            'message' => 'Sản phẩm không tồn tại'
        ];
    }

    // Kiểm tra số lượng
    if ($product['quantity'] < $quantity) {
        return [
            'success' => false,
            'message' => 'Số lượng sản phẩm không đủ'
        ];
    }

    // Khởi tạo giỏ hàng nếu chưa có
    initialize_cart();

    // Kiểm tra sản phẩm đã có trong giỏ hàng chưa
    $found = false;
    foreach ($_SESSION['cart'] as &$item) {
        if ((int)$item['product_id'] === (int)$product_id) {
            // Kiểm tra số lượng
            if ($product['quantity'] < ($item['quantity'] + $quantity)) {
                return [
                    'success' => false,
                    'message' => 'Số lượng sản phẩm không đủ'
                ];
            }

            $item['quantity'] += $quantity;
            $found = true;

            // Debug log
            error_log("DEBUG: Product $product_id found in cart. Updated quantity: {$item['quantity']}");
            break;
        }
    }
    unset($item); // Giải phóng tham chiếu để tránh side effects

    // Nếu sản phẩm chưa có trong giỏ hàng
    if (!$found) {
        $price = $product['sale_price'] > 0 ? $product['sale_price'] : $product['price'];
        $_SESSION['cart'][] = [
            'product_id' => (int)$product_id,
            'name' => $product['name'],
            'price' => $price,
            'image' => $product['image'],
            'quantity' => (int)$quantity,
            'slug' => $product['slug']
        ];

        // Debug log
        error_log("DEBUG: Product $product_id added as new item to cart. Quantity: $quantity");
    }

    // Nếu người dùng đã đăng nhập, lưu giỏ hàng vào database
    if (isset($_SESSION['user_id'])) {
        if ($found) {
            // Tìm thông tin sản phẩm trong giỏ hàng
            foreach ($_SESSION['cart'] as $item) {
                if ((int)$item['product_id'] === (int)$product_id) {
                    update_cart_item_in_database($_SESSION['user_id'], $product_id, $item['quantity']);
                    break;
                }
            }
        } else {
            $price = $product['sale_price'] > 0 ? $product['sale_price'] : $product['price'];
            save_cart_to_database(
                $_SESSION['user_id'],
                $product_id,
                $product['name'],
                $price,
                $product['image'],
                $quantity
            );
        }
    }

    return [
        'success' => true,
        'message' => 'Thêm sản phẩm vào giỏ hàng thành công'
    ];
}

/**
 * Cập nhật số lượng sản phẩm trong giỏ hàng
 */
function update_cart_item($product_id, $quantity) {

    // Lấy thông tin sản phẩm
    $product = get_product_by_id($product_id);

    if (!$product) {
        return [
            'success' => false,
            'message' => 'Sản phẩm không tồn tại'
        ];
    }

    // Kiểm tra số lượng
    if ($product['quantity'] < $quantity) {
        return [
            'success' => false,
            'message' => 'Số lượng sản phẩm không đủ'
        ];
    }

    // Khởi tạo giỏ hàng nếu chưa có
    initialize_cart();

    // Cập nhật số lượng
    foreach ($_SESSION['cart'] as &$item) {
        if ((int)$item['product_id'] === (int)$product_id) {
            $item['quantity'] = $quantity;
            break;
        }
    }
    unset($item); // Giải phóng tham chiếu

    // Nếu người dùng đã đăng nhập, cập nhật giỏ hàng trong database
    if (isset($_SESSION['user_id'])) {
        update_cart_item_in_database($_SESSION['user_id'], $product_id, $quantity);
    }

    return [
        'success' => true,
        'message' => 'Cập nhật giỏ hàng thành công'
    ];
}

/**
 * Xóa sản phẩm khỏi giỏ hàng
 */
function remove_from_cart($product_id) {
    // Khởi tạo giỏ hàng nếu chưa có
    initialize_cart();

    foreach ($_SESSION['cart'] as $key => $item) {
        if ((int)$item['product_id'] === (int)$product_id) {
            unset($_SESSION['cart'][$key]);
            // Sắp xếp lại mảng
            $_SESSION['cart'] = array_values($_SESSION['cart']);

            // Debug log
            error_log("DEBUG: Product $product_id removed from cart successfully");
            break;
        }
    }

    // Nếu người dùng đã đăng nhập, xóa sản phẩm khỏi giỏ hàng trong database
    if (isset($_SESSION['user_id'])) {
        remove_from_cart_in_database($_SESSION['user_id'], $product_id);
    }

    return [
        'success' => true,
        'message' => 'Xóa sản phẩm khỏi giỏ hàng thành công'
    ];
}

/**
 * Xóa toàn bộ giỏ hàng
 */
function clear_cart() {
    $_SESSION['cart'] = [];

    // Nếu người dùng đã đăng nhập, xóa toàn bộ giỏ hàng trong database
    if (isset($_SESSION['user_id'])) {
        clear_cart_in_database($_SESSION['user_id']);
    }

    return [
        'success' => true,
        'message' => 'Xóa giỏ hàng thành công'
    ];
}

/**
 * Lấy tổng số sản phẩm trong giỏ hàng (tổng quantity)
 */
function get_cart_count() {
    // Khởi tạo giỏ hàng nếu chưa có
    initialize_cart();

    $count = 0;
    foreach ($_SESSION['cart'] as $item) {
        $count += (int)$item['quantity'];
    }

    // Debug log
    error_log("DEBUG: get_cart_count() returning: $count");

    return (int)$count;
}

/**
 * Lấy số lượng items (sản phẩm khác nhau) trong giỏ hàng
 */
function get_cart_items_count() {
    // Khởi tạo giỏ hàng nếu chưa có
    initialize_cart();

    $count = count($_SESSION['cart']);

    // Debug log
    error_log("DEBUG: get_cart_items_count() returning: $count");

    return (int)$count;
}

/**
 * Lấy tổng tiền giỏ hàng
 */
function get_cart_total() {
    // Khởi tạo giỏ hàng nếu chưa có
    initialize_cart();

    $total = 0;
    foreach ($_SESSION['cart'] as $item) {
        $total += $item['price'] * $item['quantity'];
    }

    return $total;
}

/**
 * Lấy danh sách sản phẩm trong giỏ hàng
 *
 * @param int $limit Giới hạn số lượng sản phẩm trả về, mặc định là không giới hạn
 * @return array Danh sách sản phẩm trong giỏ hàng
 */
function get_cart_items($limit = null) {
    // Khởi tạo giỏ hàng nếu chưa có
    initialize_cart();

    // Đảm bảo tất cả items trong giỏ hàng có slug
    ensure_cart_items_have_slug();

    // Nếu không có giới hạn, trả về toàn bộ giỏ hàng
    if ($limit === null) {
        return $_SESSION['cart'];
    }

    // Nếu có giới hạn, trả về số lượng sản phẩm giới hạn
    return array_slice($_SESSION['cart'], 0, $limit);
}

/**
 * Đảm bảo tất cả items trong giỏ hàng có slug
 */
function ensure_cart_items_have_slug() {
    if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
        return;
    }

    $updated = false;
    foreach ($_SESSION['cart'] as &$item) {
        if (!isset($item['slug']) || empty($item['slug'])) {
            // Lấy slug từ database
            $product = get_product_by_id($item['product_id']);
            $item['slug'] = $product ? $product['slug'] : '';
            $updated = true;
        }
    }
    unset($item); // Giải phóng tham chiếu

    // Nếu có cập nhật, lưu lại vào database nếu user đã đăng nhập
    if ($updated && isset($_SESSION['user_id'])) {
        // Cập nhật timestamp để đồng bộ
        $_SESSION['cart_updated'] = time() * 1000;
    }
}
?>
